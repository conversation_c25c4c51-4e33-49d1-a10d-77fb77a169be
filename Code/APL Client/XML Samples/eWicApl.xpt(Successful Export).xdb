<?xml version="1.0"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xdb="http://www.borland.com/schemas/delphi/10.0/XMLDataBinding">
  <xs:element name="APLResponse" type="APLResponseType"/>
  <xs:complexType name="APLResponseType"><xs:annotation>
      <xs:appinfo xdb:docElement="APLResponse"/>
    </xs:annotation>
    <xs:sequence>
      <xs:element name="APLFiles" type="APLFilesType"/>
    </xs:sequence>
    <xs:attribute name="CompanyNumber" type="xs:integer"/>
    <xs:attribute name="StoreNumber" type="xs:integer"/>
    <xs:attribute name="UserName" type="xs:string"/>
    <xs:attribute name="Date" type="xs:string"/>
    <xs:attribute name="Time" type="xs:string"/>
    <xs:attribute name="FormatVersion" type="xs:decimal"/>
  </xs:complexType>
  <xs:complexType name="APLFilesType">
    <xs:sequence>
      <xs:element name="APLFile" type="APLFileType"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="APLFileType">
    <xs:sequence/>
    <xs:attribute name="State" type="xs:string"/>
    <xs:attribute name="BatchId" type="xs:integer"/>
    <xs:attribute name="ReceivedDate" type="xs:string"/>
  </xs:complexType>
</xs:schema>
