unit uAPLClient;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls,
  Dialogs, StdCtrls,
  XMLDoc,
  StrUtils,
  //StringUtils,
  SELoginResponse,
  SEConfigurationFileResponse,
  LaneService,
  ServerEPSConstants,
  Registry,
  ActiveX,
  ExtCtrls;

procedure Log(Msg: string; BufferIt: boolean = false);
function InitAPLClient: boolean;
function QuitAPLClient: boolean;
procedure Login;
function XML2ConfigurationFile(XMLIn: WideString; Files: NeededFileTypeArray; var XMLError: integer): integer;


implementation

uses
  SyncObjs,    //critical section
  TypInfo,Types,
  SE_WebClient,
  Base64,
  winsock,
  ZipForge,
  // AbUtils,AbBase, AbBrowse, AbZBrows, AbZipPrc, AbUnzPrc,AbUnzper,
  DateUtils,
  SECodeFileResponse,
  ..StoreConfigurations;

type
  TIdleThread = class(TThread)
  private
  protected
    procedure Execute; override;
  public
    constructor Create;
  end;

const
  sOffOn: array[boolean] of string[3] = ('OFF','ON');
  sOK: array[boolean] of string[4] = ('FAIL','OK');
  HANGUP_TIME = 300;
  LOG_NAMEONLY = 'MTX_DBC_Log';
  LOG_EXTONLY = '.txt';
  LOG_FILENAME = LOG_NAMEONLY + LOG_EXTONLY;
  SETTINGS_XML = 'StoreConfigurations.xml';
  DEFAULT_PORT = 443;
  DEFAULT_ADDR = '127.0.0.1';
  DEFAULT_TIMEOUT = 300;  // seconds
  DEFAULT_COMPANY = 999;
  DEFAULT_STORE = 1;
  DEFAULT_LANE_NUMBER = 9999;
  NOT_FOUND = -1;
  //REGISTRYMTX = 'REGISTRY.MTX';
  SETUPTXT = 'SETUP.TXT';
  OLDYEAR = 1900;
  GZIP_HEADER = 10;
  GZIP_1 = chr($1F);
  GZIP_2 = chr($8B);
  HexChrSet = '0123456789ABCDEF';
  REGISTRY_WRITE_SUCCESS = 1;
  REGISTRY_WRITE_FAILURE = -1;
  MIN_PER_HOUR = 60;
  URL_SVC1_PROD  = 'https://svc1.servereps.com/';
  PROXY_ON = true;
  PROXY_OFF = false;
  sPlural: array[boolean] of string[3] = ('s','');
  COUNT_REPEATS = true;

type
  enumTimeZero = (tDialup,tStatusSend,tDownloadFiles,tExchangeInfo);

var
  LogFilename,LocalDir,IP: string;
  Lock: TCriticalSection;
  Port,Timeout,Company,Store,Lane: integer;
  Idle: TIdleThread;
  TimeZero: array[enumTimeZero] of TDateTime;
  TimeLimitInMinutes: array[enumTimeZero] of integer;
  GotNewSettingsXML: boolean;

{$REGION '<Utility Functions>'}

procedure CreateLock;
begin
  if Lock = nil then Lock := TCriticalSection.Create;
end;

procedure AcquireLock;
begin
  if Lock = nil then Lock := TCriticalSection.Create;
  Lock.Acquire;
end;

procedure DestroyLock;
begin
  if Lock <> nil then FreeAndNil(Lock);
end;

procedure ReleaseLock;
begin
  if Lock <> nil then Lock.Release;
end;

procedure SetInitialTimers;   // initially do all these 1 minute after start
begin
  TimeLimitInMinutes[tStatusSend] := 1;
  TimeLimitInMinutes[tDownloadFiles] := 1;
  TimeLimitInMinutes[tExchangeInfo] := 1;
end;

procedure LogTimers;
begin
  Log(format('Next Dialup in %d minute%s',[TimeLimitInMinutes[tDialup],sPlural[TimeLimitInMinutes[tDialup]=1]]));
  Log(format('Next Status Send in %d minute%s',[TimeLimitInMinutes[tStatusSend],sPlural[TimeLimitInMinutes[tStatusSend]=1]]));
  Log(format('Next DownloadFiles in %d minute%s',[TimeLimitInMinutes[tDownloadFiles],sPlural[TimeLimitInMinutes[tDownloadFiles]=1]]));
  Log(format('Next ExchangeInfo (Upload log) in %d minute%s',[TimeLimitInMinutes[tExchangeInfo],sPlural[TimeLimitInMinutes[tExchangeInfo]=1]]));
end;

procedure ResetAllTimeZeros;
begin
  TimeZero[tDialup] := Now;
  TimeZero[tStatusSend] := Now;
  TimeZero[tDownloadFiles] := Now;
  TimeZero[tExchangeInfo] := Now;

  TimeLimitInMinutes[tDialup] := Timeout div 60;

  SetInitialTimers;
  LogTimers;
end;

function DialupTimeExpired: boolean;
begin
  result := SecondSpan(Now,TimeZero[tDialup]) > Timeout;
end;

function TimeExpired(TimerType: enumTimeZero; Minutes: integer): boolean;
begin
  result := MinuteSpan(Now,TimeZero[TimerType]) >= Minutes;
  if result then
    TimeZero[TimerType] := Now;
end;

function OldDate: TDateTime;
begin
  result := StartOfAYear(OLDYEAR);  // return a really OLD date...
end;

function GetDateTime(S: string): TDateTime;
var
  aYear,aMonth,aDay,aHour,aMinute,aSecond: word;
  OutDate: TDateTime;
begin
  // incoming string is of format:  '2007-01-31 12:59:59' 'YYYY-MM-DD HH:MM:SS'
  S := trim(S);
  aYear := StrToIntDef(copy(S,1,4),OLDYEAR);
  aMonth := StrToIntDef(copy(S,6,2),1);
  aDay := StrToIntDef(copy(S,9,2),1);
  aHour := StrToIntDef(copy(S,12,2),0);
  aMinute := StrToIntDef(copy(S,15,2),0);
  aSecond := StrToIntDef(copy(S,18,2),0);
  if TryEncodeDateTime(aYear,aMonth,aDay,aHour,aMinute,aSecond,0,OutDate)
    then result := OutDate
    else result := OldDate;  // return a really OLD date...
end;

function UnCompress(const S: string): string;
var
  InStream: TStream;
  OutStream: TStringStream;
  sIn,sResult: string;
  GZipSignature: boolean;    // probably unnecessary to check this
begin
  GZipSignature := (length(S) >= 2) and (S[1] = GZIP_1) and (S[2] = GZIP_2);
  if GZipSignature then
    begin
    sIn := copy(S,GZIP_HEADER+1,length(S));   // strip off GZip header bytes
    sResult := '';
    InStream := TStringStream.Create(sIn);
    try
      OutStream := TStringStream.Create(sResult);
      try
        //JTG: cannot do this in D2007+
        InflateStream(InStream,OutStream);

        result := OutStream.DataString;
      finally
        OutStream.Free;
      end;
    finally
      InStream.Free;
    end;
    end
  else
    result := S;   // do nothing if not a gzip
end;

function GetIntKeyFromRegistry(root: hkey; dir, keyName: string; Default: integer): integer;
var
  reg: TRegistry;
begin
  result := NOT_FOUND;
  try
    reg := TRegistry.create;
    try
      reg.Access := KEY_EXECUTE;         // read-only access
      reg.rootkey := root;
      if (reg.openkey(dir, false)) then
        begin
        result := Reg.ReadInteger(keyName);
        Reg.CloseKey;
        end
      else
        result := Default;
    finally
      reg.free;
    end;
  except on e : Exception do ;
  end;
end;

function TimeZoneBiasInHours: double;
var
  ATimeZone: TTimeZoneInformation;
begin
  case GetTimeZoneInformation(ATimeZone) of
    TIME_ZONE_ID_DAYLIGHT:
      result := ATimeZone.Bias + ATimeZone.DaylightBias;
    TIME_ZONE_ID_STANDARD:
      result := ATimeZone.Bias + ATimeZone.StandardBias;
    TIME_ZONE_ID_UNKNOWN:
      result := ATimeZone.Bias;
    else
      result := -1440;  // results in an error indication of -24 hours...
  end;
  result := -result/60.0;
end;

function GetLocalIP: string;
var
  wsaData: TWSAData;
  addr: TSockAddrIn;
  Phe: PHostEnt;
  szHostName: array[0..128] of Char;
begin
  Result := '';
  if WSAStartup($101, WSAData) <> 0 then
    exit;
  try
    if GetHostName(szHostName, 128) <> SOCKET_ERROR then
      begin
      Phe := GetHostByName(szHostName);
      if Assigned(Phe) then
        begin
        addr.sin_addr.S_addr := longint(plongint(Phe^.h_addr_list^)^);
        Result := inet_ntoa(addr.sin_addr);
        end;
      end;
  finally
    WSACleanup;
  end;
end;

function UnZip(Filename: string): boolean;  // fully qualified file name
//var
  // Zip: TAbUnZipper;
begin
  result := false;
  (*
  try
    Zip := TAbUnZipper.Create(nil);
    try
      if pos(':',Filename) = 0 then
        Filename := ExtractFileDrive(GetCurrentDir) + Filename;  // copy drive letter if none exists
      Zip.BaseDirectory := ExtractFileDir(Filename);
      Zip.OpenArchive(Filename);
      Zip.ExtractFiles('*.*');
      result := true;
    finally
      Zip.CloseArchive;
      Zip.Free;
    end;
  except on e: exception do
    raise;
  end;
  *)
end;

function Zip(const aFilename: string): string;
var
  Z: TZipForge;
begin
  result := '';
  try
    Z := TZipForge.Create(nil);
    try
      result := ChangeFileExt(aFilename,'.zip');
      if FileExists(result) then
        DeleteFile(result);
      Z.Filename := result;
      Z.OpenArchive;
      Z.BaseDir := ExtractFileDir(aFilename);
      Z.AddFiles(ExtractFilename(aFilename));
      Z.CloseArchive;
    finally
      Z.Free;
    end;
  except on e: exception do
    result := '';
  end;
end;

function RegLookup(FileName: string; const In_Key: string): string;
var
  Reg: TStringList;
begin
  result := '';
  Filename := ExtractFilePath(ParamStr(0)) + Filename;
  try
    if FileExists(Filename) then
      begin
      Reg := TStringList.Create;
      try
        Reg.Duplicates := dupAccept;
        Reg.CaseSensitive := false;
        Reg.Sorted := false;
        Reg.LoadFromFile(Filename);
        Reg.Sorted := true;    // optimum way.. load first THEN sort
        result := trim(Reg.Values[In_Key]);
        if length(result) > 0
          then Log(format('RegLookup: %s Value %s=%s is being used',[FileName,In_Key,result]))
          else Log(format('RegLookup: ****NOTICE: %s Value for %s not found',[FileName,In_Key]));
      finally
        Reg.Free;
      end;
      end
    else
      Log(format('RegLookup: ****NOTICE: File %s does not exist; so value for %s not found',[FileName,In_Key]));
  except on e: exception do
    Log('RegLookup: Exception - ' + e.Message);
  end;
end;

function SetFileCreateDate(Filename: string; Files: NeededFileTypeArray): integer;
var
  i: integer;
  S: string;
begin
  result := 0;
  for i := 0 to length(Files) - 1 do
    if SameText(Filename,ServerEPSDir+Files[i].File_Name) then  // find the file in the Files array
      begin
      result := FileSetDate(FileName,DateTimeToFileDate(Files[i].File_LastModified));
      if result <> 0
        then S := SysErrorMessage(result);  // allows us to interrogate with debugger
      break;   // stop looking once we find it...
      end;
end;

function XML2ConfigurationFile(XMLIn: WideString; Files: NeededFileTypeArray; var XMLError: integer): integer;
var
  XMLConfig: TXMLDocument;
  ConfigFile: IXMLConfigurationFileResponseType;
  FileData: string;
  f: textfile;
  Error: integer;
begin
  try
  CoInitialize(nil);         //CoInitializeEx(nil,COINIT_APARTMENTTHREADED);
    result := 109;   // empty XML
    try
      if XMLIn = '' then
        begin
        Log('XML2ConfigurationFile: Empty input (XMLIn) string');
        exit;
        end;

      result := ERR_SE_XML2ConfigurationFile_CreationLocal_XML;
      XMLConfig := TXMLDocument.Create(nil);
      XMLConfig.XML.Text := XMLIn;
      //Log('XML = '+XMLIn);
      result := ERR_SE_XML2ConfigurationFile_Mapping_XML;
      ConfigFile := GetConfigurationFileResponse(XMLConfig);

      Log('XML2ConfigurationFile: ConfigFile IXML object creation OK');
      try
        XMLError := ConfigFile.ErrorCode;
      except on e:exception do     // might get an exception if missing ErrorCode or if it's not an int
        begin
        Log('XML2ConfigurationFile: Error is getting ConfigFile.ErrorCode; setting it to 0');
        XMLError := ERR_OK;
        end;
      end;

      if XMLError <> ERR_OK then
        begin
        result := ERR_SE_XMLError;
        exit;
        end;

      if length(ConfigFile.FileData) <> ConfigFile.FileSize then
        begin
        result := ERR_WRONG_FILESIZE;
        exit;
        end;

      result := ERR_SE_XML2ConfigurationFile_B64Decode;
      FileData := B64Decode(ConfigFile.FileData);

      result := ERR_SE_XML2ConfigurationFile_Uncompress;
      FileData := UnCompress(FileData);
      result := ERR_SE_XML2ConfigurationFile_FileAssign;
      assignfile(f,LocalDir + ConfigFile.FileName);

      result := ERR_SE_XML2ConfigurationFile_FileRewrite;
      rewrite(f);
      try
        result := ERR_SE_XML2ConfigurationFile_FileWrite;
        write(f,FileData);
      finally
        result := ERR_SE_XML2ConfigurationFile_FileClose;
        closefile(f);
      end;
      result := ERR_SE_XML2ConfigurationFile_SetFileDate;
      Error := SetFileCreateDate(ServerEPSDir + ConfigFile.Filename,Files);
      if Error <> 0 then
        Log(format('XML2ConfigurationFile: File [%s] Error: %s',[ServerEPSDir + ConfigFile.Filename,SysErrorMessage(Error)]));
      result := ERR_OK;  // if we get here, we are fine...
      if SameText(ConfigFile.Filename,SETTINGS_XML) then
        GotNewSettingsXML := true;

    except on e: exception do
      Log('XML2ConfigurationFile: EXCEPTION - ' + e.Message);
    end;
  finally
  CoUnInitialize;
  end;
end;

function XML2CodeFile(XMLIn: WideString; var XMLError: integer): integer;
const
  COMPRESSION_NONE = 'None';    //jtg todo: remove this when SE makes this an int
var
  XMLCodeFile: TXMLDocument;
  CodeFileResponse: IXMLCodeFileResponseType;
  sContents,sCodeFile: string;
  f: textfile;
begin
  CoInitialize(nil);         //CoInitializeEx(nil,COINIT_APARTMENTTHREADED);
  try
    result := ERR_OK;
    Log('XML2CodeFile: XMLIn = ['+XMLin+']');

    XMLCodeFile := TXMLDocument.Create(nil);  // no need for 'try..finally since we don't explicitly free the interfaced obj.
    XMLCodeFile.XML.Text := XMLIn;

    CodeFileResponse := GetCodeFileResponse(XMLCodeFile);
    Log(format('XML2CodeFile: CodeFileResponse.Filename[%s] Size[%d] Action[%s]',
       [CodeFileResponse.Filename,CodeFileResponse.FileSize,CodeFileResponse.FileAction]));
    try
      XMLError := CodeFileResponse.ErrorCode;
      if XMLError <> 0 then
        begin
        result := ERR_SE_XMLError;
        Log(format('XML2CodeFile: CodeFileResponse.ErrorCode[%d] NOT ZERO - DISCONTINUING FILE CREATION...',[XMLError]));
        exit;
        end;
    except on e: exception do
      begin
      XMLError := 0;
      Log('XML2CodeFile: CodeFileResponse.ErrorCode is NULL; resetting to ZERO');
      end;
    end;

    sContents := B64Decode(CodeFileResponse.FileData);
    sCodeFile := Uncompress(sContents);
    assignfile(f,LocalDir + CodeFileResponse.FileName);

    rewrite(f);
    try
      write(f,sCodeFile);
    finally
      closefile(f);
    end;

    if length(CodeFileResponse.FileData) <> CodeFileResponse.FileSize then
      begin
      Log(format('XML2CodeFile ERROR: FileData length[%d] NOT EQUAL to reported Filesize[%d]',
         [length(CodeFileResponse.FileData),CodeFileResponse.FileSize]));
      result := ERR_WRONG_FILESIZE;  // jtg: should this be superceded by compression err?
      end;
    //XMLCodeFile.Free;    jtg: not necessary to free an interfaced object; will auto-free
    if (SameText(CodeFileResponse.FileAction,ACTION_EXTRACT_ZIP)) or
       (SameText(StrUtils.RightStr(CodeFileResponse.FileName,4),ZIPEXTENSION)) then
      begin
      Log('Unzipping ' + CodeFileResponse.FileName + ' in ' + LocalDir);
      if not UnZip(LocalDir + CodeFileResponse.FileName)
        then Log('Unzip FAILED');
      end;
  except on e: exception do
    begin
    Log('XML2CodeFile EXCEPTION: Writing CodeFileErrorXML.xml to default dir - ' + e.Message);
    result := ERR_SE_GetCodeFile_EXCEPTION;
    assignfile(f,LocalDir+'CodeFileErrorXML.xml');
    rewrite(f);
    write(f,XMLIn);
    closefile(f);
    end;
  end;
  CoUnInitialize;
end;

function GetCompanyNumber: integer;
begin
  result := 0;
  try
    result := GetIntKeyFromRegistry(HKEY_LOCAL_MACHINE, SERVEREPS_REGISTRY_LOCATION, COMPANY_KEY, DEFAULT_COMPANY);
    if result = NOT_FOUND then
      Log('Company Number NOT FOUND in Registry (or permission to access Registry Denied by OS)');
    if result < 0 then
      result := DEFAULT_COMPANY;
  except on e: exception do
    Log('GetCompanyNumber EXCEPTION '+e.Message);
  end;
end;

function GetStoreNumber: integer;
begin
  result := 0;
  try
    result := GetIntKeyFromRegistry(HKEY_LOCAL_MACHINE, SERVEREPS_REGISTRY_LOCATION, STORE_KEY, DEFAULT_STORE);
    if result = NOT_FOUND then
      Log('Store Number NOT FOUND in Registry (or permission to access Registry Denied by OS)');
    if result < 0 then
      result := DEFAULT_STORE;
  except on e: exception do
    Log('GetStoreNumber EXCEPTION '+e.Message);
  end;
end;

function ValidFilename(Filename: string): boolean;
const
  ForbiddenChars: set of Char = ['<','>','|','"','\','/',':','*','?'];
var
  i: integer;
begin
  result := Filename <> '';
  for i := 1 to length(Filename) do
    result := result and not (Filename[i] in ForbiddenChars);
end;

function FileTimeStamp(Filename: string): TDateTime;
var
  f: textfile;
  sData: string;
  PosModDate: integer;
begin
  assignfile(f,FileName);
  reset(f);
  try
    read(f,sData);
  finally
    closefile(f);
  end;

  PosModDate := pos('LastModified',sData);
  if PosModDate > 0 then    // if the XML or text file has 'LastModified' in there...
    result := GetDateTime(copy(sData,PosModDate+14,19))
  else                      // else use the file creation date as key
    begin
    result := FileDateToDateTime(FileAge(FileName));
    RecodeMillisecond(result,0);    // don't compare milliseconds....
    end;
end;

function GetVersionString(Filename,VerStr: string): string;
var
  Size,Handle: dword;
  Len: uint;
  Buffer,Value: pchar;
  TransNo: pLongInt;
  SFInfo: string;
begin
  result := '';
  Size := GetFileVersionInfoSize(pChar(FileName),Handle);
  if Size > 0 then
  begin
    Buffer := AllocMem(Size);
    try
      GetFileVersionInfo(pChar(FileName),0,Size,Buffer);
      VerQueryValue(Buffer, PChar('VarFileInfo\Translation'),Pointer(TransNo),Len);
      SFInfo := format('%s%.4x%.4x%s%s%',['StringFileInfo\',LoWord(TransNo^),HiWord(Transno^),'\',VerStr]);
      if VerQueryValue(Buffer,PChar(SFInfo),Pointer(Value),Len)
        then result := Value;
    finally
      if Assigned(Buffer) then
        FreeMem(Buffer,Size);    // always release memory that's hard-allocated
    end;
  end;
end;

function LocalFileIsDifferent(FileOnServer: NeededFileType): boolean;  // takes a single needed file type structure and returns true if needed to be updated
var
  FileVersionInfo,FullName: string;
//  CodeFileTimeStamp: TDateTime;  //per Jason, see below..
  LocalFileDT: TDateTime;
begin
  FileVersionInfo := '';
  result := (trim(FileOnServer.File_Name) <> '') and ValidFilename(FileOnServer.File_Name);
  if not result then
  begin
    Log('No server file information.');
    exit;
  end;
  //result is true if we are here; it is the default; file is different unless we prove otherwise

  // this embeds the 'version' into the filename we just got from the login results
  // because said filename does not have the version in it already..
  if (FileOnServer.Category = lfCodeFiles) and
     (SameText(StrUtils.RightStr(FileOnServer.File_Name,4),ZIPEXTENSION)) then
    FileOnServer.File_Name := LeftStr(FileOnServer.File_Name,length(FileOnServer.File_Name)-3) +
       FileOnServer.File_Version + ZIPEXTENSION;

  FullName := LocalDir + FileOnServer.File_Name;

  if not FileExists(FullName) then
  begin
    Log(format('%s does not exist locally. Will download. (Server FileVersion=%s)', [FullName, FileOnServer.File_Version]));
    exit;   //jtg: if no file, then file is DEFINITELY DIFFERENT!, so exit TRUE..
  end;

  case FileOnServer.Category of
    lfConfigFiles:
      begin
      LocalFileDT := FileTimeStamp(FullName);
      if CompareDateTime(FileOnServer.File_LastModified,OldDate) <= EqualsValue then
        begin
        Log('The ServerEPS file ' + FullName + ' has an invalid date; download will be skipped');
        result := false;
        end
      else
        result := SecondSpan(LocalFileDT,FileOnServer.File_LastModified) >= FILEDATE_RESOLUTION;
      //LogConfigDownload(FullName,result,FileOnServer.File_LastModified,LocalFileDT);
      end;
    lfCodeFiles:
      begin
      FileVersionInfo := GetVersionString(FullName,'FileVersion');
      if FileVersionInfo <> '' then   // if an exe or dll type of file...
        result := trim(FileVersionInfo) <> trim(FileOnServer.File_Version)
      else
        result := false; // file not exists, no versioning info... MUST DOWNLOAD.. // file is corrupt? then Download
      //LogCodeDownload(FullName, result, FileOnServer.File_Version, FileVersionInfo);
      end;
    end;
end;

function CopyFileTo(const Source, Destination: string): Boolean;
begin
  Result := CopyFile(PChar(Source), PChar(Destination), true);
end;

function FileToDynArray(const Filename: string): TByteDynArray;
var
  f: file of byte;
  b: char;
  i,iFileSize: integer;
begin
  SetLength(result,0);  // default
  try
    if fileexists(Filename) then
      begin
      assignfile(f,Filename);
      reset(f);
      iFileSize := FileSize(f);
      SetLength(result,iFileSize);     // build the TByteDynArray Request
      try
        for i := 0 to iFileSize-1 do
          read(f,result[i]);
      finally
        closefile(f);
      end;
      end;
  except on e: exception do
    begin
    Log('FileToDynArray - EXCEPTION: '+e.message);
    raise;   // re-raise this so we know what calling routine the error occurred in
    end;
  end;
end;

function PosInStrArray(SearchStr: string; Contents: array of string; const CaseSensitive: Boolean = True): Integer;
begin
  for Result := Low(Contents) to High(Contents) do
    begin
    if CaseSensitive then
      begin
      if SearchStr = Contents[Result] then exit;
      end
    else
      begin
      if ANSISameText(SearchStr, Contents[Result]) then exit;
      end;
    end;
  Result := -1;
end;

function StrToMonth(const AMonth: string): Byte;
begin
  Result := Succ(PosInStrArray(Uppercase(AMonth),
    ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'])); {do not localize}
end;

function Lpad(S: string; L: integer; PadChar: char = ' '): string;
begin
  result := S;
  while length(result) < L do
    result := PadChar + result;
end;

function ConvertFromSepsDate(ADate: string): string; // JTG: chg date to ADate, avoid confusion with intrinsic func of same name
var
  month,mm,dd,yyyy: string;
  p: integer;
begin
  try
    ADate := Trim(ADate);

    // discard time
    if UpperCase(ADate[Length(ADate)]) = 'M' then // this has a time stamp
      while ADate[Length(ADate)] <> ' ' do
        ADate := Copy(ADate, 1, Length(ADate)-1);
    ADate := Trim(ADate);

    // get mm [convert mmm to mm (Jan to 01)]
    p := Pos(' ', ADate);
    month := Copy(ADate, 1, p-1);
    mm := IntToStr(StrToMonth(month));
    mm := LPad(mm, 2, '0');

    // get dd
    ADate := Trim(Copy(ADate, p+1, Length(ADate))); // discard month
    p := Pos(' ', ADate);
    dd := Copy(ADate, 1, p-1);
    dd := LPad(dd, 2, '0');

    // get yyyy
    yyyy := Trim(Copy(ADate, p+1, Length(ADate)));  // discard day

    result := yyyy + mm + dd;
  except on e:exception do
    Log('ConvertFromSepsDate: EXCEPTION - '+e.message);
  end;
end;

function DateFromYYYYMMDD(DateTimeStr: string): TDateTime;   //JTG added as result of Micah 2007-02-28 request
const
  LenYYYYMMDD = 8;
var
  AYear,AMonth,ADay: Word;
  DT: TDateTime;
begin
  result := OldDate;    // if we die prematurely, this gets returned
  DateTimeStr := trim(dateTimeStr);

  // die if the lengths don't match
  if length(dateTimeStr) <> LenYYYYMMDD then exit;

  // now try to encode all the values
    try
      AYear := StrToIntDef(copy(DateTimeStr,1,4),0);    // AYear has to be a word value
      AMonth := StrToIntDef(copy(DateTimeStr,5,2),0);
      ADay := StrToIntDef(copy(DateTimeStr,7,2),0);
      if TryEncodeDateTime(AYear,AMonth,ADay,0,0,0,0,DT)
        then result := DT
        else result := OldDate;
    except
      result := OldDate;  // probably not necessary
    end;
end;

{$ENDREGION}

{$REGION '<Logging Functions>'}
// This proc takes care of all the tedious parameters of the CreateProcess API call.
function EasyCreateProcessEx(cmdLine: string; var aHandle: THandle; const Wait: Boolean = false; const TimeoutInterval: Cardinal = INFINITE): Boolean;
var
  dw: integer;
  lb: longbool;
  lp: pointer;
  ts: TStartupInfo;
  tp: TProcessinformation;
begin
  result := false;
  try
    dw:=0;
    lb:=false;
    lp:=nil;
    fillchar(ts, sizeof(ts), 0);
    fillchar(tp, sizeof(tp), 0);
    ts.dwflags := STARTF_USESHOWWINDOW;
    ts.wShowWindow := SW_HIDE;
    Result := CreateProcess(nil,pchar(cmdLine),nil,nil,lb,dw,lp,nil,ts,tp);
    if Wait and Result then
      WaitForSingleObject(tp.hProcess, TimeoutInterval);
    aHandle := tp.hProcess;
    CloseHandle(tp.hProcess);
    CloseHandle(tp.hThread);
  except on E: Exception do
  end;
end;

procedure WriteToLog(Msg: string);
var
  i: integer;
  log: TextFile;
  sLog: string;

  function DateTimeFromName(Filename: string): TDateTime;
  var
    YYYY,MM,DD,HH,NN,SS: word;
    i: integer;
    S: string;
  begin
    result := Now;
    for i := 1 to length(Filename) do
      if Filename[i] in ['0'..'9'] then
        begin
        S := copy(Filename,i,15);
        YYYY := StrToIntDef(copy(S,1,4),0);
        MM   := StrToIntDef(copy(S,5,2),0);
        DD   := StrToIntDef(copy(S,7,2),0);
        HH   := StrToIntDef(copy(S,10,2),0);
        NN   := StrToIntDef(copy(S,12,2),0);
        SS   := StrToIntDef(copy(S,14,2),0);
        if not TryEncodeDateTime(YYYY,MM,DD,HH,NN,SS,0,result)
          then result := Now;
        break;
        end;
  end;

  function CutOverToNewJournal: string;
  const
    KeepArchives: integer = 30;
  var
    Filename,YesterdaysLog: string;
    sr: TSearchRec;
  begin
    result := '';
    // delete files older than KeepArchives days old
    if FindFirst(LocalDir + LOG_NAMEONLY + '*' + LOG_EXTONLY, faAnyFile, sr) = 0 then
      try
        repeat
          Filename := LocalDir + sr.Name;
          if DaysBetween(Now,DateTimeFromName(sr.Name)) > KeepArchives then
            DeleteFile(PChar(Filename));
        until FindNext(sr) <> 0;
      finally
        SysUtils.FindClose(sr);
      end;
    // rename current archive to today's date...
    if KeepArchives > 0 then
      begin
      YesterdaysLog := LocalDir + LOG_NAMEONLY + '-' + FormatDateTime('yyyymmdd',Yesterday) + LOG_EXTONLY;
      if FileExists(YesterdaysLog) then
        result := format('Trying to rename %s to %s but it already exists!',[LogFilename,YesterdaysLog])
      else if RenameFile(LogFilename,YesterdaysLog) then
        result := format('Renamed %s to %s',[LogFilename,YesterdaysLog])
      else
        result := format('UNABLE to rename %s to %s',[LogFilename,YesterdaysLog]);
      end
    else
      begin
      SysUtils.DeleteFile(LogFilename);
      result := format('KeepArchives set to ZERO; Deleting current log: %s',[LogFilename]);
      end;
  end;

function UseCurrentLog: boolean;
var
  TimeStamp: TDateTime;
  FileDate: integer;
begin
  result := false;
  FileDate := FileAge(LogFilename);
  if FileDate >= 0 then
    begin
    TimeStamp := FileDateToDateTime(FileDate);
    result := SameDate(TimeStamp,Today);   // if the current log has the same timestamp as today, go ahead an use it.
    end;
end;

begin
  AcquireLock;
  sLog := '';
  if not UseCurrentLog
    then sLog := CutOverToNewJournal;
  AssignFile(log,LogFilename);
  {$I-} Append(log); {$I+}
  i := IOResult;
  if i <> 0 then
    if not FileExists(LogFilename) then
    begin
    {$I-} Rewrite(log); {$I+}
    i := IOResult;
    end;
  if i = 0 then
    try
      if sLog <> ''
        then writeln(log, FormatDateTime('yyyy-mm-dd hh:nn:ss.zzz ',Now) + 'DLL ' + sLog);
      writeln(log, FormatDateTime('yyyy-mm-dd hh:nn:ss.zzz ',Now) + 'DLL ' + Msg);
    finally
      CloseFile(log);
    end;
  ReleaseLock;
end;

procedure Log(Msg: string; BufferIt: boolean = false);      //DOEP-20879
const
  sBuffer: string = '';    //DOEP-20879
  Count: integer = 0;      //DOEP-20879
begin
  if BufferIt then         //DOEP-20879
    begin
    if Count = 0 then
      begin
      sBuffer := Msg;
      inc(Count);
      end
    else
      begin
      if Msg = sBuffer then
        inc(Count)
      else
        begin
        if Count = 1
          then WriteToLog(sBuffer)    // we didn't get a repeat, so just log it, and then log the new line as well
          else WriteToLog(format('%s (repeated %d times)',[sBuffer,Count]));  // log the buffered line, with the repeat count
        WriteToLog(Msg);                                               // then log the NEW line.. this one is NOT Buffered however
        Count := 0;
        sBuffer := '';
        end;
      end;
    end
  else
    WriteToLog(Msg);
end;

function PrintBinary(buffer: PChar; bufferSize: Integer): string;
var
  i: integer;
begin
  Result := '';
  for i := 0 to bufferSize - 1 do
    if ord(buffer[i]) < 32
      then result := format('%s[%2.2x]',[result,Ord(buffer[i])])
      else result := Result + buffer[i];
end;
{$ENDREGION}

{$REGION '<WebClient Calls>'}

function OpenEPSLoginResults(XMLIn: WideString; var ErrorCode: integer): NeededFileTypeArray;
const
  TITLE = 'OpenEPSLoginResults: ';
var
  XMLLogin: TXMLDocument;
  LoginResponse: IXMLLoginResponseType;
  i: integer;
  FileNum: integer;
  sError: string;
begin
  Log(TITLE+'XMLIn = '+XMLIn);
  SetLength(result,0);
  if length(trim(XMLIn)) > 0 then
    try
      CoInitialize(nil);
      try
        XMLLogin := TXMLDocument.Create(nil);
        XMLLogin.XML.Text := XMLIn;
        LoginResponse := GetLoginResponse(XMLLogin);
        ErrorCode := LoginResponse.ErrorCode;      // pass ErrorCode back out...
        FileNum := 0;
        SetLength(result,LoginResponse.ConfigurationFiles.Count + LoginResponse.CodeFiles.Count);
        for i := 0 to LoginResponse.ConfigurationFiles.Count-1 do
          begin
          Log(format('%sConfigurationFiles - File #%d',[TITLE,Filenum]));
          result[FileNum].Category := lfConfigFiles;
          result[FileNum].File_Type := LoginResponse.ConfigurationFiles.ConfigFile[i].Type_;
          result[FileNum].File_Name := lowercase(LoginResponse.ConfigurationFiles.ConfigFile[i].Name); // YHJ-691: lowercase
          result[FileNum].File_LastModified := GetDateTime(LoginResponse.ConfigurationFiles.ConfigFile[i].LastModified);
          result[FileNum].File_Version := '';
          inc(FileNum);
          end;
        for i := 0 to LoginResponse.CodeFiles.Count-1 do
          begin
          Log(format('%sCodeFiles - File #%d',[TITLE,Filenum]));
          result[FileNum].Category := lfCodeFiles;
          result[FileNum].File_Name := lowercase(LoginResponse.CodeFiles.CodeFile[i].Name); // YHJ-691: lowercase
          result[FileNum].File_Version := LoginResponse.CodeFiles.CodeFile[i].Version;
          result[FileNum].File_LastModified := OldDate;  // return a really old date
          result[FileNum].File_Type := '';
          inc(FileNum);
          end;
      except on e: exception do
        Log(format('%sEXCEPTION[%s] ErrorCode[%d] = %s',[TITLE,e.Message,ErrorCode,sError]));
      end
    finally
      CoUnInitialize;
    end
  else
    begin
    SetLength(result,0);  // zero file length
    ErrorCode := ERR_SERVER_EPS_EMPTY_LOGIN_XML;
    Log('The XML input to OpenEPSLoginResults is BLANK!!');
    end;
end;

function ServerEPS_Login(WSDLAddr: string; const CompanyNumber: Integer; const StoreNumber: Integer; const LaneNumber: Integer;
           var HardError: integer; var HardErrorStr: string; var Files: NeededFileTypeArray): TServerEpsLoginResult;
const
  TITLE = 'ServerEPS_Login: ';
var
  ErrorCode: integer;
  xml: string;
  sProxyServer: string;
begin
  sProxyServer := '';
  Log(format('%sWSDLAddr[%s] Company[%d] Store[%d] Lane[%d]',[TITLE,WSDLAddr,CompanyNumber,StoreNumber,LaneNumber]));
  HardErrorStr := '';
  try
    result := lrNone;
    HardError := ERR_OK;
    //CoInitialize(nil);
    xml := SE_OpenEpsLogin(WSDLAddr,sProxyServer,CompanyNumber,StoreNumber,LaneNumber,SE_AUTHENTICATION_KEY,HardError,HardErrorStr);
    //CoUninitialize;
    if length(trim(xml)) = 0 then
      begin
      result := lrFailure;
      Log(format('%sSE_OpenEpsLogin returned an empty XML. URL=[%s] HardError=[%d] HardErrorStr=[%s]',[TITLE,WSDLAddr,HardError,HardErrorStr]));
      end
    else
      begin
      Files := OpenEPSLoginResults(xml,ErrorCode);
      if length(Files) > 0 then
        result := lrWaitForFiles;
      end;
  except on e: exception do
    begin
    result := lrFailure;
    Log(format('%sEXCEPTION[%s]',[TITLE,e.Message,HardError,HardErrorStr]));
    end;
  end;
end;

function APLDownloadableFile(Filename: string): boolean;
begin
  result := SameText(Filename,'mtx_apl.upd') or
            SameText(Filename,'StoreConfigurations.xml') or
            SameText(Filename,'setup.txt') or
            SameText(Filename,'mtx_svc.upd');
end;


function APLGetLatestFiles(WSDLAddr: string; const CompanyNumber: Integer;
           const StoreNumber: Integer; const LaneNumber: Integer;
           var HardError: integer; var HardErrorStr: string; const Files: NeededFileTypeArray): TServerEpsLoginResult;
const
  TITLE = 'APLGetLatestFiles:';
var
  i,XMLError: integer;
  xml: string;
  sProxyServer: string;
begin
  Log(format('%s looking at a list with %d filenames...',[TITLE,length(Files)]));
  sProxyServer := '';
  HardError := ERR_OK;
  HardErrorStr := '';
  try
    result := lrNone;
    for i := 0 to length(Files) - 1 do
      begin
      Log(format('%s examining file #%d = %s',[TITLE,i,Files[i].File_Name]));
      if APLDownloadableFile(Files[i].File_Name) then
        begin
        Log(format('%s file #%d (%s) is marked DOWNLOADABLE',[TITLE,i,Files[i].File_Name]));
        if LocalFileIsDifferent(Files[i]) then
          begin
          Log(format('%s file #%d (%s) is DIFFERENT from local file.. trying download...',[TITLE,i,Files[i].File_Name]));
          case Files[i].Category of
            lfConfigFiles:
              begin
              Log(format('Calling SE_GetConfigurationFile: WSDLAddr[%s] Company[%d] Store[%d] Lane[%d] FileType[%s]',[WSDLAddr,CompanyNumber,StoreNumber,LaneNumber,Files[i].File_Type]));
              xml := SE_GetConfigurationFile(WSDLAddr,sProxyServer,CompanyNumber,StoreNumber,LaneNumber,SE_AUTHENTICATION_KEY,Files[i].File_Type,HardError,HardErrorStr);
              HardError := XML2ConfigurationFile(xml,Files,XMLError);
              if HardError = ERR_OK then
                case result of
                  lrNewCode : result := lrNewCodeAndConfig;
                  lrNewCodeAndConfig :;
                else
                  result := lrNewConfig;
                end
              else
                Log(format('%s Getting %s resulted in HardError %d',[TITLE,Files[i].File_Name,HardError]));
              end;
            lfCodeFiles:
              begin
              Log(format('Calling SE_GetCodeFile: WSDLAddr[%s] Company[%d] Store[%d] Lane[%d] FileName[%s]',[WSDLAddr,CompanyNumber,StoreNumber,LaneNumber,Files[i].File_Name]));
              xml := SE_GetCodeFile(WSDLAddr,sProxyServer,CompanyNumber,StoreNumber,LaneNumber,
                                  SE_AUTHENTICATION_KEY,Files[i].File_Name,HardError,HardErrorStr); // YHJ-691
              if length(XML) = 0 then
                Log(format('%s SE_GetCodeFile returned an EMPTY XML for %s; Err[%d] ErrStr[%s]',[TITLE,Files[i].File_Name,HardError,HardErrorStr]))
              else
                begin
                HardError := XML2CodeFile(xml,XMLError);
                if HardError = ERR_OK then
                  case result of
                    lrNewConfig        : result := lrNewCodeAndConfig;
                    lrNewCodeAndConfig : ;
                  else
                    result := lrNewCode;
                  end  //case
                else
                  Log(format('%s SE_GetCodeFile Getting %s resulted in HardError %d',[TITLE,Files[i].File_Name,HardError]));
                end;
              end;
          end; //case
          end  // if Different
        else
          Log(format('%s file #%d (%s) is NOT DIFFERENT from local file.. NO download!',[TITLE,i,Files[i].File_Name]));
        end
      else
        Log(format('%s file #%d (%s) is NOT in DOWNLOADABLE list.. NO download!',[TITLE,i,Files[i].File_Name]));
      end;  // for
    if result = lrNone then
      if HardError = ERR_OK
        then result := lrNothingNew
        else result := lrFailure;
  except on e: exception do
    begin
      result := lrFailure;
      Log(format('%s EXCEPTION[%s] Error[%d] = %S',[TITLE,e.Message,HardError,HardErrorStr]));
    end;
  end;
end;

function ExchangeInfoPerformAction(id: integer; actionCode, ADate: string): boolean;   // performs the LogUpload
const
  STATUS: array[boolean] of string[10] = ('FAILED','SUCCESSFUL');
  TITLE = 'ExchangeInfoPerformAction';
var
  s,SourceFileName,DestFileName,ErrorStr,URL,sProxyServer: string;
  Request: TFileUploadRequest;
  Response: LaneServicesResponse;
  isTodaysFile: boolean;
  L,FilenameLen,ErrorCode: integer;

  procedure SetError(errorCode, errorInfo: string);
  begin
    Request.ErrorCode := errorCode;
    Request.ErrorInfo := errorInfo;
  end;

  procedure CopyTodaysFile;  // also sets Request.ErrorCode if have problems...
  var
    s: string;
  begin
    s := '';
    if FileExists(SourceFileName) then
      begin
      if FileExists(DestFileName) then
        begin
        Log('CopyTodaysFile: trying to copy todays log, but destination file already exists.  Attempting to delete...');
        if not DeleteFile(DestFileName) then
          S := '[CopyTodaysFile: Destination file already exists. FAILED to delete ' + DestFileName + ']';
        end;
      Log('CopyTodaysFile: preparing to copy '+SourceFileName+' to '+DestFileName);
      if not CopyFileTo(SourceFileName, DestFileName) then
        s :=  '[CopyTodaysFile: FAILED to copy '+SourceFileName+' to '+DestFileName+']';
      end;

    if s <> '' then
      begin
      Log('ERROR: '+s);
      SetError(LS_ERROR_UNKNOWN, s);
      end;
  end;

  procedure UploadFile;   // also sets Request.ErrorCode if have problems...
  var
    ZipName: string;
  begin
    ZipName := Zip(DestFilename);
    Log(format('UploadFile: DestFilename[%s] --> ZipName[%s]',[DestFilename,ZipName]));
    Request.FileData := FileToDynArray(ZipName);
    L := length(Request.FileData);
    if L > MAX_UPLOAD_FILESIZE then   // if too big, don't even bother uploading and set error codes and strings
      begin
      ErrorCode := ERR_UPLOADFILESIZE_TOOBIG;
      ErrorStr := format('File %s is %d bytes too big to upload; size = %d but max allowable size = %d',[ZipName,L-MAX_UPLOAD_FILESIZE,L,MAX_UPLOAD_FILESIZE]);
      Log(format('UploadFile ERROR [SE_LSFileUpload for (%s) NOT CALLED - ErrorCode=%d ErrorStr=%s]',[ZipName,ErrorCode,ErrorStr]));
      SetError(LS_ERROR_FILE_IS_TOO_LARGE,'File is too large');
      end
    else
      begin
      try
        Log('CoInitialize before SE_LSFileUpload...');
        CoInitialize(nil);         //CoInitializeEx(nil,COINIT_APARTMENTTHREADED);
        Response := SE_LSFileUpload(URL+WSDL_ADDR_LANE,sProxyServer,Request,ErrorCode,ErrorStr);
        Log('CoUninitialize after SE_LSFileUpload...');
        CoUninitialize;
      except on e: exception do
        Log(format('UploadFile: SE_LSFileUpload: EXCEPTION - %s (%d) %s',[e.Message,ErrorCode,ErrorStr]));
      end;

      if (ErrorCode = ERR_OK) and (Response.Code = LS_SUCCESS) then
        begin
        Log('UploadFile SUCCESSFUL (SE_LSFileUpload for '+ZipName+')');
        //Log('UploadFile SUCCESSFUL (SE_LSFileUpload for '+destFileName+')');
        //if isTodaysFile then                     // we only want to do other actions like delete
        //  if not DeleteFile(destFileName) then   // original files IF the current upload was successful
        //    Log('ExchangeInfoPerformAction Cleanup failed to delete '+destFileName);
        end
      else
        Log(format('UploadFile: SE_LSFileUpload: ERROR: [(%s) FAILED to upload; ErrorCode=%d ErrorStr=%s]',[ZipName,ErrorCode,ErrorStr]));
      end;
    Log('Deleting '+ZipName);
    DeleteFile(ZipName);
    Log('Deleting '+DestFileName);
    DeleteFile(DestFileName);
  end;

  procedure PopulateRequest;
  begin
    FillChar(Request, sizeof(Request), 0);
    Request.Id := id;
    Request.Company := GetCompanyNumber;
    Request.Store := GetStoreNumber;
    Request.Lane := Lane;
  end;

begin
  try
    Log(format('%s ID(%d) ActionCode(%s) Date(%s)',[TITLE,id,actionCode,ADate]));
    if actionCode = AC_UPLOAD_JRNL then
      Log(TITLE+' actionCode = AC_UPLOAD_JRNL.. Uploading Log')
    else
      begin
      result := true;
      exit;
      end;

    result := false;       // default is failure if upload attempted
    url := RegLookup(SETUPTXT,_ServerEpsServicesHost1);
    if url = '' then
      url := URL_SVC1_PROD;
    Lane := DEFAULT_LANE_NUMBER;
    PopulateRequest;       // set all values except Filedata
    sProxyServer := '';

    ADate := ConvertFromSepsDate(ADate);   // use ADate not Date (which is an intrinsic func)
    isTodaysFile := IsToday(DateFromYYYYMMDD(ADate));
    SourceFileName := LocalDir + LOG_FILENAME;
    DestFileName := LocalDir + LOG_NAMEONLY + '-' + ADate + LOG_EXTONLY;
    if isTodaysFile then
      CopyTodaysFile;        // also sets Request.ErrorCode in case of error

    if Request.ErrorCode = '' then   // if no error yet
      if FileExists(destFileName) then
        UploadFile          // also sets Request.ErrorCode in case of error
      else
        begin
        s := TITLE+' Cannot upload log file - '+destFileName+' does not exist';
        Log('ERROR: '+s);
        SetError(LS_ERROR_FILE_DOES_NOT_EXIST, s);
        end;

    if Request.ErrorCode = '' then
      result := true                 // if still no error
    else
      begin
      Log('ERRORS OCCURRED DURING FILE UPLOAD REQUEST. Sending error notification msg');
      SetLength(Request.Filedata,0);
      Response := SE_LSFileUpload(url+WSDL_ADDR_LANE,sProxyServer,Request,ErrorCode,ErrorStr);
      Log('Error notification msg ' + STATUS[(ErrorCode = ERR_OK) and (Response.Code = LS_SUCCESS)]);
      end;
  except on e:exception do
    begin
    result := false;
    Log(TITLE+' EXCEPTION - '+e.message);
    end;
  end;
end;

function ExchangeInfo: boolean;
var
  Response: TActionEventArray;
  ErrorCode,i: integer;
  sProxyServer,sURL,ErrorStr: string;
begin
  result := false;

  sProxyServer := '';
  sURL := RegLookup(SETUPTXT,_ServerEpsServicesHost1);
  if sURL = '' then
    sURL := URL_SVC1_PROD;         // might be wrong URL here
  Lane := DEFAULT_LANE_NUMBER;
  Company := GetCompanyNumber;
  Store := GetStoreNumber;

  Log(format('ExchangeInfo URL(%s) Company(%d) Store(%d) Lane(%d)',[sURL,Company,Store,Lane]));
  ErrorCode := ERR_OK;
  ErrorStr := '';
  sProxyServer := '';
  result := false;
  try
    Response := SE_LSExchangeInfo(sURL+WSDL_ADDR_LANE,sProxyServer,GetLocalIP,'a-b-c-d',Now,TimeZoneBiasInHours,'OK',Company,Store,Lane,ErrorCode,ErrorStr);
    result := ErrorCode = 0;
    Log(format('ExchangeInfo: SE_LSExchangeInfo response has %d events',[length(Response)]));
    for i := Low(Response) to High(Response) do
      with Response[i] do
        begin
        Log(format('ExchangeInfo: ActionEvent[%d] ID[%d] Code[%s] Arguments[%s]',[i,Id,ActionCode,Arguments]));
        ExchangeInfoPerformAction(Response[i].Id, Response[i].ActionCode, Response[i].Arguments);   // performs Log upload in here, conditionally
        end;
  except on e: exception do
    Log(format('ExchangeInfo EXCEPTION - %s (%d) %s',[e.Message,ErrorCode,ErrorStr]));
  end;
  TimeLimitInMinutes[tExchangeInfo] := 10;              //default time expiration
  Log(format('ExchangeInfo Finished. Next ExchangeInfo in %d minutes.',[TimeLimitInMinutes[tExchangeInfo]]));
end;

function SendStatus: boolean;
var
  HardError: integer;
  HardErrorStr,sProxyServer,sURL: string;
begin
  result := false;
  sProxyServer := '';
  sURL := RegLookup(SETUPTXT,_ServerEpsServicesHost1);
  if sURL = '' then
    sURL := URL_SVC1_PROD;
  Lane := DEFAULT_LANE_NUMBER;
  Company := GetCompanyNumber;
  Store := GetStoreNumber;
  Log(format('SendStatus URL(%s) Company(%d) Store(%d) Lane(%d)',[sURL,Company,Store,Lane]));
  try
    if length(sURL) > 0 then
      begin
      if pos('trn',lowercase(sURL)) > 0
        then result := SE_SendStatus(sURL+WSDL_ADDR_PING,sProxyServer,Company,Store,GetLocalIP,TimeZoneBiasInHours,HardError,HardErrorStr)= ERR_OK
        else result := ExchangeInfo;
      if result
        then Log(format('SendStatus result = %s',[sOK[result]]))
        else Log(format('SendStatus  FAIL - HardError[%d] HardErrorStr[%s]',[HardError,HardErrorStr]));
      end
    else
      result := false;
  except on e: exception do
    Log(format('Connectivity Thread: EXCEPTION ConnectionStatus -> %s',[e.message]));
  end;
  TimeLimitInMinutes[tStatusSend] := MIN_PER_HOUR;      //default time expiration
  Log(format('SendStatus Finished. Next SendStatus in %d minutes.',[TimeLimitInMinutes[tDownloadFiles]]));
end;

procedure Settings;
var
  XML: IXMLStoreConfigurationsType;
  Filename: string;
begin
  try
    Company := GetCompanyNumber;
    Store := GetStoreNumber;
    Filename := ExtractFilePath(ParamStr(0)) + SETTINGS_XML;
    if FileExists(Filename) then
      begin
      Log('Loading settings from '+Filename);
      Log('CoInitialize...');
      CoInitialize(nil);         //CoInitializeEx(nil,COINIT_APARTMENTTHREADED);
      XML := LoadStoreConfigurations(Filename);
      IP := XML.DialBackupConfiguration.DBCIPAddress;
      if length(IP) = 0
        then IP := DEFAULT_ADDR;
      try
        Port := XML.DialBackupConfiguration.DBCPort;
      except on e: exception do
        Port := DEFAULT_PORT;
      end;
      try
        Timeout := XML.DialBackupConfiguration.DBCIdleTimeout;
        TimeLimitInMinutes[tDialup] := Timeout div 60;
      except on e: exception do
        Timeout := DEFAULT_TIMEOUT;
      end;
      Log('CoUninitialize...');
      CoUninitialize;
      end
    else
      begin
      IP := DEFAULT_ADDR;
      Port := DEFAULT_PORT;
      Timeout := DEFAULT_TIMEOUT;
      Log('Unable to find '+Filename+'... using default settings. RAS Name is blank so using 1st Phonebook entry until we get a new XML');
      end;
  except on e: exception do
    Log('Settings: EXCEPTION - '+e.Message);
  end;
end;

procedure Login;
var
  tempLoginResult: TServerEpsLoginResult;
  Files: NeededFileTypeArray;
  sLoginResult: string;
  loginResult:  TServerEpsLoginResult;
  ErrorCode,i: integer;
  url,ErrorStr: string;
begin
  url := RegLookup(SETUPTXT,_ServerEpsServicesHost1);
  if url = '' then
    url := URL_SVC1_PROD;
  Lane := DEFAULT_LANE_NUMBER;
  tempLoginResult := ServerEPS_Login(url+WSDL_ADDR_FILE,GetCompanyNumber,GetStoreNumber,Lane,ErrorCode,ErrorStr,Files);
  sLoginResult := GetEnumName(TypeInfo(TServerEpsLoginResult),ord(tempLoginResult));
  Log(format('Login: AFTER Login; Login result = %s; Files Count = %d ',[sLoginResult,length(Files)]));
  loginResult := tempLoginResult;

  if tempLoginResult in [lrFailure,lrWaitForLogin] then
    Log('NOT DOING GetLatestFiles because previous Login returned ' + sLoginResult)
  else
    begin
    Log('BEFORE GetLatestFiles...');
    GotNewSettingsXML := false;
    tempLoginResult := APLGetLatestFiles(url+WSDL_ADDR_FILE,GetCompanyNumber,GetStoreNumber,Lane,ErrorCode,ErrorStr,Files);
    Log('AFTER GetLatestFiles');
    loginResult := tempLoginResult;
    end;
  TimeLimitInMinutes[tDownloadFiles] := MIN_PER_HOUR;   //default time expiration
  Log(format('Login Finished. Next Login in %d minutes.',[TimeLimitInMinutes[tDownloadFiles]]));
end;

{$ENDREGION}

procedure IdleThreadDestroy;
begin
  try
    if Assigned(Idle) then
      begin
      Log(format('Idle Thread (ID=%d) Handle(%d) Destroyed',[Idle.ThreadID,Idle.Handle]));
      Idle.Terminate;
      Idle.Free;
      end;
  except on e: exception do
    Log('IdleThreadDestroy ERROR: ' + e.Message);
  end;
end;

procedure IdleThreadCreate;
begin
  try
    ResetAllTimeZeros;
    if not Assigned(Idle) then
      begin
      Log('IdleThreadCreate');
      Idle := TIdleThread.Create;
      Idle.FreeOnTerminate := true;
      Idle.Resume;
      end;
  except on e: exception do
    Log('IdleThreadCreate ERROR: ' + e.Message);
  end;
end;

constructor TIdleThread.Create;
const
  CREATE_SUSPENDED = true;
begin
  inherited Create(not CREATE_SUSPENDED);
end;

procedure TIdleThread.Execute;
begin
  try
    Log(format('Idle Thread ID(%d)) Handle(%d) EXECUTE - Timeout in %d secs',[ThreadID,Handle,Timeout]));
    while not Terminated do
      begin
      if TimeExpired(tDownloadFiles,TimeLimitInMinutes[tDownloadFiles]) then
        Login;
      if TimeExpired(tStatusSend,TimeLimitInMinutes[tStatusSend]) then
        SendStatus;
      if TimeExpired(tExchangeInfo,TimeLimitInMinutes[tExchangeInfo]) then
        ExchangeInfo
      else
        sleep(1000);
      end;
  except on e: exception do
    begin
    Log(format('EXCEPTION TIdleThread.Execute - ThreadID(%d) -> %s',[ThreadID,e.message]));
    Terminate;
    end;
  end;
end;

{$REGION '<Exported Functions>'}

function InitAPLClient: boolean;
const
  DLLNAME = 'mtx_apl.dll';
begin
  CoInitialize(nil);         //CoInitializeEx(nil,COINIT_APARTMENTTHREADED);
  result := false;
  LocalDir := ExtractFilePath(ParamStr(0));
  LogFilename :=  LocalDir + LOG_FILENAME;
  Log(format('InitAPLClient:  %s Version = %s',[DLLNAME,GetVersionString(LocalDir + DLLNAME,'FileVersion')]));
  try
    IdleThreadCreate;  //test
    result := true;
  except on e: exception do
    Log('InitAPLClient EXCEPTION - '+e.Message);
  end;
end;

function QuitAPLClient: boolean;
begin
  Log('QuitAPLClient');
  result := false;
  try
    sleep(1000);
    FreeAndNil(Idle);
    result := true;
    CoUnInitialize;
  except on e: exception do
    // nothing...
  end;
end;

{$ENDREGION}


{$REGION '<Deprecated Code>'}

{
  procedure ZipUp(S: string; DateStamp: string);
  var
    ZipCmd,ArcName,NewLogName: string;
    tmpHandle: THandle;
  begin
    ArcName := S + dateStamp + '.zip';
    NewLogName := S + dateStamp + '.txt';
    RenameFile(LocalDir + S + '.txt',LocalDir+newLogName);
    zipCmd := 'zip.exe -j "' + LocalDir + 'Archive\' + ArcName + '" "' + LocalDir + newLogName + '" "' + '"';
    EasyCreateProcessEx(zipCmd, tmpHandle, true, INFINITE);
    SysUtils.DeleteFile(LocalDir + newLogName);
  end;

procedure TProxyServer.IdMappedPortOutboundConnect(AContext: TIdContext; AException: Exception);
begin
  PeerIP := AContext.Connection.Socket.Binding.PeerIP;
end;

function DBC_GetConfigurationFile(const WSDLAddr: string; const CompanyNumber: Integer;
                    const StoreNumber: Integer; const LaneNumber: Integer;
                    const ConfigFileType: WideString;
                    var HardError: integer; var HardErrorStr: string): WideString;
var
  sProxyServer: string;
begin
  HardError := ERR_OK;
  HardErrorStr := '';
  sProxyServer := '';
  result := '';
  try
    result := SE_GetConfigurationFile(WSDLAddr,sProxyServer,CompanyNumber,StoreNumber,LaneNumber,
                    SE_AUTHENTICATION_KEY,ConfigFileType,HardError,HardErrorStr);
  except on e: exception do
    Log(format('DBC_GetConfigurationFile EXCEPTION %s (%d:%s)',[e.Message,HardError,HardErrorStr]));
  end;
end;

function DBC_GetCodeFile(const WSDLAddr: string; const CompanyNumber: Integer;
                    const StoreNumber: Integer; const LaneNumber: Integer;
                    const FileName: WideString;
                    var HardError: integer; var HardErrorStr: string): WideString;
var
  sProxyServer: string;
begin
  HardError := ERR_OK;
  HardErrorStr := '';
  sProxyServer := '';
  result := '';
  try
    result := SE_GetCodeFile(WSDLAddr,sProxyServer,CompanyNumber,StoreNumber,LaneNumber,
                      SE_AUTHENTICATION_KEY,FileName,HardError,HardErrorStr);
  except on e: exception do
    Log(format('DBC_GetCodeFile EXCEPTION %s (%d:%s)',[e.Message,HardError,HardErrorStr]));
  end;
end;

procedure TProxyServer.HangupTimerTimer(Sender: TObject);
const
  MS_PER_MINUTE = 60000;
  MS_PER_SECOND = 1000;
begin
  HangupTimer.Enabled := false;
  if HangupTimer.Interval >= 120000
    then Log(format('Connection has been open for %d minutes without a connection/transaction',[HangupTimer.Interval div MS_PER_MINUTE]))
  else if HangupTimer.Interval >= 60000
    then Log(format('Connection has been open for %d minute without a connection/transaction',[HangupTimer.Interval div MS_PER_MINUTE]))
  else
    Log(format('Connection has been open for %d seconds without a connection/transaction',[HangupTimer.Interval div MS_PER_SECOND]));
  Hangup;
  sleep(500);
  Log('Hangup re-try...');
  Hangup;
end;

function GetLatestFiles(WSDLAddr: string; const CompanyNumber: Integer;
           const StoreNumber: Integer; const LaneNumber: Integer;
           var HardError: integer; var HardErrorStr: string; const Files: NeededFileTypeArray): TServerEpsLoginResult;
var
  i,XMLError: integer;
  xml: string;
  sProxyServer: string;
begin
  sProxyServer := '';
  HardError := ERR_OK;
  HardErrorStr := '';
  try
    result := lrNone;

    for i := 0 to length(Files) - 1 do
    begin
      if LocalFileIsDifferent(Files[i]) then
      begin
        case Files[i].Category of
          lfConfigFiles:
            begin
              xml := SE_GetConfigurationFile(WSDLAddr,sProxyServer,CompanyNumber,StoreNumber,LaneNumber, SE_AUTHENTICATION_KEY,Files[i].File_Type,
                                          HardError,HardErrorStr);
              HardError := XML2ConfigurationFile(xml,Files,XMLError);
              if HardError = ERR_OK then
              begin
                case result of
                  lrNewCode          : result := lrNewCodeAndConfig;
                  lrNewCodeAndConfig : ;
                else
                  result := lrNewConfig;
                end;
              end
              else
                Log(format('GetLatestFiles had HardError(%d) getting %s',[HardError,Files[i].File_Name]));
            end;
          lfCodeFiles:
            begin
              xml := SE_GetCodeFile(WSDLAddr,sProxyServer,CompanyNumber,StoreNumber,LaneNumber,
                                  SE_AUTHENTICATION_KEY,Files[i].File_Name,HardError,HardErrorStr); // YHJ-691
              HardError := XML2CodeFile(xml,XMLError);
              begin  // Jason: are we missing 'if HardError = ERR_OK then' here?
                case result of
                  lrNewConfig        : result := lrNewCodeAndConfig;
                  lrNewCodeAndConfig : ;
                else
                  result := lrNewCode;
                end;
              end
            end;
        end;
      end; // end of if
    end; // end of for
    if result = lrNone then
      if HardError = ERR_OK
        then result := lrNothingNew
        else result := lrFailure;
  except on e: exception do
    begin
    result := lrFailure;
    Log(format('GetLatestFiles: EXCEPTION[%s] Error[%d] %s',[e.Message,HardError,HardErrorStr]));
    end;
  end;
end;

}
{$ENDREGION}

end.

