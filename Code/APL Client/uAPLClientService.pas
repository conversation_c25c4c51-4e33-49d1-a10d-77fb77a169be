unit uAPLClientService;

interface

{$IF CompilerVersion < 32.0}
This must be compiled with Delphi 10.2 (Tokyo) or later
{$IFEND}

uses
  Windows, Messages, SysUtils, Classes, Graphics, Controls, SvcMgr, Dialogs,
  ExtCtrls, APLSvcThread;

type
  TAPLClientService = class(TService)
    procedure ServiceAfterInstall(Sender: TService);      // CPCLIENTS-10531
    procedure ServiceExecute(Sender: TService);
    procedure ServiceStart(Sender: TService; var Started: Boolean);
    procedure ServiceStop(Sender: TService; var Stopped: Boolean);
    procedure ServiceShutdown(Sender: TService);
  private
    { Private declarations }
    TargetTime: TDateTime;
    FWorkerThread: TWorkerThread;
    procedure SetTargetTime;
    procedure LogRestartTime;
  public
    function GetServiceController: TServiceController; override;
    { Public declarations }
  end;

var
  APLClientService: TAPLClientService;

implementation

{$R *.DFM}
uses
  SyncObjs,    //critical section
  uAPL,
  ActiveX,
  APLClientConfiguration,
  DateUtils,
  System.Win.Registry;    // CPCLIENTS-10531

const
  APLDLL = 'mtx_apl.dll';
  APLDLL_UPD = 'mtx_apl.upd';
  APLDLL_OLD = 'mtx_apl.old';
  LOG_NAMEONLY = 'MTX_APL_Log';
  LOG_EXTONLY = '.txt';
  LOG_FILENAME = LOG_NAMEONLY + LOG_EXTONLY;
  SECOND = 1000;
  {$IFDEF APLTEST}
  MINUTES_OF_DELAY = 15;
  {$ELSE}
  MINUTES_OF_DELAY = 30;
  {$ENDIF}

type
  TInitAPLClient = function: boolean; stdcall;
  TQuitAPLClient = function: boolean; stdcall;

var
  APL_LHandle: THandle = 0;
  LogFilename,LocalDir: string;
  Lock: TCriticalSection;
  InitAPLEntry: TInitAPLClient;
  QuitAPLEntry: TQuitAPLClient;

{$REGION '<Utility Functions>'}

(*procedure ServiceController(CtrlCode: DWord); stdcall;
begin
  APLClientService.Controller(CtrlCode);
end;*)

procedure CreateLock;
begin
  if Lock = nil then Lock := TCriticalSection.Create;
end;

procedure AcquireLock;
begin
  if Lock = nil then Lock := TCriticalSection.Create;
  Lock.Acquire;
end;

procedure DestroyLock;
begin
  if Lock <> nil then FreeAndNil(Lock);
end;

procedure ReleaseLock;
begin
  if Lock <> nil then Lock.Release;
end;

(*
procedure WriteToLog(Msg: string);
var
  i: integer;
  log: TextFile;
  sLog: string;

  function DateTimeFromName(Filename: string): TDateTime;
  var
    YYYY,MM,DD,HH,NN,SS: word;
    i: integer;
    S: string;
  begin
    result := Now;
    for i := 1 to length(Filename) do
      if Filename[i] in ['0'..'9'] then
        begin
        S := copy(Filename,i,15);
        YYYY := StrToIntDef(copy(S,1,4),0);
        MM   := StrToIntDef(copy(S,5,2),0);
        DD   := StrToIntDef(copy(S,7,2),0);
        HH   := StrToIntDef(copy(S,10,2),0);
        NN   := StrToIntDef(copy(S,12,2),0);
        SS   := StrToIntDef(copy(S,14,2),0);
        if not TryEncodeDateTime(YYYY,MM,DD,HH,NN,SS,0,result)
          then result := Now;
        break;
        end;
  end;

  function CutOverToNewJournal: string;
  const
    KeepArchives: integer = 30;
  var
    Filename,YesterdaysLog: string;
    sr: TSearchRec;
  begin
    result := '';
    // delete files older than KeepArchives days old
    if FindFirst(LocalDir + LOG_NAMEONLY + '*' + LOG_EXTONLY, faAnyFile, sr) = 0 then
      try
        repeat
          Filename := LocalDir + sr.Name;
          if DaysBetween(Now,DateTimeFromName(sr.Name)) > KeepArchives then
            DeleteFile(PChar(Filename));
        until FindNext(sr) <> 0;
      finally
        SysUtils.FindClose(sr);
      end;
    // rename current archive to today's date...
    if KeepArchives > 0 then
      begin
      YesterdaysLog := LocalDir + LOG_NAMEONLY + '-' + FormatDateTime('yyyymmdd',Yesterday) + LOG_EXTONLY;
      if FileExists(YesterdaysLog) then
        result := format('Trying to rename %s to %s but it already exists!',[LogFilename,YesterdaysLog])
      else if RenameFile(LogFilename,YesterdaysLog) then
        result := format('Renamed %s to %s',[LogFilename,YesterdaysLog])
      else
        result := format('UNABLE to rename %s to %s',[LogFilename,YesterdaysLog]);
      end
    else
      begin
      SysUtils.DeleteFile(LogFilename);
      result := format('KeepArchives set to ZERO; Deleting current log: %s',[LogFilename]);
      end;
  end;

  function UseCurrentLog: boolean;
  var
    TimeStamp: TDateTime;
    FileDate: integer;
  begin
    result := false;
    FileDate := FileAge(LogFilename);
    if FileDate >= 0 then
      begin
      TimeStamp := FileDateToDateTime(FileDate);
      result := SameDate(TimeStamp,Today);   // if the current log has the same timestamp as today, go ahead an use it.
      end;
  end;

begin
  AcquireLock;
  sLog := '';
  if not UseCurrentLog
    then sLog := CutOverToNewJournal;
  AssignFile(log,LogFilename);
  {$I-} Append(log); {$I+}
  i := IOResult;
  if i <> 0 then
    if not FileExists(LogFilename) then
    begin
    {$I-} Rewrite(log); {$I+}
    i := IOResult;
    end;
  if i = 0 then
    try
      if sLog <> ''
        then writeln(log, FormatDateTime('yyyy-mm-dd hh:nn:ss.zzz ',Now) + 'SVC ' + sLog);
      writeln(log, FormatDateTime('yyyy-mm-dd hh:nn:ss.zzz ',Now) + 'SVC ' + Msg);
    finally
      CloseFile(log);
    end;
  ReleaseLock;
end;

procedure WriteToLog(Msg: string);
var
  i: integer;
  f: TextFile;
begin
  AcquireLock;
  AssignFile(f,LogFilename);
  {$I-} Append(f); {$I+}
  i := IOResult;
  if i <> 0 then
    if not FileExists(LogFilename) then
    begin
    {$I-} Rewrite(f); {$I+}
    i := IOResult;
    end;
  if i = 0 then
    try
      writeln(f, FormatDateTime('yyyy-mm-dd hh:nn:ss.zzz ',Now) + 'SVC ' + Msg);
    finally
      CloseFile(f);
    end;
  ReleaseLock;
end;
*)

procedure Log(Msg: string);
begin
  uAPL.Log('SVC '+Msg);
  //WriteToLog(Msg);
end;

procedure LogOnlyError(Msg: string);
var
  Err: integer;
begin
  Err := GetLastError;  // have to assign it because we use it twice; 1st call resets it
  if Err <> 0 then
    Log(format('****ERROR: %s error = %d',[Msg,Err]));
end;

function GetVersionString(Filename,VerStr: string): string;
var
  Size,Handle: dword;
  Len: uint;
  Buffer,Value: pchar;
  TransNo: pLongInt;
  SFInfo: string;
begin
  result := '';
  try
    Size := GetFileVersionInfoSize(pChar(FileName),Handle);
    if Size > 0 then
      begin
      Buffer := AllocMem(Size);
      try
        GetFileVersionInfo(pChar(FileName),0,Size,Buffer);
        VerQueryValue(Buffer, PChar('VarFileInfo\Translation'),Pointer(TransNo),Len);
        SFInfo := format('%s%.4x%.4x%s%s%',['StringFileInfo\',LoWord(TransNo^),HiWord(Transno^),'\',VerStr]);
        if VerQueryValue(Buffer,PChar(SFInfo),Pointer(Value),Len)
          then result := Value;
      finally
        if Assigned(Buffer) then
          FreeMem(Buffer,Size);    // always release memory that's hard-allocated
      end;
      end;
  except on e: exception do
    Log('GetVersionString: EXCEPTION = '+e.message);
  end;
end;

procedure SetLocalDirAndLogName;
begin
  LocalDir := ExtractFilePath(ParamStr(0));
  LogFilename :=  LocalDir + LOG_FILENAME;
  Log('SetLocalDirAndLogName: LogFilename = '+LogFilename);
end;

{$ENDREGION}

function UseLatestDll(aDllName, aUpdName, aOldName, aDir: string): string;
var
  dllVersion,updVersion: string;
begin
  Log('UseLatestDll');
  try
    result := '';   // default
    //GetUPD;
    dllVersion := GetVersionString(aDir + aDllName, 'FileVersion');
    Log(format('%s Version = %s',[aDllName,dllVersion]));
    if fileExists(aDir + aUpdName) then
      begin
      updVersion := GetVersionString(aDir + aUpdName, 'FileVersion');
      result := format('Versions: %s[%s] %s[%s]',[aUpdName,updVersion,aDllName,dllVersion]);
      if (updVersion <> dllVersion) then
        begin
        result := result + ' >> UPDATING';
        deleteFile(pchar(aDir + aOldName));
        sleep(100);
        renameFile(aDir + aDllName, aDir + aOldName);
        sleep(100);
        copyFile(pchar(aDir + aUpdName), pchar(aDir + aDllName),false);
        sleep(100);
        end
      else
        result := result + ' >> NO UPDATE';
      end
    else
      result := format('%s does not exist so will continue to use current DLL',[aDir+aUpdName]);
  except on e: exception do
    Log(format('UseLatestDll: EXCEPTION (%s) RESULT (%s)',[e.message,result]));
  end;
end;

function LoadAPLClientDLL: boolean;
const
  TITLE = 'LoadAPLClientDLL: ';
var
  S: string;
  Err: integer;
begin
  result := false;
  Log('LoadAPLClientDLL  >>>>>>>>>>');
  try
    SetLocalDirAndLogName;
    Log(TITLE+'Try to load ' + LocalDir + APLDLL);
    Err := GetLastError;      //just to clear it
    if APL_LHandle = 0 then
      begin
      //Log(TITLE+'Calling GetUPD');
      //GetUPD;
      Log(TITLE+'APL DLL is NOT LOADED so now we will see if a newer update has been previously downloaded');
      S := UseLatestDll(APLDLL,APLDLL_UPD,APLDLL_OLD,LocalDir);  // only update if not LOADED!!
      Log(S);
      sleep(100);   // sleep briefly to let filenames take root... 
      APL_LHandle := LoadLibrary(PChar(APLDLL));
      if APL_LHandle <> 0 then
        begin
        Log(TITLE +'LoadLibrary OK');
        @InitAPLEntry := GetProcAddress(APL_LHandle, 'InitAPL');
        LogOnlyError(TITLE+'InitAPL Function Call Address');
        @QuitAPLEntry := GetProcAddress(APL_LHandle, 'QuitAPL');
        LogOnlyError(TITLE+'QuitAPL Function Call Address');
        Log(TITLE + 'Calling InitAPL in the DLL');
        InitAPLEntry;
        end
      else
        Log(format('%s ****ERROR: LoadLibrary (Error %d) could not load ',[TITLE,GetLastError,APLDLL]));
      end
    else
      Log(TITLE+'Attempted to LoadLibrary, but handle not zero (already loaded)');
  except on e: exception do
    Log(TITLE+' EXCEPTION - ' + e.message);
  end;
end;

procedure UnloadAPLClientDLL;
const
  TITLE = 'UnloadAPLClientDLL:';
  RESTART_DELAY = SECOND*5;
begin
  Log(TITLE+' >>>>>>>>>>');
  try
    if APL_LHandle <> 0 then
      begin
      Log(TITLE+' Call QuitAPL in the APL DLL...');
      QuitAPLEntry;
      Log('UnloadAPLClientDLL APL_Dll_Busy=' + BoolToStr(APL_Dll_Busy, True));
      if APL_Dll_Busy then
      begin
        Log(format('%s QuitAPL called; delaying %d secs to allow RIO connections to close...',[TITLE,RESTART_DELAY div 1000]));
        sleep(RESTART_DELAY);
      end;
      Log(TITLE+' Unloading the APL DLL');
      if FreeLibrary(APL_LHandle) then
        begin
        Log(TITLE+' FreeLibrary SUCCESS - APL DLL Unloaded.');
        APL_LHandle := 0;
        end
      else
        Log(TITLE+' ERROR - FreeLibrary FAILED!');
      sleep(SECOND);
      end
    else
      Log(TITLE+' APL DLL already unloaded... (disregard)');
  except on e: exception do
    Log(TITLE+'EXCEPTION - '+e.message);
  end;
end;

procedure ServiceController(CtrlCode: DWord); stdcall;
begin
  APLClientService.Controller(CtrlCode);
end;

function TAPLClientService.GetServiceController: TServiceController;
begin
  Result := ServiceController;
end;

procedure TAPLClientService.LogRestartTime;
begin
  Log('APL DLL is scheduled to check for DLL update at: ' + FormatDateTime('yyyy-mm-dd hh:nn:ss',TargetTime));
end;

procedure TAPLClientService.ServiceAfterInstall(Sender: TService);  // CPCLIENTS-10531
var
  Reg: TRegistry;
begin
  Reg := TRegistry.Create(KEY_READ or KEY_WRITE);
  try
    Reg.RootKey := HKEY_LOCAL_MACHINE;
    if Reg.OpenKey('\SYSTEM\CurrentControlSet\Services\' + name, false) then
    begin
      Reg.WriteString('Description', 'WIC Approved Product List download service');
      Reg.CloseKey;
    end;
  finally
    Reg.Free;
  end;
end;

procedure TAPLClientService.ServiceExecute(Sender: TService);
begin
  Self.Status := csRunning;
  while not Terminated do
  begin
    ServiceThread.ProcessRequests(False);
    if not Terminated then
      TThread.Sleep(500);
  end;
  (*
  SetLocalDirAndLogName;
  SetTargetTime;
  Log('Service EXECUTE START >>>>>>>>>> ');
  //LoadAPLClientDLL;
  Self.Status := csRunning;
  while not Terminated do
    try
      if Now > TargetTime then
        try
          Log(format('APL DLL should have downloaded APL files and a possible DLL update %d minutes ago; ready to unload/reload DLL',[MINUTES_OF_DELAY]));
          (*
          UnloadAPLClientDLL;
          LoadAPLClientDLL;
          ReplaceDate(TargetTime,Tomorrow);
          LogRestartTime;
          //* )
        except on e: exception do
          Log('TAPLClientService.ServiceExecute EXCEPTION during DLL Load/Unload process: ' + e.Message);
        end;
      sleep(SECOND);
      ////////////////////////// ServiceThread.ProcessRequests(false);   // if get a termination then we quit...
    except on e: exception do
      Log('TAPLClientService.ServiceExecute EXCEPTION during main service loop' + e.Message);
    end;
  Log('Service EXECUTE END >>>>>>>>>> ');
  UnloadAPLClientDLL;
  *)
end;

procedure TAPLClientService.ServiceShutdown(Sender: TService);
begin
  Log('ServiceShutdown >>>>>>>>>> ');
///////////     UnloadAPLClientDLL;            // CPCLIENTS-10531
end;

procedure TAPLClientService.SetTargetTime;
var
  Config: IXMLAPLClientConfigurationType;
begin
  try
    CoInitialize(nil);         //CoInitializeEx(nil,COINIT_APARTMENTTHREADED);
    try
      Log('SetTargetTime BEGIN');
      Config := LoadAPLClientConfiguration(CONFIGFILE);
      if Config <> nil then
        begin
        Log('Config read OK, Daily Download Time = ' + Config.DownloadTime);
        TargetTime := IncMinute(StrToTime(Config.DownloadTime),MINUTES_OF_DELAY);
        ReplaceDate(TargetTime,Today);
        if TargetTime < Now then
          ReplaceDate(TargetTime,Tomorrow);
        end
      else
        begin
        Log(format('Config read FAILED; APL DLL update time set for %d minutes from now',[MINUTES_OF_DELAY]));
        TargetTime := IncMinute(Now,MINUTES_OF_DELAY);
        end;
    finally
      CoUnInitialize;
    end;
  except on e:exception do
    begin
    Log(format('Error reading Configuration file %s: %s',[CONFIGFILE,e.Message]));
    TargetTime := Now;
    ReplaceDate(TargetTime,Tomorrow);
    end;
  end;
  LogRestartTime;
end;

procedure TAPLClientService.ServiceStart(Sender: TService; var Started: Boolean);
begin
  FWorkerThread := TWorkerThread.Create(True);
  Self.Status := csStartPending;
  FWorkerThread.Start;
  (*
  SetLocalDirAndLogName;
  SetTargetTime;
  LoadAPLClientDLL;
  *)
  Log('Service START >>>>>>>>>> ');
  Started := True;
end;

procedure TAPLClientService.ServiceStop(Sender: TService; var Stopped: Boolean);
begin
  Log('Service STOP Stopping Worker Thread ');
  FWorkerThread.Terminate;
  FWorkerThread.WaitFor;
  FreeAndNil(FWorkerThread);
  Log('Service STOP Worker Thread Stopped');
  Self.Status := csStopped;
  Log('Service STOP >>>>>>>>>> ');
  (*
  UnloadAPLClientDLL;
  *)
  Stopped := True;
end;

end.
