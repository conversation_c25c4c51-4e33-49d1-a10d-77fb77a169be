object frmMain: TfrmMain
  Left = 0
  Top = 0
  BorderIcons = [biSystemMenu, biMinimize]
  BorderStyle = bsSingle
  Caption = 'APL Client Configuration'
  ClientHeight = 431
  ClientWidth = 425
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  GlassFrame.Enabled = True
  OldCreateOrder = False
  OnClose = FormClose
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object boxTestRequest: TGroupBox
    Left = 434
    Top = 8
    Width = 385
    Height = 418
    Caption = 'Test Request '
    TabOrder = 1
    object lblDomain: TLabel
      Left = 16
      Top = 19
      Width = 39
      Height = 13
      Caption = 'Domain:'
    end
    object lblResponse: TLabel
      Left = 10
      Top = 44
      Width = 93
      Height = 13
      Caption = 'RESPONSE (text)'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object LMDLabel1: TLabel
      Left = 11
      Top = 225
      Width = 92
      Height = 13
      Caption = 'RESPONSE (XML)'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object edtDomain: TEdit
      Left = 63
      Top = 16
      Width = 178
      Height = 21
      TabOrder = 0
      Text = 'https://www.servereps.com'
    end
    object btnTestRequest: TButton
      Left = 256
      Top = 12
      Width = 121
      Height = 23
      Caption = 'Test Request'
      TabOrder = 1
      OnClick = btnTestRequestClick
    end
    object Memo: TMemo
      Left = 3
      Top = 99
      Width = 369
      Height = 120
      TabOrder = 4
    end
    object WebBrowser1: TWebBrowser
      Left = 10
      Top = 246
      Width = 357
      Height = 159
      TabOrder = 5
      ControlData = {
        4C000000E62400006F1000000000000000000000000000000000000000000000
        000000004C000000000000000000000001000000E0D057007335CF11AE690800
        2B2E126208000000000000004C0000000114020000000000C000000000000046
        8000000000000000000000000000000000000000000000000000000000000000
        00000000000000000100000000000000000000000000000000000000}
    end
    object btnDLL: TButton
      Left = 256
      Top = 37
      Width = 121
      Height = 25
      Caption = 'DLL Start'
      TabOrder = 2
      OnClick = btnDLLClick
    end
    object btnGetUPD: TButton
      Left = 256
      Top = 64
      Width = 121
      Height = 25
      Caption = 'GetUPD'
      TabOrder = 3
      OnClick = btnGetUPDClick
    end
  end
  object boxConfiguration: TGroupBox
    Left = 8
    Top = 8
    Width = 409
    Height = 418
    Caption = 'Configuration '
    TabOrder = 0
    object lblConfigStoreNumber: TLabel
      Left = 41
      Top = 54
      Width = 70
      Height = 13
      Caption = 'Store Number:'
    end
    object lblConfigCompanyNumber: TLabel
      Left = 22
      Top = 31
      Width = 89
      Height = 13
      Caption = 'Company Number:'
    end
    object lblConfigDownloadTime: TLabel
      Left = 35
      Top = 77
      Width = 76
      Height = 13
      Caption = 'Download Time:'
    end
    object lblState: TLabel
      Left = 15
      Top = 194
      Width = 26
      Height = 13
      Caption = 'State'
    end
    object lblFilename: TLabel
      Left = 247
      Top = 194
      Width = 42
      Height = 13
      Caption = 'Filename'
    end
    object lblPath: TLabel
      Left = 55
      Top = 194
      Width = 134
      Height = 13
      Caption = 'POS Filepath for APL Import'
    end
    object lblUsername: TLabel
      Left = 59
      Top = 100
      Width = 52
      Height = 13
      Caption = 'Username:'
    end
    object lblPassword: TLabel
      Left = 61
      Top = 123
      Width = 50
      Height = 13
      Caption = 'Password:'
    end
    object LMDLabel4: TLabel
      Left = 37
      Top = 146
      Width = 74
      Height = 13
      Caption = 'Last Download:'
    end
    object lblServiceRunning: TLabel
      Left = 223
      Top = 376
      Width = 129
      Height = 14
      Caption = 'SERVICE IS STOPPED'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clRed
      Font.Height = -12
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Image1: TImage
      Left = 15
      Top = 387
      Width = 26
      Height = 18
    end
    object lblZip: TLabel
      Left = 383
      Top = 194
      Width = 19
      Height = 13
      Caption = 'Zip?'
    end
    object lblCompanyNumber: TLabel
      Left = 121
      Top = 31
      Width = 21
      Height = 13
      Caption = '999'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object lblStoreNumber: TLabel
      Left = 121
      Top = 54
      Width = 21
      Height = 13
      Caption = '999'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object edtState1: TEdit
      Left = 15
      Top = 206
      Width = 34
      Height = 21
      CharCase = ecUpperCase
      TabOrder = 5
      OnEnter = edtDownloadTimeEnter
    end
    object edtState2: TEdit
      Left = 15
      Top = 226
      Width = 34
      Height = 21
      CharCase = ecUpperCase
      TabOrder = 8
      OnEnter = edtDownloadTimeEnter
    end
    object edtState3: TEdit
      Left = 15
      Top = 246
      Width = 34
      Height = 21
      CharCase = ecUpperCase
      TabOrder = 11
      OnEnter = edtDownloadTimeEnter
    end
    object edtState4: TEdit
      Left = 15
      Top = 266
      Width = 34
      Height = 21
      CharCase = ecUpperCase
      TabOrder = 14
      OnEnter = edtDownloadTimeEnter
    end
    object edtState5: TEdit
      Left = 15
      Top = 286
      Width = 34
      Height = 21
      CharCase = ecUpperCase
      TabOrder = 17
      OnEnter = edtDownloadTimeEnter
    end
    object edtPath1: TEdit
      Left = 55
      Top = 206
      Width = 186
      Height = 21
      CharCase = ecUpperCase
      TabOrder = 6
      OnEnter = edtDownloadTimeEnter
    end
    object edtFilename1: TEdit
      Left = 247
      Top = 206
      Width = 130
      Height = 21
      CharCase = ecUpperCase
      TabOrder = 7
      OnEnter = edtDownloadTimeEnter
    end
    object edtPath2: TEdit
      Left = 55
      Top = 226
      Width = 186
      Height = 21
      CharCase = ecUpperCase
      TabOrder = 9
      OnEnter = edtDownloadTimeEnter
    end
    object edtPath3: TEdit
      Left = 55
      Top = 246
      Width = 186
      Height = 21
      CharCase = ecUpperCase
      TabOrder = 12
      OnEnter = edtDownloadTimeEnter
    end
    object edtPath4: TEdit
      Left = 55
      Top = 266
      Width = 186
      Height = 21
      CharCase = ecUpperCase
      TabOrder = 15
      OnEnter = edtDownloadTimeEnter
    end
    object edtPath5: TEdit
      Left = 55
      Top = 286
      Width = 186
      Height = 21
      CharCase = ecUpperCase
      TabOrder = 18
      OnEnter = edtDownloadTimeEnter
    end
    object edtFilename2: TEdit
      Left = 247
      Top = 226
      Width = 130
      Height = 21
      CharCase = ecUpperCase
      TabOrder = 10
      OnEnter = edtDownloadTimeEnter
    end
    object edtFilename3: TEdit
      Left = 247
      Top = 246
      Width = 130
      Height = 21
      CharCase = ecUpperCase
      TabOrder = 13
      OnEnter = edtDownloadTimeEnter
    end
    object edtFilename4: TEdit
      Left = 247
      Top = 266
      Width = 130
      Height = 21
      CharCase = ecUpperCase
      TabOrder = 16
      OnEnter = edtDownloadTimeEnter
    end
    object edtFilename5: TEdit
      Left = 247
      Top = 286
      Width = 130
      Height = 21
      CharCase = ecUpperCase
      TabOrder = 19
      OnEnter = edtDownloadTimeEnter
    end
    object btnSave: TButton
      Left = 15
      Top = 322
      Width = 153
      Height = 48
      Caption = 'Save Configuration'
      Enabled = False
      TabOrder = 20
      OnClick = btnSaveClick
    end
    object edtDownloadTime: TEdit
      Left = 119
      Top = 73
      Width = 82
      Height = 21
      Hint = 'should be in HH:MM 24-hour format'
      ParentShowHint = False
      ShowHint = True
      TabOrder = 1
      Text = '23:59'
      OnEnter = edtDownloadTimeEnter
    end
    object edtUsername: TEdit
      Left = 119
      Top = 97
      Width = 178
      Height = 21
      TabOrder = 2
      Text = 'AplExport'
      OnEnter = edtDownloadTimeEnter
    end
    object edtPassword: TEdit
      Left = 119
      Top = 120
      Width = 178
      Height = 21
      PasswordChar = '*'
      TabOrder = 3
      Text = '1ArgoFind'
      OnEnter = edtDownloadTimeEnter
    end
    object edtLastDownload: TMaskEdit
      Left = 119
      Top = 143
      Width = 82
      Height = 21
      Hint = 
        'this is automatically updated on every file download'#13#10#13#10'edit thi' +
        's only if you wish to manually modify the'#13#10'configuration file'
      ParentShowHint = False
      ShowHint = True
      TabOrder = 4
      Text = ''
      OnEnter = edtDownloadTimeEnter
    end
    object btnServiceStartStop: TButton
      Left = 192
      Top = 322
      Width = 185
      Height = 48
      Caption = 'Service Start'
      TabOrder = 0
      OnClick = btnServiceStartStopClick
    end
    object chkZip1: TCheckBox
      Left = 384
      Top = 208
      Width = 22
      Height = 17
      Hint = 'check this box if you wish the downloaded'#13#10'file to be zipped up'
      ParentShowHint = False
      ShowHint = True
      TabOrder = 21
      OnEnter = chkZip1Enter
    end
    object chkZip2: TCheckBox
      Left = 384
      Top = 228
      Width = 22
      Height = 17
      Hint = 'check this box if you wish the downloaded'#13#10'file to be zipped up'
      ParentShowHint = False
      ShowHint = True
      TabOrder = 22
      OnEnter = chkZip1Enter
    end
    object chkZip3: TCheckBox
      Left = 384
      Top = 248
      Width = 22
      Height = 17
      Hint = 'check this box if you wish the downloaded'#13#10'file to be zipped up'
      ParentShowHint = False
      ShowHint = True
      TabOrder = 23
      OnEnter = chkZip1Enter
    end
    object chkZip4: TCheckBox
      Left = 384
      Top = 268
      Width = 22
      Height = 17
      Hint = 'check this box if you wish the downloaded'#13#10'file to be zipped up'
      ParentShowHint = False
      ShowHint = True
      TabOrder = 24
      OnEnter = chkZip1Enter
    end
    object chkZip5: TCheckBox
      Left = 384
      Top = 288
      Width = 22
      Height = 17
      Hint = 'check this box if you wish the downloaded'#13#10'file to be zipped up'
      ParentShowHint = False
      ShowHint = True
      TabOrder = 25
      OnEnter = chkZip1Enter
    end
    object btnDownloadNow: TButton
      Left = 265
      Top = 11
      Width = 138
      Height = 25
      Hint = 
        'be mindful of the fact that if the "Last Download"'#13#10'field is too' +
        ' recent, then you might get nothing!'#13#10'Therefore, if desired, mod' +
        'ify "Last Download" '#13#10'and "Save Configuration" to force a file d' +
        'ownload'
      Caption = 'Download APL Now'
      ParentShowHint = False
      ShowHint = True
      TabOrder = 26
      OnClick = btnDownloadNowClick
    end
  end
  object XMLConfig: TXMLDocument
    Left = 360
    Top = 88
    DOMVendorDesc = 'MSXML'
  end
  object ServiceStatus: TTimer
    Interval = 5000
    OnTimer = ServiceStatusTimer
    Left = 328
    Top = 88
  end
  object ImageList: TImageList
    Left = 328
    Top = 128
    Bitmap = {
      494C010105000900040010001000FFFFFFFFFF10FFFFFFFFFFFFFFFF424D3600
      0000000000003600000028000000400000002000000001002000000000000020
      0000000000000000000000000000000000000000000077242100680B0400680B
      0400680B0400680B0400680B0400680B0400680B0400680B0400680B0400680B
      0400680B0400680B040083363300FBF6F5000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000000000000000000000000000000000000B1676200D7BEBC0077242100680B
      0400680B0400680B0400680B0400680B0400680B0400680B0400680B0400680B
      0400680B040083363300E7D6D600AE3A35000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000009A484200B1676200D7BEBC007724
      2100680B0400680B0400680B0400680B0400680B0400680B0400680B0400680B
      040083363300E7D6D600AE3A3500A3150A000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000009A4842009A484200B1676200D7BE
      BC00680B0400680B0400680B0400680B0400680B0400680B0400680B0400680B
      0400E7D6D600AE3A3500A3150A00A3150A000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000009A4842009A4842009A4842009A48
      4200D7BEBC0077242100680B0400680B0400680B0400680B040083363300E7D6
      D600A3150A00A3150A00A3150A00A3150A000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000009A4842009A4842009A4842009A48
      4200B1676200D7BEBC0077242100680B0400680B040083363300E7D6D600AE3A
      3500A3150A00A3150A00A3150A00A3150A000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000009A4842009A4842009A4842009A48
      42009A484200B1676200D7BEBC007724210083363300E7D6D600AE3A3500A315
      0A00A3150A00A3150A00A3150A00A3150A000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000009A4842009A4842009A4842009A48
      42009A4842009A484200B1676200FAF2F200FCF7F700AE3A3500A3150A00A315
      0A00A3150A00A3150A00A3150A00A3150A000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000009A4842009A4842009A4842009A48
      42009A4842009A484200A6555000FCF9F800FAF2F200A9292300A3150A00A315
      0A00A3150A00A3150A00A3150A00A3150A000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000009A4842009A4842009A4842009A48
      42009A484200A6555000EEE3E200D8858100DE9A9700F1E1E000A9292300A315
      0A00A3150A00A3150A00A3150A00A3150A000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000009A4842009A4842009A4842009A48
      4200A6555000EEE3E200D8858100CC4D4400CC4D4400DE9A9700F1E1E000A929
      2300A3150A00A3150A00A3150A00A3150A000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000009A4842009A4842009A4842009A48
      4200EEE3E200D8858100CC4D4400CC4D4400CC4D4400CC4D4400DE9A9700F1E1
      E000A3150A00A3150A00A3150A00A3150A000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000009A4842009A484200A6555000EEE3
      E200CC4D4400CC4D4400CC4D4400CC4D4400CC4D4400CC4D4400CC4D4400CC4D
      4400F1E1E000A9292300A3150A00A3150A000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000009A484200A6555000EEE3E200D885
      8100CC4D4400CC4D4400CC4D4400CC4D4400CC4D4400CC4D4400CC4D4400CC4D
      4400DE9A9700F1E1E000A9292300A3150A000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000000000000000000000000000000000000A6555000EEE3E200D8858100CC4D
      4400CC4D4400CC4D4400CC4D4400CC4D4400CC4D4400CC4D4400CC4D4400CC4D
      4400CC4D4400DE9A9700F1E1E000A92923000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000000000000000000000000000000000000FBF6F500D8858100CC4D4400CC4D
      4400CC4D4400CC4D4400CC4D4400CC4D4400CC4D4400CC4D4400CC4D4400CC4D
      4400CC4D4400CC4D4400DE9A9700000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000808080008080
      8000808080000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      00000000000000000000000000000000000000000000593DB10045229E004522
      9E0045229E0045229E0045229E0045229E0045229E0045229E0045229E004522
      9E0045229E0045229E00593DB100F6F5FD000000000000000000000000000000
      0000FFFF0000FF000000FF000000FF000000FF000000FF000000000000008080
      8000808080000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000007656D400BDB3E300593DB1004522
      9E0045229E0045229E0045229E0045229E0045229E0045229E0045229E004522
      9E0045229E00593DB100C9C4E9005836D6000000000000000000000000000000
      0000FFFF0000FF000000BFBF0000FFFF0000FFFF0000FF000000000000008080
      8000808080000000000000000000000000000000000000000000000000000000
      000083A0800043793D0024671C0014630C001A7C10003494290060AA580098BA
      9400000000000000000000000000000000000000000000000000000000000000
      0000A281A0007A3D76005E175A005606510060075A007C1E76009B4F9600B38F
      B100000000000000000000000000000000006942CA007959D500BDB3E300593D
      B10045229E0045229E0045229E0045229E0045229E0045229E0045229E004522
      9E00593DB100C9C4E9005836D6003F07CE000000000000000000000000000000
      0000FFFF0000FF0000000000000080808000FFFF0000FF000000000000008080
      8000808080000000000000000000000000000000000000000000000000000000
      000013450D000B4305000E58060016740B001D8A11002097130020A3120034B1
      2800000000000000000000000000000000000000000000000000000000000000
      0000460C4200380034004801430051004C0063015D0072016B007D017500941A
      8C00000000000000000000000000000000006942CA006942CA007959D500BDB3
      E30045229E0045229E0045229E0045229E0045229E0045229E0045229E004522
      9E00C9C4E9005836D6003F07CE003F07CE000000000000000000000000000000
      0000FFFF0000FF0000000000000000000000BFBF0000FF000000000000008080
      8000808080000000000000000000000000000000000000000000809D7D000E38
      09000C44060015600C0025801A00339B290048B03C004DBC42004EC0410046C6
      38003CC22F009CBF9800000000000000000000000000000000009E7E9C003B0B
      3800370234004403410056065100680A62007A0E7400870D8000930C8B009908
      8F00AF20A600BC96BA0000000000000000006942CA006942CA006942CA006942
      CA00BDB3E3005638AE0045229E0045229E0045229E0045229E00593DB100C9C4
      E9003F07CE003F07CE003F07CE003F07CE000000000000000000000000000000
      0000FFFF0000FF000000FF000000FF000000FF000000FF000000000000008080
      80008080800000000000000000000000000000000000000000003A6B35000424
      01000E4B07001B6C12002F8E260045AB3D005DBD52006ACB60006BCF610060D2
      530033C8230072C26900000000000000000000000000000000006C3669002100
      1F003C0339004E084900620C5C0078167300891D8200961F8E009F1A9700A613
      9D00AF05A400B95FB30000000000000000006942CA006942CA006942CA006942
      CA007A5BD600BDB3E3005638AE0045229E0045229E00593DB100C9C4E9005836
      D6003F07CE003F07CE003F07CE003F07CE000000000000000000000000000000
      0000FFFF0000FF000000FF000000FF000000FF000000FF000000000000008080
      8000808080008080800000000000000000000000000000000000154010000525
      01000F5307002173180039972E0057B54D007ACB700093D98B0082D8780074D7
      67003ECA2F004BC23E000000000000000000000000000000000041103E002100
      1F003E053A00540C4F006C186800862780009A369300AD40A700AF3DA700B029
      A700B608AB00B830AF0000000000000000006942CA006942CA006942CA006942
      CA006942CA007A5BD600BDB3E3005334AC00593DB100C9C4E9005836D6003F07
      CE003F07CE003F07CE003F07CE003F07CE000000000000000000000000000000
      0000FFFF0000FF000000FF000000FF000000FF000000FF000000000000008080
      8000808080008080800080808000000000000000000000000000051F02000424
      000012540A0023751B00409A340060B756007DCE740094DA8A0095DC8D007CD8
      710041CC310031C3210000000000000000000000000000000000240322002100
      1F003D073A005A12550076237100923A8C00AB56A600BE67B900C465BF00C149
      BA00B70FAD00BB13B00000000000000000006942CA006942CA006942CA006942
      CA006942CA006942CA007A5BD600F5F3FD00F8F7FD005836D6003F07CE003F07
      CE003F07CE003F07CE003F07CE003F07CE00000000000000000000000000FF00
      0000FF000000FF000000FF000000FF000000FF000000FF000000FF0000000000
      0000808080008080800080808000808080000000000000000000062303000424
      00001154090023761A003C9531005DB7530078C86F008BD681008CDA820079D6
      6E0040CA310033C423000000000000000000000000000000000020021F001D02
      1B003F093C005B1857007C2B760096459100B466AF00C97EC300CC80C800C75B
      BF00BA12B000BA13B00000000000000000006942CA006942CA006942CA006942
      CA006942CA006942CA007350D200F9F8FE00F7F5FD00502BD4003F07CE003F07
      CE003F07CE003F07CE003F07CE003F07CE000000000000000000FF000000FF00
      0000FF000000FF000000FF000000FF000000FF000000FF000000FF000000FF00
      0000000000008080800080808000808080000000000000000000133C0F000322
      00000F5008001D691400328929004FAA450068C05E0077CC6F007AD26F006AD1
      5F0037C727004AC13B00000000000000000000000000000000003E0F3C001E02
      1C003D083A005615510072286D0092418D00AB5CA600C176BD00C775C200C254
      BB00B411A900BA31B10000000000000000006942CA006942CA006942CA006942
      CA006942CA007350D200E6E3F6009178ED009D88EF00E8E6F9004D26D3003F07
      CE003F07CE003F07CE003F07CE003F07CE0000000000FF000000FF000000FF00
      0000FF000000FF000000FF000000FF000000FF000000FF000000FF000000FF00
      0000FF00000000000000808080008080800000000000000000003A6A3500031D
      00000A410400145A0C00267C1E003C99320051AF47005FC053005DC5530053C5
      45002BC01B006FC0660000000000000000000000000000000000683465001700
      160034063100490F4700691E630084347F009E459800AE54A900B44EAE00B438
      AB00B60CAB00BB5FB50000000000000000006942CA006942CA006942CA006942
      CA007350D200E6E3F6009178ED00632FE800632FE8009D88EF00E8E6F9004D26
      D3003F07CE003F07CE003F07CE003F07CE00FFFF0000FF000000FF000000FF00
      0000FF000000FF000000FF000000FF000000FF000000FF000000FF000000FF00
      0000FF000000FF000000000000008080800000000000000000007F9C7C000D35
      0900083A0200115209001B6D110028841F00379B2C0041AB360043B936003ABA
      2C0039BD2B009CBF9800000000000000000000000000000000009C7C9A003209
      2F002D042B003E0B3C0052134E006D1E6800822A8000932F8D009E2C9700A81D
      A000B123A800BD97BB0000000000000000006942CA006942CA006942CA006942
      CA00E6E3F6009178ED00632FE800632FE800632FE800632FE8009D88EF00E8E6
      F9003F07CE003F07CE003F07CE003F07CE00FFFF0000FF000000FF000000FF00
      0000FF00000000000000808080000000000000000000FFFF0000FF000000FF00
      0000FF000000FF00000000000000808080000000000000000000000000000000
      000012450C00073D01000B5103000E600500147709001A880E001A950D0030A9
      2300000000000000000000000000000000000000000000000000000000000000
      0000390B36002901260036053300460642005B0956006E0967007C087500931D
      8B00000000000000000000000000000000006942CA006942CA007350D200E6E3
      F600632FE800632FE800632FE800632FE800632FE800632FE800632FE800632F
      E800E8E6F9004D26D3003F07CE003F07CE00FFFF0000FF000000FF000000FF00
      0000000000008080800080808000000000000000000000000000FFFF0000FF00
      0000FF000000FF00000000000000808080000000000000000000000000000000
      000084A18000437A3D002164190012610A0015710B00308C25005EA7560096B8
      9200000000000000000000000000000000000000000000000000000000000000
      00009D7D9C006F376B0051154D004506410053094F0074206E00964D9100B28E
      B000000000000000000000000000000000006942CA007350D200E6E3F6009178
      ED00632FE800632FE800632FE800632FE800632FE800632FE800632FE800632F
      E800A18DEF00E3DFF8004D26D3003F07CE00FFFF0000FF000000FF000000FF00
      0000000000008080800080808000000000000000000000000000FFFF0000FF00
      0000FF000000FF00000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000007350D200E6E3F6009178ED00632F
      E800632FE800632FE800632FE800632FE800632FE800632FE800632FE800632F
      E800632FE800A390F000E3DFF8004D26D30000000000FFFF0000FF000000FF00
      0000000000000000000000000000000000000000000000000000FFFF0000FF00
      0000FF0000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000000000000000000000000000000000000F6F5FD009178ED00632FE800632F
      E800632FE800632FE800632FE800632FE800632FE800632FE800632FE800632F
      E800632FE800632FE800A390F000F2F0FD00424D3E000000000000003E000000
      2800000040000000200000000100010000000000000100000000000000000000
      000000000000000000000000FFFFFF0080000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      00000000000000000001000000000000F807FFFFFFFF8000F007FFFFFFFF0000
      F007F00FF00F0000F007F00FF00F0000F007C003C0030000F007C003C0030000
      F003C003C0030000F001C003C0030000E000C003C0030000C000C003C0030000
      8000C003C00300000000C003C00300000180F00FF00F000001C0F00FF00F0000
      01C1FFFFFFFF000087C3FFFFFFFF000000000000000000000000000000000000
      000000000000}
  end
end
