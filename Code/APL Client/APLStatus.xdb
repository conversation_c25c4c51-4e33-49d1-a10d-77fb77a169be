<?xml version="1.0"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xdb="http://www.borland.com/schemas/delphi/10.0/XMLDataBinding">
  <xs:element name="Lane" type="LaneType"/>
  <xs:complexType name="LaneType"><xs:annotation>
      <xs:appinfo xdb:docElement="Lane"/>
    </xs:annotation>
    <xs:sequence>
      <xs:element name="Drive" type="DriveType"/>
      <xs:element name="OSVersion" type="xs:string"/>
      <xs:element name="IpAddresses" type="IpAddressesType"/>
      <xs:element name="Modules" type="ModulesType"/>
    </xs:sequence>
    <xs:attribute name="Number" type="xs:integer"/>
    <xs:attribute name="LaneType" type="xs:string"/>
    <xs:attribute name="UpdateTime" type="xs:string"/>
  </xs:complexType>
  <xs:complexType name="DriveType">
    <xs:sequence/>
    <xs:attribute name="Letter" type="xs:string"/>
    <xs:attribute name="DriveSize" type="xs:integer"/>
    <xs:attribute name="FreeSpace" type="xs:integer"/>
  </xs:complexType>
  <xs:complexType name="IpAddressesType">
    <xs:sequence>
      <xs:element name="IPAddress" type="xs:decimal"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ModulesType">
    <xs:sequence>
      <xs:element name="Module" type="ModuleType" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ModuleType">
    <xs:sequence/>
    <xs:attribute name="Name" type="xs:string"/>
    <xs:attribute name="Version" type="xs:decimal"/>
  </xs:complexType>
  <xs:complexType name="ModuleType2">
    <xs:sequence/>
    <xs:attribute name="Name" type="xs:string"/>
    <xs:attribute name="Version" type="xs:decimal"/>
  </xs:complexType>
  <xs:complexType name="ModuleType22">
    <xs:sequence/>
    <xs:attribute name="Name" type="xs:string"/>
    <xs:attribute name="Version" type="xs:decimal"/>
  </xs:complexType>
</xs:schema>
