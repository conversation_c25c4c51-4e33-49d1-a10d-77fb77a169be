﻿<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <PropertyGroup>
        <ProjectGuid>{9ce724ec-c42f-4150-9bf1-0a290ab1db58}</ProjectGuid>
        <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
        <DCC_DCCCompiler>DCC32</DCC_DCCCompiler>
        <DCC_DependencyCheckOutputName>R:\mtx_apl.dll</DCC_DependencyCheckOutputName>
        <MainSource>mtx_apl.dpr</MainSource>
        <FrameworkType>None</FrameworkType>
        <ProjectVersion>18.4</ProjectVersion>
        <Base>True</Base>
        <Config Condition="'$(Config)'==''">Release</Config>
        <Platform Condition="'$(Platform)'==''">Win32</Platform>
        <TargetedPlatforms>129</TargetedPlatforms>
        <AppType>Library</AppType>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Base' or '$(Base)'!=''">
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Base)'=='true') or '$(Base_Win32)'!=''">
        <Base_Win32>true</Base_Win32>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win64' and '$(Base)'=='true') or '$(Base_Win64)'!=''">
        <Base_Win64>true</Base_Win64>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Release' or '$(Cfg_1)'!=''">
        <Cfg_1>true</Cfg_1>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Cfg_1)'=='true') or '$(Cfg_1_Win32)'!=''">
        <Cfg_1_Win32>true</Cfg_1_Win32>
        <CfgParent>Cfg_1</CfgParent>
        <Cfg_1>true</Cfg_1>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Debug' or '$(Cfg_2)'!=''">
        <Cfg_2>true</Cfg_2>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Cfg_2)'=='true') or '$(Cfg_2_Win32)'!=''">
        <Cfg_2_Win32>true</Cfg_2_Win32>
        <CfgParent>Cfg_2</CfgParent>
        <Cfg_2>true</Cfg_2>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base)'!=''">
        <GenDll>true</GenDll>
        <SanitizedProjectName>mtx_apl</SanitizedProjectName>
        <VerInfo_IncludeVerInfo>true</VerInfo_IncludeVerInfo>
        <VerInfo_AutoGenVersion>true</VerInfo_AutoGenVersion>
        <VerInfo_MajorVer>828</VerInfo_MajorVer>
        <VerInfo_MinorVer>7</VerInfo_MinorVer>
        <VerInfo_Build>1</VerInfo_Build>
        <VerInfo_Locale>1033</VerInfo_Locale>
        <VerInfo_Keys>CompanyName=MTXEPS, Inc.;FileDescription=Approved Products List DLL;FileVersion=828.7.0.1;InternalName=;LegalCopyright=(c) MTXEPS;LegalTrademarks=;OriginalFilename=;ProductName=;ProductVersion=*******;Comments=D2007</VerInfo_Keys>
        <DCC_Namespace>System;Xml;Data;Datasnap;Web;Soap;Winapi;Vcl;Xml.Win;System.Win;Mitov.Encoding;$(DCC_Namespace)</DCC_Namespace>
        <DCC_UnitSearchPath>Z:\829.2\Common;Z:\829.2\OpenEPS;Z:\Delphi\XE6\Component Source\LockBox 2.07 (D3-7)\source;Z:\Delphi\XE6\Component Source\Abbrevia 5.0;Z:\Delphi\XE6\Component Source\XmlParser (D4-7);Z:\Delphi\Delphi6\ZipForge 2.65\Source;Z:\Delphi\XE6\Component Source\madCollection\madKernel\Sources;Z:\Delphi\XE6\Component Source\madCollection\madBasic\Sources;Z:\Delphi\XE6\Component Source\SecureBlackbox 12.0.269\Sources;Z:\Delphi\XE6\Component Source\SecureBlackbox 12.0.269\Sources\dcu.unicode;Z:\Delphi\XE6\Component Source\SecureBlackbox 12.0.269\Classes\IndySSL\Client;Z:\Delphi\XE6\Component Source\SecureBlackbox 12.0.269\Classes\IndySSL\Server;Z:\Delphi\XE6\Component Source\SecureBlackbox 12.0.269\Sources\dcu;Z:\Delphi\Tokyo Source\Indy10\Core;Z:\Delphi\Tokyo Source\Indy10\Protocols;Z:\Delphi\Tokyo Source\Indy10\System;Z:\Delphi\Tokyo Source\indy\abstraction;Z:\Delphi\Tokyo Source\indy\implementation;Z:\Delphi\XE6\Component Source\DCP-IBM\source;Z:\Delphi\XE6\Component Source\Misc;Z:\Delphi\Tokyo Source\soap;Z:\Delphi\XE6\Component Source\oxml\units;$(DCC_UnitSearchPath)</DCC_UnitSearchPath>
        <DCC_Define>USE_INDY;INDYSSL;D2010_AND_LATER;__WOLF__;AUTHENTICODE;MSWINDOWS;LOGGING;APL;$(DCC_Define)</DCC_Define>
        <DCC_Alignment>1</DCC_Alignment>
        <DCC_WriteableConstants>true</DCC_WriteableConstants>
        <DCC_ExeOutput>R:\</DCC_ExeOutput>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Win32)'!=''">
        <DCC_Namespace>Data.Win;Datasnap.Win;Web.Win;Soap.Win;Bde;$(DCC_Namespace)</DCC_Namespace>
        <BT_BuildType>Debug</BT_BuildType>
        <VerInfo_Keys>CompanyName=;FileDescription=$(MSBuildProjectName);FileVersion=*******;InternalName=;LegalCopyright=;LegalTrademarks=;OriginalFilename=;ProductName=$(MSBuildProjectName);ProductVersion=*******;Comments=;ProgramID=com.embarcadero.$(MSBuildProjectName)</VerInfo_Keys>
        <VerInfo_MajorVer>1</VerInfo_MajorVer>
        <DCC_ExeOutput>C:\Program Files\MicroTrax\APLService\</DCC_ExeOutput>
        <VerInfo_MinorVer>0</VerInfo_MinorVer>
        <Manifest_File>(None)</Manifest_File>
        <VerInfo_Build>0</VerInfo_Build>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Win64)'!=''">
        <Icon_MainIcon>mtx_apl_Icon.ico</Icon_MainIcon>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1)'!=''">
        <Version>7.0</Version>
        <DCC_DebugInformation>0</DCC_DebugInformation>
        <DCC_LocalDebugSymbols>False</DCC_LocalDebugSymbols>
        <DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
        <DCC_Define>RELEASE;$(DCC_Define)</DCC_Define>
        <DCC_ExeOutput>C:\Program Files\MicroTrax\APLService\</DCC_ExeOutput>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1_Win32)'!=''">
        <VerInfo_MajorVer>1</VerInfo_MajorVer>
        <VerInfo_MinorVer>0</VerInfo_MinorVer>
        <VerInfo_Keys>CompanyName=NCR;FileDescription=$(MSBuildProjectName);FileVersion=*******;InternalName=APL Client DLL;LegalCopyright=2021 @NCR;LegalTrademarks=;OriginalFilename=;ProductName=$(MSBuildProjectName);ProductVersion=*******;Comments=;ProgramID=com.embarcadero.$(MSBuildProjectName)</VerInfo_Keys>
        <VerInfo_Release>1</VerInfo_Release>
        <VerInfo_AutoGenVersion>false</VerInfo_AutoGenVersion>
        <VerInfo_AutoIncVersion>true</VerInfo_AutoIncVersion>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2)'!=''">
        <Version>7.0</Version>
        <DCC_Define>DEBUG;XDEBUG;$(DCC_Define)</DCC_Define>
        <DCC_ExeOutput>R:\</DCC_ExeOutput>
        <DCC_ResourcePath>O:\D2007;..\Common\;..\OpenEPS;E:\Dev\Compo\LockBox for D2007\source;E:\dev\compo\Abbrevia 3.04\source;E:\Dev\Compo\XmlParser (D4-7);c:\Dev\Compo\ZipForge 2.65 (D4-8);E:\Dev\Compo\ComponentAce\ZipForge\Source;Z:\Delphi\2007 Source\Win32\soap;Z:\Delphi\XE6\Component Source\SecureBlackbox 12.0.266\Sources;Z:\Delphi\XE6\Component Source\SecureBlackbox 12.0.266\Classes\IndySSL\Client;Z:\Delphi\2010 Source\Indy\Indy10\Core;Z:\Delphi\2010 Source\Indy\Indy10\Protocols;Z:\Delphi\2010 Source\Indy\Indy10\System;$(DCC_ResourcePath)</DCC_ResourcePath>
        <DCC_ObjPath>O:\D2007;..\Common\;..\OpenEPS;E:\Dev\Compo\LockBox for D2007\source;E:\dev\compo\Abbrevia 3.04\source;E:\Dev\Compo\XmlParser (D4-7);c:\Dev\Compo\ZipForge 2.65 (D4-8);E:\Dev\Compo\ComponentAce\ZipForge\Source;Z:\Delphi\2007 Source\Win32\soap;Z:\Delphi\XE6\Component Source\SecureBlackbox 12.0.266\Sources;Z:\Delphi\XE6\Component Source\SecureBlackbox 12.0.266\Classes\IndySSL\Client;Z:\Delphi\2010 Source\Indy\Indy10\Core;Z:\Delphi\2010 Source\Indy\Indy10\Protocols;Z:\Delphi\2010 Source\Indy\Indy10\System;$(DCC_ObjPath)</DCC_ObjPath>
        <DCC_IncludePath>O:\D2007;..\Common\;..\OpenEPS;E:\Dev\Compo\LockBox for D2007\source;E:\dev\compo\Abbrevia 3.04\source;E:\Dev\Compo\XmlParser (D4-7);c:\Dev\Compo\ZipForge 2.65 (D4-8);E:\Dev\Compo\ComponentAce\ZipForge\Source;Z:\Delphi\2007 Source\Win32\soap;Z:\Delphi\XE6\Component Source\SecureBlackbox 12.0.266\Sources;Z:\Delphi\XE6\Component Source\SecureBlackbox 12.0.266\Classes\IndySSL\Client;Z:\Delphi\2010 Source\Indy\Indy10\Core;Z:\Delphi\2010 Source\Indy\Indy10\Protocols;Z:\Delphi\2010 Source\Indy\Indy10\System;$(DCC_IncludePath)</DCC_IncludePath>
        <DCC_WriteableConstants>True</DCC_WriteableConstants>
        <DCC_DcuOutput>O:\Delphi10Tokyo\</DCC_DcuOutput>
        <DCC_ObjOutput>O:\D2007</DCC_ObjOutput>
        <DCC_HppOutput>O:\D2007</DCC_HppOutput>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2_Win32)'!=''">
        <VerInfo_MajorVer>1</VerInfo_MajorVer>
        <VerInfo_MinorVer>0</VerInfo_MinorVer>
        <VerInfo_Build>0</VerInfo_Build>
        <VerInfo_Keys>CompanyName=;FileDescription=$(MSBuildProjectName);FileVersion=*******;InternalName=;LegalCopyright=;LegalTrademarks=;OriginalFilename=;ProductName=$(MSBuildProjectName);ProductVersion=*******;Comments=;ProgramID=com.embarcadero.$(MSBuildProjectName)</VerInfo_Keys>
        <Manifest_File>(None)</Manifest_File>
        <Debugger_HostApplication>C:\Program Files\MicroTrax\APLService\APLClientSRV.exe</Debugger_HostApplication>
        <Debugger_RunParams>/GUI</Debugger_RunParams>
        <DCC_ExeOutput>C:\Program Files\MicroTrax\APLService\</DCC_ExeOutput>
    </PropertyGroup>
    <ProjectExtensions>
        <Borland.Personality>Delphi.Personality.12</Borland.Personality>
        <Borland.ProjectType/>
        <BorlandProject>
            <Delphi.Personality>
                <Parameters>
                    <Parameters Name="UseLauncher">False</Parameters>
                    <Parameters Name="LoadAllSymbols">True</Parameters>
                    <Parameters Name="LoadUnspecifiedSymbols">False</Parameters>
                </Parameters>
                <VersionInfo>
                    <VersionInfo Name="IncludeVerInfo">True</VersionInfo>
                    <VersionInfo Name="AutoIncBuild">True</VersionInfo>
                    <VersionInfo Name="MajorVer">828</VersionInfo>
                    <VersionInfo Name="MinorVer">7</VersionInfo>
                    <VersionInfo Name="Release">0</VersionInfo>
                    <VersionInfo Name="Build">1</VersionInfo>
                    <VersionInfo Name="Debug">False</VersionInfo>
                    <VersionInfo Name="PreRelease">False</VersionInfo>
                    <VersionInfo Name="Special">False</VersionInfo>
                    <VersionInfo Name="Private">False</VersionInfo>
                    <VersionInfo Name="DLL">False</VersionInfo>
                    <VersionInfo Name="Locale">1033</VersionInfo>
                    <VersionInfo Name="CodePage">1252</VersionInfo>
                </VersionInfo>
                <VersionInfoKeys>
                    <VersionInfoKeys Name="CompanyName">MTXEPS, Inc.</VersionInfoKeys>
                    <VersionInfoKeys Name="FileDescription">Approved Products List DLL</VersionInfoKeys>
                    <VersionInfoKeys Name="FileVersion">828.7.0.1</VersionInfoKeys>
                    <VersionInfoKeys Name="InternalName"/>
                    <VersionInfoKeys Name="LegalCopyright">(c) MTXEPS</VersionInfoKeys>
                    <VersionInfoKeys Name="LegalTrademarks"/>
                    <VersionInfoKeys Name="OriginalFilename"/>
                    <VersionInfoKeys Name="ProductName"/>
                    <VersionInfoKeys Name="ProductVersion">*******</VersionInfoKeys>
                    <VersionInfoKeys Name="Comments">D2007</VersionInfoKeys>
                </VersionInfoKeys>
                <Source>
                    <Source Name="MainSource">mtx_apl.dpr</Source>
                </Source>
                <Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\dcloffice2k250.bpl">Microsoft Office 2000 Sample Automation Server Wrapper Components</Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\dclofficexp250.bpl">Microsoft Office XP Sample Automation Server Wrapper Components</Excluded_Packages>
                </Excluded_Packages>
            </Delphi.Personality>
            <Platforms>
                <Platform value="Linux64">True</Platform>
                <Platform value="Win32">True</Platform>
                <Platform value="Win64">False</Platform>
            </Platforms>
        </BorlandProject>
        <ProjectFileVersion>12</ProjectFileVersion>
    </ProjectExtensions>
    <Import Project="$(BDS)\Bin\CodeGear.Delphi.Targets"/>
    <ItemGroup>
        <DelphiCompile Include="$(MainSource)">
            <MainSource>MainSource</MainSource>
        </DelphiCompile>
        <DCCReference Include="uAPL.pas">
            <Form>DM</Form>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="APLStatus.pas"/>
        <BuildConfiguration Include="Debug">
            <Key>Cfg_2</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
        <BuildConfiguration Include="Base">
            <Key>Base</Key>
        </BuildConfiguration>
        <BuildConfiguration Include="Release">
            <Key>Cfg_1</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
    </ItemGroup>
    <Import Project="$(BDS)\Bin\CodeGear.Delphi.Targets" Condition="Exists('$(BDS)\Bin\CodeGear.Delphi.Targets')"/>
    <Import Project="$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj" Condition="Exists('$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj')"/>
</Project>
