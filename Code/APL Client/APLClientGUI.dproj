﻿<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <PropertyGroup>
        <ProjectGuid>{663b8a9e-620d-48a2-a65c-8eb9da18d06a}</ProjectGuid>
        <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
        <DCC_DCCCompiler>DCC32</DCC_DCCCompiler>
        <DCC_DependencyCheckOutputName>C:\Program Files\Microtrax\APLService\APLClientGUI.exe</DCC_DependencyCheckOutputName>
        <MainSource>APLClientGUI.dpr</MainSource>
        <Base>True</Base>
        <Config Condition="'$(Config)'==''">Release</Config>
        <TargetedPlatforms>1</TargetedPlatforms>
        <AppType>Application</AppType>
        <FrameworkType>VCL</FrameworkType>
        <ProjectVersion>18.4</ProjectVersion>
        <Platform Condition="'$(Platform)'==''">Win32</Platform>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Base' or '$(Base)'!=''">
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Base)'=='true') or '$(Base_Win32)'!=''">
        <Base_Win32>true</Base_Win32>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Release' or '$(Cfg_1)'!=''">
        <Cfg_1>true</Cfg_1>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Cfg_1)'=='true') or '$(Cfg_1_Win32)'!=''">
        <Cfg_1_Win32>true</Cfg_1_Win32>
        <CfgParent>Cfg_1</CfgParent>
        <Cfg_1>true</Cfg_1>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Debug' or '$(Cfg_2)'!=''">
        <Cfg_2>true</Cfg_2>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Cfg_2)'=='true') or '$(Cfg_2_Win32)'!=''">
        <Cfg_2_Win32>true</Cfg_2_Win32>
        <CfgParent>Cfg_2</CfgParent>
        <Cfg_2>true</Cfg_2>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base)'!=''">
        <Manifest_File>$(BDS)\bin\default_app.manifest</Manifest_File>
        <DCC_UnitSearchPath>Z:\829.2\Common;Z:\829.2\OpenEPS;Z:\Delphi\XE6\Component Source\LockBox 2.07 (D3-7)\source;Z:\Delphi\XE6\Component Source\Abbrevia 5.0;Z:\Delphi\XE6\Component Source\XmlParser (D4-7);Z:\Delphi\Delphi6\ZipForge 2.65\Source;Z:\Delphi\XE6\Component Source\madCollection\madKernel\Sources;Z:\Delphi\XE6\Component Source\madCollection\madBasic\Sources;Z:\Delphi\XE6\Component Source\SecureBlackbox 12.0.269\Sources;Z:\Delphi\XE6\Component Source\SecureBlackbox 12.0.269\Sources\dcu.unicode;Z:\Delphi\Tokyo Source\Indy10\Core;Z:\Delphi\Tokyo Source\Indy10\Protocols;Z:\Delphi\Tokyo Source\Indy10\System;Z:\Delphi\Tokyo Source\indy\abstraction;Z:\Delphi\Tokyo Source\indy\implementation;Z:\Delphi\XE6\Component Source\DCP-IBM\source;Z:\Delphi\XE6\Component Source\SecureBlackbox 12.0.269\Classes\IndySSL\Client;Z:\Delphi\XE6\Component Source\SecureBlackbox 12.0.269\Classes\IndySSL\Server;Z:\Delphi\XE6\Component Source\Misc;Z:\Delphi\Tokyo Source\soap;Z:\Delphi\XE6\Component Source\oxml\units;$(DCC_UnitSearchPath)</DCC_UnitSearchPath>
        <DCC_Namespace>Vcl;Vcl.Imaging;Vcl.Touch;Vcl.Samples;Vcl.Shell;System;Xml;Data;Datasnap;Web;Soap;Winapi;Xml.Win;System.Win;$(DCC_Namespace)</DCC_Namespace>
        <VerInfo_Build>11</VerInfo_Build>
        <VerInfo_AutoGenVersion>true</VerInfo_AutoGenVersion>
        <VerInfo_IncludeVerInfo>true</VerInfo_IncludeVerInfo>
        <VerInfo_Locale>1033</VerInfo_Locale>
        <VerInfo_MajorVer>827</VerInfo_MajorVer>
        <VerInfo_MinorVer>1</VerInfo_MinorVer>
        <VerInfo_Keys>CompanyName=;FileDescription=;FileVersion=827.1.0.11;InternalName=;LegalCopyright=;LegalTrademarks=;OriginalFilename=;ProductName=;ProductVersion=*******;Comments=D2007</VerInfo_Keys>
        <SanitizedProjectName>APLClientGUI</SanitizedProjectName>
        <DCC_Define>USE_INDY;INDYSSL;D2010_AND_LATER;__WOLF__;AUTHENTICODE;$(DCC_Define)</DCC_Define>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Win32)'!=''">
        <VerInfo_Build>0</VerInfo_Build>
        <VerInfo_MajorVer>1</VerInfo_MajorVer>
        <VerInfo_MinorVer>0</VerInfo_MinorVer>
        <DCC_Namespace>Data.Win;Datasnap.Win;Web.Win;Soap.Win;Bde;$(DCC_Namespace)</DCC_Namespace>
        <Icon_MainIcon>APLClientGUI_Icon2.ico</Icon_MainIcon>
        <VerInfo_Keys>CompanyName=;FileVersion=*******;InternalName=;LegalCopyright=;LegalTrademarks=;OriginalFilename=;ProductVersion=*******;Comments=;ProgramID=com.embarcadero.$(MSBuildProjectName);FileDescription=$(MSBuildProjectName);ProductName=$(MSBuildProjectName)</VerInfo_Keys>
        <UWP_DelphiLogo44>$(BDS)\bin\Artwork\Windows\UWP\delphi_UwpDefault_44.png</UWP_DelphiLogo44>
        <UWP_DelphiLogo150>$(BDS)\bin\Artwork\Windows\UWP\delphi_UwpDefault_150.png</UWP_DelphiLogo150>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1)'!=''">
        <Icon_MainIcon>APLClientGUI_Icon2.ico</Icon_MainIcon>
        <Version>7.0</Version>
        <DCC_DebugInformation>0</DCC_DebugInformation>
        <DCC_LocalDebugSymbols>False</DCC_LocalDebugSymbols>
        <DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
        <DCC_Define>RELEASE;$(DCC_Define)</DCC_Define>
        <DCC_ExeOutput>C:\Program Files\MicroTrax\APLService\</DCC_ExeOutput>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1_Win32)'!=''">
        <VerInfo_Keys>CompanyName=NCR;FileVersion=*******;InternalName=APLClientGUI;LegalCopyright=2021 @NCR;LegalTrademarks=;OriginalFilename=;ProductVersion=*******;Comments=;ProgramID=com.embarcadero.$(MSBuildProjectName);FileDescription=$(MSBuildProjectName);ProductName=$(MSBuildProjectName)</VerInfo_Keys>
        <VerInfo_Build>3</VerInfo_Build>
        <VerInfo_MinorVer>0</VerInfo_MinorVer>
        <VerInfo_MajorVer>1</VerInfo_MajorVer>
        <DCC_WriteableConstants>true</DCC_WriteableConstants>
        <DCC_Alignment>1</DCC_Alignment>
        <BT_BuildType>Debug</BT_BuildType>
        <VerInfo_Release>1</VerInfo_Release>
        <VerInfo_AutoGenVersion>false</VerInfo_AutoGenVersion>
        <VerInfo_AutoIncVersion>true</VerInfo_AutoIncVersion>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2)'!=''">
        <Version>7.0</Version>
        <DCC_Define>DEBUG;$(DCC_Define)</DCC_Define>
        <DCC_ResourcePath>O:\D2007;..\OpenEPS;E:\Dev\Compo\LockBox for D2007\source;$(DCC_ResourcePath)</DCC_ResourcePath>
        <DCC_ObjPath>O:\D2007;..\OpenEPS;E:\Dev\Compo\LockBox for D2007\source;$(DCC_ObjPath)</DCC_ObjPath>
        <DCC_IncludePath>O:\D2007;..\OpenEPS;E:\Dev\Compo\LockBox for D2007\source;$(DCC_IncludePath)</DCC_IncludePath>
        <DCC_ExeOutput>C:\Program Files\Microtrax\APLService</DCC_ExeOutput>
        <DCC_WriteableConstants>True</DCC_WriteableConstants>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2_Win32)'!=''">
        <BT_BuildType>Debug</BT_BuildType>
        <VerInfo_MajorVer>1</VerInfo_MajorVer>
        <VerInfo_MinorVer>0</VerInfo_MinorVer>
        <VerInfo_Build>0</VerInfo_Build>
        <VerInfo_Keys>CompanyName=;FileVersion=*******;InternalName=;LegalCopyright=;LegalTrademarks=;OriginalFilename=;ProductVersion=*******;Comments=;ProgramID=com.embarcadero.$(MSBuildProjectName);FileDescription=$(MSBuildProjectName);ProductName=$(MSBuildProjectName)</VerInfo_Keys>
    </PropertyGroup>
    <ProjectExtensions>
        <Borland.Personality>Delphi.Personality.12</Borland.Personality>
        <Borland.ProjectType/>
        <BorlandProject>
            <Delphi.Personality>
                <Parameters>
                    <Parameters Name="UseLauncher">False</Parameters>
                    <Parameters Name="LoadAllSymbols">True</Parameters>
                    <Parameters Name="LoadUnspecifiedSymbols">False</Parameters>
                </Parameters>
                <VersionInfo>
                    <VersionInfo Name="IncludeVerInfo">True</VersionInfo>
                    <VersionInfo Name="AutoIncBuild">True</VersionInfo>
                    <VersionInfo Name="MajorVer">827</VersionInfo>
                    <VersionInfo Name="MinorVer">1</VersionInfo>
                    <VersionInfo Name="Release">0</VersionInfo>
                    <VersionInfo Name="Build">11</VersionInfo>
                    <VersionInfo Name="Debug">False</VersionInfo>
                    <VersionInfo Name="PreRelease">False</VersionInfo>
                    <VersionInfo Name="Special">False</VersionInfo>
                    <VersionInfo Name="Private">False</VersionInfo>
                    <VersionInfo Name="DLL">False</VersionInfo>
                    <VersionInfo Name="Locale">1033</VersionInfo>
                    <VersionInfo Name="CodePage">1252</VersionInfo>
                </VersionInfo>
                <VersionInfoKeys>
                    <VersionInfoKeys Name="CompanyName"/>
                    <VersionInfoKeys Name="FileDescription"/>
                    <VersionInfoKeys Name="FileVersion">827.1.0.11</VersionInfoKeys>
                    <VersionInfoKeys Name="InternalName"/>
                    <VersionInfoKeys Name="LegalCopyright"/>
                    <VersionInfoKeys Name="LegalTrademarks"/>
                    <VersionInfoKeys Name="OriginalFilename"/>
                    <VersionInfoKeys Name="ProductName"/>
                    <VersionInfoKeys Name="ProductVersion">*******</VersionInfoKeys>
                    <VersionInfoKeys Name="Comments">D2007</VersionInfoKeys>
                </VersionInfoKeys>
                <Source>
                    <Source Name="MainSource">APLClientGUI.dpr</Source>
                </Source>
                <Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\dcloffice2k250.bpl">Microsoft Office 2000 Sample Automation Server Wrapper Components</Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\dclofficexp250.bpl">Microsoft Office XP Sample Automation Server Wrapper Components</Excluded_Packages>
                </Excluded_Packages>
            </Delphi.Personality>
            <Platforms>
                <Platform value="Win32">True</Platform>
            </Platforms>
        </BorlandProject>
        <ProjectFileVersion>12</ProjectFileVersion>
    </ProjectExtensions>
    <Import Project="$(BDS)\Bin\CodeGear.Delphi.Targets"/>
    <ItemGroup>
        <DelphiCompile Include="$(MainSource)">
            <MainSource>MainSource</MainSource>
        </DelphiCompile>
        <DCCReference Include="uAPLGUI.pas">
            <Form>frmMain</Form>
        </DCCReference>
        <DCCReference Include="eWicAplxptSuccessfulExport.pas"/>
        <DCCReference Include="uAPL.pas">
            <Form>DM</Form>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="..\OpenEPS\ServerEPSConstants.pas"/>
        <DCCReference Include="eWicMtxExportingResultError.pas"/>
        <DCCReference Include="eWicMtxExportingResultRestriction.pas"/>
        <DCCReference Include="APLClientConfiguration.pas"/>
        <BuildConfiguration Include="Debug">
            <Key>Cfg_2</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
        <BuildConfiguration Include="Base">
            <Key>Base</Key>
        </BuildConfiguration>
        <BuildConfiguration Include="Release">
            <Key>Cfg_1</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
    </ItemGroup>
    <Import Project="$(BDS)\Bin\CodeGear.Delphi.Targets" Condition="Exists('$(BDS)\Bin\CodeGear.Delphi.Targets')"/>
    <Import Project="$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj" Condition="Exists('$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj')"/>
</Project>
