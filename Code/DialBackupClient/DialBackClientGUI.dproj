﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ProjectGuid>{6d05f32f-1733-424a-8df1-0dd02b73ab38}</ProjectGuid>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <DCC_DCCCompiler>DCC32</DCC_DCCCompiler>
    <DCC_DependencyCheckOutputName>C:\Program Files\Microtrax\DialBackupClient\DialBackClientGUI.exe</DCC_DependencyCheckOutputName>
    <MainSource>DialBackClientGUI.dpr</MainSource>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <Version>7.0</Version>
    <DCC_DebugInformation>False</DCC_DebugInformation>
    <DCC_LocalDebugSymbols>False</DCC_LocalDebugSymbols>
    <DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
    <DCC_Define>RELEASE</DCC_Define>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <Version>7.0</Version>
    <DCC_Define>DEBUG</DCC_Define>
    <DCC_UnitSearchPath>..\common</DCC_UnitSearchPath>
    <DCC_ResourcePath>..\common</DCC_ResourcePath>
    <DCC_ObjPath>..\common</DCC_ObjPath>
    <DCC_IncludePath>..\common</DCC_IncludePath>
    <DCC_ExeOutput>C:\Program Files\Microtrax\DialBackupClient</DCC_ExeOutput>
  </PropertyGroup>
  <ProjectExtensions>
    <Borland.Personality>Delphi.Personality</Borland.Personality>
    <Borland.ProjectType />
    <BorlandProject>
<BorlandProject><Delphi.Personality><Parameters><Parameters Name="UseLauncher">False</Parameters><Parameters Name="LoadAllSymbols">True</Parameters><Parameters Name="LoadUnspecifiedSymbols">False</Parameters></Parameters><VersionInfo><VersionInfo Name="IncludeVerInfo">True</VersionInfo><VersionInfo Name="AutoIncBuild">True</VersionInfo><VersionInfo Name="MajorVer">826</VersionInfo><VersionInfo Name="MinorVer">2</VersionInfo><VersionInfo Name="Release">0</VersionInfo><VersionInfo Name="Build">0</VersionInfo><VersionInfo Name="Debug">False</VersionInfo><VersionInfo Name="PreRelease">False</VersionInfo><VersionInfo Name="Special">False</VersionInfo><VersionInfo Name="Private">False</VersionInfo><VersionInfo Name="DLL">False</VersionInfo><VersionInfo Name="Locale">1033</VersionInfo><VersionInfo Name="CodePage">1252</VersionInfo></VersionInfo><VersionInfoKeys><VersionInfoKeys Name="CompanyName"></VersionInfoKeys><VersionInfoKeys Name="FileDescription"></VersionInfoKeys><VersionInfoKeys Name="FileVersion">826.2.0.0</VersionInfoKeys><VersionInfoKeys Name="InternalName"></VersionInfoKeys><VersionInfoKeys Name="LegalCopyright"></VersionInfoKeys><VersionInfoKeys Name="LegalTrademarks"></VersionInfoKeys><VersionInfoKeys Name="OriginalFilename"></VersionInfoKeys><VersionInfoKeys Name="ProductName"></VersionInfoKeys><VersionInfoKeys Name="ProductVersion">*******</VersionInfoKeys><VersionInfoKeys Name="Comments"></VersionInfoKeys></VersionInfoKeys><Source><Source Name="MainSource">DialBackClientGUI.dpr</Source></Source></Delphi.Personality></BorlandProject></BorlandProject>
  </ProjectExtensions>
  <Import Project="$(MSBuildBinPath)\Borland.Delphi.Targets" />
  <ItemGroup>
    <DelphiCompile Include="DialBackClientGUI.dpr">
      <MainSource>MainSource</MainSource>
    </DelphiCompile>
    <DCCReference Include="uDiaBackupClientGUI.pas">
      <Form>frmDialBackup</Form>
    </DCCReference>
  </ItemGroup>
</Project>