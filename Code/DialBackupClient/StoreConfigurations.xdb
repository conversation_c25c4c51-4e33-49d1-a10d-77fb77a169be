<?xml version="1.0"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xdb="http://www.borland.com/schemas/delphi/10.0/XMLDataBinding">
  <xs:element name="StoreConfigurations" type="StoreConfigurationsType"/>
  <xs:complexType name="StoreConfigurationsType"><xs:annotation>
      <xs:appinfo xdb:docElement="StoreConfigurations"/>
    </xs:annotation>
    <xs:sequence>
      <xs:element name="ProcessingOptions" type="ProcessingOptionsType"/>
      <xs:element name="PrintSettings" type="PrintSettingsType"/>
      <xs:element name="ReceiptTexts" type="ReceiptTextsType"/>
      <xs:element name="Hosts" type="xs:string"/>
      <xs:element name="DialBackupConfiguration" type="DialBackupConfigurationType"/>
    </xs:sequence>
    <xs:attribute name="Version" type="xs:decimal"/>
    <xs:attribute name="LastModified" type="xs:string"/>
  </xs:complexType>
  <xs:complexType name="ProcessingOptionsType">
    <xs:sequence>
      <xs:element name="PasswordExpirationDays" type="xs:integer"/>
      <xs:element name="TruncateCardDataAtEOD" type="xs:string"/>
      <xs:element name="FtpStoreNumber" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PrintSettingsType">
    <xs:sequence>
      <xs:element name="FontName" type="xs:string"/>
      <xs:element name="FontSize" type="xs:integer"/>
      <xs:element name="FontStyles" type="FontStylesType"/>
      <xs:element name="Orientation" type="xs:string"/>
      <xs:element name="NumberOfCopies" type="xs:integer"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="FontStylesType">
    <xs:sequence/>
    <xs:attribute name="Bold" type="xs:string"/>
    <xs:attribute name="Italic" type="xs:string"/>
    <xs:attribute name="Underline" type="xs:string"/>
    <xs:attribute name="StrikeOut" type="xs:string"/>
  </xs:complexType>
  <xs:complexType name="ReceiptTextsType">
    <xs:sequence>
      <xs:element name="ReceiptText" type="ReceiptTextType" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ReceiptTextType">
    <xs:sequence>
      <xs:element name="ReceiptHeaderLine1" type="xs:string"/>
      <xs:element name="ReceiptHeaderLine2" type="xs:string"/>
      <xs:element name="ReceiptHeaderLine3" type="xs:string"/>
      <xs:element name="ReceiptHeaderLine4" type="xs:string"/>
      <xs:element name="ReceiptFooterLine1" type="xs:string"/>
      <xs:element name="ReceiptFooterLine2" type="xs:string"/>
      <xs:element name="CheckDepositLegend" type="xs:string"/>
      <xs:element name="CheckDepositBankName" type="xs:string"/>
      <xs:element name="CheckDepositAccountNumber" type="xs:string"/>
    </xs:sequence>
    <xs:attribute name="StoreNumber" type="xs:integer"/>
  </xs:complexType>
  <xs:complexType name="DialBackupConfigurationType">
    <xs:sequence>
      <xs:element name="DBCIPAddress" type="xs:decimal"/>
      <xs:element name="DBCPort" type="xs:integer"/>
      <xs:element name="DBCIdleTimeout" type="xs:integer"/>
      <xs:element name="DBCRAS" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>
</xs:schema>
