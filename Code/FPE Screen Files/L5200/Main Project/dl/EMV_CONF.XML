<?xml version="1.0" encoding="utf-8"?>
<EMVTables>
	<ApprovedConfigurations>
		<!-- List of approved EMV L2 kernel configurations to use in application. This section is optional. If not present, application will use 1C as default.
			Configuration element defines one configuration to use by FPE32 application.
			ApprovalNumber attribute contains one of the approved configurations – "2-03036-1-1C-UTS-0215-4.3.d" (i.e. "1C"), "2-03036-1-2C-UTS-0215-4.3.d" (i.e. "2C"), or "2-03036-1-3C-UTS-0215-4.3.d" (i.e. "3C"), "2-03036-1-4C-UTS-0215-4.3.d" (i.e. "4C"), and "2-03036-1-5C-UTS-0215-4.3.d" (i.e. "5C"). This information is published on EMVCo site.

			Note: FPE32 v7.U.510 - v7.U.520 will use the first Configuration element’s ApprovalNumber attribute 
			value from the ApprovedConfigurations element; the rest of the elements will be ignored.
		-->
		<Configuration ApprovalNumber="2-03036-1-1C-UTS-0215-4.3.d" />
		
	</ApprovedConfigurations>
	<AIDList>
		<!-- List of supported card AIDs and configuration options for them 
			APP element contains data for one supported card application
			AIDLength attribute is optional and contains AID length in bytes. Format: ASCII Numeric. Length: 2 digits.
			AID attribute contains Card Application Identifier (AID). Format: ASCII HEX. Length: 10 - 32.
			Control attribute contains Kernel option bits. Format: ASCII HEX. Length: 2 digits.
			SupportedVersions attribute contains a list of supported card application versions. The first byte is the count of version numbers, followed by 4 BCD digits (2 bytes) per version.
			ADT attribute is optional and contains a list of additional non-EMV tags that the kernel should keep in storage.
			Label attribute defines application name for display, if the card does not present one.
		-->

		<!-- TAIDREC 1 - Visa Credit or Debit -->
		<APP AIDLength="07" AID="A0000000031010" Control="04"
			SupportedVersions="030096008D008C" 
			Label="Visa Credit or Debit">
			<!-- Note: Tag elements are supported starting from FPE32 v7.U.510 RC2-->
			<Tag name="EQ05" value="0810000000D84004F800D84000A800" comment="Action codes:Denial/online/default"/>
			<Tag name="9F1B" value="000000000000" comment="Terminal Floor Limit - $0.00"/>
		</APP>
		<!-- TAIDREC 2 - Visa Electron -->
		<APP AIDLength="07" AID="A0000000032010" Control="04"
			SupportedVersions="01008C" 
			Label="Visa Electron">
			<Tag name="EQ05" value="0010000000DC4004F800DC4000A800" comment="Action codes:Denial/online/default"/>
			<Tag name="9F1B" value="000000000000" comment="Terminal Floor Limit - $0.00"/>
		</APP>
		<!-- TAIDREC 3 - Visa PLUS -->
		<APP AIDLength="07" AID="A0000000038010" Control="04"
			SupportedVersions="01008C" 
			Label="Visa PLUS"
		/>
		<!-- TAIDREC 4 - MasterCard Credit or Debit -->
		<APP AIDLength="07" AID="A0000000041010" Control="04"
			SupportedVersions="010002" 
			Label="MasterCard Credit or Debit"
		/>
		<!-- TAIDREC 5 - American Express -->
		<APP AIDLength="06" AID="A00000002501" Control="04"
			SupportedVersions="010001" 
			Label="Amex">
			<Tag name="9F1B" value="000000000000" comment="Terminal Floor Limit - $0.00"/>
		</APP>
		<!-- TAIDREC 6 - Interac Debit -->
		<!-- Note: Under Interac rules, card-not-present transactions and fallback to the magnetic stripe are not permitted. This is why Control attribute value is set to "04"-->
		<!-- Interac has additional ADT(Additional Data Tags) field with non-standard tag list that kernel must support -->
		<APP AIDLength="07" AID="A0000002771010" Control="04"
			SupportedVersions="03000100830084" 
			ADT="02DF620102205F56010303"
			Label="Interac Debit"
		/>
		<!-- TAIDREC 7 - Cirrus -->
		<APP AIDLength="07" AID="A0000000046000" Control="04"
			SupportedVersions="03000100830084" 
			Label="Cirrus"
		/>
		<!-- TAIDREC 8 - MasterCard Maestro(Debit) -->
		<APP AIDLength="07" AID="A0000000043060" Control="04"
			SupportedVersions="010002" 
			NoCVMLimit="000000000500"
			Label="Maestro(Debit)"
		/>
		<!-- TAIDREC 9 - Diners Club/Discover -->
		<APP AIDLength="07" AID="A0000001523010" Control="04"
			SupportedVersions="010001" 
			Label="Discover"
			
		/>
		<!-- TAIDREC 10 - DNA Shared Debit AID -->
		<APP AIDLength="07" AID="A0000006200620" Control="04"
			SupportedVersions="010001" 
			Label="DNA Shared Debit AID"
		/>
		<!-- TAIDREC 11 - Common U.S. Debit AID – Diners/Discover -->
		<APP AIDLength="07" AID="A0000001524010" Control="04"
			SupportedVersions="010001"
			Label="Common U.S. Debit AID – Diners/Discover"
		/>
		<!-- TAIDREC 12 - Common U.S. Debit AID – MasterCard Maestro -->
		<APP AIDLength="07" AID="A0000000042203" Control="04"
			SupportedVersions="010002" 
			Label="Common U.S. Debit AID – MasterCard Maestro">
			<Tag name="EQ05" value="0000000000FE50BCF800FE50BCA000" comment="Action codes:Denial/online/default"/>
		</APP>
		<!-- TAIDREC 13 - Common U.S. Debit AID – Visa -->
		<APP AIDLength="07" AID="A0000000980840" Control="04"
			SupportedVersions="01008C" 
			Label="Common U.S. Debit AID – Visa"
		/>
		<!-- TAIDREC 14 - Japan Credit Bureau (JCB)-->
		<APP AIDLength="07" AID="A0000000651010" Control="04"
			SupportedVersions="010200" 
			Label="Japan Credit Bureau"
		/>
		<!-- TAIDREC 15 - Visa Interlink -->
		<APP AIDLength="07" AID="A0000000033010" Control="04"
			SupportedVersions="030096008D008C" 
			Label="Visa Credit or Debit">
			<Tag name="EQ05" value="0010000000DC4004F800DC4000A800" comment="Action codes:Denial/online/default"/>
		</APP>

		<!-- Contactless EMV/MSD MasterCard
			Split MasterCard AID "A000000004" in ranges to allow specific rules for Maestro US Debit 
			A00000000410	Standard MasterCard
			A00000000420	MasterCard
			A0000000042203	Maestro US Debit
			A00000000430	MasterCard(43010) and Maestro(43060)
			A00000000440	MasterCard
			A00000000450	MasterCard
		-->
		<CLAPP AID="A00000000410" Control="20"
			SupportedVersions="**********" Kernel="02"
			Label="Contactless EMV/MSD MasterCard">
			<Tag name="9F1D" value="6C7A000000000000" comment="Terminal Risk Management Data. Needed for ApplePay Mastercard." />
			<Tag name="EQ32" value="************" comment="Reader Contactless Floor Limit - $100.00" />
			<Tag name="EQ31" value="************" comment="Reader Contactless Transaction Limit $300.00"/>
			<Tag name="EQ33" value="************" comment="Reader CVM Required Limit - $10.00"/>
			<Tag name="EQ05" value="000000000000000000000000000000" comment="Terminal Action Code – Online"/>
			<Response Name="CLEMV" Variables="Emv.Status,Emv.Tag.EQ20,Emv.Tag.84,Emv.Tag.5A,Emv.Tag.95,Emv.Tag.9F27,Emv.Tag.9F26,Emv.Tag.9F36,Emv.Tag.9F10,Emv.Tag.9F34,Emv.Tag.9F37,Emv.Tag.EQ22,Emv.Tag.EQ23,Emv.Tag.EQ26,Emv.Tag.9B,encAccountNumber,Emv.Tag.EQ27,E2EEncType,E2EEncryptionStatus,WhitelistName,Emv.Tag.57,Emv.Tag.5F20,Emv.Tag.5F24,Emv.Tag.5F30,Emv.Tag.9F1F,Emv.Tag.9F20,TransArmorKeyId,VoltageKTB,DUKPTP2P.KSN" />
			<Response Name="CLMSD" Variables="Emv.Status,Track1Data,Track2Data,Track3Data,ServiceCode,TrackDataSource,WhitelistName,BIN.Name,Emv.Tag.EQ26" />
		</CLAPP>
		<CLAPP AID="A00000000420" Control="20"
			SupportedVersions="**********" Kernel="02"
			Label="Contactless EMV/MSD MasterCard">
			<Tag name="9F1D" value="6C7A000000000000" comment="Terminal Risk Management Data. Needed for ApplePay Mastercard." />
			<Tag name="EQ32" value="************" comment="Reader Contactless Floor Limit - $100.00" />
			<Tag name="EQ31" value="************" comment="Reader Contactless Transaction Limit $300.00"/>
			<Tag name="EQ33" value="************" comment="Reader CVM Required Limit - $10.00"/>
			<Tag name="EQ05" value="000000000000000000000000000000" comment="Terminal Action Code – Online"/>
			<Response Name="CLEMV" Variables="Emv.Status,Emv.Tag.EQ20,Emv.Tag.84,Emv.Tag.5A,Emv.Tag.95,Emv.Tag.9F27,Emv.Tag.9F26,Emv.Tag.9F36,Emv.Tag.9F10,Emv.Tag.9F34,Emv.Tag.9F37,Emv.Tag.EQ22,Emv.Tag.EQ23,Emv.Tag.EQ26,Emv.Tag.9B,encAccountNumber,Emv.Tag.EQ27,E2EEncType,E2EEncryptionStatus,WhitelistName,Emv.Tag.57,Emv.Tag.5F20,Emv.Tag.5F24,Emv.Tag.5F30,Emv.Tag.9F1F,Emv.Tag.9F20,TransArmorKeyId,VoltageKTB,DUKPTP2P.KSN" />
			<Response Name="CLMSD" Variables="Emv.Status,Track1Data,Track2Data,Track3Data,ServiceCode,TrackDataSource,WhitelistName,BIN.Name,Emv.Tag.EQ26" />
		</CLAPP>
		<CLAPP AID="A0000000042203" Control="20"
			SupportedVersions="**********" Kernel="02"
			Label="Maestro US Debit">
			<Tag name="9F1D" value="6C7A000000000000" comment="Terminal Risk Management Data. Needed for ApplePay Mastercard." />
			<Tag name="EQ32" value="************" comment="Reader Contactless Floor Limit - $100.00" />
			<Tag name="EQ31" value="************" comment="Reader Contactless Transaction Limit $300.00"/>
			<Tag name="EQ33" value="************" comment="Reader CVM Required Limit - $10.00"/>
			<Tag name="EQ05" value="000000000000000000000000000000" comment="Terminal Action Code – Online"/>
			<Response Name="CLEMV" Variables="Emv.Status,Emv.Tag.EQ20,Emv.Tag.84,Emv.Tag.5A,Emv.Tag.95,Emv.Tag.9F27,Emv.Tag.9F26,Emv.Tag.9F36,Emv.Tag.9F10,Emv.Tag.9F34,Emv.Tag.9F37,Emv.Tag.EQ22,Emv.Tag.EQ23,Emv.Tag.EQ26,Emv.Tag.9B,encAccountNumber,Emv.Tag.EQ27,E2EEncType,E2EEncryptionStatus,WhitelistName,Emv.Tag.57,Emv.Tag.5F20,Emv.Tag.5F24,Emv.Tag.5F30,Emv.Tag.9F1F,Emv.Tag.9F20,TransArmorKeyId,VoltageKTB,DUKPTP2P.KSN" />
			<Response Name="CLMSD" Variables="Emv.Status,Track1Data,Track2Data,Track3Data,ServiceCode,TrackDataSource,WhitelistName,BIN.Name,Emv.Tag.EQ26" />
		</CLAPP>
		<CLAPP AID="A00000000430" Control="20"
			SupportedVersions="**********" Kernel="02"
			Label="Contactless EMV/MSD MasterCard">
			<Tag name="9F1D" value="6C7A000000000000" comment="Terminal Risk Management Data. Needed for ApplePay Mastercard." />
			<Tag name="EQ32" value="************" comment="Reader Contactless Floor Limit - $100.00" />
			<Tag name="EQ31" value="************" comment="Reader Contactless Transaction Limit $300.00"/>
			<Tag name="EQ33" value="************" comment="Reader CVM Required Limit - $10.00"/>
			<Tag name="EQ05" value="000000000000000000000000000000" comment="Terminal Action Code – Online"/>
			<Response Name="CLEMV" Variables="Emv.Status,Emv.Tag.EQ20,Emv.Tag.84,Emv.Tag.5A,Emv.Tag.95,Emv.Tag.9F27,Emv.Tag.9F26,Emv.Tag.9F36,Emv.Tag.9F10,Emv.Tag.9F34,Emv.Tag.9F37,Emv.Tag.EQ22,Emv.Tag.EQ23,Emv.Tag.EQ26,Emv.Tag.9B,encAccountNumber,Emv.Tag.EQ27,E2EEncType,E2EEncryptionStatus,WhitelistName,Emv.Tag.57,Emv.Tag.5F20,Emv.Tag.5F24,Emv.Tag.5F30,Emv.Tag.9F1F,Emv.Tag.9F20,TransArmorKeyId,VoltageKTB,DUKPTP2P.KSN" />
			<Response Name="CLMSD" Variables="Emv.Status,Track1Data,Track2Data,Track3Data,ServiceCode,TrackDataSource,WhitelistName,BIN.Name,Emv.Tag.EQ26" />
		</CLAPP>
		<CLAPP AID="A00000000440" Control="20"
			SupportedVersions="**********" Kernel="02"
			Label="Contactless EMV/MSD MasterCard">
			<Tag name="9F1D" value="6C7A000000000000" comment="Terminal Risk Management Data. Needed for ApplePay Mastercard." />
			<Tag name="EQ32" value="************" comment="Reader Contactless Floor Limit - $100.00" />
			<Tag name="EQ31" value="************" comment="Reader Contactless Transaction Limit $300.00"/>
			<Tag name="EQ33" value="************" comment="Reader CVM Required Limit - $10.00"/>
			<Tag name="EQ05" value="000000000000000000000000000000" comment="Terminal Action Code – Online"/>
			<Response Name="CLEMV" Variables="Emv.Status,Emv.Tag.EQ20,Emv.Tag.84,Emv.Tag.5A,Emv.Tag.95,Emv.Tag.9F27,Emv.Tag.9F26,Emv.Tag.9F36,Emv.Tag.9F10,Emv.Tag.9F34,Emv.Tag.9F37,Emv.Tag.EQ22,Emv.Tag.EQ23,Emv.Tag.EQ26,Emv.Tag.9B,encAccountNumber,Emv.Tag.EQ27,E2EEncType,E2EEncryptionStatus,WhitelistName,Emv.Tag.57,Emv.Tag.5F20,Emv.Tag.5F24,Emv.Tag.5F30,Emv.Tag.9F1F,Emv.Tag.9F20,TransArmorKeyId,VoltageKTB,DUKPTP2P.KSN" />
			<Response Name="CLMSD" Variables="Emv.Status,Track1Data,Track2Data,Track3Data,ServiceCode,TrackDataSource,WhitelistName,BIN.Name,Emv.Tag.EQ26" />
		</CLAPP>
		<CLAPP AID="A00000000450" Control="20"
			SupportedVersions="**********" Kernel="02"
			Label="Contactless EMV/MSD MasterCard">
			<Tag name="9F1D" value="6C7A000000000000" comment="Terminal Risk Management Data. Needed for ApplePay Mastercard." />
			<Tag name="EQ32" value="************" comment="Reader Contactless Floor Limit - $100.00" />
			<Tag name="EQ31" value="************" comment="Reader Contactless Transaction Limit $300.00"/>
			<Tag name="EQ33" value="************" comment="Reader CVM Required Limit - $10.00"/>
			<Tag name="EQ05" value="000000000000000000000000000000" comment="Terminal Action Code – Online"/>
			<Response Name="CLEMV" Variables="Emv.Status,Emv.Tag.EQ20,Emv.Tag.84,Emv.Tag.5A,Emv.Tag.95,Emv.Tag.9F27,Emv.Tag.9F26,Emv.Tag.9F36,Emv.Tag.9F10,Emv.Tag.9F34,Emv.Tag.9F37,Emv.Tag.EQ22,Emv.Tag.EQ23,Emv.Tag.EQ26,Emv.Tag.9B,encAccountNumber,Emv.Tag.EQ27,E2EEncType,E2EEncryptionStatus,WhitelistName,Emv.Tag.57,Emv.Tag.5F20,Emv.Tag.5F24,Emv.Tag.5F30,Emv.Tag.9F1F,Emv.Tag.9F20,TransArmorKeyId,VoltageKTB,DUKPTP2P.KSN" />
			<Response Name="CLMSD" Variables="Emv.Status,Track1Data,Track2Data,Track3Data,ServiceCode,TrackDataSource,WhitelistName,BIN.Name,Emv.Tag.EQ26" />
		</CLAPP>
		<!-- Visa Contactless EMV(qVSDC)/MSD -->
		<CLAPP AID="A000000003" Control="1F"
			SupportedVersions="**********" Kernel="03"
			Label="Visa Contactless EMV/MSD">
			<Tag name="9F66" value="B600C000" comment="VISA TTQ"/>
			<Response Name="CLEMV" Variables="Emv.Status,Emv.Tag.EQ20,Emv.Tag.84,Emv.Tag.5A,Emv.Tag.95,Emv.Tag.9F27,Emv.Tag.9F26,Emv.Tag.9F36,Emv.Tag.9F10,Emv.Tag.9F34,Emv.Tag.9F37,Emv.Tag.EQ22,Emv.Tag.EQ23,Emv.Tag.EQ26,Emv.Tag.9B,encAccountNumber,Emv.Tag.EQ27,E2EEncType,E2EEncryptionStatus,WhitelistName,Emv.Tag.57,Emv.Tag.5F20,Emv.Tag.5F24,Emv.Tag.5F30,Emv.Tag.9F1F,Emv.Tag.9F20,TransArmorKeyId,VoltageKTB,DUKPTP2P.KSN" />
			<Response Name="CLMSD" Variables="Emv.Status,Track1Data,Track2Data,Track3Data,ServiceCode,TrackDataSource,WhitelistName,BIN.Name" />
		</CLAPP>
		<!-- Visa Contactless US Debit EMV(qVSDC)/MSD -->
		<CLAPP AID="A000000098" Control="1F"
			SupportedVersions="**********" Kernel="03"
			Label="Visa US Debit">
			<Tag name="9F66" value="B600C000" comment="VISA TTQ"/>
			<Response Name="CLEMV" Variables="Emv.Status,Emv.Tag.EQ20,Emv.Tag.84,Emv.Tag.5A,Emv.Tag.95,Emv.Tag.9F27,Emv.Tag.9F26,Emv.Tag.9F36,Emv.Tag.9F10,Emv.Tag.9F34,Emv.Tag.9F37,Emv.Tag.EQ22,Emv.Tag.EQ23,Emv.Tag.EQ26,Emv.Tag.9B,encAccountNumber,Emv.Tag.EQ27,E2EEncType,E2EEncryptionStatus,WhitelistName,Emv.Tag.57,Emv.Tag.5F20,Emv.Tag.5F24,Emv.Tag.5F30,Emv.Tag.9F1F,Emv.Tag.9F20,TransArmorKeyId,VoltageKTB,DUKPTP2P.KSN" />
			<Response Name="CLMSD" Variables="Emv.Status,Track1Data,Track2Data,Track3Data,ServiceCode,TrackDataSource,WhitelistName,BIN.Name" />
		</CLAPP>
		<!-- Amex Contactless EMV/MSD-->
		<CLAPP AID="A00000002501" Control="00"
			SupportedVersions="**********" Kernel="04"
			Label="Amex Contactless EMV/MSD">
			<Tag name="DFD412" value="0000003C" comment="Unpredictable number range"/>
			<Tag name="EQ05" value="0000000000C400000000DF50840000" comment="Action codes:Denial/online/default"/>
			<Tag name="9F6D" value="C0" comment="Expresspay Terminal Capabilities"/>
			<Tag name="9F6E" value="D8E00000" comment="Enhanced Expresspay Terminal Capabilities"/>
			<Tag name="EQ31" value="************" comment="Terminal Contactless Transaction Limit - $40.00"/>
			<Tag name="EQ32" value="************" comment="Terminal Contactless Floor Limit - $20.00"/>
			<Tag name="EQ33" value="************" comment="Terminal CVM Required Limit - $30.00"/>
			<Response Name="CLEMV" Variables="Emv.Status,Emv.Tag.EQ20,Emv.Tag.84,Emv.Tag.5A,Emv.Tag.95,Emv.Tag.9F27,Emv.Tag.9F26,Emv.Tag.9F36,Emv.Tag.9F10,Emv.Tag.9F34,Emv.Tag.9F37,Emv.Tag.EQ22,Emv.Tag.EQ23,Emv.Tag.EQ26,Emv.Tag.9B,encAccountNumber,Emv.Tag.EQ27,E2EEncType,E2EEncryptionStatus,WhitelistName,Emv.Tag.57,Emv.Tag.5F20,Emv.Tag.5F24,Emv.Tag.5F30,Emv.Tag.9F1F,Emv.Tag.9F20,TransArmorKeyId,VoltageKTB,DUKPTP2P.KSN" />
			<Response Name="CLMSD" Variables="Emv.Status,Track1Data,Track2Data,Track3Data,ServiceCode,TrackDataSource,WhitelistName,BIN.Name,Emv.Tag.EQ26" />
		</CLAPP>
		<!-- Discover Contactless D-PAS 
			A00000015230 Discover Card
			A00000015240 Discover US Debit
		-->
		<CLAPP AID="A00000015230" Control="01"
			SupportedVersions="**********" Kernel="05"
			Label="Discover D-PAS">
			<Tag name="9F66" value="B600C000" comment="Discover TTQ"/>
			<Tag name="EQ05" value="000000000000000000000000000000" comment="Action codes:Denial/online/default"/>
			<Tag name="EQ31" value="************" comment="Terminal Contactless Transaction Limit - $40.00"/>
			<Tag name="EQ32" value="************" comment="Terminal Contactless Floor Limit - $20.00"/>
			<Tag name="EQ33" value="************" comment="Terminal CVM Required Limit - $30.00"/>
			<Response Name="CLEMV" Variables="Emv.Status,Emv.Tag.EQ20,Emv.Tag.84,Emv.Tag.5A,Emv.Tag.95,Emv.Tag.9F27,Emv.Tag.9F26,Emv.Tag.9F36,Emv.Tag.9F10,Emv.Tag.9F34,Emv.Tag.9F37,Emv.Tag.EQ22,Emv.Tag.EQ23,Emv.Tag.EQ26,Emv.Tag.9B,encAccountNumber,Emv.Tag.EQ27,E2EEncType,E2EEncryptionStatus,WhitelistName,Emv.Tag.57,Emv.Tag.5F20,Emv.Tag.5F24,Emv.Tag.5F30,Emv.Tag.9F1F,Emv.Tag.9F20,TransArmorKeyId,VoltageKTB,DUKPTP2P.KSN" />
			<Response Name="CLMSD" Variables="Emv.Status,Track1Data,Track2Data,Track3Data,ServiceCode,TrackDataSource,WhitelistName,BIN.Name,Emv.Tag.EQ26" />
		</CLAPP>
		<CLAPP AID="A00000015240" Control="01"
			SupportedVersions="**********" Kernel="05"
			Label="Discover D-PAS">
			<Tag name="9F66" value="B600C000" comment="Discover TTQ"/>
			<Tag name="EQ05" value="000000000000000000000000000000" comment="Action codes:Denial/online/default"/>
			<Tag name="EQ31" value="************" comment="Terminal Contactless Transaction Limit - $40.00"/>
			<Tag name="EQ32" value="************" comment="Terminal Contactless Floor Limit - $20.00"/>
			<Tag name="EQ33" value="************" comment="Terminal CVM Required Limit - $30.00"/>
			<Response Name="CLEMV" Variables="Emv.Status,Emv.Tag.EQ20,Emv.Tag.84,Emv.Tag.5A,Emv.Tag.95,Emv.Tag.9F27,Emv.Tag.9F26,Emv.Tag.9F36,Emv.Tag.9F10,Emv.Tag.9F34,Emv.Tag.9F37,Emv.Tag.EQ22,Emv.Tag.EQ23,Emv.Tag.EQ26,Emv.Tag.9B,encAccountNumber,Emv.Tag.EQ27,E2EEncType,E2EEncryptionStatus,WhitelistName,Emv.Tag.57,Emv.Tag.5F20,Emv.Tag.5F24,Emv.Tag.5F30,Emv.Tag.9F1F,Emv.Tag.9F20,TransArmorKeyId,VoltageKTB,DUKPTP2P.KSN" />
			<Response Name="CLMSD" Variables="Emv.Status,Track1Data,Track2Data,Track3Data,ServiceCode,TrackDataSource,WhitelistName,BIN.Name,Emv.Tag.EQ26" />
		</CLAPP>
		<!-- Discover Contactless ZIP -->
		<CLAPP AID="A000000324" Control="01"
			SupportedVersions="**********" Kernel="05"
			Label="Discover ZIP">
			<Tag name="9F66" value="B600C000" comment="Discover TTQ"/>
			<Tag name="EQ05" value="000000000000000000000000000000" comment="Action codes:Denial/online/default"/>
			<Tag name="EQ31" value="************" comment="Terminal Contactless Transaction Limit - $40.00"/>
			<Tag name="EQ32" value="************" comment="Terminal Contactless Floor Limit - $20.00"/>
			<Tag name="EQ33" value="************" comment="Terminal CVM Required Limit - $30.00"/>
			<Response Name="CLMSD" Variables="Emv.Status,Track1Data,Track2Data,Track3Data,ServiceCode,TrackDataSource,WhitelistName,BIN.Name,Emv.Tag.EQ26" />
		</CLAPP>
	</AIDList>

	<Tags>
		<!-- These tags will be used in kernel setup for EMV transaction.
		Note: Define only read-write tags here. 
		Avoid listing read-only tags, such as "9F33", "9F40", "EQ03", "9F35", and "EQ04", 
		as the values for these tags built into FPE32 are used and the tag values provided in this section are ignored.
		-->
		<Tag name="5F36" value="02" comment="Transaction currency exponent"/>
		<Tag name="5F2A" value="0840" comment="Transaction currency code"/>
		<Tag name="9F1A" value="0840" comment="Terminal country code"/>
		<Tag name="EQ01" value="9F3704" comment="Default DDOL. List of [tag, length]."/>
		<Tag name="EQ02" value="9F0206" comment="Default TDOL. List of [tag, length]."/>
		<Tag name="EQ06" value="0000000000000050" comment="Target Percentage (0%)/Maximum Target Percentage (0%)/Threshold Value ($.50)"/>
		<Tag name="EQ05" value="000000000000000000000000000000" comment="Action codes:Denial/online/default"/>
		<Tag name="EQ34" value="01" comment="Contactless zero amount allowed"/>

		<Tag name="9F01" value="223344556677" comment="Acquirer ID"/>
		<Tag name="9F15" value="1234" comment="Merchant category code"/>
		<Tag name="9F16" value="303030303030303031323334353637" comment="Merchant ID"/>
		<Tag name="9F1B" value="000000000000" comment="Terminal floor limit"/>

	</Tags>
	<EmvResponses>
		<!-- List of variables returned in EMV transaction step response 
			Event defines variables to be fired after particular EMV transaction step 
			Name attribute values are predefined – "StartEmv", "ContinueEmv", "CompleteEmv", and "RemoveCard".
			Variables attribute value is a comma separated list of variables to send after the corresponding EMV transaction step.
		-->
		<Event Name="StartEmv" Variables="Emv.Status,Emv.Tag.5A,Emv.Tag.5F34,Emv.Tag.5F20,Emv.Tag.5F24,Emv.Tag.82,Emv.Tag.EQ22,Emv.Tag.EQ25,Emv.Tag.EQ26,Emv.Tag.84,Emv.Tag.9F06,PanHash,Emv.Tag.57,Emv.Tag.9F27,Emv.Tag.9F26,Emv.Tag.Encrypted.56,Emv.Tag.Encrypted.57,Emv.Tag.Encrypted.9F1F,Emv.Tag.Encrypted.9F20,Emv.Tag.Encrypted.9F6B,DUKPTP2P.KSN,pinstatus" />
		<Event Name="ContinueEmv" Variables="Emv.Status,Emv.Tag.50,Emv.Tag.5F2A,Emv.Tag.5F34,Emv.Tag.82,Emv.Tag.84,Emv.Tag.95,Emv.Tag.9A,Emv.Tag.9B,Emv.Tag.9C,Emv.Tag.9F02,Emv.Tag.9F03,Emv.Tag.9F06,Emv.Tag.9F09,Emv.Tag.9F10,Emv.Tag.9F1A,Emv.Tag.9F21,Emv.Tag.9F26,Emv.Tag.9F27,Emv.Tag.9F33,Emv.Tag.9F34,Emv.Tag.9F35,Emv.Tag.9F36,Emv.Tag.9F37,Emv.Tag.9F1E,Emv.Tag.9F39,Emv.Tag.9F41,Emv.Tag.9F53,Emv.Tag.EQ26,DUKPTP2P.KSN,pinblock,pinstatus" />
  		<Event Name="CompleteEmv" Variables="Emv.Status,Emv.Tag.EQ24,Emv.Tag.EQ26,Emv.Tag.EQ22,Emv.Tag.9F27,Emv.Tag.9F26,Emv.Tag.9F36,Emv.Tag.95,Emv.Tag.9F34,Emv.Tag.9B" />
		<Event Name="OnlinePIN" Variables="Emv.Status,ksn,pinblock,encryptedpin,pinstatus"  comment="this is used for online pin retry when pin is rejected by the host" /> 
		<Event Name="RemoveCard" Variables="Emv.Status" />
		<Event Name="CLEMV" Variables="Emv.Status" comment="This response element will be used if contactless card could not be read, for example, if it does not have known AID." />
	</EmvResponses>

	<Forms>
		<!-- List of forms used during contact and contacless EMV transactions.
			Form element defines screen name to be used for particular process during EMV transaction.
			Event attribute contains pre-defined name associated with particular process performed during EMV transaction. 
			Name attribute defines screen name to use for particular process during EMV transaction. Same form can be used for several processes. 
		-->
		<Form Event="EMV_Wait" Name="EMV_Wait" />
		<Form Event="EMV_InsertCard" Name="MTXEMVSWIPE" />
		<Form Event="EMV_RemoveCard" Name="EMV_RemoveCard" />
		<Form Event="EMV_Swipe" Name="MTXSWIPE" />
		<Form Event="EMV_ApplicationSelection" Name="EMV_ApplicationSelection" />
		<Form Event="EMV_LanguageSelection" Name="EMV_LanguageSelection" />
		<Form Event="EMV_PIN_Entry" Name="EMV_PIN_Entry" />
		<Form Event="EMV_PIN_ReEntry" Name="EMV_PIN_ReEntry" />
		<Form Event="EMV_PIN_EntryLast" Name="EMV_PIN_EntryLast" />
		<Form Event="EMV_PIN_ReEntryLast" Name="EMV_PIN_ReEntryLast" />
		<Form Event="EMV_OnlinePIN" Name="EMV_OnlinePIN" />
		<Form Event="EMV_Signature" Name="EMV_Signature" />
		<Form Event="EMV_InsertChip" Name="EMV_InsertChip" />
		<Form Event="EMV_TapOnline" Name="EMV_TapOnline" />
	</Forms>
</EMVTables>
