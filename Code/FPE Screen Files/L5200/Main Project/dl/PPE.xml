<?xml version="1.0" encoding="US-ASCII" standalone="yes"?>
<!--This file generated by FormBuilder. Do not edit.-->
<FPEScripts Version="1"><UserVariables><Variable Name="Caption_Btn1" Value=""/><Variable Name="Caption_Btn2" Value=""/><Variable Name="Caption_Btn3" Value=""/><Variable Name="Caption_Btn4" Value=""/><Variable Name="Caption_Btn5" Value=""/><Variable Name="Caption_Btn6" Value=""/><Variable Name="Caption_lbl1" Value=""/><Variable Name="Caption_lbl2" Value=""/><Variable Name="Caption_lbl3" Value=""/><Variable Name="Caption_lbl4" Value=""/><Variable Name="Caption_Prompt1" Value=""/><Variable Name="Caption_Prompt2" Value=""/><Variable Name="ContactlessRead" SectionTag="" Value=""/><Variable Name="DUKPTP2P.KSN" Value=""/><Variable Name="EncryptData" Value=""/><Variable Name="ExpDate" Value=""/><Variable Name="FirstName" Value=""/><Variable Name="FPEKSN" Value=""/><Variable Name="HardkeyPress" Value=""/><Variable Name="LastName" Value=""/><Variable Name="PAN" Value=""/><Variable Name="ReturnValueCANCEL" SectionTag="" Value="&quot;CANCEL&quot;"/><Variable Name="SignatureBlock" SectionTag="" Value=""/><Variable Name="SignatureStatus" SectionTag="" Value=""/><Variable Name="SmartCardATR" Value=""/><Variable Name="SoftkeyPress" Value=""/><Variable Name="SwipeResult" Value=""/></UserVariables><Form Name="MTXPROMPT"><Events/><Controls/></Form><Form Name="MTXSIGN"><Events><Event Name="OnScreenEntry"><Actions><Action Name="VariableClear"><Parameter Value="SignatureStatus"/></Action></Actions></Event></Events><Controls><Control Name="TextBox" Id="188"><Events><Event Name="OnAutoEnter"><Actions><Action Name="FireEvent"><Parameter Value="HardkeyPress"/><Parameter Value="&quot;ENTER&quot;"/></Action><Action Name="FireEvent"><Parameter Value="SignatureStatus"/><Parameter Value="SignatureStatus"/></Action><Action Name="FireEvent"><Parameter Value="SignatureBlock"/><Parameter Value="sig"/></Action></Actions></Event><Event Name="OnMinimumPointLimit"><Actions><Action Name="VariableSet"><Parameter Value="SignatureStatus"/><Parameter Value="&quot;1&quot;"/></Action></Actions></Event><Event Name="OnPointLimit"><Actions><Action Name="VariableSet"><Parameter Value="SignatureStatus"/><Parameter Value="&quot;1&quot;"/></Action></Actions></Event></Events></Control><Control Name="btnClear" Id="194"><Events><Event Name="OnPressed"><Actions><Action Name="VariableClear"><Parameter Value="SignatureStatus"/></Action><Action Name="VariableClear"><Parameter Value="sig"/></Action></Actions></Event></Events></Control><Control Name="btnEnter" Id="196"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="HardkeyPress"/><Parameter Value="&quot;ENTER&quot;"/></Action><Action Name="FireEvent"><Parameter Value="SignatureStatus"/><Parameter Value="SignatureStatus"/></Action><Action Name="FireEvent"><Parameter Value="SignatureBlock"/><Parameter Value="sig"/></Action></Actions></Event></Events></Control><Control Name="PhantomCancel" Id="50"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="HardkeyPress"/><Parameter Value="ReturnValueCANCEL"/></Action></Actions></Event></Events></Control></Controls></Form><Form Name="MTXSOFTKEY2"><Events/><Controls><Control Name="btnOne" Id="223" Variable="Caption_Btn1"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="SoftkeyPress"/><Parameter Value="&quot;1&quot;"/></Action><Action Name="ControlDisable"><Parameter Value="223"/></Action><Action Name="ControlDisable"><Parameter Value="225"/></Action></Actions></Event></Events></Control><Control Name="btnTwo" Id="225" Variable="Caption_Btn2"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="SoftkeyPress"/><Parameter Value="&quot;2&quot;"/></Action><Action Name="ControlDisable"><Parameter Value="223"/></Action><Action Name="ControlDisable"><Parameter Value="225"/></Action></Actions></Event></Events></Control><Control Name="Phantom" Id="58"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="HardkeyPress"/><Parameter Value="&quot;CANCEL&quot;"/></Action></Actions></Event></Events></Control></Controls></Form><Form Name="MTXSOFTKEY1"><Events/><Controls><Control Name="btnOne" Id="240" Variable="Caption_Btn1"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="SoftkeyPress"/><Parameter Value="&quot;1&quot;"/></Action><Action Name="ControlDisable"><Parameter Value="240"/></Action></Actions></Event></Events></Control><Control Name="Phantom" Id="86"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="HardkeyPress"/><Parameter Value="ReturnValueCANCEL"/></Action></Actions></Event></Events></Control></Controls></Form><Form Name="MTXSOFTKEY3"><Events/><Controls><Control Name="btnOne" Id="255" Variable="Caption_Btn1"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="SoftkeyPress"/><Parameter Value="&quot;1&quot;"/></Action><Action Name="ControlDisable"><Parameter Value="255"/></Action><Action Name="ControlDisable"><Parameter Value="257"/></Action><Action Name="ControlDisable"><Parameter Value="259"/></Action></Actions></Event></Events></Control><Control Name="btnTwo" Id="257" Variable="Caption_Btn2"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="SoftkeyPress"/><Parameter Value="&quot;2&quot;"/></Action><Action Name="ControlDisable"><Parameter Value="255"/></Action><Action Name="ControlDisable"><Parameter Value="257"/></Action><Action Name="ControlDisable"><Parameter Value="259"/></Action></Actions></Event></Events></Control><Control Name="btnThree" Id="259" Variable="Caption_Btn3"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="SoftkeyPress"/><Parameter Value="&quot;3&quot;"/></Action><Action Name="ControlDisable"><Parameter Value="255"/></Action><Action Name="ControlDisable"><Parameter Value="257"/></Action><Action Name="ControlDisable"><Parameter Value="259"/></Action></Actions></Event></Events></Control><Control Name="Phantom" Id="105"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="HardkeyPress"/><Parameter Value="&quot;CANCEL&quot;"/></Action></Actions></Event></Events></Control></Controls></Form><Form Name="CLOSEDFRM"><Events><Event Name="OnScreenEntry"><Actions><Action Name="VariableSet"><Parameter Value="Caption_Btn1"/><Parameter Value="&quot;&quot;"/></Action><Action Name="VariableSet"><Parameter Value="Caption_Btn2"/><Parameter Value="&quot;&quot;"/></Action><Action Name="VariableSet"><Parameter Value="Caption_Btn3"/><Parameter Value="&quot;&quot;"/></Action><Action Name="VariableSet"><Parameter Value="Caption_Btn4"/><Parameter Value="&quot;&quot;"/></Action><Action Name="VariableSet"><Parameter Value="Caption_Btn5"/><Parameter Value="&quot;&quot;"/></Action><Action Name="VariableSet"><Parameter Value="Caption_Btn6"/><Parameter Value="&quot;&quot;"/></Action><Action Name="VariableSet"><Parameter Value="SoftkeyPress"/><Parameter Value="&quot;&quot;"/></Action><Action Name="VariableSet"><Parameter Value="HardkeyPress"/><Parameter Value="&quot;&quot;"/></Action><Action Name="VariableSet"><Parameter Value="LastName"/><Parameter Value="&quot;&quot;"/></Action><Action Name="VariableSet"><Parameter Value="FirstName"/><Parameter Value="&quot;&quot;"/></Action><Action Name="VariableSet"><Parameter Value="ExpDate"/><Parameter Value="&quot;&quot;"/></Action><Action Name="VariableSet"><Parameter Value="FPEKSN"/><Parameter Value="&quot;&quot;"/></Action><Action Name="VariableSet"><Parameter Value="EncryptData"/><Parameter Value="&quot;&quot;"/></Action><Action Name="VariableSet"><Parameter Value="Caption_lbl1"/><Parameter Value="&quot;&quot;"/></Action><Action Name="VariableSet"><Parameter Value="Caption_lbl2"/><Parameter Value="&quot;&quot;"/></Action><Action Name="VariableSet"><Parameter Value="Caption_lbl3"/><Parameter Value="&quot;&quot;"/></Action><Action Name="VariableSet"><Parameter Value="Caption_lbl4"/><Parameter Value="&quot;&quot;"/></Action><Action Name="VariableSet"><Parameter Value="Caption_Prompt1"/><Parameter Value="&quot;&quot;"/></Action><Action Name="VariableSet"><Parameter Value="Caption_Prompt2"/><Parameter Value="&quot;&quot;"/></Action><Action Name="VariableSet"><Parameter Value="SwipeResult"/><Parameter Value="&quot;&quot;"/></Action><Action Name="VariableSet"><Parameter Value="ReturnValueCANCEL"/><Parameter Value="&quot;CANCEL&quot;"/></Action><Action Name="VariableSet"><Parameter Value="SignatureStatus"/><Parameter Value="&quot;&quot;"/></Action></Actions></Event></Events><Controls/></Form><Form Name="MTXSOFTKEY4"><Events/><Controls><Control Name="btnOne" Id="331" Variable="Caption_Btn1"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="SoftkeyPress"/><Parameter Value="&quot;1&quot;"/></Action><Action Name="ControlDisable"><Parameter Value="331"/></Action><Action Name="ControlDisable"><Parameter Value="0"/></Action><Action Name="ControlDisable"><Parameter Value="335"/></Action><Action Name="ControlDisable"><Parameter Value="337"/></Action></Actions></Event></Events></Control><Control Name="btnTwo" Id="333" Variable="Caption_Btn2"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="SoftkeyPress"/><Parameter Value="&quot;2&quot;"/></Action><Action Name="ControlDisable"><Parameter Value="331"/></Action><Action Name="ControlDisable"><Parameter Value="0"/></Action><Action Name="ControlDisable"><Parameter Value="335"/></Action><Action Name="ControlDisable"><Parameter Value="337"/></Action></Actions></Event></Events></Control><Control Name="btnThree" Id="335" Variable="Caption_Btn3"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="SoftkeyPress"/><Parameter Value="&quot;3&quot;"/></Action><Action Name="ControlDisable"><Parameter Value="331"/></Action><Action Name="ControlDisable"><Parameter Value="0"/></Action><Action Name="ControlDisable"><Parameter Value="335"/></Action><Action Name="ControlDisable"><Parameter Value="337"/></Action></Actions></Event></Events></Control><Control Name="btnFour" Id="337" Variable="Caption_Btn4"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="SoftkeyPress"/><Parameter Value="&quot;4&quot;"/></Action><Action Name="ControlDisable"><Parameter Value="331"/></Action><Action Name="ControlDisable"><Parameter Value="0"/></Action><Action Name="ControlDisable"><Parameter Value="335"/></Action><Action Name="ControlDisable"><Parameter Value="337"/></Action></Actions></Event></Events></Control><Control Name="Phantom" Id="216"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="HardkeyPress"/><Parameter Value="&quot;CANCEL&quot;"/></Action></Actions></Event></Events></Control></Controls></Form><Form Name="MTXSOFTKEY5"><Events/><Controls><Control Name="btnOne" Id="344" Variable="Caption_Btn1"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="SoftkeyPress"/><Parameter Value="&quot;1&quot;"/></Action><Action Name="ControlDisable"><Parameter Value="344"/></Action><Action Name="ControlDisable"><Parameter Value="346"/></Action><Action Name="ControlDisable"><Parameter Value="348"/></Action><Action Name="ControlDisable"><Parameter Value="350"/></Action><Action Name="ControlDisable"><Parameter Value="352"/></Action></Actions></Event></Events></Control><Control Name="btnTwo" Id="346" Variable="Caption_Btn2"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="SoftkeyPress"/><Parameter Value="&quot;2&quot;"/></Action><Action Name="ControlDisable"><Parameter Value="344"/></Action><Action Name="ControlDisable"><Parameter Value="346"/></Action><Action Name="ControlDisable"><Parameter Value="348"/></Action><Action Name="ControlDisable"><Parameter Value="350"/></Action><Action Name="ControlDisable"><Parameter Value="352"/></Action></Actions></Event></Events></Control><Control Name="btnThree" Id="348" Variable="Caption_Btn3"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="SoftkeyPress"/><Parameter Value="&quot;3&quot;"/></Action><Action Name="ControlDisable"><Parameter Value="344"/></Action><Action Name="ControlDisable"><Parameter Value="346"/></Action><Action Name="ControlDisable"><Parameter Value="348"/></Action><Action Name="ControlDisable"><Parameter Value="350"/></Action><Action Name="ControlDisable"><Parameter Value="352"/></Action></Actions></Event></Events></Control><Control Name="btnFour" Id="350" Variable="Caption_Btn4"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="SoftkeyPress"/><Parameter Value="&quot;4&quot;"/></Action><Action Name="ControlDisable"><Parameter Value="344"/></Action><Action Name="ControlDisable"><Parameter Value="346"/></Action><Action Name="ControlDisable"><Parameter Value="348"/></Action><Action Name="ControlDisable"><Parameter Value="350"/></Action><Action Name="ControlDisable"><Parameter Value="352"/></Action></Actions></Event></Events></Control><Control Name="btnFive" Id="352" Variable="Caption_Btn5"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="SoftkeyPress"/><Parameter Value="&quot;5&quot;"/></Action><Action Name="ControlDisable"><Parameter Value="344"/></Action><Action Name="ControlDisable"><Parameter Value="346"/></Action><Action Name="ControlDisable"><Parameter Value="348"/></Action><Action Name="ControlDisable"><Parameter Value="350"/></Action><Action Name="ControlDisable"><Parameter Value="352"/></Action></Actions></Event></Events></Control><Control Name="Phantom" Id="202"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="HardkeyPress"/><Parameter Value="&quot;CANCEL&quot;"/></Action></Actions></Event></Events></Control></Controls></Form><Form Name="WICCANFRM"><Events/><Controls/></Form><Form Name="WICENDFRM"><Events/><Controls/></Form><Form Name="WICINPFRM"><Events/><Controls/></Form><Form Name="WICLOCFRM"><Events/><Controls/></Form><Form Name="WICREMFRM"><Events/><Controls/></Form><Form Name="WICRETFRM"><Events/><Controls/></Form><Form Name="WICRXFRM"><Events/><Controls/></Form><Form Name="WICSTFRM"><Events/><Controls/></Form><Form Name="WICWTFRM"><Events/><Controls/></Form><Form Name="MTXSOFTKEY6"><Events/><Controls><Control Name="btnOne" Id="359" Variable="Caption_Btn1"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="SoftkeyPress"/><Parameter Value="&quot;1&quot;"/></Action><Action Name="ControlDisable"><Parameter Value="359"/></Action><Action Name="ControlDisable"><Parameter Value="361"/></Action><Action Name="ControlDisable"><Parameter Value="369"/></Action><Action Name="ControlDisable"><Parameter Value="363"/></Action><Action Name="ControlDisable"><Parameter Value="365"/></Action><Action Name="ControlDisable"><Parameter Value="367"/></Action></Actions></Event></Events></Control><Control Name="btnTwo" Id="361" Variable="Caption_Btn2"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="SoftkeyPress"/><Parameter Value="&quot;2&quot;"/></Action><Action Name="ControlDisable"><Parameter Value="359"/></Action><Action Name="ControlDisable"><Parameter Value="361"/></Action><Action Name="ControlDisable"><Parameter Value="369"/></Action><Action Name="ControlDisable"><Parameter Value="363"/></Action><Action Name="ControlDisable"><Parameter Value="365"/></Action><Action Name="ControlDisable"><Parameter Value="367"/></Action></Actions></Event></Events></Control><Control Name="btnFour" Id="363" Variable="Caption_Btn4"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="SoftkeyPress"/><Parameter Value="&quot;4&quot;"/></Action><Action Name="ControlDisable"><Parameter Value="359"/></Action><Action Name="ControlDisable"><Parameter Value="361"/></Action><Action Name="ControlDisable"><Parameter Value="369"/></Action><Action Name="ControlDisable"><Parameter Value="363"/></Action><Action Name="ControlDisable"><Parameter Value="365"/></Action><Action Name="ControlDisable"><Parameter Value="367"/></Action></Actions></Event></Events></Control><Control Name="btnFive" Id="365" Variable="Caption_Btn5"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="SoftkeyPress"/><Parameter Value="&quot;5&quot;"/></Action><Action Name="ControlDisable"><Parameter Value="359"/></Action><Action Name="ControlDisable"><Parameter Value="361"/></Action><Action Name="ControlDisable"><Parameter Value="369"/></Action><Action Name="ControlDisable"><Parameter Value="363"/></Action><Action Name="ControlDisable"><Parameter Value="365"/></Action><Action Name="ControlDisable"><Parameter Value="367"/></Action></Actions></Event></Events></Control><Control Name="btnSix" Id="367" Variable="Caption_Btn6"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="SoftkeyPress"/><Parameter Value="&quot;6&quot;"/></Action><Action Name="ControlDisable"><Parameter Value="359"/></Action><Action Name="ControlDisable"><Parameter Value="361"/></Action><Action Name="ControlDisable"><Parameter Value="369"/></Action><Action Name="ControlDisable"><Parameter Value="363"/></Action><Action Name="ControlDisable"><Parameter Value="365"/></Action><Action Name="ControlDisable"><Parameter Value="367"/></Action></Actions></Event></Events></Control><Control Name="btnThree" Id="369" Variable="Caption_Btn3"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="SoftkeyPress"/><Parameter Value="&quot;3&quot;"/></Action><Action Name="ControlDisable"><Parameter Value="359"/></Action><Action Name="ControlDisable"><Parameter Value="361"/></Action><Action Name="ControlDisable"><Parameter Value="369"/></Action><Action Name="ControlDisable"><Parameter Value="363"/></Action><Action Name="ControlDisable"><Parameter Value="365"/></Action><Action Name="ControlDisable"><Parameter Value="367"/></Action></Actions></Event></Events></Control><Control Name="Phantom" Id="233"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="HardkeyPress"/><Parameter Value="&quot;CANCEL&quot;"/></Action></Actions></Event></Events></Control></Controls></Form><Form Name="MTXSWIPE"><Events/><Controls><Control Name="btnManual" Id="433"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="HardkeyPress"/><Parameter Value="&quot;ENTER&quot;"/></Action></Actions></Event></Events></Control><Control Name="btnCancel" Id="435"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="HardkeyPress"/><Parameter Value="ReturnValueCANCEL"/></Action></Actions></Event></Events></Control><Control Name="MSR" Id="437"><Events><Event Name="OnSwipeBad"><Actions><Action Name="FireEvent"><Parameter Value="SwipeResult"/><Parameter Value="&quot;-1&quot;"/></Action></Actions></Event><Event Name="OnSwipeGood"><Actions><Action Name="FireEvent"><Parameter Value="Track1Data"/><Parameter Value="Track1Data"/></Action><Action Name="FireEvent"><Parameter Value="Track2Data"/><Parameter Value="Track2Data"/></Action><Action Name="FireEvent"><Parameter Value="PAN"/><Parameter Value="cnum"/></Action><Action Name="FireEvent"><Parameter Value="LastName"/><Parameter Value="lname"/></Action><Action Name="FireEvent"><Parameter Value="FirstName"/><Parameter Value="fname"/></Action><Action Name="FireEvent"><Parameter Value="ExpDate"/><Parameter Value="expd"/></Action><Action Name="FireEvent"><Parameter Value="TrackDataSource"/><Parameter Value="TrackDataSource"/></Action><Action Name="FireEvent"><Parameter Value="encTrack1Data"/><Parameter Value="encTrack1Data"/></Action><Action Name="FireEvent"><Parameter Value="encTrack2Data"/><Parameter Value="encTrack2Data"/></Action><Action Name="FireEvent"><Parameter Value="FPEKSN"/><Parameter Value="DUKPTP2P.KSN"/></Action><Action Name="FireEvent"><Parameter Value="SwipeResult"/><Parameter Value="&quot;1&quot;"/></Action><Action Name="FireEvent"><Parameter Value="ServiceCode"/><Parameter Value="ServiceCode"/></Action></Actions></Event></Events></Control></Controls></Form><Form Name="bgd"><Events/><Controls><Control Name="Label5" Id="210" Variable="Caption_lbl2"><Events/></Control><Control Name="Label3" Id="211" Variable="Caption_lbl3"><Events/></Control><Control Name="Label" Id="446" Variable="Caption_Prompt1"><Events/></Control><Control Name="Label1" Id="505" Variable="Caption_Prompt2"><Events/></Control><Control Name="Label2" Id="506" Variable="Caption_lbl1"><Events/></Control><Control Name="Label4" Id="507" Variable="Caption_lbl4"><Events/></Control></Controls></Form><Form Name="MTXYESNO"><Events/><Controls><Control Name="btnYes" Id="521" Variable="Caption_Btn1"><Events/></Control><Control Name="btnNo" Id="569" Variable="Caption_Btn2"><Events/></Control></Controls></Form><Form Name="MTXEMVSWIPE"><Events><Event Name="OnScreenEntry"><Actions><Action Name="VariableSet"><Parameter Value="ContactlessRead"/><Parameter Value="&quot;&quot;"/></Action></Actions></Event></Events><Controls><Control Name="btnManual" Id="597"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="HardkeyPress"/><Parameter Value="&quot;ENTER&quot;"/></Action></Actions></Event></Events></Control><Control Name="btnCancel" Id="613"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="HardkeyPress"/><Parameter Value="ReturnValueCANCEL"/></Action></Actions></Event></Events></Control><Control Name="MSR" Id="629"><Events><Event Name="OnContactlessRead"><Actions><Action Name="FireEvent"><Parameter Value="ContactlessRead"/><Parameter Value="&quot;1&quot;"/></Action><Action Name="FireEvent"><Parameter Value="Track1Data"/><Parameter Value="Track1Data"/></Action><Action Name="FireEvent"><Parameter Value="Track2Data"/><Parameter Value="Track2Data"/></Action><Action Name="FireEvent"><Parameter Value="PAN"/><Parameter Value="cnum"/></Action><Action Name="FireEvent"><Parameter Value="LastName"/><Parameter Value="lname"/></Action><Action Name="FireEvent"><Parameter Value="FirstName"/><Parameter Value="fname"/></Action><Action Name="FireEvent"><Parameter Value="ExpDate"/><Parameter Value="expd"/></Action><Action Name="FireEvent"><Parameter Value="encTrack1Data"/><Parameter Value="encTrack1Data"/></Action><Action Name="FireEvent"><Parameter Value="encTrack2Data"/><Parameter Value="encTrack2Data"/></Action><Action Name="FireEvent"><Parameter Value="TrackDataSource"/><Parameter Value="TrackDataSource"/></Action><Action Name="FireEvent"><Parameter Value="FPEKSN"/><Parameter Value="DUKPTP2P.KSN"/></Action></Actions></Event><Event Name="OnSmartCardInsert"><Actions><Action Name="FireEvent"><Parameter Value="SmartCardATR"/><Parameter Value="SmartCardATR"/></Action></Actions></Event><Event Name="OnSwipeBad"><Actions><Action Name="FireEvent"><Parameter Value="SwipeResult"/><Parameter Value="&quot;-1&quot;"/></Action></Actions></Event><Event Name="OnSwipeGood"><Actions><Action Name="FireEvent"><Parameter Value="Track1Data"/><Parameter Value="Track1Data"/></Action><Action Name="FireEvent"><Parameter Value="Track2Data"/><Parameter Value="Track2Data"/></Action><Action Name="FireEvent"><Parameter Value="PAN"/><Parameter Value="cnum"/></Action><Action Name="FireEvent"><Parameter Value="LastName"/><Parameter Value="lname"/></Action><Action Name="FireEvent"><Parameter Value="FirstName"/><Parameter Value="fname"/></Action><Action Name="FireEvent"><Parameter Value="ExpDate"/><Parameter Value="expd"/></Action><Action Name="FireEvent"><Parameter Value="TrackDataSource"/><Parameter Value="TrackDataSource"/></Action><Action Name="FireEvent"><Parameter Value="FPEKSN"/><Parameter Value="DUKPTP2P.KSN"/></Action><Action Name="FireEvent"><Parameter Value="encTrack1Data"/><Parameter Value="encTrack1Data"/></Action><Action Name="FireEvent"><Parameter Value="encTrack2Data"/><Parameter Value="encTrack2Data"/></Action><Action Name="FireEvent"><Parameter Value="SwipeResult"/><Parameter Value="&quot;1&quot;"/></Action><Action Name="FireEvent"><Parameter Value="ServiceCode"/><Parameter Value="ServiceCode"/></Action></Actions></Event></Events></Control></Controls></Form><Form Name="MTXDIGITALID"><Events/><Controls><Control Name="btnManual" Id="720"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="HardkeyPress"/><Parameter Value="&quot;ENTER&quot;"/></Action></Actions></Event></Events></Control><Control Name="btnCancel" Id="740"><Events><Event Name="OnPressed"><Actions><Action Name="FireEvent"><Parameter Value="HardkeyPress"/><Parameter Value="ReturnValueCANCEL"/></Action></Actions></Event></Events></Control><Control Name="MSR" Id="745"><Events><Event Name="OnSwipeBad"><Actions><Action Name="FireEvent"><Parameter Value="SwipeResult"/><Parameter Value="&quot;-1&quot;"/></Action></Actions></Event><Event Name="OnSwipeGood"><Actions><Action Name="FireEvent"><Parameter Value="Track1Data"/><Parameter Value="Track1Data"/></Action><Action Name="FireEvent"><Parameter Value="Track2Data"/><Parameter Value="Track2Data"/></Action><Action Name="FireEvent"><Parameter Value="PAN"/><Parameter Value="cnum"/></Action><Action Name="FireEvent"><Parameter Value="LastName"/><Parameter Value="lname"/></Action><Action Name="FireEvent"><Parameter Value="FirstName"/><Parameter Value="fname"/></Action><Action Name="FireEvent"><Parameter Value="ExpDate"/><Parameter Value="expd"/></Action><Action Name="FireEvent"><Parameter Value="TrackDataSource"/><Parameter Value="TrackDataSource"/></Action><Action Name="FireEvent"><Parameter Value="encTrack1Data"/><Parameter Value="encTrack1Data"/></Action><Action Name="FireEvent"><Parameter Value="encTrack2Data"/><Parameter Value="encTrack2Data"/></Action><Action Name="FireEvent"><Parameter Value="FPEKSN"/><Parameter Value="DUKPTP2P.KSN"/></Action><Action Name="FireEvent"><Parameter Value="SwipeResult"/><Parameter Value="&quot;1&quot;"/></Action></Actions></Event></Events></Control></Controls></Form></FPEScripts>
