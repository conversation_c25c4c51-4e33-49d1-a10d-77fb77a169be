<WICPromptsTable>
	<StartSession FN="WICSTFRM" P1="Please&#x20;Insert" P2="Your&#x20;WIC&#x20;Card" P3="" P4="" P5="" P6="" P7="" P8="" P9=""/>
	<EndSession FN="WICENDFRM" P1="WIC&#x20;Session&#x20;Completed" P2="" P3="" P4="" P5="" P6="" P7="" P8="" P9=""/>
	<GetPIN FN="WICPINFRM" P1="Please&#x20;Enter&#x20;Your&#x20;PIN" P2="" P3="" P4="" P5="" P6="" P7="" P8="" P9=""/>
	<ReenterPIN FN="WICPINFRM" P1="Re-enter PIN" P2="" P3="" P4="" P5="" P6="" P7="" P8="" P9=""/>
	<InvalidPIN FN="WICINVFRM" P1="Invalid PIN" P2="Re-enter PIN" P3="" P4="" P5="" P6="" P7="" P8="" P9=""/>
	<GetRx FN="WICRXFRM" P1="Reading&#x20;WIC&#x20;Prescription" P2="Please&#x20;Wait&#x2E;&#x2E;&#x2E;" P3="" P4="" P5="" P6="" P7="" P8="" P9=""/>
	<UpdateRx FN="WICRXFRM" P1="Updating&#x20;WIC&#x20;Prescription" P2="Please&#x20;Wait&#x2E;&#x2E;&#x2E;" P3="" P4="" P5="" P6="" P7="" P8="" P9=""/>
	<PleaseWait FN="WICWTFRM" P1="Please&#x20;Wait&#x2E;&#x2E;&#x2E;" P2="" P3="" P4="" P5="" P6="" P7="" P8="" P9=""/>
	<ReturnToClinic FN="WICRETFRM" P1="Card&#x20;Error&#x2C;" P2="Return&#x20;to&#x20;Clinic" P3="" P4="" P5="" P6="" P7="" P8="" P9=""/>
	<TransCancelled FN="WICCANFRM" P1="Transaction&#x20;Cancelled" P2="" P3="" P4="" P5="" P6="" P7="" P8="" P9=""/>
	<PINLocked FN="WICLOCFRM" P1="PIN&#x20;Locked&#x2C;" P2="Return&#x20;to&#x20;Clinic" P3="" P4="" P5="" P6="" P7="" P8="" P9=""/>
	<UtilizationApproval FN="WICINPFRM" P1="Approve utilization of benefits" P2="" P3="" P4="" P5="" P6="" P7="" P8="" P9=""/>
	<WICRemoveICC FN="WICREMFRM" P1="Please&#x20;remove&#x20;the&#x20;card" P2="" P3="" P4="" P5="" P6="" P7="" P8="" P9=""/>
	<NonWICCardInserted FN="WICCANFRM" P1="Invalid/Damaged Card" P2="" P3="" P4="" P5="" P6="" P7="" P8="" P9=""/>
	<CardRemovedAfterUpdate FN="WICCANFRM" P1="Card Removed" P2=" Transaction Completed" P3="" P4="" P5="" P6="" P7="" P8="" P9="" />
	<NoCurrentWIC FN="WICRETFRM" P1="No Current WIC" P2="" P3="" P4="" P5="" P6="" P7="" P8="" P9="" />
	<CardRemovedError FN="WICCANFRM" P1="Card Removed" P2="" P3="" P4="" P5="" P6="" P7="" P8="" P9="" />
	<DamagedCardInserted FN="WICCANFRM" P1="Invalid/Damaged Card" P2="" P3="" P4="" P5="" P6="" P7="" P8="" P9="" />
</WICPromptsTable>
