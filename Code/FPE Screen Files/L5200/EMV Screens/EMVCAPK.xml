<Emv xmlns="http://www.nbstech.com/keys.xsd">
  <!-- These are testing keys, do not use them in production terminal ! -->
  <Rid id="A000000003">
		<Capk id="99">
			<Modulus>AB79FCC9520896967E776E64444E5DCDD6E13611874F3985722520425295EEA4BD0C2781DE7F31CD3D041F565F747306EED62954B17EDABA3A6C5B85A1DE1BEB9A34141AF38FCF8279C9DEA0D5A6710D08DB4124F041945587E20359BAB47B7575AD94262D4B25F264AF33DEDCF28E09615E937DE32EDC03C54445FE7E382777</Modulus>
			<Exponent>03</Exponent>
			<Hash>4ABFFD6B1C51212D05552E431C5B17007D2F5E6D</Hash>
		</Capk>
		<Capk id="98">
			<Modulus>CA026E52A695E72BD30AF928196EEDC9FAF4A619F2492E3FB31169789C276FFBB7D43116647BA9E0D106A3542E3965292CF77823DD34CA8EEC7DE367E08070895077C7EFAD939924CB187067DBF92CB1E785917BD38BACE0C194CA12DF0CE5B7A50275AC61BE7C3B436887CA98C9FD39</Modulus>
			<Exponent>03</Exponent>
			<Hash>E7AC9AA8EED1B5FF1BD532CF1489A3E5557572C1</Hash>
		</Capk>
		<Capk id="95">
			<Modulus>BE9E1FA5E9A803852999C4AB432DB28600DCD9DAB76DFAAA47355A0FE37B1508AC6BF38860D3C6C2E5B12A3CAAF2A7005A7241EBAA7771112C74CF9A0634652FBCA0E5980C54A64761EA101A114E0F0B5572ADD57D010B7C9C887E104CA4EE1272DA66D997B9A90B5A6D624AB6C57E73C8F919000EB5F684898EF8C3DBEFB330C62660BED88EA78E909AFF05F6DA627B</Modulus>
			<Exponent>03</Exponent>
			<Hash>EE1511CEC71020A9B90443B37B1D5F6E703030F6</Hash>
		</Capk>
    <Capk id="94">
      <Modulus>ACD2B12302EE644F3F835ABD1FC7A6F62CCE48FFEC622AA8EF062BEF6FB8BA8BC68BBF6AB5870EED579BC3973E121303D34841A796D6DCBC41DBF9E52C4609795C0CCF7EE86FA1D5CB041071ED2C51D2202F63F1156C58A92D38BC60BDF424E1776E2BC9648078A03B36FB554375FC53D57C73F5160EA59F3AFC5398EC7B67758D65C9BFF7828B6B82D4BE124A416AB7301914311EA462C19F771F31B3B57336000DFF732D3B83DE07052D730354D297BEC72871DCCF0E193F171ABA27EE464C6A97690943D59BDABB2A27EB71CEEBDAFA1176046478FD62FEC452D5CA393296530AA3F41927ADFE434A2DF2AE3054F8840657A26E0FC617</Modulus>
      <Exponent>03</Exponent>
      <Hash>C4A3C43CCF87327D136B804160E47D43B60E6E0F</Hash>
    </Capk>
    <Capk id="92">
      <Modulus>996AF56F569187D09293C14810450ED8EE3357397B18A2458EFAA92DA3B6DF6514EC060195318FD43BE9B8F0CC669E3F844057CBDDF8BDA191BB64473BC8DC9A730DB8F6B4EDE3924186FFD9B8C7735789C23A36BA0B8AF65372EB57EA5D89E7D14E9C7B6B557460F10885DA16AC923F15AF3758F0F03EBD3C5C2C949CBA306DB44E6A2C076C5F67E281D7EF56785DC4D75945E491F01918800A9E2DC66F60080566CE0DAF8D17EAD46AD8E30A247C9F</Modulus>
      <Exponent>03</Exponent>
      <Hash>429C954A3859CEF91295F663C963E582ED6EB253</Hash>
    </Capk>
    <Capk id="50">
      <Modulus>D11197590057B84196C2F4D11A8F3C05408F422A35D702F90106EA5B019BB28AE607AA9CDEBCD0D81A38D48C7EBB0062D287369EC0C42124246AC30D80CD602AB7238D51084DED4698162C59D25EAC1E66255B4DB2352526EF0982C3B8AD3D1CCE85B01DB5788E75E09F44BE7361366DEF9D1E1317B05E5D0FF5290F88A0DB47</Modulus>
      <Exponent>010001</Exponent>
      <Hash>B769775668CACB5D22A647D1D993141EDAB7237B</Hash>
    </Capk>
	</Rid>
  <Rid id="A000000004">
    <Capk id="FE">
      <Modulus>A653EAC1C0F786C8724F737F172997D63D1C3251C44402049B865BAE877D0F398CBFBE8A6035E24AFA086BEFDE9351E54B95708EE672F0968BCD50DCE40F783322B2ABA04EF137EF18ABF03C7DBC5813AEAEF3AA7797BA15DF7D5BA1CBAF7FD520B5A482D8D3FEE105077871113E23A49AF3926554A70FE10ED728CF793B62A1</Modulus>
      <Exponent>03</Exponent>
      <Hash>9A295B05FB390EF7923F57618A9FDA2941FC34E0</Hash>
    </Capk>
    <Capk id="FA">
      <Modulus>A90FCD55AA2D5D9963E35ED0F440177699832F49C6BAB15CDAE5794BE93F934D4462D5D12762E48C38BA83D8445DEAA74195A301A102B2F114EADA0D180EE5E7A5C73E0C4E11F67A43DDAB5D55683B1474CC0627F44B8D3088A492FFAADAD4F42422D0E7013536C3C49AD3D0FAE96459B0F6B1B6056538A3D6D44640F94467B108867DEC40FAAECD740C00E2B7A8852D</Modulus>
      <Exponent>03</Exponent>
      <Hash>5BED4068D96EA16D2D77E03D6036FC7A160EA99C</Hash>
    </Capk>
    <Capk id="EF">
      <Modulus>A191CB87473F29349B5D60A88B3EAEE0973AA6F1A082F358D849FDDFF9C091F899EDA9792CAF09EF28F5D22404B88A2293EEBBC1949C43BEA4D60CFD879A1539544E09E0F09F60F065B2BF2A13ECC705F3D468B9D33AE77AD9D3F19CA40F23DCF5EB7C04DC8F69EBA565B1EBCB4686CD274785530FF6F6E9EE43AA43FDB02CE00DAEC15C7B8FD6A9B394BABA419D3F6DC85E16569BE8E76989688EFEA2DF22FF7D35C043338DEAA982A02B866DE5328519EBBCD6F03CDD686673847F84DB651AB86C28CF1462562C577B853564A290C8556D818531268D25CC98A4CC6A0BDFFFDA2DCCA3A94C998559E307FDDF915006D9A987B07DDAEB3B</Modulus>
      <Exponent>03</Exponent>
      <Hash>21766EBB0EE122AFB65D7845B73DB46BAB65427A</Hash>
    </Capk>
    <Capk id="F8">
      <Modulus>A1F5E1C9BD8650BD43AB6EE56B891EF7459C0A24FA84F9127D1A6C79D4930F6DB1852E2510F18B61CD354DB83A356BD190B88AB8DF04284D02A4204A7B6CB7C5551977A9B36379CA3DE1A08E69F301C95CC1C20506959275F41723DD5D2925290579E5A95B0DF6323FC8E9273D6F849198C4996209166D9BFC973C361CC826E1</Modulus>
      <Exponent>03</Exponent>
      <Hash>F06ECC6D2AAEBF259B7E755A38D9A9B24E2FF3DD</Hash>
    </Capk>
    <Capk id="F3">
      <Modulus>98F0C770F23864C2E766DF02D1E833DFF4FFE92D696E1642F0A88C5694C6479D16DB1537BFE29E4FDC6E6E8AFD1B0EB7EA0124723C333179BF19E93F10658B2F776E829E87DAEDA9C94A8B3382199A350C077977C97AFF08FD11310AC950A72C3CA5002EF513FCCC286E646E3C5387535D509514B3B326E1234F9CB48C36DDD44B416D23654034A66F403BA511C5EFA3</Modulus>
      <Exponent>03</Exponent>
      <Hash>A69AC7603DAF566E972DEDC2CB433E07E8B01A9A</Hash>
    </Capk>
    <Capk id="F1">
      <Modulus>A0DCF4BDE19C3546B4B6F0414D174DDE294AABBB828C5A834D73AAE27C99B0B053A90278007239B6459FF0BBCD7B4B9C6C50AC02CE91368DA1BD21AAEADBC65347337D89B68F5C99A09D05BE02DD1F8C5BA20E2F13FB2A27C41D3F85CAD5CF6668E75851EC66EDBF98851FD4E42C44C1D59F5984703B27D5B9F21B8FA0D93279FBBF69E090642909C9EA27F898959541AA6757F5F624104F6E1D3A9532F2A6E51515AEAD1B43B3D7835088A2FAFA7BE7</Modulus>
      <Exponent>03</Exponent>
      <Hash>D8E68DA167AB5A85D8C3D55ECB9B0517A1A5B4BB</Hash>
    </Capk>
  </Rid>
  <Rid id="A000000277">
    <Capk id="01">
      <Modulus>A744349661D7539CA0E334D90AB99DD01482A52522EC3B2ABDB2F13FCD9F21F8CCF7FB6D46AD2D557A70250D91B453BD2F6B349D79113F69F1EEE5DD8F7399618DFEF057C8B4F3A8026EAE0E1DA93C4F0F710353D9490BE8907F51F5785BAF45CE79BE44B23AE2241828DD466677284D33C32911A265AAF229E582C4683DD249</Modulus>
      <Exponent>010001</Exponent>
      <Hash>A8DB2D6ACE6A238E82E859069DCC6F8B0B1493CE</Hash>
    </Capk>
    <Capk id="02">
      <Modulus>E0FFBEE77CEE02ADEE8B4B004D26FB46FAF3CE033E3B874D73D099A966A0657497CEE214E95DF2BF5C9D359A3B10C05CFC9929BEF3070036FD19AE661B173E486CBC9B04E4B2E2D0D8209CAF5C200929FA252A21BD69A9A47488844A1DE5BF8729CE5E5E92117047BDDE9FBE72397FD9FC3BE459D8ED06FD6CADF0AFF39C93CC07B312EE2ACB3D4A15E919F57481CFEF</Modulus>
      <Exponent>010001</Exponent>
      <Hash>BAF3652A9F5D0FF45EECECA55B4597B1F53BE283</Hash>
    </Capk>
    <Capk id="03">
      <Modulus>E51505CCECDD0799BAFE097200DF40FFC2154836B8D67F1E99D4D415F73C04A9FAD73825D32AE5BA77FCD02597393CE4D8104B6010D5A8F28F2B47BB8298DAFD63C9C0BEF62AF937265614E31AE2AB45B60B968DB9DEE55602C169F6C16D4579BAA24765560300A3056F894BA8FFA566D9CCD79453D804B97DA14F21F9C3528ED8B8A368A9FACF05C46C13A9BA020618425EF1ACF6CB4DD5DCD050273502114B59EE90EBD833F70C2F324741E79A19A1</Modulus>
      <Exponent>010001</Exponent>
      <Hash>0FB60A1BCA38095F3CC578D2DEC95F779840A343</Hash>
    </Capk>
    <Capk id="09">
      <Modulus>F802C308544873AD2225A81943732A4B7CFFA4E3157D17CD5A7723F858F0B11E636D2930FA933778F27C7C49127E0CCA317021CFE8E0F773785EB3FF07587E98CE8ED4FE9E1CA1859F41A9CF2572D8A093C5465F5A29612A45B1700F4DA13814C3D4DF075EAADE8DB4BE4D7B3AE0256F7A0C12E34BD416CAC4F9250C38B7E13B</Modulus>
      <Exponent>010001</Exponent>
      <Hash>A2974D6B8F302A923740E2BC6217075202037B8B</Hash>
    </Capk>
    <Capk id="40">
      <Modulus>F802C308544873AD2225A81943732A4B7CFFA4E3157D17CD5A7723F858F0B11E636D2930FA933778F27C7C49127E0CCA317021CFE8E0F773785EB3FF07587E98CE8ED4FE9E1CA1859F41A9CF2572D8A093C5465F5A29612A45B1700F4DA13814C3D4DF075EAADE8DB4BE4D7B3AE0256F7A0C12E34BD416CAC4F9250C38B7E13B</Modulus>
      <Exponent>010001</Exponent>
      <Hash>2015497BE4B86F104BBF337691825EED64E101CA</Hash>
    </Capk>
  </Rid>
  <Rid id="A000000025">
    <Capk id="C1">
      <Modulus>E69E319C34D1B4FB43AED4BD8BBA6F7A8B763F2F6EE5DDF7C92579A984F89C4A9C15B27037764C58AC7E45EFBC34E138E56BA38F76E803129A8DDEB5E1CC8C6B30CF634A9C9C1224BF1F0A9A18D79ED41EBCF1BE78087AE8B7D2F896B1DE8B7E784161A138A0F2169AD33E146D1B16AB595F9D7D98BE671062D217F44EB68C68640C7D57465A063F6BAC776D3E2DAC61</Modulus>
      <Exponent>03</Exponent>
      <Hash>DC79D6B5FC879362299BC5A637DAD2E0D99656B8</Hash>
    </Capk>
    <Capk id="C2">
      <Modulus>B875002F38BA26D61167C5D440367604AD38DF2E93D8EE8DA0E8D9C0CF4CC5788D11DEA689E5F41D23A3DA3E0B1FA5875AE25620F5A6BCCEE098C1B35C691889D7D0EF670EB8312E7123FCC5DC7D2F0719CC80E1A93017F944D097330EDF945762FEE62B7B0BA0348228DBF38D4216E5A67A7EF74F5D3111C44AA31320F623CB3C53E60966D6920067C9E082B746117E48E4F00E110950CA54DA3E38E5453BD5544E3A6760E3A6A42766AD2284E0C9AF</Modulus>
      <Exponent>03</Exponent>
      <Hash>8E748296359A7428F536ADDA8E2C037E2B697EF6</Hash>
    </Capk>
    <Capk id="C3">
      <Modulus>B93182ABE343DFBF388C71C4D6747DCDEC60367FE63CFAA942D7D323E688D0832836548BF0EDFF1EDEEB882C75099FF81A93FA525C32425B36023EA02A8899B9BF7D7934E86F997891823006CEAA93091A73C1FDE18ABD4F87A22308640C064C8C027685F1B2DB7B741B67AB0DE05E870481C5F972508C17F57E4F833D63220F6EA2CFBB878728AA5887DE407D10C6B8F58D46779ECEC1E2155487D52C78A5C03897F2BB580E0A2BBDE8EA2E1C18F6AAF3EB3D04C3477DEAB88F150C8810FD1EF8EB0596866336FE2C1FBC6BEC22B4FE5D885647726DB59709A505F75C49E0D8D71BF51E4181212BE2142AB2A1E8C0D3B7136CD7B7708E4D</Modulus>
      <Exponent>03</Exponent>
      <Hash>12F1790CB0273DC73C6E70784BC24C12E8DB71F6</Hash>
    </Capk>
  </Rid>
   <Rid id="A000000152">
    <Capk id="5A">
      <Modulus>EDD8252468A705614B4D07DE3211B30031AEDB6D33A4315F2CFF7C97DB918993C2DC02E79E2FF8A2683D5BBD0F614BC9AB360A448283EF8B9CF6731D71D6BE939B7C5D0B0452D660CF24C21C47CAC8E26948C8EED8E3D00C016828D642816E658DC2CFC61E7E7D7740633BEFE34107C1FB55DEA7FAAEA2B25E85BED948893D07</Modulus>
      <Exponent>03</Exponent>
    </Capk>
    <Capk id="5B">
      <Modulus>D3F45D065D4D900F68B2129AFA38F549AB9AE4619E5545814E468F382049A0B9776620DA60D62537F0705A2C926DBEAD4CA7CB43F0F0DD809584E9F7EFBDA3778747BC9E25C5606526FAB5E491646D4DD28278691C25956C8FED5E452F2442E25EDC6B0C1AA4B2E9EC4AD9B25A1B836295B823EDDC5EB6E1E0A3F41B28DB8C3B7E3E9B5979CD7E079EF024095A1D19DD</Modulus>
      <Exponent>03</Exponent>
    </Capk>
    <Capk id="5C">
      <Modulus>833F275FCF5CA4CB6F1BF880E54DCFEB721A316692CAFEB28B698CAECAFA2B2D2AD8517B1EFB59DDEFC39F9C3B33DDEE40E7A63C03E90A4DD261BC0F28B42EA6E7A1F307178E2D63FA1649155C3A5F926B4C7D7C258BCA98EF90C7F4117C205E8E32C45D10E3D494059D2F2933891B979CE4A831B301B0550CDAE9B67064B31D8B481B85A5B046BE8FFA7BDB58DC0D7032525297F26FF619AF7F15BCEC0C92BCDCBC4FB207D115AA65CD04C1CF982191</Modulus>
      <Exponent>03</Exponent>
	  </Capk>
  </Rid>
  <Rid id="A000000612">
<Capk id="7C">
<Modulus>ADF020304DB974C2AD2C5B794A95C6C8843FBC4CB874C36023B16497A67513882F7FB29CBE44E2F494B911A24236FA51FB945378A445B86D945C9DB2E9FDE53BC33EDC471F24AB005F483009468E9A4C06F81D0EFAE0E0363ED343FAA405527BEB1F4B55148FFBAC15C4A71499E4AABA45BBB1D0BF644D3501D33853E0DC74AFBA058B3991D6AAAC0CCFD4C45BC97BA1</Modulus>
<Exponent>03</Exponent>
<Hash>761EF64CAEBAFE21D25AD446FD377E3A6622C2B3</Hash>
</Capk>
<Capk id="F1">
      <Modulus>A0DCF4BDE19C3546B4B6F0414D174DDE294AABBB828C5A834D73AAE27C99B0B053A90278007239B6459FF0BBCD7B4B9C6C50AC02CE91368DA1BD21AAEADBC65347337D89B68F5C99A09D05BE02DD1F8C5BA20E2F13FB2A27C41D3F85CAD5CF6668E75851EC66EDBF98851FD4E42C44C1D59F5984703B27D5B9F21B8FA0D93279FBBF69E090642909C9EA27F898959541AA6757F5F624104F6E1D3A9532F2A6E51515AEAD1B43B3D7835088A2FAFA7BE7</Modulus>
      <Exponent>03</Exponent>
      <Hash>D8E68DA167AB5A85D8C3D55ECB9B0517A1A5B4BB</Hash>
    </Capk>
    <Capk id="05">
      <Modulus>B8 04 8A BC 30 C9 0D 97 63 36 54 3E 3F D7 09 1C 8F E4 80 0D F8 20 ED 55 E7 E9 48 13 ED 00 55 5B 57 3F EC A3 D8 4A F6 13 1A 65 1D 66 CF F4 28 4F B1 3B 63 5E DD 0E E4 01 76 D8 BF 04 B7 FD 1C 7B AC F9 AC 73 27 DF AA 8A A7 2D 10 DB 3B 8E 70 B2 DD D8 11 CB 41 96 52 5E A3 86 AC C3 3C 0D 9D 45 75 91 64 69 C4 E4 F5 3E 8E 1C 91 2C C6 18 CB 22 DD E7 C3 56 8E 90 02 2E 6B BA 77 02 02 E4 52 2A 2D D6 23 D1 80 E2 15 BD 1D 15 07 FE 3D C9 0C A3 10 D2 7B 3E FC CD 8F 83 DE 30 52 CA D1 E4 89 38 C6 8D 09 5A AC 91 B5 F3 7E 28 BB 49 EC 7E D5 97</Modulus>
      <Exponent>03</Exponent>
      <Hash>EB FA 0D 5D 06 D8 CE 70 2D A3 EA E8 90 70 1D 45 E2 74 C8 45</Hash>
    </Capk>
</Rid>
<FileIdentifier>jeffis cool</FileIdentifier>
</Emv>