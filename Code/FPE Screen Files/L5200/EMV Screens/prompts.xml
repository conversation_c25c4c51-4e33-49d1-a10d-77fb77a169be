﻿<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<Prompts>
	<!-- The following prompts are used on the default screens for contact EMV transactions provided by Equinox.-->
	<Prompt ID="welcome"          LanguageCode="en" LanguageName="English" Text="Welcome" />
	<Prompt ID="welcome"          LanguageCode="es" LanguageName="Spanish" Text="Bienvenida" />
	<Prompt ID="welcome"          LanguageCode="fr" LanguageName="French" Text="Bonjour" />
	<Prompt ID="insert"           LanguageCode="en" LanguageName="English" Text="Please insert, swipe or tap your card" />
	<Prompt ID="insert"           LanguageCode="es" LanguageName="Spanish" Text="Por favor, inserte, pase o toque su tarjeta" />
	<Prompt ID="insert"           LanguageCode="fr" LanguageName="French" Text="Inséz, glissez ou passez votre carte" />
	<Prompt ID="reInsert"         LanguageCode="en" LanguageName="English" Text="Please insert, swipe or tap your card again" />
	<Prompt ID="reInsert"         LanguageCode="es" LanguageName="Spanish" Text="Por favor, inserte, pase o toque su tarjeta ancora" />
	<Prompt ID="reInsert"         LanguageCode="fr" LanguageName="French" Text="Inséz, glissez ou passez votre carte encore" />
	<Prompt ID="swipe"            LanguageCode="en" LanguageName="English" Text="Please swipe your card" />
	<Prompt ID="swipe"            LanguageCode="es" LanguageName="Spanish" Text="Por favor, pase su tarjeta" />
	<Prompt ID="swipe"            LanguageCode="fr" LanguageName="French" Text="Glisser votre carte" />
	<Prompt ID="reSwipe"          LanguageCode="en" LanguageName="English" Text="Please swipe your card again" />
	<Prompt ID="reSwipe"          LanguageCode="es" LanguageName="Spanish" Text="Por favor, pase su tarjeta de nuevo" />
	<Prompt ID="reSwipe"          LanguageCode="fr" LanguageName="French" Text="Glisser votre carte encore" />
	<Prompt ID="selectapp"        LanguageCode="en" LanguageName="English" Text="Select application" />
	<Prompt ID="selectapp"        LanguageCode="es" LanguageName="Spanish" Text="Seleccione aplicación" />
	<Prompt ID="selectapp"        LanguageCode="fr" LanguageName="French" Text="Selectionnez application" />
	<Prompt ID="selectlang"       LanguageCode="en" LanguageName="English" Text="Select language" />
	<Prompt ID="selectlang"       LanguageCode="es" LanguageName="Spanish" Text="Seleccione lengua" />
	<Prompt ID="selectlang"       LanguageCode="fr" LanguageName="French" Text="Selectionnez une langue" />
	<Prompt ID="enterpin"         LanguageCode="en" LanguageName="English" Text="Please enter your PIN" />
	<Prompt ID="enterpin"         LanguageCode="es" LanguageName="Spanish" Text="Por favor, introduzca PIN" />
	<Prompt ID="enterpin"         LanguageCode="fr" LanguageName="French" Text="Entrez votre NIP" />
	<Prompt ID="reenterpin"       LanguageCode="en" LanguageName="English" Text="Please enter your PIN again" />
	<Prompt ID="reenterpin"       LanguageCode="es" LanguageName="Spanish" Text="Por favor, introduzca PIN de nuevo" />
	<Prompt ID="reenterpin"       LanguageCode="fr" LanguageName="French" Text="Entrez votre NIP encore" />
	<Prompt ID="reenterpin_tries" LanguageCode="en" LanguageName="English" Text="Tries left" />
	<Prompt ID="reenterpin_tries" LanguageCode="es" LanguageName="Spanish" Text="Intentos restantes" />
	<Prompt ID="reenterpin_tries" LanguageCode="fr" LanguageName="French" Text="Vous avez laisse essais" />
	<Prompt ID="cancelbutton"     LanguageCode="en" LanguageName="English" Text="Cancel" />
	<Prompt ID="cancelbutton"     LanguageCode="fr" LanguageName="French" Text="Annuler" />
	<Prompt ID="cancelbutton" 	  LanguageCode="es" LanguageName="Spanish" Text="Cancelar" />
	<Prompt ID="donebutton" 	  LanguageCode="en" LanguageName="English" Text="Done" />
	<Prompt ID="donebutton" 	  LanguageCode="fr" LanguageName="French" Text="Entendu" />
	<Prompt ID="donebutton" 	  LanguageCode="es" LanguageName="Spanish" Text="Validar" />
	<Prompt ID="sign" 			  LanguageCode="en" LanguageName="English" Text="Please sign here" />
	<Prompt ID="sign" 			  LanguageCode="fr" LanguageName="French" Text="S'il vous plaît signer ici" />
	<Prompt ID="sign" 			  LanguageCode="es" LanguageName="Spanish" Text="Por favor, firme aquí"/>
    
    <!-- The following prompts are used on the NCR defined signed screens -->
   	<Prompt ID="enterAmount"      LanguageCode="en" LanguageName="English" Text="Please Enter an Amount" />
	<Prompt ID="enterAmount"      LanguageCode="es" LanguageName="Spanish" Text="es Please Enter an Amount" />
	<Prompt ID="enterAmount"      LanguageCode="fr" LanguageName="French" Text="fr Please Enter an Amount" />
   	<Prompt ID="enterCashBack"    LanguageCode="en" LanguageName="English" Text="Please Enter Cash Back Amount" />
	<Prompt ID="enterCashBack"    LanguageCode="es" LanguageName="Spanish" Text="es Please Enter Cash Back Amount" />
	<Prompt ID="enterCashBack"    LanguageCode="fr" LanguageName="French" Text="fr Please Enter Cash Back Amount" />
   	<Prompt ID="enterCVV2"        LanguageCode="en" LanguageName="English" Text="Enter the CVV2 (Security Code)" />
	<Prompt ID="enterCVV2"        LanguageCode="es" LanguageName="Spanish" Text="es Enter the CVV2 (Security Code)" />
	<Prompt ID="enterCVV2"        LanguageCode="fr" LanguageName="French" Text="fr Enter the CVV2 (Security Code)" />
    <Prompt ID="enterExpDate"     LanguageCode="en" LanguageName="English" Text="Enter the Card Expiration Date (mm/yy)" />
	<Prompt ID="enterExpDate"     LanguageCode="es" LanguageName="Spanish" Text="es Enter the Card Expiration Date (mm/yy)" />
	<Prompt ID="enterExpDate"     LanguageCode="fr" LanguageName="French" Text="fr Enter the Card Expiration Date (mm/yy)" />
    <Prompt ID="enterDriverID"    LanguageCode="en" LanguageName="English" Text="Enter Your Driver ID" />
	<Prompt ID="enterDriverID"    LanguageCode="es" LanguageName="Spanish" Text="es Enter Your Driver ID" />
	<Prompt ID="enterDriverID"    LanguageCode="fr" LanguageName="French" Text="fr Enter Your Driver ID" />
    <Prompt ID="enterOdometer"    LanguageCode="en" LanguageName="English" Text="Enter Your Odometer Reading" />
	<Prompt ID="enterOdometer"    LanguageCode="es" LanguageName="Spanish" Text="es Enter Your Odometer Reading" />
	<Prompt ID="enterOdometer"    LanguageCode="fr" LanguageName="French" Text="fr Enter Your Odometer Reading" />
    <Prompt ID="enterVehicleID"   LanguageCode="en" LanguageName="English" Text="Enter Your Vehicle ID" />
	<Prompt ID="enterVehicleID"   LanguageCode="es" LanguageName="Spanish" Text="es Enter Your Vehicle ID" />
	<Prompt ID="enterVehicleID"   LanguageCode="fr" LanguageName="French" Text="fr Enter Your Vehicle ID" />    
    <Prompt ID="enterCardNumber"  LanguageCode="en" LanguageName="English" Text="Enter the Card Number" />
	<Prompt ID="enterCardNumber"  LanguageCode="es" LanguageName="Spanish" Text="es Enter the Card Number" />
	<Prompt ID="enterCardNumber"  LanguageCode="fr" LanguageName="French" Text="fr Enter the Card Number" />    
    <Prompt ID="enterPhoneNumber" LanguageCode="en" LanguageName="English" Text="Please Enter Your Phone Number" />
	<Prompt ID="enterPhoneNumber" LanguageCode="es" LanguageName="Spanish" Text="es Please Enter Your Phone Number" />
	<Prompt ID="enterPhoneNumber" LanguageCode="fr" LanguageName="French" Text="fr Please Enter Your Phone Number" />    
   	<Prompt ID="enterPIN"         LanguageCode="en" LanguageName="English" Text="Please Enter Your PIN" />
	<Prompt ID="enterPIN"         LanguageCode="es" LanguageName="Spanish" Text="es Please Enter Your PIN" />
	<Prompt ID="enterPIN"         LanguageCode="fr" LanguageName="French" Text="fr Please Enter Your PIN" />    
    <Prompt ID="enterPINwithCredit" LanguageCode="en" LanguageName="English" Text="Please Enter Your PIN" />
	<Prompt ID="enterPINwithCredit" LanguageCode="es" LanguageName="Spanish" Text="es Please Enter Your PIN" />
	<Prompt ID="enterPINwithCredit" LanguageCode="fr" LanguageName="French" Text="fr Please Enter Your PIN" />    
   	<Prompt ID="enterZipcode"     LanguageCode="en" LanguageName="English" Text="Please Enter Your Zip Code" />
	<Prompt ID="enterZipcode"     LanguageCode="es" LanguageName="Spanish" Text="es Please Enter Your Zip Code" />
	<Prompt ID="enterZipcode"     LanguageCode="fr" LanguageName="French" Text="fr Please Enter Your Zip Code" /> 
    <Prompt ID="enterMemberNo1"   LanguageCode="en" LanguageName="English" Text="Enter Member Number" />
	<Prompt ID="enterMemberNo1"   LanguageCode="es" LanguageName="Spanish" Text="es Enter Member Number" />
	<Prompt ID="enterMemberNo1"   LanguageCode="fr" LanguageName="French" Text="fr Enter Member Number" /> 
    <Prompt ID="enterMemberNo2"   LanguageCode="en" LanguageName="English" Text="or Slide Member Card" />
	<Prompt ID="enterMemberNo2"   LanguageCode="es" LanguageName="Spanish" Text="es or Slide Member Card" />
	<Prompt ID="enterMemberNo2"   LanguageCode="fr" LanguageName="French" Text="fr or Slide Member Card" /> 
   
	
	<!-- The following prompts are built in FPE32. The most of them are used by FPE32v7.U.500 for contact EMV transactions support-->
	<!-- Language names -->
	<Prompt ID='{0001}' LanguageCode='en' Text='English' LanguageName="English" />
	<Prompt ID='{0001}' LanguageCode='fr' Text='Français' LanguageName="French" />
	<Prompt ID='{0001}' LanguageCode='es' Text='Español' LanguageName="Spanish" />

	<!-- Range 03 - 21 EMVCo User Interface Standard Messages, see EMV Contactless Book A, 9.4 User Interface Standard Messages section -->
	<!-- Note: Most of the messages are NOT used for Contact EMV. The messages in range 03 - 21 are mostly applicable to Contactless EMV or reserved for future use. -->
	<Prompt ID='{0003}' LanguageCode='en' Text='Approved' Comment='EMVCo User Interface Standard Message 03' LanguageName="English" />
	<Prompt ID='{0003}' LanguageCode='es' Text='Aprobado' Comment='EMVCo User Interface Standard Message 03' LanguageName="Spanish" />
	<Prompt ID='{0003}' LanguageCode='fr' Text='Approuvé' Comment='EMVCo User Interface Standard Message 03' LanguageName="French" />
	<Prompt ID='{0007}' LanguageCode='en' Text='Not Authorized' Comment='EMVCo User Interface Standard Message 07' LanguageName="English" />
	<Prompt ID='{0007}' LanguageCode='es' Text='No autorizado' Comment='EMVCo User Interface Standard Message 07' LanguageName="Spanish" />
	<Prompt ID='{0007}' LanguageCode='fr' Text='Pas autorisé' Comment='EMVCo User Interface Standard Message 07' LanguageName="French" />	
	<Prompt ID='{0009}' LanguageCode='en' Text='Please enter your PIN' Comment='EMVCo User Interface Standard Message 09' LanguageName="English" />
	<Prompt ID='{0009}' LanguageCode='es' Text='Por favor, introduzca su PIN' Comment='EMVCo User Interface Standard Message 09' LanguageName="Spanish" />
	<Prompt ID='{0009}' LanguageCode='fr' Text='Entrez votre NIP' Comment='EMVCo User Interface Standard Message 09' LanguageName="French" />	
	<Prompt ID='{000F}' LanguageCode='en' Text='Processing error' Comment='EMVCo User Interface Standard Message 0F' LanguageName="English" />
	<Prompt ID='{000F}' LanguageCode='es' Text='Error de procesamiento' Comment='EMVCo User Interface Standard Message 0F' LanguageName="Spanish" />
	<Prompt ID='{000F}' LanguageCode='fr' Text='Transformation erreur' Comment='EMVCo User Interface Standard Message 0F' LanguageName="French" />
	<Prompt ID='{0010}' LanguageCode='en' Text='Please remove card' Comment='EMVCo User Interface Standard Message 10' LanguageName="English" />
	<Prompt ID='{0010}' LanguageCode='es' Text='Por favor, retire la tarjeta' Comment='EMVCo User Interface Standard Message 10' LanguageName="Spanish" />
	<Prompt ID='{0010}' LanguageCode="fr" Text="Retirez la carte SVP" Comment='EMVCo User Interface Standard Message 10' LanguageName="French" />
	<Prompt ID='{0014}' LanguageCode='en' Text='Welcome' Comment='EMVCo User Interface Standard Message 14' LanguageName="English" />
	<Prompt ID='{0014}' LanguageCode='es' Text='Bienvenida' Comment='EMVCo User Interface Standard Message 14' LanguageName="Spanish" />
	<Prompt ID='{0014}' LanguageCode='fr' Text='Bonjour' Comment='EMVCo User Interface Standard Message 14' LanguageName="French" />
	<Prompt ID='{0015}' LanguageCode='en' Text='Present card' Comment='EMVCo User Interface Standard Message 15' LanguageName="English" />
	<Prompt ID='{0015}' LanguageCode='es' Text='Presente la tarjeta' Comment='EMVCo User Interface Standard Message 15' LanguageName="Spanish" />
	<Prompt ID='{0015}' LanguageCode='fr' Text='Présenter votre carte' Comment='EMVCo User Interface Standard Message 15' LanguageName="French" />
	<Prompt ID='{0016}' LanguageCode='en' Text='Processing...' Comment='EMVCo User Interface Standard Message 16' LanguageName="English" />
	<Prompt ID='{0016}' LanguageCode='es' Text='Procesando...' Comment='EMVCo User Interface Standard Message 16' LanguageName="Spanish" />
	<Prompt ID='{0016}' LanguageCode='fr' Text='Transformation...' Comment='EMVCo User Interface Standard Message 16' LanguageName="French" />
	<Prompt ID='{0017}' LanguageCode='en' Text='Card read OK. Please remove card' Comment='EMVCo User Interface Standard Message 17' LanguageName="English" />
	<Prompt ID='{0017}' LanguageCode='es' Text='Tarjeta leída correctamente. Por favor, retire la tarjeta' Comment='EMVCo User Interface Standard Message 17' LanguageName="Spanish" />
	<Prompt ID='{0018}' LanguageCode='en' Text='Please insert or swipe card' Comment='EMVCo User Interface Standard Message 18' LanguageName="English" />
	<Prompt ID='{0018}' LanguageCode='es' Text='Por favor, inserte o pase la tarjeta' Comment='EMVCo User Interface Standard Message 18' LanguageName="Spanish" />
	<Prompt ID='{0019}' LanguageCode='en' Text='Please present one card only' Comment='EMVCo User Interface Standard Message 19' LanguageName="English" />
	<Prompt ID='{0019}' LanguageCode='es' Text='Por favor, presente sólo una tarjeta' Comment='EMVCo User Interface Standard Message 19' LanguageName="Spanish" />
	<Prompt ID='{001A}' LanguageCode='en' Text='Approved. Please sign' Comment='EMVCo User Interface Standard Message 1A' LanguageName="English" />
	<Prompt ID='{001A}' LanguageCode='es' Text='Aprobado. Por favor firme' Comment='EMVCo User Interface Standard Message 1A' LanguageName="Spanish" />
	<Prompt ID='{001B}' LanguageCode='en' Text='Authorizing...\nPlease wait...' Comment='EMVCo User Interface Standard Message 1B' LanguageName="English" />
	<Prompt ID='{001B}' LanguageCode='es' Text='Autorizando...\nPor favor, espere...' Comment='EMVCo User Interface Standard Message 1B' LanguageName="Spanish" />
	<Prompt ID='{001B}' LanguageCode='fr' Text="Autorisation...\nS'il vous plaît attendre..." Comment='EMVCo User Interface Standard Message 1B' LanguageName="French" />
	<Prompt ID='{001C}' LanguageCode='en' Text='Insert, swipe or try another card' Comment='EMVCo User Interface Standard Message 1C' LanguageName="English" />
	<Prompt ID='{001C}' LanguageCode='es' Text='Inserte, pase o pruebe con otra tarjeta' Comment='EMVCo User Interface Standard Message 1C' LanguageName="Spanish" />
	<Prompt ID='{001D}' LanguageCode='en' Text='Please insert card' Comment='EMVCo User Interface Standard Message 1D' LanguageName="English" />
	<Prompt ID='{001D}' LanguageCode='es' Text='Por favor, inserte la tarjeta' Comment='EMVCo User Interface Standard Message 1D' LanguageName="Spanish" />
	<Prompt ID='{001E}' LanguageCode='en' Text='' Comment='EMVCo User Interface Standard Message 1E,(empty)' LanguageName="English" />
	<Prompt ID='{001E}' LanguageCode='es' Text='' Comment='EMVCo User Interface Standard Message 1E,(empty)' LanguageName="Spanish" />
	<Prompt ID='{001E}' LanguageCode='fr' Text='' Comment='EMVCo User Interface Standard Message 1E,(empty)' LanguageName="French" />	
	<Prompt ID='{0020}' LanguageCode='en' Text='See Phone for Instructions' Comment='EMVCo User Interface Standard Message 20' LanguageName="English" />
	<Prompt ID='{0020}' LanguageCode='es' Text='Vea teléfono para obtener instrucciones' Comment='EMVCo User Interface Standard Message 20' LanguageName="Spanish" />
	<Prompt ID='{0021}' LanguageCode='en' Text='Present card again' Comment='EMVCo User Interface Standard Message 21' LanguageName="English" />
	<Prompt ID='{0021}' LanguageCode='es' Text='Presente la tarjeta de nuevo' Comment='EMVCo User Interface Standard Message 21' LanguageName="Spanish" />
	<Prompt ID='{0021}' LanguageCode='fr' Text='Présenter votre carte encore' Comment='EMVCo User Interface Standard Message 21' LanguageName="French" />

	<!-- Range 1000 - 1100 Miscellaneous messages -->
	<Prompt ID='{1000}' LanguageCode='en' Text='Message #%i' Comment='This message will be displayed if message with same ID was not found in this file' LanguageName="English"/>
	<Prompt ID='{1000}' LanguageCode='es' Text='Mensaje #%i' Comment='This message will be displayed if message with same ID was not found in this file' LanguageName="Spanish"/>
	<Prompt ID='{1001}' LanguageCode='en' Text='Transaction canceled' LanguageName="English"/>
	<Prompt ID='{1001}' LanguageCode='es' Text='Transacción cancelada' LanguageName="Spanish"/>
	<Prompt ID='{1002}' LanguageCode='en' Text='Card removed' LanguageName="English"/>
	<Prompt ID='{1002}' LanguageCode='es' Text='Tarjeta retirada' LanguageName="Spanish"/>
	<Prompt ID='{1002}' LanguageCode='fr' Text='Annulée la carte SVP' LanguageName="French"/>
	<Prompt ID='{1003}' LanguageCode='en' Text='Selected %s' LanguageName="English"/>
	<Prompt ID='{1003}' LanguageCode='es' Text='Seleccionado %s' LanguageName="Spanish"/>
	<Prompt ID='{1003}' LanguageCode='fr' Text='Choisi %s' LanguageName="French"/>
	<Prompt ID='{1004}' LanguageCode='en' Text='Please remove card and swipe it' Comment='Fallback' LanguageName="English"/>
	<Prompt ID='{1004}' LanguageCode='es' Text='Por favor, retire la tarjeta y pásela por el lector de banda' Comment='Fallback' LanguageName="Spanish"/>
	<Prompt ID='{1005}' LanguageCode='en' Text='%i PIN tries left' LanguageName="English"/>
	<Prompt ID='{1005}' LanguageCode='es' Text='%i ntentos de PIN restantes' LanguageName="Spanish"/>
	<Prompt ID='{1005}' LanguageCode='fr' Text='%i NIP laisse essais' LanguageName="French"/>
	<Prompt ID='{1006}' LanguageCode='en' Text='PIN bypassed' LanguageName="English"/>
	<Prompt ID='{1006}' LanguageCode='es' Text='PIN evitado' LanguageName="Spanish"/>
	<Prompt ID='{1006}' LanguageCode='fr' Text='NIP bypass' LanguageName="French"/>
	<Prompt ID='{1007}' LanguageCode='en' Text='You have last try left' LanguageName="English"/>
	<Prompt ID='{1007}' LanguageCode='es' Text='Ultimo intento' LanguageName="Spanish"/>
	<Prompt ID='{1007}' LanguageCode='fr' Text='Vous avez laisse dernier essai' LanguageName="French"/>
	
	<!-- Range 2000 - 2099 EMV transaction execution error messages -->
	<Prompt ID='{2000}' LanguageCode='en' Text='Error #%i occured' Comment='This message will be displayed if error message with same ID was not found in this file' LanguageName="English"/>
	<Prompt ID='{2000}' LanguageCode='es' Text='Error #%i ocurrió' Comment='This message will be displayed if error message with same ID was not found in this file' LanguageName="Spanish"/>
	<Prompt ID='{2001}' LanguageCode='en' Text='Could not read card, remove and try again' Comment='Bad ATR' LanguageName="English"/>
	<Prompt ID='{2001}' LanguageCode='es' Text='No se pudo leer la tarjeta, retire la tarjeta y vuelva a intentarlo' Comment='Bad ATR' LanguageName="Spanish"/>
	<Prompt ID='{2002}' LanguageCode='en' Text='Form not found' Comment='Form not found' LanguageName="English"/>
	<Prompt ID='{2002}' LanguageCode='es' Text='Formulario no encontrado' Comment='Form not found' LanguageName="Spanish"/>
	<Prompt ID='{2003}' LanguageCode='en' Text='EMV configuration load failed' LanguageName="English"/>
	<Prompt ID='{2003}' LanguageCode='es' Text='Error en la carga de la configuración EMV' LanguageName="Spanish"/>
	<Prompt ID='{2004}' LanguageCode='en' Text='Set gc:begintrans to start the transaction' LanguageName="English"/>
	<Prompt ID='{2004}' LanguageCode='es' Text='Poner a 1 gc:begintrans para iniciar la transacción' LanguageName="Spanish"/>
	<Prompt ID='{2005}' LanguageCode='en' Text='PIN tries exceeded' LanguageName="English"/>
	<Prompt ID='{2005}' LanguageCode='es' Text='Numero de intentos de PIN superado' LanguageName="Spanish"/>
	<Prompt ID='{2006}' LanguageCode='en' Text='PIN is blocked' LanguageName="English" />
	<Prompt ID='{2006}' LanguageCode='es' Text='PIN está bloqueado' LanguageName="Spanish"/>
	<Prompt ID='{2007}' LanguageCode='en' Text='Card read failure' LanguageName="English"/>
	<Prompt ID='{2007}' LanguageCode='es' Text='Error de lectura de tarjeta' LanguageName="Spanish"/>
	<Prompt ID='{2008}' LanguageCode='en' Text='Invalid transaction step' LanguageName="English"/>
	<Prompt ID='{2008}' LanguageCode='es' Text='Orden de la transacción inválido' LanguageName="Spanish"/>
	<Prompt ID='{2011}' LanguageCode='en' Text='Transaction timed out' LanguageName="English"/>
	<Prompt ID='{2011}' LanguageCode='es' Text='Agotado el tiempo de espera de la transacción' LanguageName="Spanish"/>
	<Prompt ID='{2012}' LanguageCode='en' Text='PIN encryption error' LanguageName="English"/>
	<Prompt ID='{2012}' LanguageCode='es' Text='Error de cifrado de PIN' LanguageName="Spanish"/>
	<Prompt ID='{2012}' LanguageCode='fr' Text='NIP erreur de chiffrement' LanguageName="French"/>
	<Prompt ID='{2013}' LanguageCode='en' Text='Card data encryption error' LanguageName="English"/>
	<Prompt ID='{2013}' LanguageCode='es' Text='Error de cifrado de datos' LanguageName="Spanish"/>
	<Prompt ID='{2013}' LanguageCode='fr' Text='Erreur de cryptage des données' LanguageName="French"/>
	<Prompt ID='{2014}' LanguageCode='en' Text='This is a chip card' LanguageName="English"/>
	<Prompt ID='{2014}' LanguageCode='es' Text='Esta es una tarjeta chip' LanguageName="Spanish"/>
	<Prompt ID='{2014}' LanguageCode='fr' Text="Il se agit d'une carte à puce" LanguageName="French"/>
	<Prompt ID='{2015}' LanguageCode='en' Text='Transaction aborted' LanguageName="English"/>
	<Prompt ID='{2015}' LanguageCode='es' Text='Transacción abortada' LanguageName="Spanish"/>
	<Prompt ID='{2015}' LanguageCode='fr' Text='Transaction interrompre' LanguageName="French"/>
	<Prompt ID='{2016}' LanguageCode='en' Text='EMV configuration load failed\nInvalid  public key hash for RID: %s, index: %02X' LanguageName="English"/>
	<Prompt ID='{2017}' LanguageCode='en' Text='Card is not supported' LanguageName="English"/>
	<Prompt ID='{2017}' LanguageCode='es' Text='Tarjeta no es compatible' LanguageName="Spanish"/>
	<Prompt ID='{2017}' LanguageCode='fr' Text='Carte ne est pas support&#233;' LanguageName="French"/>
	<Prompt ID='{2018}' LanguageCode='en' Text='Contactless card removed prematurely' LanguageName="English"/>

	<!-- Range 2100 - 2199 EMV kernel status messages for debugging -->
	<Prompt ID='{2100}' LanguageCode='en' Text='Ok' LanguageName="English"/>
	<Prompt ID='{2101}' LanguageCode='en' Text='Transaction finished with an error, unspecified reason' LanguageName="English"/>
	<Prompt ID='{2101}' LanguageCode='es' Text='Transacción error, razones sin especificar' LanguageName="Spanish"/>
	<Prompt ID='{2101}' LanguageCode='fr' Text='Transaction a échoué, raison non spécifiée' LanguageName="French"/>	
	<Prompt ID='{2102}' LanguageCode='en' Text='Data length exceeds tag limits' LanguageName="English"/>
	<Prompt ID='{2103}' LanguageCode='en' Text='TLV nesting exceeds 4 levels' LanguageName="English"/>
	<Prompt ID='{2104}' LanguageCode='en' Text='TLV parser, unexpected end of data' LanguageName="English"/>
	<Prompt ID='{2105}' LanguageCode='en' Text='TLV Tag coding exceeds 4 bytes' LanguageName="English"/>
	<Prompt ID='{2106}' LanguageCode='en' Text='TLV length coding exceeds 4 bytes' LanguageName="English"/>
	<Prompt ID='{2107}' LanguageCode='en' Text='TLV data length exceeds 255 bytes' LanguageName="English"/>
	<Prompt ID='{2108}' LanguageCode='en' Text='Specified tag does not exist' LanguageName="English"/>
	<Prompt ID='{2109}' LanguageCode='en' Text='Perform list of AIDs processing' LanguageName="English"/>
	<Prompt ID='{210A}' LanguageCode='en' Text='Card is blocked' LanguageName="English"/>
	<Prompt ID='{210B}' LanguageCode='en' Text='Card does not support EMV "Select" command (mandatory)' LanguageName="English"/>
	<Prompt ID='{210C}' LanguageCode='en' Text='DOL does not contain tag 9F37' LanguageName="English"/>
	<Prompt ID='{210D}' LanguageCode='en' Text='Attempted overwrite of ICC data detected' LanguageName="English"/>
	<Prompt ID='{210E}' LanguageCode='en' Text='Mandatory data missing' LanguageName="English"/>
	<Prompt ID='{210F}' LanguageCode='en' Text='ASCII character > 0x7F present' LanguageName="English"/>
	<Prompt ID='{2110}' LanguageCode='en' Text='Buffer contains ADF' LanguageName="English"/>
	<Prompt ID='{2111}' LanguageCode='en' Text='Invalid data detected' LanguageName="English"/>
	<Prompt ID='{2112}' LanguageCode='en' Text='Data detected' LanguageName="English"/>
	<Prompt ID='{2113}' LanguageCode='en' Text='Out of range data encountered' LanguageName="English"/>
	<Prompt ID='{2114}' LanguageCode='en' Text='Identifies AID on candidate list which is to be deleted.' LanguageName="English"/>
	<Prompt ID='{2115}' LanguageCode='en' Text='No application was selected.' LanguageName="English"/>
	<Prompt ID='{2116}' LanguageCode='en' Text='Application is Visa "Easy Entry" type' LanguageName="English"/>
	<Prompt ID='{2117}' LanguageCode='en' Text='ICC has returned 6A83 (no more records)' LanguageName="English"/>
	<Prompt ID='{2118}' LanguageCode='en' Text='Candidate list has been exhausted' LanguageName="English"/>
	<Prompt ID='{2119}' LanguageCode='en' Text='No supported applications found, use magnetic stripe instead' LanguageName="English"/>
	<Prompt ID='{211A}' LanguageCode='en' Text='Application selection successful' LanguageName="English"/>
	<Prompt ID='{211B}' LanguageCode='en' Text='Payment services environment processing is being performed' LanguageName="English"/>
	<Prompt ID='{211C}' LanguageCode='en' Text='List of AIDs processing is being performed' LanguageName="English"/>
	<Prompt ID='{211D}' LanguageCode='en' Text='Keyboard not present, but application needs manual confirmation' LanguageName="English"/>
	<Prompt ID='{211E}' LanguageCode='en' Text='Buffer contains DDF' LanguageName="English"/>
	<Prompt ID='{211F}' LanguageCode='en' Text='Select DF response = 6283' LanguageName="English"/>
	<Prompt ID='{2120}' LanguageCode='en' Text='DOL does not contain tag 9F33' LanguageName="English"/>
	<Prompt ID='{2121}' LanguageCode='en' Text='Tag A5 missing (FCI template)' LanguageName="English"/>
	<Prompt ID='{2122}' LanguageCode='en' Text='Certificate revoked' LanguageName="English"/>
	<Prompt ID='{2123}' LanguageCode='en' Text='Bad status returned by card' LanguageName="English"/>
	<Prompt ID='{2125}' LanguageCode='en' Text='Public key missing (or expired)' LanguageName="English"/>
	<Prompt ID='{2126}' LanguageCode='en' Text='Atmel cryptoprocessor has returned SW1 SW2 != 90 00' LanguageName="English"/>
	<Prompt ID='{2127}' LanguageCode='en' Text='Atmel cryptoprocesor comms failure' LanguageName="English"/>
	<Prompt ID='{2128}' LanguageCode='en' Text='Data store full' LanguageName="English"/>
	<Prompt ID='{2129}' LanguageCode='en' Text='Data not stored' LanguageName="English"/>
	<Prompt ID='{212A}' LanguageCode='en' Text='Library wishes to auto select an application.' LanguageName="English"/>
	<Prompt ID='{212B}' LanguageCode='en' Text='PIN encryption has failed (key error, ATMEL failure, etc.)' LanguageName="English"/>
	<Prompt ID='{212C}' LanguageCode='en' Text='Script command has returned SW1 != 90 || 62 || 63' LanguageName="English"/>
	<Prompt ID='{212D}' LanguageCode='en' Text='Constructed tag found in DOL list ' LanguageName="English"/>
	<Prompt ID='{212E}' LanguageCode='en' Text='Unknown tag found in DOL list' LanguageName="English"/>
	<Prompt ID='{212F}' LanguageCode='en' Text='Non terminal source tag found in PDOL' LanguageName="English"/>
	<Prompt ID='{2131}' LanguageCode='en' Text='CVM process requires offline encrypted PIN' LanguageName="English"/>
	<Prompt ID='{2132}' LanguageCode='en' Text='CVM process complete' LanguageName="English"/>
	<Prompt ID='{2133}' LanguageCode='en' Text='CVM process requires signature' LanguageName="English"/>
	<Prompt ID='{2134}' LanguageCode='en' Text='CVM process requires offline plain text PIN' LanguageName="English"/>
	<Prompt ID='{2135}' LanguageCode='en' Text='CVM process requires online encrypted PIN' LanguageName="English"/>
	<Prompt ID='{2136}' LanguageCode='en' Text='CVM process found incorrect PIN' LanguageName="English"/>
	<Prompt ID='{2137}' LanguageCode='en' Text='CVM process found failed PIN' LanguageName="English"/>
	<Prompt ID='{2138}' LanguageCode='en' Text='CVM process found failed PIN, signature is required' LanguageName="English"/>
	<Prompt ID='{2139}' LanguageCode='en' Text='CVM process found blocked PIN' LanguageName="English"/>
	<Prompt ID='{213A}' LanguageCode='en' Text='CVM process found blocked PIN, signature is required' LanguageName="English"/>
	<Prompt ID='{213B}' LanguageCode='en' Text='ICC has returned cryptogram exceeding that requested' LanguageName="English"/>
	<Prompt ID='{213C}' LanguageCode='en' Text='Host connection cancelled' LanguageName="English"/>
	<Prompt ID='{213D}' LanguageCode='en' Text='CVM process needs application to validate custom condition' LanguageName="English"/>
	<Prompt ID='{213E}' LanguageCode='en' Text='CVM process needs application to perform custom CVM method' LanguageName="English"/>
	<Prompt ID='{213F}' LanguageCode='en' Text='Command has returned 6A81 (function not supported)' LanguageName="English"/>
	<Prompt ID='{2140}' LanguageCode='en' Text='Mode not enabled.' LanguageName="English"/>
	<Prompt ID='{2141}' LanguageCode='en' Text='Additional tag found' LanguageName="English"/>
	<Prompt ID='{2142}' LanguageCode='en' Text='Bad user supplied function parameter, pointer = NULL, etc.' LanguageName="English"/>
</Prompts>
