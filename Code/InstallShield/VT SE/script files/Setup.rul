#include "ifx.h" 

#define LOG_FILE_NAME "MTXInstLog.txt"
#define LOG_FILE_PATH "C:\\" //FOLDER_DESKTOP

#define DEFAULT_OPENEPS_FOLDER "C:\\Program Files\\MicroTrax\\OpenEPS"
#define DEFAULT_WINEPSIP	"127.0.0.1"
#define DEFAULT_WINEPSPORT  "6201"
#define DEFAULT_POSTYPE		"99"

#define REG_APP_COMPAT_FLAGS "SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\AppCompatFlags"

#define CUSTOM_CONFIG_PATH	"C:\\"
#define CUSTOM_CONFIG_FILENAME "CpInstall.ini"
#define INI_SECTION_GENERAL = "General"
#define INI_IDENT_CHAIN = "Chain"
#define INI_IDENT_BRANCH = "Branch"
#define INI_IDENT_DESC = "Desc"

#define INI_SECTION_MTX = "MTX"
#define INI_IDENT_DRIVE = "Drive"
#define INI_IDENT_PROXYIP = "ProxyIP"
#define INI_IDENT_PROXYPORT = "ProxyPort"
#define KEK3_REG_PATH "Software\\MTXEPS\\OpenEPS" // DEV-18500

//Build-Settings-Command Line Arguments: "-s -a -i"c:\1 111" -t02 -h192.168.1.2 -c1234 -o1234"
//-a -s -t02 -h10.250.32.46 -p2100 -c12345 -o54321

#define RES_DIALOG_ID       12008
#define RES_PBUT_NEXT           1  
#define RES_PBUT_CANCEL         9  
#define RES_PBUT_BACK          12
#define RES_EDITBOX_TITLE     300
#define RES_LISTBOX_TITLE     400
#define RES_DIALOG_EDITBOX    301
#define RES_DIALOG_LISTBOX    401
#define RES_STA_DESC          710  
#define RES_DIALOG_SELECTFOLDER_ID 12019
#define RES_DIALOG_COMBO_POS     1303
#define RES_DIALOG_COMBO_OPENEPS 1305

STRING POSType, InstallPath, WinEPSIP, WinEPSPort;
STRING CompanyNumber, StoreNumber; // YHJ-500
NUMBER nCompanyNumber, nStoreNumber; // YHJ-500
BOOL   bZipIt;
STRING svZipPath, svZipParms;

prototype AddToLog(BYVAL STRING);
prototype SdSelectPOS(BYVAL LIST, BYVAL LIST, BYVAL LIST, BYREF STRING, BYREF STRING);
prototype SdSelectStore(BYVAL LIST, BYVAL LIST, BYVAL LIST, BYREF STRING, BYREF STRING); // YHJ-500
//prototype SdSelectDrive(BYVAL STRING, BYVAL STRING, BYREF STRING);
prototype SdSelectDrive(BYREF STRING, BYREF STRING);
prototype StrToNumDef(BYVAL STRING, BYREF NUMBER);
prototype IsNumeric(BYVAL STRING, BYREF BOOL);

function IsNumeric(szStr, nvResult)
	NUMBER tmpNum;
	STRING tmpStr;
begin
	nvResult = FALSE;
	StrToNum(tmpNum, szStr);
	NumToStr(tmpStr, tmpNum);
	nvResult = (szStr==tmpStr);
end;
               
function StrToNumDef(szStr, nvResult)
begin
   nvResult = 0;
   StrToNum(nvResult, szStr);
end;

function AddToLog(svLogInfo)
	NUMBER nLogFile;
	NUMBER nvResult;
	STRING svDate, svTime;
begin
	OpenFileMode(FILE_MODE_APPEND);
	if( OpenFile(nLogFile, LOG_FILE_PATH, LOG_FILE_NAME) < 0 ) then
		CreateFile(nLogFile, LOG_FILE_PATH, LOG_FILE_NAME);
	endif;
	GetSystemInfo(DATE, nvResult, svDate);
	GetSystemInfo(TIME, nvResult, svTime);
	WriteLine(nLogFile, svDate + ' ' + svTime + ' ' + svLogInfo);
	CloseFile(nLogFile);
end;
/*
function SdSelectDrive(szTitle, szMsg, svDrive)
    STRING szDialogName, svPreselectedDrive;
    NUMBER nResult, nCmdValue, hwndDlg;
    LIST   listLocalDrives;
    BOOL   bDone;
begin
   
   //AddToLog("Select Drive Begin.");
   // Specify a name to identify the custom dialog in this setup.
   szDialogName = "CustomDialog";

   // Define the dialog.  Pass a null string in the second parameter
   // to get the dialog from_isuser.dll or _isres.dll.  Pass a null
   // string in the third parameter because the dialog is identified
   // by its ID in the fourth parameter.

   nResult = EzDefineDialog (szDialogName, "", "", RES_DIALOG_ID); 
  
   if (nResult < 0) then
      // Report an error; then terminate.
      MessageBox ("Error in defining dialog", SEVERE);
      abort;
   endif;

   // Loop until done.
   repeat
      // Display the dialog and return the next dialog event.
      nCmdValue = WaitOnDialog (szDialogName);
      // Respond to the event.
      switch (nCmdValue)
         case DLG_CLOSE:
            // The user clicked the window's close button.
            Do (EXIT);
         case DLG_ERR:
            MessageBox ("Unable to display dialog. Setup canceled.", SEVERE);
            abort;
         case DLG_INIT:
            // Get the dialog's window handle.
            hwndDlg = CmdGetHwndDlg (szDialogName);
            //KL Disable the edit box
            _WinSubEnableControl(hwndDlg, RES_DIALOG_EDITBOX, DISABLE);            
            // Set the window title.  SetWindowText is prototyped
            // in Winsub.h and defined in Winsub.rul.
            SetWindowText (hwndDlg, @TITLE_CAPTIONBAR);
            CtrlSetText (szDialogName, RES_EDITBOX_TITLE, "Selected Drive");
            CtrlSetText (szDialogName, RES_LISTBOX_TITLE, "Drive List:");
            // Set the message that appears at the top of the dialog.
            CtrlSetText (szDialogName,  RES_STA_DESC, szMsg);
            SdSetDlgTitle(szDialogName, hwndDlg, szTitle);
            // Fill the dialog's listbox with the names of all folders
            // that reside in the root of the Windows drive.
            listLocalDrives = ListCreate(STRINGLIST);
            //GetValidDrivesList(listLocalDrives, FIXED_DRIVE, -1);
            GetValidDrivesList(listLocalDrives, REMOVEABLE_DRIVE, -1);
            
            
            CtrlSetList (szDialogName, RES_DIALOG_LISTBOX, listLocalDrives);
            CtrlSetList (szDialogName, RES_DIALOG_COMBO_POS, listLocalDrives);
    		ListGetFirstString(listLocalDrives, svPreselectedDrive);
    		ListDestroy(listLocalDrives);
            // Select the preselected folder.
            //ListGetFirstString(listFileNames, svString);
            CtrlGetCurSel (szDialogName, RES_DIALOG_LISTBOX, svPreselectedDrive);
            
            // Put the name of the preselected folder into the edit box.
            CtrlSetText (szDialogName, RES_DIALOG_EDITBOX, svPreselectedDrive);
            //CtrlSetText (szDialogName, RES_DIALOG_EDITBOX,  svPreselectedDrive + ":" ^ "Program Files" ^ "MicroTrax" ^ "OpenEPS");
         case RES_DIALOG_LISTBOX:
            // Get the current listbox selection.
            CtrlGetCurSel (szDialogName, RES_DIALOG_LISTBOX, svPreselectedDrive);
            // Put the current selection in the edit box.
            CtrlSetText (szDialogName, RES_DIALOG_EDITBOX, svPreselectedDrive); 
            //CtrlSetText (szDialogName, RES_DIALOG_EDITBOX,  svPreselectedDrive + ":" ^ "Program Files" ^ "MicroTrax" ^ "OpenEPS");
         case RES_PBUT_BACK:
            bDone = TRUE;
         case RES_PBUT_NEXT:
            // Get the selection from the Edit box.
            CtrlGetText (szDialogName, RES_DIALOG_EDITBOX, svPreselectedDrive);
            //CtrlGetText (szDialogName, RES_DIALOG_EDITBOX,  svPreselectedDrive + ":" ^ "Program Files" ^ "MicroTrax" ^ "OpenEPS");
            bDone = TRUE;
         case RES_PBUT_CANCEL:
            // The user clicked the Cancel button.
            Do (EXIT);
     endswitch;

	until bDone;

	// Close the custom dialog box.
	EndDialog (szDialogName);

	// Remove the custom dialog box from memory.
	ReleaseDialog (szDialogName);

	// If the edit box was closed with the Done button,
	// display the selected item.
   
	if (nCmdValue = RES_PBUT_NEXT) then
		svDrive = svPreselectedDrive;
		return NEXT;
	elseif (nCmdValue = RES_PBUT_BACK) then
		return BACK;
	else
		return CANCEL;
	endif;
	//AddToLog("Select Drive End.");
end;
*/

function SdSelectDrive(svPOSDrive, svOpenEPSDrive) // 825.0
    STRING szDialogName, svPreselectedDrive, sTmp;
    NUMBER nResult, nCmdValue, hwndDlg, nListResult;
    LIST   listLocalDrives, listTmp;
    BOOL   bDone;
begin   
   //AddToLog("Select Drive Begin.");
   szDialogName = "CustomDialog";
   nResult = EzDefineDialog (szDialogName, "", "", RES_DIALOG_SELECTFOLDER_ID); 
  
   if (nResult < 0) then
      MessageBox ("Error in defining dialog", SEVERE);
      abort;
   endif;

   repeat
      nCmdValue = WaitOnDialog (szDialogName);
      switch (nCmdValue)
         case DLG_CLOSE:
            Do (EXIT);
         case DLG_ERR:
            MessageBox ("Unable to display dialog. Setup canceled.", SEVERE);
            abort;
         case DLG_INIT:
            // Get the dialog's window handle.
            hwndDlg = CmdGetHwndDlg (szDialogName);
            
            SetWindowText (hwndDlg, @TITLE_CAPTIONBAR);
            
            listLocalDrives = ListCreate(STRINGLIST);
            listTmp = ListCreate(STRINGLIST);
            GetValidDrivesList(listLocalDrives, FIXED_DRIVE, -1);
            GetValidDrivesList(listTmp, REMOVEABLE_DRIVE, -1);
            
			nListResult = ListGetFirstString(listTmp, sTmp);
			while (nListResult != END_OF_LIST) 
				if (sTmp != "A") && (sTmp != "B") then
					ListAddString(listLocalDrives, sTmp, AFTER);
				endif;
				nListResult = ListGetNextString(listTmp, sTmp);
			endwhile;
			
            CtrlSetList (szDialogName, RES_DIALOG_COMBO_POS, listLocalDrives);
            CtrlSetList (szDialogName, RES_DIALOG_COMBO_OPENEPS, listLocalDrives);
            ListGetFirstString(listLocalDrives, svPreselectedDrive);
            //svPreselectedDrive = "C";            
            //CtrlGetCurSel (szDialogName, RES_DIALOG_COMBO_POS, svPreselectedDrive);
            //CtrlGetCurSel (szDialogName, RES_DIALOG_COMBO_OPENEPS, svPreselectedDrive);
            CtrlSetText (szDialogName, RES_DIALOG_COMBO_POS, svPreselectedDrive);
            CtrlSetText (szDialogName, RES_DIALOG_COMBO_OPENEPS, svPreselectedDrive);
            
    		ListDestroy(listLocalDrives);            
         case RES_PBUT_BACK:
            bDone = TRUE;
         case RES_PBUT_NEXT:
            CtrlGetText (szDialogName, RES_DIALOG_COMBO_POS, svPOSDrive);
            //MessageBox("You selected POS drive " + svPOSDrive, INFORMATION);
            CtrlGetText (szDialogName, RES_DIALOG_COMBO_OPENEPS, svOpenEPSDrive);
            //MessageBox("You selected OpenEPS drive " + svOpenEPSDrive, INFORMATION);
            //CtrlGetText (szDialogName, RES_DIALOG_EDITBOX,  svPreselectedDrive + ":" ^ "Program Files" ^ "MicroTrax" ^ "OpenEPS");
            bDone = TRUE;
         case RES_PBUT_CANCEL:
            Do (EXIT);
     endswitch;

	until bDone;

	// Close the custom dialog box.
	EndDialog (szDialogName);

	// Remove the custom dialog box from memory.
	ReleaseDialog (szDialogName);

	// If the edit box was closed with the Done button,
	// display the selected item.
   
	if (nCmdValue = RES_PBUT_NEXT) then
		//svPOSDrive = svPreselectedDrive;
		return NEXT;
	elseif (nCmdValue = RES_PBUT_BACK) then
		return BACK;
	else
		return CANCEL;
	endif;
end;

//________________________________________________________________________________

function SdSelectPOS(listDisplay, listPOS, listDir, svSelectedPOS, svSelectedDir)
	STRING  szDialogName, svSelection, szDesc, svString;
	NUMBER  nCmdValue, nResult, nI, nListResult;
	BOOL    bDone;
	HWND    hwndDlg;
begin
	szDialogName = "CustomDialog";
	nResult = EzDefineDialog (szDialogName, "", "", RES_DIALOG_ID);
	if (nResult < 0) then
		AddToLog ("ERROR: Aborted by Installshield - Error in defining dialog"); // YHJ-06    
		MessageBox ("Error in defining dialog", SEVERE); 
		abort;
	endif;
  
  repeat 
    nCmdValue = WaitOnDialog (szDialogName);
    switch (nCmdValue)
      case DLG_CLOSE: 
           Do(EXIT);
      case DLG_ERR:
           AddToLog ("ERROR: Aborted by Installshield - Unable to display dialog"); // YHJ-06
           MessageBox ("Unable to display dialog. Setup canceled.", SEVERE);           
           abort;
      case DLG_INIT:
           hwndDlg = CmdGetHwndDlg (szDialogName);
           //_WinSubEnableControl(hwndDlg, RES_DIALOG_EDITBOX, DISABLE);
           //SetWindowText (hwndDlg, "OpenEPS Installation");
           szDesc = "Please select a POS. Select 'Others' if an alternate POS DLL path is needed.\nDrive for POS DLL will be selected next.";
           CtrlSetText (szDialogName,  RES_STA_DESC, szDesc);
           SdSetDlgTitle(szDialogName, hwndDlg, "Select POS installation");           
           CtrlSetText (szDialogName, RES_EDITBOX_TITLE, "Selected POS:");
           CtrlSetText (szDialogName, RES_LISTBOX_TITLE, "POS System List:");
           
           CtrlSetList (szDialogName, RES_DIALOG_LISTBOX, listDisplay);
           CtrlGetCurSel (szDialogName, RES_DIALOG_LISTBOX, svString);
           CtrlSetText (szDialogName, RES_DIALOG_EDITBOX, svString);
      case RES_DIALOG_LISTBOX:
           CtrlGetCurSel (szDialogName, RES_DIALOG_LISTBOX, svSelection);
           CtrlSetText (szDialogName, RES_DIALOG_EDITBOX, svSelection);
      case RES_PBUT_BACK:
           bDone = TRUE;
      case RES_PBUT_NEXT:
           CtrlGetText (szDialogName, RES_DIALOG_EDITBOX, svSelection);
           bDone = TRUE;
      case RES_PBUT_CANCEL:
           Do(EXIT);
    endswitch;
  until bDone;
  
  EndDialog (szDialogName);
  ReleaseDialog (szDialogName);

  if (nCmdValue = RES_PBUT_NEXT) then
    nListResult = ListGetFirstString(listDisplay, svSelectedPOS);
    ListGetFirstString(listDir, svSelectedDir);
    while (nListResult != END_OF_LIST) 
      if (svSelectedPOS = svSelection) then
	    return NEXT;
      endif;      
      nListResult = ListGetNextString(listDisplay, svSelectedPOS);
      ListGetNextString(listDir, svSelectedDir);
    endwhile;
    return NEXT;
elseif (nCmdValue = RES_PBUT_BACK) then
		return BACK;
  else
    return CANCEL;
  endif;
end;

//________________________________________________________________________________

function SdSelectStore(listDisplay, listStore, listStoreNum, svSelectedStore, svSelectedStoreNum)
	STRING  szDialogName, svSelection, szDesc, svString, svSelectedIdx;	
	NUMBER  nCmdValue, nResult, nI, nListResult;
	BOOL    bDone;
	HWND    hwndDlg;
begin
	szDialogName = "CustomDialog";
  nResult = EzDefineDialog (szDialogName, "", "", RES_DIALOG_ID);
  if (nResult < 0) then
    AddToLog ("ERROR: Aborted by Installshield - Error in defining dialog"); // YHJ-06    
    MessageBox ("Error in defining dialog", SEVERE); 
    abort;
  endif;
  
  repeat 
    nCmdValue = WaitOnDialog (szDialogName);
    switch (nCmdValue)
      case DLG_CLOSE: 
           Do(EXIT);
      case DLG_ERR:
           AddToLog ("ERROR: Aborted by Installshield - Unable to display dialog"); // YHJ-06
           MessageBox ("Unable to display dialog. Setup canceled.", SEVERE);           
           abort;
      case DLG_INIT:
           hwndDlg = CmdGetHwndDlg (szDialogName);
           SetWindowText (hwndDlg, "Store Selection Screen");
           szDesc = "Please select a Store.";
           CtrlSetText (szDialogName,  RES_STA_DESC, szDesc);
           SdSetDlgTitle(szDialogName, hwndDlg, "Select Store");           
           CtrlSetText (szDialogName, RES_EDITBOX_TITLE, "Selected Store:");
           CtrlSetText (szDialogName, RES_LISTBOX_TITLE, "Store List:");
           
           CtrlSetList (szDialogName, RES_DIALOG_LISTBOX, listDisplay);
           CtrlGetCurSel (szDialogName, RES_DIALOG_LISTBOX, svString);
           CtrlSetText (szDialogName, RES_DIALOG_EDITBOX, svString);
      case RES_DIALOG_LISTBOX:
           CtrlGetCurSel (szDialogName, RES_DIALOG_LISTBOX, svSelection);
           CtrlSetText (szDialogName, RES_DIALOG_EDITBOX, svSelection);
      case RES_PBUT_BACK:
           bDone = TRUE;
      case RES_PBUT_NEXT:
           CtrlGetText (szDialogName, RES_DIALOG_EDITBOX, svSelection);
           bDone = TRUE;
      case RES_PBUT_CANCEL:
           Do(EXIT);
    endswitch;
  until bDone;
  
  EndDialog (szDialogName);
  ReleaseDialog (szDialogName);

  if (nCmdValue = RES_PBUT_NEXT) then
    nListResult = ListGetFirstString(listDisplay, svSelectedStore);
    ListGetFirstString(listStore, svSelectedIdx);
    ListGetFirstString(listStoreNum, svSelectedStoreNum);    
    while (nListResult != END_OF_LIST) 
      if (svSelectedStore = svSelection) then
      	svSelectedStore = svSelectedIdx; // YHJ-500
	    return NEXT;
      endif;      
      nListResult = ListGetNextString(listDisplay, svSelectedStore);
      ListGetNextString(listStore, svSelectedIdx);
      ListGetNextString(listStoreNum, svSelectedStoreNum);
    endwhile;
    return NEXT;
elseif (nCmdValue = RES_PBUT_BACK) then
		return BACK;
  else
    return CANCEL;
  endif;
end;

//________________________________________________________________________________

function OnFirstUIBefore()
    NUMBER  nResult,nSetupType, nIndex, nStoreCnt;
    STRING  szTitle, szMsg, sIndex;
    STRING  szLicenseFile, szQuestion;
    STRING  szField1, szField2, svDrive; 
    BOOL    bSetPath, bAddUser, bData, bNum, bCustomConfig, bMTXPOSInstallDirSelected, bPostBack; 
    STRING  svParameters, svParamPrefix, svDestPath, svString;  
    NUMBER  nListResult, ListLength, ListResult; 
    
    LIST    paramsID, listPOS, listDir, listDisplay;
    STRING  svPOS, svDir, svSelectedPOS, svSelectedDir, svIniFileName;
    STRING  sPOSDrive, sOpenEPSDrive, sPOSInstallDir, sOpenEPSInstallDir; // 825.0
    LIST    listStore, listStoreNum; // YHJ-500    
    STRING	svStore, svStoreNum, svSelectedStore, svSelectedStoreNum; // YHJ-500
    
    STRING  svValue, svResult, szKey, szNumName, szDir;
    NUMBER  nvSize, nType;
    
    LIST    listVersion;
    STRING  svFileVersion, szVersion, szMajorVer, szMinorVer, szSPVer, szBuildVer;
    NUMBER  nzMajorVer, nzMinorVer, nzSPVer, nzBuildVer;
    
    BOOL    IsValidIE, FoundPOSType;    
    BOOL	bResult; // DEV-18500    
begin

bZipIt = 0;

//TARGETDIR = DEFAULT_OPENEPS_FOLDER;
TARGETDIR = ""; // YHJ-500
//FeatureSetTarget ( MEDIA , "<MtxPosDir>" , DEFAULT_OPENEPS_FOLDER );

Dlg_Start:
    AddToLog("-----------------------------------------------------------------------");
    AddToLog("OpenEPS SE Installshield");
    AddToLog("-----------------------------------------------------------------------");
    
Check_OSVersion: // Windows 95 OSR1 or higher
    AddToLog("Checking OS Version ... (Requires Windows 95 OSR1 or higher)");
    if (SYSINFO.nISOSL = ISOSL_WIN95) then
        if (SYSINFO.WIN9X.bVersionNotFound = TRUE) then
            AddToLog("Found Windows 95 original. Needs to upgrade to OSR1");
            szMsg = "Found Windows 95 original. Needs to upgrade to OSR1\n";
            szMsg = szMsg + "Run W95setup.exe in CD-ROM please.\n\n";
        endif;        
    endif;
 
Abort_If_Low_Requirement:   
    if (szMsg != "") then
        szMsg = szMsg + "Install again after installing above requirement(s).";
        if (MODE = NORMALMODE) then
        	MessageBox (szMsg, INFORMATION);
        endif;
        AddToLog("Installation aborted by installer.");
        abort;
    endif;
    szMsg = "";
    
Set_BlankKEK3: // DEV-18500
	AddToLog ("Checking Security...");	
	//[HKEY_LOCAL_MACHINE\SOFTWARE\MTXEPS]
	//"KEK3"="20101025145938-FA61677CA3457D9C635D85CA612F9ABE3546302E56587827141947EA20B60F57"
	bResult = TRUE;
	RegDBSetDefaultRoot(HKEY_LOCAL_MACHINE);
	if (RegDBKeyExist(KEK3_REG_PATH) < 0) then
		AddToLog("Creating Security...");
		bResult = (RegDBCreateKey (KEK3_REG_PATH) >= 0);
		if (!bResult) then
			AddToLog("Failed to create security");
		endif;
	endif;
	
	if (bResult) then
		nType = REGDB_STRING;		
		svResult = "";
		RegDBGetKeyValueEx (KEK3_REG_PATH, "KEK3", nType, svResult, nResult);
		if (svResult == "") then
			bResult = (RegDBSetKeyValueEx (KEK3_REG_PATH, "KEK3", REGDB_STRING, "blank-key", -1) >= 0);
			if (bResult) then
				AddToLog("Set blank security");
			else
				AddToLog("Failed to set security");
			endif;
		else
			AddToLog("Security was set already");
		endif;
	endif;
	
	if (!bResult) then
		if (MODE = NORMALMODE) then
	        MessageBox(@WARNING_USERPRIVILEGES, WARNING);
	    endif;
	    AddToLog(@WARNING_USERPRIVILEGES);
		abort;
	endif;
    AddToLog("After Checking Security"); 

Dlg_SdLicense:
	bCustomConfig = FALSE;
	bPostBack = FALSE;
	
	Disable(BACKBUTTON);
    szLicenseFile = SUPPORTDIR ^ "license.txt";
    szTitle    = "";
    szMsg      = "";
    szQuestion = "";
    nResult    = SdLicense( szTitle, szMsg, szQuestion, szLicenseFile );
    //if (nResult = BACK) goto Dlg_Start;    
    //AddToLog("Dlg_SdLicense end.");

ParseCmdLine:
	if (MODE = NORMALMODE) goto InitVars;
	AddToLog("Install Mode: Silent");
		bSetPath = FALSE;
        bAddUser = FALSE;
        svParameters = CMDLINE;
		bData = FALSE;
		for nIndex = 1 to StrLength(svParameters)
			if svParameters[nIndex] == "\"" then
				if bData then
					bData = FALSE;
				else
					bData = TRUE;
				endif;    
			elseif svParameters[nIndex] == " " then
				if !bData then
					svParameters[nIndex] = "|";
				endif;
			endif;
		endfor;
        
        paramsID = ListCreate(STRINGLIST);
        StrGetTokens( paramsID, svParameters, "|");
        
        FoundPOSType = FALSE;
        nListResult = ListGetFirstString(paramsID, svString);        
        while(nListResult != END_OF_LIST)
			StrSub(svParamPrefix, svString, 0, 2);
			if((svParamPrefix = "-t") || (svParamPrefix = "/t")) then
				FoundPOSType = TRUE;
			endif;
			nListResult = ListGetNextString(paramsID, svString); 
        endwhile;
        
        if InstallPath = "" then 
        	InstallPath = DEFAULT_OPENEPS_FOLDER; 
        endif;        
        
        nListResult = ListGetFirstString(paramsID, svString);       
		while(nListResult != END_OF_LIST)
			StrSub(svParamPrefix, svString, 0, 2);
			if ((svParamPrefix = "-i") || (svParamPrefix = "/i")) then
				//if !FoundPOSType then
					StrSub(InstallPath, svString, 2, StrLength(svString));
					if(InstallPath[0] = "\"") then
						StrSub(InstallPath, InstallPath, 1, StrLength(InstallPath));
						StrSub(InstallPath, InstallPath, 0, StrLength(InstallPath) - 1);
					endif;
					// if can create else set default
					bSetPath = TRUE;
				//endif;
			elseif ((svParamPrefix = "-t") || (svParamPrefix = "/t")) then // POSType
				StrSub(POSType, svString, 2, StrLength(svString));				
				//InstallPath = DEFAULT_OPENEPS_FOLDER; 
				if (POSType == "") then
					POSType = "99";
				endif;
				if (POSType == "02") then				
   					InstallPath = "C:\\ACS\\Bin";
   				elseif (POSType == "03") then
   					InstallPath = "C:\\Program Files\\Storeline\\Winpos\\Drv32";
   				elseif (POSType == "04") then
   					InstallPath = "C:\\Program Files\\Posware\\Winpos\\Drv32";
   				elseif (POSType == "05") then
   					InstallPath = "C:\\Grocery";
   				elseif (POSType == "06") then
   					InstallPath = "C:\\OF";
				elseif (POSType == "07") then // 825.0
   					InstallPath = "C:\\Program Files\\MicroTrax\\OpenEPS"; // 825.0
   				endif;        			
			elseif((svParamPrefix = "-h") || (svParamPrefix = "/h")) then // Host
				StrSub(WinEPSIP, svString, 2, StrLength(svString));
				/*if (WinEPSIP == "") then 
					WinEPSIP = DEFAULT_WINEPSIP;
				endif;*/				
			elseif((svParamPrefix = "-p") || (svParamPrefix = "/p")) then // Port
				StrSub(WinEPSPort, svString, 2, StrLength(svString));
				if (WinEPSPort == "") then 
					WinEPSPort = DEFAULT_WINEPSPORT;
				endif;				
			elseif((svParamPrefix = "-c") || (svParamPrefix = "/c")) then // Company Number // YHJ-500 <
				StrSub(CompanyNumber, svString, 2, StrLength(svString));
				IsNumeric(CompanyNumber, bNum);
				if (CompanyNumber == "") || !bNum then 
					CompanyNumber = "-1";
				endif;
				StrToNum (nResult, CompanyNumber);	
				nCompanyNumber = nResult;
			elseif((svParamPrefix = "-o") || (svParamPrefix = "/o")) then // Store Number
				StrSub(StoreNumber, svString, 2, StrLength(svString));
				IsNumeric(StoreNumber, bNum);
				if (StoreNumber == "") || !bNum then 
					StoreNumber = "-1";
				endif; 
				StrToNum (nResult, StoreNumber);
				nStoreNumber = nResult; // YHJ-500 >
			endif;
			nListResult = ListGetNextString(paramsID, svString); 
		endwhile; 
		ListDestroy( paramsID );
        /*
		if (WinEPSIP == "") then 
			WinEPSIP = DEFAULT_WINEPSIP;
		endif;*/
		
		if (WinEPSPort == "") then 
			WinEPSPort = DEFAULT_WINEPSPORT;
		endif;

		AddToLog("POS Type: " + POSType);
		AddToLog("mtx_pos.dll Install Path: " + InstallPath);
		AddToLog("mtx_eps.dll Install Path: " + DEFAULT_OPENEPS_FOLDER);
		//AddToLog("WinEPS IP: " + WinEPSIP);
		//AddToLog("WinEPS Port: " + WinEPSPort);				
		
		if (ExistsDir (InstallPath) != EXISTS) then
        	if (CreateDir (InstallPath) < 0) then
	            AddToLog ("Unable to create directory - " + InstallPath);
            	abort;
            else
            	AddToLog ("Created successfully - " + InstallPath);
            endif;
        endif;         
	    
	    FeatureSetTarget ( MEDIA , "<MtxPosDir>" , InstallPath );
	    TARGETDIR = DEFAULT_OPENEPS_FOLDER;	    	    
		//TARGETDIR = InstallPath; // path for eps dll
	goto End_OnFirstUIBefore:
	
/* *********************************************/
/* NORMAL MODE
/* *********************************************/

InitVars: // { YHJ-525 }
    svSelectedPOS = "";
    svSelectedDir = "";
    sPOSInstallDir = ""; // 825.0
    sOpenEPSInstallDir = ""; // 825.0
    nCompanyNumber = 0;
    nStoreNumber = 0;
    CompanyNumber = "";
    StoreNumber = "";
    WinEPSIP = "";
    WinEPSPort = "";            
    bMTXPOSInstallDirSelected = FALSE;
    
CheckCpInstall: // { YHJ-525 }
	Enable(BACKBUTTON);
	if bCustomConfig then
		bCustomConfig = FALSE; 
		goto Dlg_SdLicense;
	endif;
	svIniFileName = CUSTOM_CONFIG_PATH^CUSTOM_CONFIG_FILENAME;
	if (FindFile (CUSTOM_CONFIG_PATH, CUSTOM_CONFIG_FILENAME, svResult) != 0) then
		goto Dlg_CompanyNumber:
	else
		bCustomConfig = TRUE;
		AddToLog("<CUSTOM CONFIG> Found " + svIniFileName);
	endif;
	
	// POS
	//svSelectedPOS = "Retalix StoreNext ISS45";
	//svSelectedDir = "C:\\Program Files\\Posware\\Winpos\\Drv32";
	
	// Company Number
	GetProfString (svIniFileName, "General", "Chain", svResult);
	if (svResult != "") then	
		IsNumeric(svResult, bNum);
		if bNum then
			StrToNum (nCompanyNumber, svResult);
			nCompanyNumber = nCompanyNumber + 100000; 
			NumToStr(svValue, nCompanyNumber);
			CompanyNumber = svValue;  		  	
		else
			AddToLog ("<CUSTOM CONFIG> Company Number should be numeric value. - Company Number=" + svResult);
		endif;
	else
		AddToLog("<CUSTOM CONFIG> Fail to get Chain from " + svIniFileName);
	endif;
	
// CUSTOM CONFIG FUNCTION
// Company, Store Number
Custom_SelectStore:
    listStore = ListCreate(STRINGLIST);
    listStoreNum = ListCreate(STRINGLIST);
    listDisplay = ListCreate(STRINGLIST);
	
	nIndex = 1;
	nStoreCnt = 0;
	while (nIndex != -1)
		NumToStr(sIndex, nIndex);
		GetProfString (svIniFileName, "General" + sIndex, "Branch", svStoreNum);
		if (svStoreNum != "") then
			IsNumeric(svStoreNum, bNum);
			if bNum then
				ListAddString(listStoreNum, svStoreNum, AFTER);
    			GetProfString (svIniFileName, "General" + sIndex, "Desc", svStore);
    			//ListAddString(listStore, svStore, AFTER);
    			ListAddString(listStore, sIndex, AFTER);
    			ListAddString(listDisplay, "[Store " + svStoreNum + "] " + svStore, AFTER);
    			nStoreCnt = nStoreCnt + 1;
			else
				AddToLog("<CUSTOM CONFIG> (" +  sIndex + ") General - Branch is not a numeric value.");
			endif;
			nIndex = nIndex + 1;
		else
			AddToLog("<CUSTOM CONFIG> (" +  sIndex + ") General - Branch has no value.");
			nIndex = -1;
		endif;
	endwhile;   
    
    ListAddString(listStore, "Not On List", AFTER);
    ListAddString(listStoreNum, "", AFTER);
    ListAddString(listDisplay, "Not On List", AFTER);

    svSelectedStore = "";
    svSelectedStoreNum = "";

    if nStoreCnt > 0 then
		nResult = SdSelectStore (listDisplay, listStore, listStoreNum, svSelectedStore, svSelectedStoreNum);
	 	if (nResult = BACK) then
			ListDestroy(listStore);
  	 		ListDestroy(listStoreNum);
			ListDestroy(listDisplay);
			goto Dlg_SdLicense;
    	endif;
		if (svSelectedStoreNum != "") then
			StoreNumber = svSelectedStoreNum;
			StrToNum (nStoreNumber, StoreNumber);
		else
			AddToLog("<CUSTOM CONFIG> 'Not On List' selected.");
		endif;
	endif;

    ListDestroy(listStore);
    ListDestroy(listStoreNum);
	ListDestroy(listDisplay);
	
 	// Drive, ProxyIP, ProxyPort
 	if StoreNumber != "" then
 		GetProfString (svIniFileName, "MTX" + svSelectedStore, "Drive", svResult); 		
 		if (svResult != "") then
 			if ((svResult >= "c") && (svResult <= "z")) || ((svResult >= "C") && (svResult <= "Z")) then
 				TARGETDIR = svResult + ":" ^ "Program Files" ^ "MicroTrax" ^ "OpenEPS";
 			else
 				AddToLog("<CUSTOM CONFIG> Invalid Drive Letter - " + svResult);	
 			endif;
 		else
 			//ExistsDir ( szPath );
 			AddToLog("<CUSTOM CONFIG> Failed to read MTX - Drive.");
 		endif;

 		GetProfString (svIniFileName, "MTX" + svSelectedStore, "ProxyIP", svResult);
 		WinEPSIP = svResult;
 		GetProfString (svIniFileName, "MTX" + svSelectedStore, "ProxyPort", svResult);
 		WinEPSPort = svResult;
		GetProfString (svIniFileName, "MTX" + svSelectedStore, "POS", svResult);
		if svResult == "SN" then
			svSelectedPOS = "Retalix StoreNext ISS45";
			svSelectedDir = "\\Program Files\\Posware\\Winpos\\Drv32";
		endif;
		if svResult == "SM" then
			svSelectedPOS = "StoreNext ScanMaster";
			svSelectedDir = "\\Grocery";
		endif;
		if svResult == "SL" then
			svSelectedPOS = "Retalix StoreLine";
			svSelectedDir = "\\Program Files\\Storeline\\Winpos\\Drv32";
		endif;
		AddToLog("<CUSTOM CONFIG> SelectedPOS = " + svSelectedPOS);
		AddToLog("<CUSTOM CONFIG> SelectedDir = " + svSelectedDir);
		if (svSelectedDir !=  "") then 
        	//FeatureSetTarget ( MEDIA , "<MtxPosDir>" , svSelectedDir ); /// 825.0
        	//bMTXPOSTypeSelected = TRUE; /// 825.0
        	sPOSInstallDir = svSelectedDir; // 825.0
        endif;
 	endif;
 	AddToLog("After Custom_SelectStore");

Dlg_CompanyNumber: // if no company, store number it's not custom config
	if bCustomConfig && (nCompanyNumber != 0) && (nStoreNumber != 0) goto DlgSelectPOS;

    nCompanyNumber = 0;
    nStoreNumber = 0;
    CompanyNumber = "";
    StoreNumber = "";
    szTitle  = "ServerEPS Configurations"; 
    szMsg    = "Please enter Company and Store Number (Numeric values only)";
    szField1 = "Company Number:";
    szField2 = "Store Number:"; // YHJ-500 >
   
    nResult  = SdShowDlgEdit2( szTitle, szMsg, szField1, szField2, CompanyNumber, StoreNumber ); // YHJ-500
    if (nResult = BACK) goto Dlg_SdLicense;
    
    if (CompanyNumber == "") || (StoreNumber == "") then // YHJ-500 <
    	MessageBox ("Company Number and Store Number are required.", SEVERE);
		goto Dlg_CompanyNumber;
	endif;
	IsNumeric(CompanyNumber, bNum);
	if !bNum then
    	MessageBox ("Company Number should be numeric value.", SEVERE);
		goto Dlg_CompanyNumber;
	endif;
	IsNumeric(StoreNumber, bNum);
	if !bNum then
    	MessageBox ("Store Number should be numeric value.", SEVERE);
		goto Dlg_CompanyNumber;
	endif;	
	
	StrToNum (nResult, CompanyNumber);
	nCompanyNumber = nResult;
	StrToNum (nResult, StoreNumber);
	nStoreNumber = nResult;
	if (nCompanyNumber = 0) || (nStoreNumber = 0) then
		MessageBox ("Company Number and Store Number should be greater than 0.", SEVERE);
		goto Dlg_CompanyNumber;
	endif; // YHJ-500 >
	AddToLog("After Dlg_CompanyNumber");
	AddToLog("    Company Number = " + CompanyNumber);
	AddToLog("    Store Number = " + StoreNumber);

DlgSelectPOS:
	//if bCustomConfig && bMTXPOSTypeSelected goto Dlg_SelectDrive; /// 825.0
	if bCustomConfig && (sPOSInstallDir != "") goto Dlg_SelectDrive; // 825.0	
        
    listPOS = ListCreate(STRINGLIST);
    listDir = ListCreate(STRINGLIST);
    listDisplay = ListCreate(STRINGLIST);
    
    ListAddString(listPOS, "01. JPMA", AFTER);
    ListAddString(listDir, "\\Program Files\\MicroTrax\\OpenEPS", AFTER);
    ListAddString(listPOS, "02. NCR ACS", AFTER);
    ListAddString(listDir, "\\ACS\\Bin", AFTER);    
    ListAddString(listPOS, "03. Retalix StoreLine", AFTER);
    ListAddString(listDir, "\\Program Files\\Storeline\\Winpos\\Drv32", AFTER);
    ListAddString(listPOS, "04. Retalix StoreNext ISS45", AFTER);
    ListAddString(listDir, "\\Program Files\\Posware\\Winpos\\Drv32", AFTER);
    ListAddString(listPOS, "05. Storenext ScanMaster version 2.03.01", AFTER);
    ListAddString(listDir, "\\Grocery", AFTER);
    ListAddString(listPOS, "06. Systech Openfield", AFTER);    
    ListAddString(listDir, "\\OF", AFTER);
    ListAddString(listPOS, "07. Virtual Terminal", AFTER);
    ListAddString(listDir, "\\Program Files\\MicroTrax\\OpenEPS", AFTER);    
/*
    ListAddString(listPOS, "ACR", AFTER);
    ListAddString(listDir, DEFAULT_OPENEPS_FOLDER, AFTER);
    ListAddString(listPOS, "AutoStar", AFTER);
    ListAddString(listDir, DEFAULT_OPENEPS_FOLDER, AFTER);
    ListAddString(listPOS, "Casio", AFTER);
    ListAddString(listDir, DEFAULT_OPENEPS_FOLDER, AFTER);
    ListAddString(listPOS, "Easy Scan", AFTER);
    ListAddString(listDir, DEFAULT_OPENEPS_FOLDER, AFTER);
    ListAddString(listPOS, "IBM SA", AFTER);
    ListAddString(listDir, DEFAULT_OPENEPS_FOLDER, AFTER);
    ListAddString(listPOS, "IBM/PSI Self Checkout", AFTER);
    ListAddString(listDir, DEFAULT_OPENEPS_FOLDER, AFTER);
    ListAddString(listPOS, "Innovax Aurora", AFTER);
    ListAddString(listDir, DEFAULT_OPENEPS_FOLDER, AFTER);
    ListAddString(listPOS, "Innovax Safari", AFTER);
    ListAddString(listDir, DEFAULT_OPENEPS_FOLDER, AFTER);
    ListAddString(listPOS, "JDA WinDSS", AFTER);
    ListAddString(listDir, DEFAULT_OPENEPS_FOLDER, AFTER);
    //ListAddString(listPOS, "MTXEPS Virtual Terminal", AFTER);    
    ListAddString(listPOS, "NCR FastLane", AFTER);
    ListAddString(listDir, DEFAULT_OPENEPS_FOLDER, AFTER);
    ListAddString(listPOS, "Petco", AFTER);
    ListAddString(listDir, "C:\\Windss", AFTER);
    ListAddString(listPOS, "RORC", AFTER);
    ListAddString(listDir, DEFAULT_OPENEPS_FOLDER, AFTER);    
    ListAddString(listPOS, "U-Scan Self Checkout", AFTER);
    ListAddString(listDir, DEFAULT_OPENEPS_FOLDER, AFTER);
*/
    nListResult = ListGetFirstString(listPOS, svPOS);
    ListGetFirstString(listDir, svDir);
    while (nListResult != END_OF_LIST)
      ListAddString(listDisplay, svPOS + "  [ " + svDir + " ]", AFTER);            
      nListResult = ListGetNextString(listPOS, svPOS);
      ListGetNextString(listDir, svDir);      
    endwhile;
    
    ListAddString(listPOS, "99. Others", AFTER);
    ListAddString(listDir, "", AFTER);
    ListAddString(listDisplay, "99. Others", AFTER);    
   
    svSelectedPOS = "";
    svSelectedDir = "";    
	nResult = SdSelectPOS (listDisplay, listPOS, listDir, svSelectedPOS, svSelectedDir);
 	if (nResult = BACK) then
		ListDestroy(listPOS);
   		ListDestroy(listDir);
		ListDestroy(listDisplay);
		if bCustomConfig goto Dlg_SdLicense;
		goto Dlg_CompanyNumber;
    endif;
    
    ListDestroy(listPOS);
    ListDestroy(listDir);
	ListDestroy(listDisplay);
	
	AddToLog("After DlgSelectPOS");
    
    if (svSelectedDir == "") then 
    	goto Dlg_SdAskDestPath;
    else
        //FeatureSetTarget ( MEDIA , "<MtxPosDir>" , svSelectedDir ); /// 825.0
        //bMTXPOSTypeSelected = TRUE; /// 825.0
        sPOSInstallDir = svSelectedDir; // 825.0        
        AddToLog("    POS Selected = " + svSelectedPOS);
        AddToLog("    POS Install Dir = " + sPOSInstallDir);
    	goto Dlg_SelectDrive;    	
    endif;  
    
Dlg_SdAskDestPath: // if POS: Others selected
    szTitle = "";
    szMsg   = "";
    szDir = DEFAULT_OPENEPS_FOLDER;
    nResult = SdAskDestPath( szTitle, szMsg, szDir, 0 );
    if (nResult = BACK) goto DlgSelectPOS;
    FeatureSetTarget ( MEDIA , "<MtxPosDir>" , szDir );
    bMTXPOSInstallDirSelected = TRUE;
    AddToLog("After Dlg_SdAskDestPath");
    AddToLog("    POS Installation Dir = " + szDir);
	    
Dlg_SelectDrive:
	//if bCustomConfig  && (TARGETDIR != "") goto Dlg_WinEPSAddr;
    AddToLog("Install Mode: Normal Mode");
	//AddToLog("Dlg_SelectDrive Begin");
    //szTitle  = "Destination drive";
    //szMsg    = "Please select destination drive from folders listed:";
    nResult  = SdSelectDrive( sPOSDrive, sOpenEPSDrive );
    if (nResult = BACK) then
    	if bCustomConfig goto Dlg_SdLicense;
    	goto DlgSelectPOS;
    endif;
	if (sPOSDrive == "") then // 825.0
    	MessageBox ("POS DLL Drive is required.", SEVERE);
		goto Dlg_SelectDrive;
	endif;    
	if (sOpenEPSDrive == "") then // 825.0
    	MessageBox ("OpenEPS Folder is required.", SEVERE);
		goto Dlg_SelectDrive;
	endif;
		
    TARGETDIR = sOpenEPSDrive + ":" ^ "Program Files" ^ "MicroTrax" ^ "OpenEPS";
    if (!bMTXPOSInstallDirSelected) then
    	FeatureSetTarget ( MEDIA , "<MtxPosDir>", sPOSDrive + ":" ^ sPOSInstallDir );
    endif;
    AddToLog("After Dlg_SelectDrive");
    AddToLog("    OpenEPS Dir =" + TARGETDIR);
    AddToLog("    POS DLL Dir=" + sPOSDrive + ":" ^ sPOSInstallDir);
    //MessageBox ("    TARGETDIR=" + TARGETDIR, INFORMATION);
    //MessageBox ("    POSDir=" + sPOSDrive + ":" ^ sPOSInstallDir, INFORMATION);        
/*	
Dlg_WinEPSAddr:
    if (AskYesNo("Will you be installing the ServerEPS Transaction Proxy Client?", YES) != YES) then
    	AddToLog("Choose to use Proxy Client");
    	WinEPSIP    = "";
    	WinEPSPort  = "6201";
    	goto Dlg_ObjDialogs;
    endif;
    
	if bCustomConfig  && (WinEPSIP != "") && (WinEPSPort != "") goto Dlg_ObjDialogs;
	//Disable(BACKBUTTON); 
    szTitle  = "OpenEPS configuration";
    //szMsg    = "Please enter IP address and port number of the WinEPS server";
    szMsg    = "Please enter the IP address and port number of the ServerEPS Transaction Proxy Client.";  // YHJ-500
    szField1 = "IP address:";
    szField2 = "Port:";
    
    WinEPSIP    = "127.0.0.1";
    WinEPSPort  = "6201";
    
    nResult  = SdShowDlgEdit2( szTitle, szMsg, szField1, szField2, WinEPSIP, WinEPSPort ); // YHJ-500
    if (nResult = BACK) then
    	if bCustomConfig goto Dlg_SdLicense;
    	goto Dlg_SelectDrive;
    endif;
    //AddToLog("Dlg_WinEPSAddr End");
    if (WinEPSIP == "") || (WinEPSPort == "") then 
    	MessageBox ("IP Address and Port Number are required.", SEVERE);
		goto Dlg_WinEPSAddr;
	endif;
	AddToLog("After Dlg_WinEPSAddr");
	AddToLog("    WinEPSIP = " + WinEPSIP);
	AddToLog("    WinEPSPort = " + WinEPSPort);

Dlg_ObjDialogs:
    //AddToLog("Obj_Dialogs Begin");
    nResult = ShowObjWizardPages(nResult);
    if (nResult = BACK) goto Dlg_WinEPSAddr;
*/	    
    // setup default status
    SetStatusWindow(0, "");
    Enable(STATUSEX);
    StatusUpdate(ON, 100);  
    //AddToLog("Obj_Dialogs End");
    
End_OnFirstUIBefore:
    return 0; 

	AddToLog("OnFirstUIBefore End.");
end;

//////////////////////////////////////////////////////////////////////////////
//
//  FUNCTION:   OnFirstUIAfter
//
//  EVENT:      FirstUIAfter event is sent after file transfer, when installation 
//              is run for the first time on given machine. In this event handler 
//              installation usually displays UI that will inform end user that
//              installation has been completed successfully.
//
///////////////////////////////////////////////////////////////////////////////
function OnFirstUIAfter()
    NUMBER  nResult, nvFileHandle;
    STRING sRegKey; // YHJ-500 >
    STRING svValue;
    NUMBER nvType, nvSize; // YHJ-500 >        
begin 
	//AddToLog("OnFirstUIAfter Begin.");
    Disable(STATUSEX);
                               
    OpenFileMode(FILE_MODE_APPEND);
    CreateFile(nvFileHandle, TARGETDIR, "setup.txt");
    //WriteLine(nvFileHandle, "CTCPREMOTEADDR1=" + WinEPSIP);
    //WriteLine(nvFileHandle, "CTCPREMOTEPORT1=" + WinEPSPort);   
    WriteLine(nvFileHandle, "USEWEBSERVICE=" + "Y");
    WriteLine(nvFileHandle, "PROXYSERVER=");
    CloseFile(nvFileHandle);

    if (CompanyNumber != "-1") && (StoreNumber != "-1") then // YHJ-500 < 
    	sRegKey = "Software\\MTXEPS\\ServerEPS\\Common";
    	RegDBSetDefaultRoot(HKEY_LOCAL_MACHINE);
  		//if (RegDBGetKeyValueEx(sRegKey, "CompanyNumber", nvType, svValue, nvSize) < 0) then 
  			NumToStr(svValue, nCompanyNumber);
  			RegDBSetKeyValueEx(sRegKey, "CompanyNumber", REGDB_NUMBER, svValue, -1);
  			AddToLog("Registry settings are updated: " + sRegKey+"\\CompanyNumber");
  		//endif;    
  		//if (RegDBGetKeyValueEx(sRegKey, "StoreNumber", nvType, svValue, nvSize) < 0) then 
  			NumToStr(svValue, nStoreNumber);
  			RegDBSetKeyValueEx(sRegKey, "StoreNumber", REGDB_NUMBER, svValue, -1);
  			AddToLog("Registry settings are updated: " + sRegKey+"\\StoreNumber");
  		//endif;
  	endif; // YHJ-500 >
    
    /*
    nResult = AskYesNo("Do you want a shortcut to the Virtual Terminal on the Desktop?", YES);
    
    if (nResult == YES) then
    
    endif;
    */

    // remove uninstallation feature (from the registry)
    RegDBSetDefaultRoot(HKEY_LOCAL_MACHINE);
    RegDBDeleteKey("Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\" + PRODUCT_GUID);
    
    if (SYSINFO.nWinMajor >= 6) && (SYSINFO.nWinMinor >= 1) then // win7, server 2008
		AddToLog("Trying to stop Program Compatibility Assistant Service");      
		if (LaunchAppAndWait( "net", "stop \"Program Compatibility Assistant Service\"", LAAW_OPTION_WAIT|LAAW_OPTION_MINIMIZED) < 0) then 
			AddToLog("ERROR: failed to execute 'net stop \"Program Compatibility Assistant Service\"'");
		else
			AddToLog("'net stop \"Program Compatibility Assistant Service\"' executed successfully");
		endif;
    endif;
    
    // set AppCompatFlags\GUID to 0x77 - Program Compatibility Service will not check this app
    // http://support.microsoft.com/kb/931709
    RegDBSetDefaultRoot(HKEY_CURRENT_USER);
	if (RegDBKeyExist(REG_APP_COMPAT_FLAGS) < 0) then
    	RegDBCreateKey(REG_APP_COMPAT_FLAGS);
    endif;
    if RegDBSetKeyValueEx (REG_APP_COMPAT_FLAGS, PRODUCT_GUID, REGDB_NUMBER, "119", -1) = 0 then
		AddToLog("Set AppCompatFlags successfully - " + "\\HKEY_CURRENT_USER\\" + REG_APP_COMPAT_FLAGS + "\\" + PRODUCT_GUID);
	else
		AddToLog("Failed to set AppCompatFlags - " + "\\HKEY_CURRENT_USER\\" + REG_APP_COMPAT_FLAGS + "\\" + PRODUCT_GUID);
	endif;   

    
    
    ShowObjWizardPages(NEXT); 
    //AddToLog("OnFirstUIAfter End.");
    return 0;
    
end;

function OnMaintUIBefore()
    STRING svResult,szCaption;
    NUMBER nResult;
begin
	   // TO DO:   if you want to enable background, window title, and caption bar title   								
	   // SetTitle( @TITLE_MAIN, 24, WHITE );					
	   // SetTitle( @TITLE_CAPTIONBAR, 0, BACKGROUNDCAPTION );  
	   // SetColor(BACKGROUND,RGB (0, 128, 128));					
	   // Enable( FULLWINDOWMODE );						
	   // Enable( BACKGROUND );							
	
    //Maintenance Mode 
    //AddToLog("OnMainUIBefore Begin."); 
    svResult = SdLoadString(IFX_MAINTUI_MSG);
    szCaption = SdLoadString(IFX_ONMAINTUI_CAPTION);
	   nResult = SprintfBox(MB_OKCANCEL,szCaption,"%s",svResult);
	   if (nResult = IDCANCEL) then
	       exit;
	   elseif(nResult = IDOK) then
	       // setup default status
        SetStatusWindow(0, "");
        Enable(STATUSEX);
        StatusUpdate(ON, 100);

	       //-->Remove all components
	       FeatureRemoveAll();
    endif;
    //AddToLog("OnMainUIBefore End."); 
    return 0;
end;

function OnMaintUIAfter()
begin
    //AddToLog("OnMainUIAfter Begin."); 
    Disable(STATUSEX);                                               

    ShowObjWizardPages(NEXT); 
    //AddToLog("OnMainUIAfter End."); 
end;

function OnMoving()
    STRING szAppPath;
begin 
    //AddToLog("OnMoving Begin."); 
    // Set LOGO Compliance Application Path 
    // TO DO : if your application .exe is in a subfolder of TARGETDIR then add subfolder 
    szAppPath = TARGETDIR;
    RegDBSetItem(REGDB_APPPATH, szAppPath);
    RegDBSetItem(REGDB_APPPATH_DEFAULT, szAppPath ^ @PRODUCT_KEY); 
    //AddToLog("OnMoving End."); 
end;
//---------------------------------------------------------------------------
// OnCheckMediaPassword
//
// Displays a password dialog if the media is password protected.
//
// Note: This event is called for all setups.
//---------------------------------------------------------------------------
function OnCheckMediaPassword()
string szResult, szMsg;
BOOL bValidated;
begin
    //AddToLog("OnCheckMediaPassword Begin."); 
	// Check whether the setup author selected this option.
	if( !SHOW_PASSWORD_DIALOG ) then;
		return ISERR_SUCCESS;
	endif;

	// Check whether the password has been specified previously.
	LogReadCustomString( MEDIA_PASSWORD_KEY, szResult );
	if( FeatureValidate( MEDIA, "", szResult ) == 0 ) then
		return ISERR_SUCCESS;
	endif;

	// "Back" button should be disabled.
	Disable( BACKBUTTON );

	// Attempt to validate the media.
	bValidated = FALSE;

	// Loop until the password is validated.
	while( !bValidated )

		// Prompt for password.
		if( EnterPassword( "", "", szResult ) < ISERR_SUCCESS ) then
			abort; // Failed to display password dialog
		endif;

		// Attempt to validate the media.
		if( FeatureValidate( MEDIA, "", szResult ) == 0 ) then			
			// Store the media password for maintenance mode.
			LogWriteCustomString( MEDIA_PASSWORD_KEY, szResult );
			bValidated = TRUE;
		else
			szMsg = SdLoadString( IDS_IFX_ERROR_INVALID_MEDIA_PASSWORD );
			MessageBox( szMsg, INFORMATION );
		endif;

	endwhile;

	// Enable "Back" button.
	Enable( BACKBUTTON );  
	//AddToLog("OnCheckMediaPassword End."); 

end;
//---------------------------------------------------------------------------
// OnSetUpdateMode
//
// OnSetUpdateMode is called directly by the framework to set the UPDATEMODE
// InstallShield system variable appropriately to control which UI events
// are called by OnShowUI.
//
// Note: This event is called for all setups.
//---------------------------------------------------------------------------
function OnSetUpdateMode()
	number	nIgnore, nMediaFlags, nInstalledVersion, nUpdateVersion, nResult;
	string	szVersion, szIgnore, szMsg;
begin
	//AddToLog("OnSetUpdateMode Begin.");
	 
	UPDATEMODE = FALSE; // Non-update mode by default.

	// Get the media flags.
	MediaGetData( MEDIA, MEDIA_FIELD_MEDIA_FLAGS, nMediaFlags, szIgnore );

	if( ! ( nMediaFlags & MEDIA_FLAG_UPDATEMODE_SUPPORTED ) ) then
		return ISERR_SUCCESS; // Update mode not supported by the setup.
	endif;

	// TODO: If you are updating an application that was installed by a previous
	// version of InstallShield, IFX_INSTALLED_VERSION will be empty, and
	// VERSION_COMPARE_RESULT_NOT_INSTALLED will be returned by
	// VerProductCompareVersions. Change the value of IFX_INSTALLED_VERSION (and
	// IFX_INSTALLED_DISPLAY_VERSION) here based on application specific version
	// information determined by the setup. Only do this if IFX_INSTALLED_VERSION
	// is empty.
	//if ( !StrLengthChars( IFX_INSTALLED_VERSION ) && MAINTENANCE ) then
	//	IFX_INSTALLED_VERSION = "X.XX.XXX";
	//	IFX_INSTALLED_DISPLAY_VERSION = IFX_INSTALLED_VERSION;
	//endif;

	// Verify that the installed version is valid.
	if( !StrLengthChars( IFX_INSTALLED_VERSION ) && MAINTENANCE ) then
		// If this error occurs, IFX_INSTALLED_VERSION needs to be set manually.
		szMsg = SdLoadString( IDS_IFX_ERROR_UPDATE_NO_INSTALLED_VERSION );
		MessageBox( szMsg, SEVERE );
		abort;
	endif;

	// Verify that the product version is valid.
	if( !StrLengthChars( IFX_PRODUCT_VERSION ) ) then
		// If this error occures, IFX_PRODUCT_VERSION was not initialized correctly.
		szMsg = SdLoadString( IDS_IFX_ERROR_UPDATE_NO_PRODUCT_VERSION );
		MessageBox( szMsg, SEVERE );
		abort;
	endif;

	// Do the version comparison.
	nResult = VerProductCompareVersions();

	// Make sure that valid data was returned by VerProductCompareVersions
	if( nResult < ISERR_SUCCESS ) then
		szMsg = SdLoadString( IDS_IFX_ERROR_UPDATE_VERSION_COMPARE_FAILURE );
		MessageBox( szMsg, SEVERE );
		abort;
	endif;

	// Set update mode if this is a differential media or the product is already installed and the versions do not match.
	UPDATEMODE = ( nMediaFlags & MEDIA_FLAG_FORMAT_DIFFERENTIAL || ( MAINTENANCE && ( nResult != VERSION_COMPARE_RESULT_SAME ) ) );
    
    //AddToLog("OnSetUpdateMode End.");
    
end;

function OnBegin()
begin
    DialogSetInfo(DLG_INFO_ALTIMAGE, SUPPORTDIR ^ "MTX.bmp", TRUE);
end;

// --- include script file section ---