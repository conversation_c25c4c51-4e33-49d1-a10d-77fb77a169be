// (c) MTXEPS, Inc. 1988-2008
unit StoreMonitoring;

interface

uses
  SysUtils, XMLDoc, StrUtils, Classes,
  TerminalConstants,
  ConnectivityThread,

{$IFDEF MTXEPSDLL}
  {$DEFINE MTXEPSDLL_OR_TEST_OR_FUEL}
{$ENDIF MTXEPSDLL}

{$IFDEF TEST}
  {$DEFINE MTXEPSDLL_OR_TEST_OR_FUEL}
{$ENDIF TEST}

{$IFDEF FUEL}
  {$DEFINE MTXEPSDLL_OR_TEST_OR_FUEL}
{$ENDIF FUEL}

{$IFDEF MTXEPSDLL_OR_TEST_OR_FUEL}
  DLLTypes,
  {$IFNDEF FUEL}
  SCATConstants, MTX_EPS_IProcs, scat2,
  {$IFNDEF WOLF}
  stcpipL,
  {$ENDIF}
  OEOfflineClass,   // can't have these in fuel, it creates IP thread, not good
  {$ENDIF FUEL}
{$ENDIF MTXEPSDLL_OR_TEST_OR_FUEL}

  ComputerInfo,TrxLog,
  MTX_XMLClasses,
  WinEpsStatusXML,
  StoreConfigurationsXML,
  MTXEncryptionUtils,
  MTX_Constants,MTX_Lib,UWinEPSConfiguration;

{$IFDEF MSWINDOWS}
//procedure GetCountAndAmount(var count, amount: integer; filename: string); // XE: Remove WinEPS - not for OpenEPS
{$ENDIF}

{$IFDEF MTXEPSDLL_OR_TEST_OR_FUEL}
function GetLaneStatusXMLString(Lane: integer = 0): string;  //JTG Dev 5879 allow default so we can specify lane number
function GetAbbreviatedLaneStatusXMLString : string;
{$IFNDEF FUEL}
procedure GetCountAndAmountForLane(var Count,Amount,SigCount,OldestTrxAge,AvgTrxAge: integer; Filename: string; var SeqNums: string);
{$ELSE FUEL}
function GetCountAndAmountFuelEPS(var Count,Amount: integer; Filename: string): integer;  // returns #sigs for ofline?
{$ENDIF FUEL}
//function MatchingFiles(Filename: string; Attr: integer): TStrings; // XE: Remove WinEPS - not for OpenEPS
{$ENDIF MTXEPSDLL_OR_TEST_OR_FUEL}

{$IFDEF TEST}
var
  EngineStatusStr: string;
{$ENDIF TEST}

implementation

uses
  FinalizationLog,
  StringUtils, 
  uKEK3,
  {$IFDEF MSWINDOWS}
  Windows,
  {$ENDIF}
  GeneralUtilities,
  UXMLCommon;

const
  _Lane = 'Lane';
  _Number = 'Number';
  _TermType = 'TermType';
  _OfflineCount = 'OfflineCount';
  _OfflineAmount = 'OfflineAmount';
  _OfflineOldestTrxAge = 'OfflineOldestTrxAge';
  _OfflineAvgTrxAge = 'OfflineAvgTrxAge';

function IsProperExtension(const S: string): boolean;    //JTG Dev 8488 & improve general functionality
const
  EFT = '.eft';
begin
  {$IFDEF FUEL}
  result := SameText('.eps',ExtractFileExt(S));    //JTG Dev 20061
  {$ELSE}
  result := SameText(EFT,ExtractFileExt(S)) and
    ((StrToIntDef(copy(S,4,4),-1) > 0) or (StrToIntDef(copy(S,12,4),-1) > 0));
  {$ENDIF}
end;

function HasProperExtension(const S: string): boolean;    //JTG Dev  20061 for FUEL only
begin
  result := SameText('.eps',ExtractFileExt(S));
end;

{$IFDEF MTXEPSDLL_OR_TEST_OR_FUEL}
function LocalOrNetworkPath: string;  //JTG DOEP-16864
var
  aLane: string;
begin
  // get file path for TOR, offline and seqnum files
  result := '';
  {$IFNDEF FUEL}
  result := MTX_EPS_IProcs.Reg_Lookup(DefaultDir+OpenEPSIni,cALTFILEPATH,true);
  {$ENDIF FUEL}
  if result = '' then
    result := DefaultDir
  else
    begin
    result := IncludeTrailingPathDelimiter(result);
    {$IFDEF FUEL}
    aLane := '01';
    {$ELSE FUEL}
    aLane := MTX_EPS_IProcs.MakeLaneNumber;
    {$ENDIF FUEL}
    //if aLane = '00'
    //  then AltFilePath := ''
    result := result + 'Lane' + aLane; // + PathSeparator;   JTG no need for PathSeparator due to next line
    end;

  if result <> ''
    then result := IncludeTrailingPathDelimiter(result);
  SM(format('LocalOrNetworkPath = %s',[result]));
end;

{$IFNDEF FUEL}
procedure GetCountAndAmountForLane(var Count,Amount,SigCount,OldestTrxAge,AvgTrxAge: integer; Filename: string; var SeqNums: string);
var
  sRec: TSearchRec;
  MyOfflineProcessor: TOfflineProcessor;
  MyAmount,MyCount: integer;
  PathedFilename,S: string;      //JTG DOEP-16864
  LaneNoStr: string;             // CPCLIENTS-4861
begin
  Count := 0;
  Amount := 0;
  MyCount := 0;
  MyAmount := 0;
  SeqNums := '';
  SigCount := 0; // returns a SigCount for OFF* files
  LaneNoStr := IntToStr(StrToIntDef(SignOnSet.LaneNumber,0));     // CPCLIENTS-4861
  LaneNoStr := LaneNoStr.PadLeft(4, '0');                         // CPCLIENTS-4861
  if Filename.Contains('????') then                               // CPCLIENTS-4861
    Filename := ReplaceStr(Filename, '????', LaneNoStr);          // CPCLIENTS-4861
  try
    (* JTG possible new way of getting files... don't do this without further testing though...
    EftFiles := MatchingFiles(trim(DefaultDir + Filename),faAnyFile);
    for i := 0 to EftFiles.Count-1 do
      if IsProperEFT(EftFiles.Strings[i]) then
      begin
        MyOfflineProcessor := TOfflineProcessor.Create;
        try
          MyOfflineProcessor.ValidAmountAndCount(DefaultDir + EftFiles.Strings[i],MyAmount,MyCount);
          result := MyOfflineProcessor.SignatureCount; // 0 for TORs anyway
          inc(Count,MyCount);
          inc(Amount,MyAmount);
        finally
          MyOfflineProcessor.Free;
        end;
      end;
    *)

    PathedFilename := trim(LocalOrNetworkPath + Filename);    //JTG DOEP-16864
    if SysUtils.FindFirst(PathedFilename,faAnyFile,sRec) = 0 then
      try
        repeat
          // The lane has files like offnnnn.eft and tornnnn.eft (where nnnn is the 4 digit lane numer)
          // Make sure that "nnnn" is contained within the filename

          //numstr := AnsiReplaceStr(sRec.Name,EFT,'');   //JTG: LAME: AnsiReplaceStr is case sensitive!!
          //numstr := copy(numstr,(Length(numstr) - 4)+1, 4);
          // if (sRec.Attr and faAnyFile) = sRec.Attr then   //JTG redundant since already specified in FindFirst...
          if IsProperExtension(sRec.Name) then
          begin
            MyOfflineProcessor := TOfflineProcessor.Create;
            try
              PathedFilename := LocalOrNetworkPath + sRec.Name;    //JTG DOEP-16864
              S := MyOfflineProcessor.ValidAmountAndCount(PathedFilename,MyAmount,MyCount,OldestTrxAge,AvgTrxAge,SeqNums);  //JTG DOEP-16864
//              SM(S);
              inc(SigCount,MyOfflineProcessor.SignatureCount); // 0 for TORs anyway   // also, need to INCREMENT result for each file
              inc(Count,MyCount);
              inc(Amount,MyAmount);
              SM(format('GetCountAndAmountForLane File(%s) has %d sigs, Count=%d for Amount(%d), OldestAge=%d, AvgAge=%d',[PathedFilename,MyOfflineProcessor.SignatureCount,MyCount,MyAmount,OldestTrxAge,AvgTrxAge]));
              //SM(format('GetCountAndAmountForLane File(%s) seq nums = [%s]',[PathedFilename,SeqNums]));
            finally
              MyOfflineProcessor.Free;
            end;
          end;
        until SysUtils.FindNext(sRec) <> 0;
        SM(format('GetCountAndAmountForLane TOTAL %d sigs, Count=%d for Amount(%d)',[SigCount,MyCount,MyAmount]));
      finally
        SysUtils.FindClose(sRec);
      end
    else
      SM(format('GetCountAndAmountForLane: file [%s] not found so TOTAL %d sigs, Count=%d for Amount(%d)',[PathedFilename,sigCount,MyCount,MyAmount])); //JTG DOEP-16864
  except on e: exception do
    SM('ERROR in StoreMonitoring.GetCountAndAmountForLane: ' + e.message);
  end;
end;
{$ENDIF FUEL}
{$ENDIF MTXEPSDLL_OR_TEST_OR_FUEL}

function FileToStrNoCRLF(const Filename: string): string; // CRLF become spaces    DEV-13819
var
  f: file of AnsiChar; // char;                 // 828.5
  ch: AnsiChar; // char;                        // 828.5
  PreviousFileMode: integer;
  S: string;
begin
  try
    result := '';
    S := '';
    if fileexists(Filename) then
      begin
      PreviousFileMode := FileMode;
      FileMode := fmOpenRead or fmShareDenyNone;
      S := 'AssignFile FAILED';
      assignfile(f,Filename);
      S := 'Reset FAILED';
      reset(f);
      S := 'Read FAILED';
      try
        while not eof(f) do
          begin
          read(f,ch);
          case ch of
            CR: result := result + ' | ';     // vert bar line separators per Jeff   13 JAN 2010
            LF: ; // nothing
            else result := result + ch;
          end;
          end;
        S := 'CloseFile FAILED';   // if get here, then ALL the reads worked OK
      finally
        closefile(f);
        S := '';
        FileMode := PreviousFileMode;
      end;
      end;
  except on e: exception do
    begin
    SM(format('PROBLEM - %s - in FileToStrNoCRLF in converting [%s] to string[%s] (Error: %s) ',[S,Filename,result,e.message]));
    raise;   // re-raise this so we know what calling routine the error occurred in
    end;
  end;
end;

function KEK3Timestamp: string;
var
  S,Year,Month,Day,Hour,Minute,Second: string;
begin
  S := GetKEK3Timestamp;   // expecting a specific format of YYYYMMDDHHNNSS
  if length(S) >= 14 then       // allow for > 14, in case future formats include fractional seconds or something else
    begin
    Year := copy(S,1,4);
    Month := copy(S,5,2);
    Day := copy(S,7,2);
    Hour := copy(S,9,2);
    Minute := copy(S,11,2);
    Second := copy(S,13,2);
    result := format('%s-%s-%s %s:%s:%s',[Year,Month,Day,Hour,Minute,Second]);
    end
  else
    result := 'INVALID TIMESTAMP';
end;

// AFR, 4/12/2007, DEV-1688
{$IFDEF MTXEPSDLL_OR_TEST_OR_FUEL}
function GetLaneStatusXMLString(Lane: integer = 0): string;
const
  sYN: array[boolean] of string[1] = ('N','Y');
const
{$IFDEF FUEL}
  IsFuelDLL = true;
{$ELSE}
  IsFuelDLL = false;
{$ENDIF}

//  TORFilename: array[boolean] of string[20] = ('tor????.eft','TOR.EPS');
//  OFFFilename: array[boolean] of string[20] = ('off????.eft','ofline01.EPS');

var
//  xml: TXMLWinEpsStatus;
//  xmlLane: IXMLLaneStatusType;
//  xmlModule: IXMLModuleType;
  compinfo: TComputerInfo;
  i,count,amount,SigCount,ErrorCode,iSize,AvgTrxAge,OldestTrxAge: integer;
  Root, N1, N2, N3, Connectivity, Files,OneFile: TXMLParserNode; //JTG DEV-13819 Files declaration
  {$IFDEF MSWINDOWS}
  vGlobalMemoryStatus: TMemoryStatus;
  {$ENDIF}
  vHeapStatus: THeapStatus;
  DLLFullPath,PreviousVersion,CurrentVersion,ErrorStr: string;
  UpdateTimestamp,CashierSignon: TDateTime;
  TimeValueExists: boolean;
  dllName : string;
  tempStr: string;
  emptyStr: string;
const
  MB = 1024*1024;
  _WinEPSStatus = 'WinEPSStatus';
      _Version = 'Version';
      _LastModified = 'LastModified';
  _LocalMachine = 'LocalMachine';
      _UpdateTime = 'UpdateTime';
      _WinEPSService = 'WinEPSService';
          _Dir = 'Dir';
          _Status = 'Status';
      _OpenEpsKEK3 = 'OpenEpsKEK3';
          _KEK3Timestamp = 'Timestamp';
      _Drive = 'Drive';
          _Letter = 'Letter';
          _DriveSize = 'DriveSize';
          _FreeSpace = 'FreeSpace';
      _OSVersion = 'OSVersion';
      _LastDebitBINUpdate = 'LastDebitBINUpdate';
      _IpAddresses = 'IpAddresses';
          _IPAddress = 'IPAddress';
      _Memory = 'Memory';
        _PhysicalTotal = 'PhysicalTotalMB';
        _PhysicalAvail = 'PhysicalAvailMB';
        _TotalPageFile = 'TotalPageFileMB';
        _AvailPageFile = 'AvailPageFileMB';
        _VirtualTotal = 'VirtualTotalMB';
        _VirtualAvail = 'VirtualAvailMB';
        _HeapTotalAddrSpace = 'HeapTotalAddrSpace';
        _HeapTotalUncommitted = 'HeapTotalUncommitted';
        _HeapTotalCommitted = 'HeapTotalCommitted';
        _HeapTotalAllocated = 'HeapTotalAllocated';
        _HeapTotalFree = 'HeapTotalFree';
        _HeapFreeSmall = 'HeapFreeSmall';
        _HeapFreeBig = 'HeapFreeBig';
        _HeapOverhead = 'HeapOverhead';
        _HeapHeapErrorCode = 'HeapHeapErrorCode';
      _Hosts = 'Hosts';
          _Host = 'Host';
              _Name = 'Name';
              //_Status = 'Status';
              _CompleteTransactions = 'CompleteTransactions';
                  _ApprovedCount = 'ApprovedCount';
                  _ApprovedAmount = 'ApprovedAmount';
                  _DeclinedCount = 'DeclinedCount';
              _PendingTransactions = 'PendingTransactions';
              _ProblemTransactions = 'ProblemTransactions';  // CPCLIENTS-19642
                  _TorCount = 'TorCount';
                  _TorAmount = 'TorAmount';
//                  _OfflineCount = 'OfflineCount';
//                  _OfflineAmount = 'OfflineAmount';
                  _SignatureCount = 'SignatureCount';
      _Modules = 'Modules';
          _Module = 'Module';
            _SHA256 = 'SHA256';
            _Size = 'Size';
            _PreviousVersion = 'PreviousVersion';  //JTG 26852
            _UpdateTimeStamp = 'UpdateTimeStamp';  //JTG 26852
            _P2P = 'P2P';             // JTG 25062
      _WhiteList = 'WhiteList';    //68127
        _Present = 'Present';      //68127
        _MD5 = 'MD5';              //68127
              //Name, Version
  _Lanes = 'Lanes';
//      _Lane = 'Lane';
//          _Number = 'Number';
          _LaneType = 'LaneType';
          // UpdateTime
          // Drive, Letter, DriveSize, FreeSpace
          // OSVersion
          // IPAddresses, IPAddress
          _POSApplicationVersion = 'PosApplicationVersion';
          _POS = 'POS';
            _LastCashierSignon = 'LastCashierSignon';
          // PendingTransactions
          _PinPad = 'PinPad';
              //_TermType = 'TermType';
              _ReportedType = 'ReportedType';
              _ApplicationVersion = 'ApplicationVersion';
              _ApplicationVersionLoadDate = 'ApplicationVersionLoadDate';
              _DataVersion = 'DataVersion';
              _OsVersion2 = 'OsVersion';
              _ModelNumber = 'ModelNumber';
              _SerialNumber = 'SerialNumber';
              _AdditionalInfo = 'AdditionalInfo';
              _TermXPIVersion = 'XPIVersion';      //JTG 32744 tag for XPI Version
              _TermKernelVersion = 'KernelVersion';    // TFS-9800
          // Modules
          _ConfigFiles = 'ConfigFiles';
              _TermConfig = 'TermConfig';
              // Version, LastModified
              _CardProcessingProfiles = 'CardProcessingProfiles';
              // Version, LastModified
          _BioStatus = 'BioStatus';
          _LegacyProxyClient = 'LegacyProxyClient';    //Dev 24329
            _HasConnected = 'HasConnected';            //Dev 24329
          _ConnectivityTest = 'ConnectivityTest';
          _TimeStamp = 'TimeStamp';
          _Files = 'Files';                    //DEV-13819
            _File = 'File';                    //DEV-13819
              _Contents = 'Contents';          //DEV-13819
begin
  try
    {$IFDEF MSWINDOWS}
    GlobalMemoryStatus(vGlobalMemoryStatus);  // JTG 25316...
    {$WARN SYMBOL_PLATFORM OFF}
    vHeapStatus := GetHeapStatus;             // JTG 25316...
    {$WARN SYMBOL_PLATFORM ON}
    {$ENDIF}
	compinfo := TComputerInfo.Create;
    try
      Root := TXMLParserNode.Create(nil);
      try
        Root.Name := _Lane;
        {$IFNDEF FUEL}
        if Lane = 0   // if the default param is used, then use SignOnSet's LaneNumber
          then Root.Attr.Values[_Number] := IntToStr(StrToIntDef(SignOnSet.LaneNumber,0))
          else Root.Attr.Values[_Number] := IntToStr(Lane);
        {$ELSE FUEL}
         Root.Attr.Values[_Number] := '01';
        {$ENDIF FUEL}
        Root.Attr.Values[_LaneType] := TermTypeDescription[Term_TCPN];
        Root.Attr.Values[_UpdateTime] := FormatDateTime(FORMAT_LASTMODIFIED, Now);
        { Drive }
        if compinfo.letter <> '' then
        begin
          N1 := Root.AddChild(_Drive);
          N1.Attr.Values[_Letter] := compinfo.letter;
          N1.Attr.Values[_DriveSize] := IntToStr(compinfo.capacity);
          N1.Attr.Values[_FreeSpace] := IntToStr(compinfo.FreeSpace);
        end;
        { OpenEpsKEK3}
        N1 := Root.AddChild(_OpenEpsKEK3);
        N1.Attr.Values[_KEK3Timestamp] := KEK3Timestamp;   // function will return a timestamp string

        { OSVersion }
        Root.AddChild(_OSVersion).Text := compinfo.GetOSString;
        { IPAddresses }
        N1 := Root.AddChild(_IPAddresses);
          for i := 0 to compinfo.IP.Count - 1 do
            N1.AddChild(_IPAddress).Text := compinfo.IP.Strings[i];

        N1 := Root.AddChild(_Memory);
        {$IFDEF MSWINDOWS}
        N1.Attr.Values[_PhysicalTotal] := format('%d',[vGlobalMemoryStatus.dwTotalPhys div MB]);
        N1.Attr.Values[_PhysicalAvail] := format('%d',[vGlobalMemoryStatus.dwAvailPhys div MB]);
        N1.Attr.Values[_TotalPageFile] := format('%d',[vGlobalMemoryStatus.dwTotalPageFile div MB]);
        N1.Attr.Values[_AvailPageFile] := format('%d',[vGlobalMemoryStatus.dwAvailPageFile div MB]);
        N1.Attr.Values[_VirtualTotal] := format('%d',[vGlobalMemoryStatus.dwTotalVirtual div MB]);
        N1.Attr.Values[_VirtualAvail] := format('%d',[vGlobalMemoryStatus.dwAvailVirtual div MB]);
        //N1.Attr.Values[_MemoryLoad] := format('%d',[vGlobalMemoryStatus.dwMemoryLoad]);
        N1.Attr.Values[_HeapTotalAddrSpace] := format('%d',[vHeapStatus.TotalAddrSpace]);
        N1.Attr.Values[_HeapTotalUncommitted] := format('%d',[vHeapStatus.TotalUncommitted]);
        N1.Attr.Values[_HeapTotalCommitted] := format('%d',[vHeapStatus.TotalCommitted]);
        N1.Attr.Values[_HeapTotalAllocated] := format('%d',[vHeapStatus.TotalAllocated]);
        N1.Attr.Values[_HeapTotalFree] := format('%d',[vHeapStatus.TotalFree]);
        N1.Attr.Values[_HeapFreeSmall] := format('%d',[vHeapStatus.FreeSmall]);
        N1.Attr.Values[_HeapFreeBig] := format('%d',[vHeapStatus.FreeBig]);
        N1.Attr.Values[_HeapOverhead] := format('%d',[vHeapStatus.Overhead]);
        N1.Attr.Values[_HeapHeapErrorCode] := format('%d',[vHeapStatus.HeapErrorCode]);
        {$ENDIF}

        {$IFNDEF FUEL}
        Root.AddChild(_POSApplicationVersion).Text := trim(POSTVersion); //Dev 8688 JTG remove potential control chars that would kill an XML
        {$ELSE FUEL}
        Root.AddChild(_POSApplicationVersion).Text := 'FUEL';
        {$ENDIF FUEL}
        { PendingTransactions }

        N1 := Root.AddChild(_POS);
        if CashierSignonFromLastUpdateFile('Other',CashierSignon,TimeValueExists,ErrorCode,ErrorStr) then //JTG Dev 26852
          begin
          if TimeValueExists
            then N1.Attr.Values[_LastCashierSignon] := FormatDateTime('yyyy-mm-dd hh:nn:ss',CashierSignon)
            else N1.Attr.Values[_LastCashierSignon] := '';
          end
        else
          SM(format('StoreMonitoring.GetLaneStatusXMLString.CashierSignonFromLastUpdateFile ERROR(%d) = %s',[ErrorCode,ErrorStr]));

        N1 := Root.AddChild(_PendingTransactions);
        {$IFNDEF FUEL}
        GetCountAndAmountForLane(Count,Amount,SigCount,OldestTrxAge,AvgTrxAge,'tor????.eft',emptyStr);
        {$ELSE FUEL}
        GetCountAndAmountFuelEPS(Count,Amount,'TOR.EPS');;
        {$ENDIF FUEL}

        N1.Attr.Values[_TorCount] := IntToStr(Count);
        N1.Attr.Values[_TorAmount] := IntToStr(Amount);

        {$IFNDEF FUEL}
        GetCountAndAmountForLane(Count,Amount,SigCount,OldestTrxAge,AvgTrxAge,'off????.eft',emptyStr);    // CPCLIENTS-19642
        {$ELSE FUEL}
        SigCount := GetCountAndAmountFuelEPS(Count,Amount,'ofline01.EPS');
        {$ENDIF FUEL}

        // N1.Attr.Values[_OfflineCount] := IntToStr(abs(Count-SigCount));  // Dev 13849 supercedes this line
        N1.Attr.Values[_OfflineCount] := IntToStr(abs(Count));    // Dev 13849...  sigcount and count are handled at a lower level now
        N1.Attr.Values[_OfflineAmount] := IntToStr(Amount);
        N1.Attr.Values[_OfflineOldestTrxAge] := IntToStr(OldestTrxAge);
        N1.Attr.Values[_OfflineAvgTrxAge] := IntToStr(AvgTrxAge);
        N1.Attr.Values[_SignatureCount] := IntToStr(SigCount);

        N1 := Root.AddChild(_ProblemTransactions);  // CPCLIENTS-19642
        GetCountAndAmountForLane(Count,Amount,SigCount,OldestTrxAge,AvgTrxAge,'offProblems????.eft',emptyStr);   // CPCLIENTS-19642

        N1.Attr.Values[_OfflineCount] := IntToStr(abs(Count));    // Dev 13849...  sigcount and count are handled at a lower level now
        N1.Attr.Values[_OfflineAmount] := IntToStr(Amount);
        N1.Attr.Values[_OfflineOldestTrxAge] := IntToStr(OldestTrxAge);
        N1.Attr.Values[_OfflineAvgTrxAge] := IntToStr(AvgTrxAge);
        N1.Attr.Values[_SignatureCount] := IntToStr(SigCount);

        { PinPad }
        N1 := Root.AddChild(_PinPad);
          {$IFDEF FUEL}
          N1.Attr.Values[_TermType] := 'NONE';
          N1.Attr.Values[_ReportedType] := 'FUEL LANE'; // JTG 20061
          N1.AddChild(_ApplicationVersion).Text := 'NONE';
          N1.AddChild(_DataVersion).Text := 'NONE';
          N1.AddChild(_OsVersion2).Text := 'NONE';   // and set the XML child accordingly
          N1.AddChild(_ModelNumber).Text := 'NONE';
          N1.AddChild(_SerialNumber).Text := 'NONE';
          N1.AddChild(_AdditionalInfo).Text := '';
          {$ELSE}
          N1.Attr.Values[_TermType] := Trim(DLLTypes.wc.OpenEPSParameters.Terminal + ' ' + DLLTypes.ModelName);  //JTG 13950 //CPCLIENTS-11598 Added model name of specific PinPad family like UPP/Engage/LUXE
          N1.Attr.Values[_ReportedType] := GetTermTypeDesc(DLLTypes.TermType,True); //JTG 13950 // compile
          if TermAppVer <> ''
            then N1.AddChild(_ApplicationVersion).Text := TermAppVer   //JTG 32744
            else N1.AddChild(_ApplicationVersion).Text := scat2.SCATVersion;
          if TermDataVersion <> ''
            then N1.AddChild(_DataVersion).Text := TermDataVersion     //JTG 32744
            else N1.AddChild(_DataVersion).Text := scat2.ParmVersion;

          tempStr := ReadLastUpdateFile(SignOnSet.PinPadSerialNumber,_LastUpdateApplicationVersionLoadDate);
          N1.AddChild(_ApplicationVersionLoadDate).Text := tempStr; // ok to be blank if not found in ini file

          SM(format('StoreMonitoring.GetLaneStatusXMLString TermType[%s] TermGUI(ModelNumber) [%s] TermSerialNumber[%s] TermXPIVersion[%s] TermPackages (Addl Info) [%s]  TermDataVersion[%s]',
             [DLLTypes.TermType,TermGUI,TermSerialNumber,TermXPIVersion,TermPackages,TermDataVersion]));
          if length(TermOS) > 0                  // if we have a TermOS from the MX, then fix PinPadOS and use that
            then SignOnSet.PinPadOS := TermOS;   // PinPadOS now easily handles even the longest MX OS strings
          N1.AddChild(_OsVersion2).Text := SignOnSet.PinPadOS;   // and set the XML child accordingly

          //former logic...
          //if (length(TermOS) > 20) and (copy(TermOS,1,20) = SignOnSet.PinPadOS) // TermOS only set for MX terminals
          //  then N1.AddChild(_OsVersion2).Text := TermOS                        // but PinPadOS only stores 20 chars, so use other var in this case
          //  else N1.AddChild(_OsVersion2).Text := SignOnSet.PinPadOS;           // Dev 14540

          N1.AddChild(_ModelNumber).Text := TermGUI;                            // Dev 14540
          if TermSerialNumber <> '' then
            begin
            SignOnSet.PinPadSerialNumber := TermSerialNumber;
            LastKnownSN := SignOnSet.PinPadSerialNumber;
            SM(format('StoreMonitoring.GetLaneStatusXMLString PinPadSerialNumber[%s]',[SignOnSet.PinPadSerialNumber]));
            end
          else if trim(SignOnSet.PinPadSerialNumber) = '' then
            begin
            SM(format('StoreMonitoring.GetLaneStatusXMLString PinPadSerialNumber is blank, using LastKnownSN[%s]',[LastKnownSN]));
            SignOnSet.PinPadSerialNumber := LastKnownSN;
            end
          else
            begin
            LastKnownSN := SignOnSet.PinPadSerialNumber;
            SM(format('StoreMonitoring.GetLaneStatusXMLString PinPadSerialNumber[%s] SETTING LastKnownSN[%s]',[SignOnSet.PinPadSerialNumber,LastKnownSN]));
            end;
          N1.AddChild(_SerialNumber).Text := SignOnSet.PinPadSerialNumber;
          if (TermXPIVersion <> '') or (TermKernelVersion <> '') then         //JTG 32744  // TFS-9800
            begin
            if (TermXPIVersion <> '') then                                                 // TFS-9800
              N1.AddChild(_TermXPIVersion).Text := TermXPIVersion;            //JTG 32744
            if (TermKernelVersion <> '') then                                              // TFS-9800
              N1.AddChild(_TermKernelVersion).Text := TermKernelVersion;                   // TFS-9800
            N1.AddChild(_AdditionalInfo).Text := TermPackages;                //JTG 32744
            end
          else
            N1.AddChild(_AdditionalInfo).Text := SignOnSet.AdditionalInfo;
          {$ENDIF}
          {
          N1.AddChild(_OSVersion);
          N1.AddChild(_ModelNumber);
          N1.AddChild(_SerialNumber);
          }
        { Modules }
        N1 := Root.AddChild(_Modules);
        {$IFNDEF FUEL}
        N2 := N1.AddChild(_Module);
        // DOEP-50096 - OpenEPS.net: Report the openeps.net dll in the lane status to Server
        dllName := Format('OpenEpsNet%d.dll', [StrToIntDef(SignOnSet.LaneNumber,0)]);
        if not FileExists(DefaultDir + dllName) then
          dllName := MTXPOSDLL;

        // DOEP-68109, 68117 - Remove the OEN Lane numbers from the ServerEPS status message  
        if dllName = MTXPOSDLL then
        begin
          N2.Attr.Values[_Name] := dllName;
          N2.Attr.Values[_Version] := MTXPOSDLLVersionNo
        end
        else
        begin
          N2.Attr.Values[_Name] := 'OpenEpsNet.dll';
          N2.Attr.Values[_Version] := OENDLLVersionNo;
        end;

        DLLFullPath := DLLPath(dllName)+dllName;                          // JTG Doep-25063 .. look for DLLs in multiple places
        {$IFNDEF WOLF}
        N2.Attr.Values[_SHA256] := MTXEncryptionUtils.SBHashSHA256(DLLFullPath);                 // JTG Doep-25063 .. look for DLLs in multiple places
        {$ENDIF}
        iSize := FileSizeInBytes(DLLFullPath);                               // JTG Doep-25063 .. look for DLLs in multiple places
        N2.Attr.Values[_Size] := IntToStr(iSize);

        if GetFileInfoFromLastUpdateFile(MTXPOSDLL,PreviousVersion,CurrentVersion,UpdateTimestamp,TimeValueExists,ErrorCode,ErrorStr) then//JTG Dev 26852
          begin
          N2.Attr.Values[_PreviousVersion] := PreviousVersion;
          //N2.Attr.Values[_CurrentVersion] := CurrentVersion;   //already lists the version
          if TimeValueExists
            then N2.Attr.Values[_UpdateTimeStamp] := FormatDateTime('yyyy-mm-dd hh:nn:ss',UpdateTimestamp)
            else N2.Attr.Values[_UpdateTimeStamp] := '';
          end
        else
          SM(format('StoreMonitoring.GetLaneStatusXMLString.GetFileInfoFromLastUpdateFile for %s ERROR(%d) = %s',[MTXPOSDLL,ErrorCode,ErrorStr]));

        //N2.Attr.Values[_SHA256] := SBHashSHA256(MTXPOSDLL);      //SB Method
        //N2.Attr.Values[_SHA256] := HashSHA(DefaultDir + MTXPOSDLL);    //LB Method
        //N2.Attr.Values[_Size] := IntToStr(FileSizeInBytes(DefaultDir + MTXPOSDLL));  // DefaultDir doesn't work for ISS45
        if iSize = 0 then
          N2.Attr.Values[_Size] := IntToStr(FileSizeInBytes(MTXPOSDLL));
        N2 := N1.AddChild(_Module);
        // DOEP-48301 - OpenEPS.net - Need to report mtx_eps.dll version to ServerEPS
        dllName := Format('MTX_EPS%d.dll', [StrToIntDef(SignOnSet.LaneNumber,0)]);
        if not FileExists(DefaultDir + dllName) then
          dllName := MTXEPSDLL;
        //N2.Attr.Values[_SHA256] := SBHashSHA256(DefaultDir + MTXEPSDLL);
        //N2.Attr.Values[_Size] := IntToStr(FileSizeInBytes(DefaultDir + MTXEPSDLL)); // DefaultDir works for everyone here
        {$IFDEF LINUX}
        DLLFullPath := DLLPath(MTXEPSDLL)+ MTXEPSDLL;                    // JTG Doep-25063 .. look for DLLs in multiple places
        {$ELSE} // linux doesn't support OpenEPS.NET
        DLLFullPath := DLLPath(dllName)+ dllName;
        {$ENDIF}
        if trim(MTXEPSDLLVersionNo) = '' then                               //21650 21684
          begin                                                             //21650 21684
          MTXEPSDLLVersionNo := GetVersionInfo(DLLFullPath, 'FileVersion'); //21650 21684
          SM(format('StoreMonitoring.GetLaneStatusXMLString Getting Version[%s] from [%s]',[MTXEPSDLLVersionNo,DLLFullPath]));  //21649
          end;                                                              //21650 21684
        N2.Attr.Values[_Name] := MTXEPSDLL; // DOEP-68109, 68117
        N2.Attr.Values[_Version] := MTXEPSDLLVersionNo;
        {$IFNDEF WOLF}
        N2.Attr.Values[_SHA256] := SBHashSHA256(DLLFullPath);            //JTG Doep-25063 .. look for DLLs in multiple places
        {$ENDIF}
        N2.Attr.Values[_Size] := IntToStr(FileSizeInBytes(DLLFullPath)); //JTG Doep-25063 .. look for DLLs in multiple places
        if GetFileInfoFromLastUpdateFile(MTXEPSDLL,PreviousVersion,CurrentVersion,UpdateTimestamp,TimeValueExists,ErrorCode,ErrorStr) then//JTG Dev 26852
          begin
          N2.Attr.Values[_PreviousVersion] := PreviousVersion;
          //N2.Attr.Values[_CurrentVersion] := CurrentVersion;   //already lists the version
          if TimeValueExists
            then N2.Attr.Values[_UpdateTimeStamp] := FormatDateTime('yyyy-mm-dd hh:nn:ss',UpdateTimestamp)
            else N2.Attr.Values[_UpdateTimeStamp] := '';
          end
        else
          SM(format('StoreMonitoring.GetLaneStatusXMLString.GetFileInfoFromLastUpdateFile for %s ERROR(%d) = %s',[MTXEPSDLL,ErrorCode,ErrorStr]));

        N3 := N2.AddChild(_P2P);
        N3.Text := sYN[P2PEncryption];

        N3 := N2.AddChild(_WhiteList);                             //68127
        N3.Attr.Values[_Present] := sYN[MD5ofP2PWhiteList <> ''];  //68127
        if MD5ofP2PWhiteList <> '' then
          N3.Attr.Values[_MD5] := CleanMD5(MD5ofP2PWhiteList);       //68127

        {$ENDIF FUEL}  //IF NOT FUEL

        if IsFuelDLL then
          begin
          N2 := N1.AddChild(_Module);
          N2.Attr.Values[_Name] := MTXFUELDLL;
          N2.Attr.Values[_Version] := GetVersionInfo(DefaultDir + MTXFUELDLL,'FileVersion');
          N2 := N1.AddChild(_Module);
          N2.Attr.Values[_Name] := MXTFUELSRV;
          N2.Attr.Values[_Version] := GetVersionInfo(DefaultDir + MXTFUELSRV,'FileVersion');
          N2 := N1.AddChild(_Module);       //JTG add SE DLL version per Jason 3/2/2009
          N2.Attr.Values[_Name] := MTXSEDLL;
          N2.Attr.Values[_Version] := GetVersionInfo(DefaultDir + MTXSEDLL,'FileVersion');
          {$IFNDEF WOLF}
          N2.Attr.Values[_SHA256] := SBHashSHA256(MTXSEDLL);
          {$ENDIF}
          N2.Attr.Values[_Size] := IntToStr(FileSizeInBytes(DefaultDir + MTXSEDLL));
          end
        else
          begin
          // Support OEN
//          dllName := Format('MTX_SE%d.dll',[StrToIntDef(SignOnSet.LaneNumber,0)]);
//          if not FileExists(DefaultDir + dllName) then
//            dllName := MTXSEDLL;
//          N2 := N1.AddChild(_Module);       //JTG add SE DLL version per Jason 3/2/2009
//          N2.Attr.Values[_Name] := MTXSEDLL; // DOEP-68109, 68117
//          N2.Attr.Values[_Version] := MTXSEDLLVersionNo;
//          //N2.Attr.Values[_SHA256] := SBHashSHA256(DefaultDir + MTXSEDLL);
//          //N2.Attr.Values[_Size] := IntToStr(FileSizeInBytes(DefaultDir + MTXSEDLL));
//          {$IFDEF LINUX}
//          DLLFullPath := DLLPath(MTXSEDLL)+MTXSEDLL;   //JTG Doep-25063 .. look for DLLs in multiple places
//          {$ELSE} // linux doesn't support OpenEPS.NET
//          DLLFullPath := DLLPath(dllName)+dllName;
//          {$ENDIF}
//          N2.Attr.Values[_SHA256] := {$IFDEF LINUX} 'Not Used in Linux' {$ELSE} SBHashSHA256(DLLFullPath) {$ENDIF};  //JTG Doep-25063 .. look for DLLs in multiple places
//          N2.Attr.Values[_Size] := {$IFDEF LINUX} '0' {$ELSE} IntToStr(FileSizeInBytes(DLLFullPath)) {$ENDIF};       //JTG Doep-25063 .. look for DLLs in multiple places
//          if GetFileInfoFromLastUpdateFile(MTXSEDLL,PreviousVersion,CurrentVersion,UpdateTimestamp,TimeValueExists,ErrorCode,ErrorStr) then //JTG Dev 26852
//            begin
//            N2.Attr.Values[_PreviousVersion] := PreviousVersion;
//            //N2.Attr.Values[_CurrentVersion] := CurrentVersion;
//            if TimeValueExists
//              then N2.Attr.Values[_UpdateTimeStamp] := FormatDateTime('yyyy-mm-dd hh:nn:ss',UpdateTimestamp)
//              else N2.Attr.Values[_UpdateTimeStamp] := '';
//            end
//          else
//            SM(format('StoreMonitoring.GetLaneStatusXMLString.GetFileInfoFromLastUpdateFile for %s ERROR(%d) = %s',[MTXSEDLL,ErrorCode,ErrorStr]));
          end;

        { ConfigFiles }
        N1 := Root.AddChild(_ConfigFiles);
        N2 := N1.AddChild(_TermConfig);
        N2.Attr.Values[_Name] := DLLTypes.ConfigFileName;
        N2.Attr.Values[_Version] := IntToStr(DLLTypes.wc.ParameterVersion);
        //N2.Attr.Values[_LastModified] := DLLTypes.wc.LastModified; // TODO
        N2 := N1.AddChild(_CardProcessingProfiles);
        N2.Attr.Values[_Name] := MTX_Constants.CardProcessingProfilesFileName;
        if Assigned(XMLCardProcessingProfile) then
        begin
          N2.Attr.Values[_Version] := XMLCardProcessingProfile.XMLObj.Version;
          N2.Attr.Values[_LastModified] := XMLCardProcessingProfile.XMLObj.LastModified;
        end;
        { BioStatus }
        {$IFDEF FUEL}
        Root.AddChild(_BioStatus).Text := 'NONE';
        {$ELSE FUEL}
        {$IFNDEF WOLF}
        if StcpipL.Fstcpip = nil
          then Root.AddChild(_BioStatus).Text := GetSocketStatusString(bsNotConnected)
          else Root.AddChild(_BioStatus).Text := GetSocketStatusString(CheckIPServerSocketPendingSocket(Fstcpip));
        N3 := Root.AddChild(_LegacyProxyClient);                           //Dev 24329
        N3.Attr.Values[_HasConnected] := sYN[ConnectivityThread.LegacyProxyClientConnected];  //Dev 24329
        {$ENDIF WOLF}
        {$ENDIF FUEL}

        //Root.AddChild(_ConnectivityTest).Text := ConnectivityThread.ConnectivityTest;
        Connectivity := Root.AddChild(_ConnectivityTest);
        Connectivity.Text := ConnectivityThread.ConnectivityTest;
        Connectivity.Attr.Values[_TimeStamp] := FormatDateTime('yyyy-mm-dd hh:nn:ss',ConnectivityTestTime);

        Files := Root.AddChild(_Files);              //DEV-13819
        OneFile := Files.AddChild(_File);            //DEV-13819
        OneFile.Attr.Values[_Name] := SETUPTXT;      //DEV-13819
        OneFile.AddChild(_Contents).Text := FileToStrNoCRLF(DefaultDir + SETUPTXT);     //DEV-13819

        OneFile := Files.AddChild(_File);                 //DEV-13819
        OneFile.Attr.Values[_Name] := 'openeps.ini';      //DEV-13819
        OneFile.AddChild(_Contents).Text := FileToStrNoCRLF(DefaultDir + 'openeps.ini');   //DEV-13819

        result := ReplaceString(Root.GetXMLStr, CR+LF, ''); { engine doesn't like getting msg with CRLF } 
      finally
        FreeAndNil(Root);
      end;                                                                      
      {
       // working XML
      result := '<Lane Number="1" LaneType="OpenEPS TCP/IP" UpdateTime="2008-01-24 12:55:40"><Drive Letter="C:" DriveSize="46696" FreeSpace="4822"/><OSVersion>5.1|-1|Windows XP</OSVersion>'
        + '<IpAddresses><IPAddress>*************</IPAddress></IpAddresses><PosApplicationVersion>825.0.0.3</PosApplicationVersion><PendingTransactions TorCount="0" TorAmount="0" OfflineCount="0" OfflineAmount="0" SignatureCount="0"/>'
        + '<PinPad TermType="SCAT-L4250"><ApplicationVersion>0000</ApplicationVersion><DataVersion>0000</DataVersion><OsVersion></OsVersion><ModelNumber></ModelNumber><SerialNumber></SerialNumber></PinPad>'
        + '<Modules><Module Name="MTX_POS.dll" Version="824.0.0.21"/><Module Name="MTX_EPS.dll" Version="825.0.0.103"/></Modules>'
        + '<ConfigFiles><TermConfig Name="Virtual Terminal.xml" Version="34"/><CardProcessingProfiles Name="CardProcessingProfile.xml" Version="1.0" LastModified="2008-01-23 13:08:52"/></ConfigFiles><BioStatus>Not Connected</BioStatus></Lane>';
      }
    finally
      if Assigned(compinfo) then FreeAndNil(compinfo); //CPCLIENTS-18308 - To Free an Object
    end;
  except on e: exception do
    SM('EXCEPTION: StoreMonitoring.GetLaneStatusXMLString.  ' + e.message);
  end;
end;
{$ENDIF MTXEPSDLL_OR_TEST}

function GetAbbreviatedLaneStatusXMLString : string;
var Root, N1 : TXMLParserNode;
    offlineTransactionCount, offlineTransactionAmount,
    torTransactionCount, torTransactionAmount, sigCount, AvgTrxAge, OldestTrxAge : integer; // CPCLIENTS-2615
    seqNums: string;
const
//  _Number = 'Number';
//  _Lane = 'Lane';
  _PendingOfflineTransactions = 'PendingOfflineTransactions';
  _ProblemOfflineTransactions = 'ProblemOfflineTransactions';   // CPCLIENTS-19642
  _PendingTORTransactions = 'PendingTORTransactions'; // CPCLIENTS-2615
  _PendingSignatures = 'PendingSignatures'; // CPCLIENTS-2615
  _PendingSequenceNumbers = 'PendingSequenceNumbers';
  sYN: array[boolean] of string[1] = ('N','Y');
begin
  torTransactionCount := 0;
  torTransactionAmount := 0;
  sigCount := 0;
  seqNums := '';
  try
    try
      Root := TXMLParserNode.Create(nil);

      Root.Name := _Lane;
      {$IFNDEF FUEL}
      Root.Attr.Values[_Number] := IntToStr(StrToIntDef(SignOnSet.LaneNumber,0));
      {$ELSE FUEL}
      Root.Attr.Values[_Number] := '01';
      {$ENDIF FUEL}

      {$IFNDEF FUEL}
      GetCountAndAmountForLane(torTransactionCount, torTransactionAmount, sigCount, OldestTrxAge, AvgTrxAge, 'tor????.eft',emptyStr);  //CPCLIENTS-2615
      GetCountAndAmountForLane(offlineTransactionCount, offlineTransactionAmount, sigCount, OldestTrxAge, AvgTrxAge, 'off????.eft', seqNums);
      {$ELSE FUEL}
      GetCountAndAmountFuelEPS(offlineTransactionCount, offlineTransactionAmount, 'ofline01.EPS');
      {$ENDIF FUEL}

      N1 := Root.AddChild(_PendingOfflineTransactions);
      N1.Text :=  sYN[offlineTransactionCount > 0];
      N1 := Root.AddChild(_PendingTORTransactions); //CPCLIENTS-2615
      N1.Text :=  sYN[torTransactionCount > 0]; //CPCLIENTS-2615
      N1 := Root.AddChild(_PendingSignatures); //CPCLIENTS-2615
      N1.Text :=  sYN[sigCount > 0]; //CPCLIENTS-2615

      N1 := Root.AddChild(_OfflineCount); //CPCLIENTS-4112
      N1.Text :=  IntToStr(offlineTransactionCount);
      N1 := Root.AddChild(_OfflineAmount); //CPCLIENTS-4112
      N1.Text :=  IntToStr(offlineTransactionAmount);
      N1 := Root.AddChild(_PendingSequenceNumbers); //CPCLIENTS-4112
      N1.Text := seqNums;

      GetCountAndAmountForLane(offlineTransactionCount, offlineTransactionAmount, sigCount, OldestTrxAge, AvgTrxAge, 'offProblems????.eft', seqNums); // CPCLIENTS-19642

      N1 := Root.AddChild(_ProblemOfflineTransactions);  // CPCLIENTS-19642
      N1.Text :=  sYN[offlineTransactionCount > 0];
      N1 := Root.AddChild(_PendingTORTransactions);
      N1.Text :=  sYN[torTransactionCount > 0];
      N1 := Root.AddChild(_PendingSignatures);
      N1.Text :=  sYN[sigCount > 0];

      N1 := Root.AddChild(_OfflineCount);
      N1.Text :=  IntToStr(offlineTransactionCount);
      N1 := Root.AddChild(_OfflineAmount);
      N1.Text :=  IntToStr(offlineTransactionAmount);
      N1 := Root.AddChild(_PendingSequenceNumbers);
      N1.Text := seqNums;


      N1 := Root.AddChild(_TermType); // CPCLIENTS-3357
      N1.Text :=  GetTermTypeDesc(DLLTypes.TermType); // CPCLIENTS-3357

      result := Root.GetXMLStr;
      MsgDebug('StoreMonitoring.GetAbbreviatedLaneStatusXMLString = ' + result);
    except on e: exception do
      SM('EXCEPTION: StoreMonitoring.GetAbbreviatedLaneStatusXMLString.  ' + e.message);
    end;
  finally
	  FreeAndNil(Root);
  end;
end;

initialization
  ExtendedLog('StoreMonitoring Initialization');
finalization
  ExtendedLog('StoreMonitoring Finalization');

end.



