unit SaturnDeclarations;

interface

uses
  SysUtils,MRTypesAnsiChar;

const
  FIELD_CASHIER_ID = 'CashierID';
  FIELD_PURCHASE_AMOUNT = 'PurchaseAmount';
  FIELD_TRANSACTION_TYPE = 'TransactionType';
  FIELD_TENDER_TYPE = 'TenderType';
  FIELD_AMOUNT_CHANGE_ALLOWED = 'AmountChangeAllowed';
  FIELD_POS_TRANSACTION_NUMBER = 'POSTransactionNumber';
  FIELD_POS_VERSION = 'POSVersion';
  FIELD_POS_TYPE = 'POSType';
  NOSEND = 'NOSEND';

  ERR_TESTFILE_NOT_FOUND = 1;
  ERR_TESTFILE_EMPTY = 2;
  ERR_TESTFILE_SYNTAX_ERROR = 3;
  xTEST: array[boolean] of string[20] = ('FAIL','PASS');
  sTF: array[boolean] of string[5] = ('FALSE','TRUE');
  sOK: array[boolean] of string[6] = ('FAILED','OK');
  sSUCCESS: array[boolean] of string[8] = ('FAIL','SUCCESS');

  STATUS_DELAY = 250; // ms

  ConfigureReadyStr: array[0..2] of string[20] = ('Wait','ContinueSignOn','Done');
  TenderTypeStatusStr: array[0..1] of string[20] = ('Loop','OK');
  ScatReadyStr: array[0..1] of string[20] = ('Loop','Ready');
  UserVarSet: set of char = ['A'..'Z'];

type
  enumCommand = (zUnknown,zSendTransaction,zConfirmTransaction,
     zFolder,zReplace,zLoop,zEndLoop,zSet,zGet,zCheckerSignOn,zCheckerSignOff,zBeginOrder,zEndOrder,
     zExpect,zNotExpect,zWait,zWaitUntil,zExecute,zExecuteAndWait,
     zStore,zIncrement,zDecrement,zDescription,zInitialize,zTimeout,zDelete,zVar,zTransactionComplete,zIncrementPostTransactionNumber,
     zReset,zValidateData,zRun,zTest,zTicketLookup,zTimer,zLoadDLLs,zUnloadDLLs,zComment,zKill,zShowVersion);
  enumSetValue = (svUnknown,svTenderType,svTransactionType,svPAN,svExpiration,svCurrency,svTrxAmount,svCashierID,svLane,
     svSeqNum,svPostTransactionNumber,svCustomerOK,svTenderTypeStatus,svTimeout,svConfigureReady,svScatReady,svPostVersion,
     svOverrideFlag,svChangeAmountAllowed,svTransactionTimeout,svResponseCode,svApprovedAmount,svTenderTypeMTX,svReceiptRequired,
     svCashbackAmount,svManagerID,svPIN,svManagerIDOrSecondaryID,svFeeAmount,svAccountType,svAccountBalance,svFSAAmount,svFSADental,
     svFSAMedical,svFSAVision,svHIPAmount,svRxAmount,svEBTCashBalance,svAuthorizationNumber,svManualEntryTrack2Flag,svCheckType,
     svCompanyNumber,svStoreNumber, // these two do registry settings
     svCheckTransitRoutingNumber,svCheckAccountNumber,svCheckNumber,svHostDeclineMessage,svCustomerDisplay,svCashierDisplay,
     svPurchaseAmount,svTaxAmount,svEBTFoodStampBalance,svTrack1Data,svTrack2Data,svCustomerName,svVoucherNumber,
     svPrimaryIDType,svPrimaryID,svStateCode,svDateOfBirth,svManualEntryMICRFlag,svTrainingTransaction,
     svManualEntryIDFlag,svSecondaryIDType,svSecondaryID,svPhoneNumber,
     svFleetData,svOdometer,svVehicleID,svDriverID,svPOSEnteredRestrictionCode,svProductRestrictionCode,
     svCashierOKCashback,svHostSlotNumber,svUPCCode,svPONumber,svZipCode,svLanguageID,svCVV2,svBioDOB,svBioFreqShop,
     svItemExpDate,svWirelessPIN,svControlNumber,svECCProductCode,svVisaTranID,svSocialSecurityNumber,svPayrollCheckIssueDate,
     svServerInformation,svCheckManualMICRString,svLoyaltyCardFlag,svPrintEBTCashBalance,svPrintEBTFoodStampBalance,
     svHostRetrievalReferenceNumber,svHostReferenceNumber,svMerchantIDNumber,svSVSAuthorizationNumber,svRewardsVoucherRedeemedDate,
     svProductType,svRewardsVoucherNumber,svRewardsKeyCode,svRewardsVoucherIssuer,svDukptKeySerialNumber,svFrequentShopperData,
     svNewPIN,svTransactionDate,svTransactionTime,svMICRFields,sveWicRx,svCustomerFirstName,svCustomerLastName,svRawMICR,
     svExtendedLaneType,svTaxExempt);
  enumExpectValue = (evUnknown,evApprovedAmount,evResponseCode,evCashbackAmount,evTimer);
  enumStatus = (esUnknown,esScatStatus,esScatReady,esHostStatus,esTenderTypeStatus,esConfigureReady);
  enumUserVarSetting = (uvUnknown,uvSeqNum,uvTrxAmount,uvCashbackAmount);
  enumOperator = (eoUnknown,eoLessThan,eoGreaterThan,eoEqual);
  TOpenStringArray = array of AnsiString;


const
  CommandString: array[enumCommand] of string =
    ('Unknown','SendTransaction','ConfirmTransaction',
     'Folder','Replace','Loop','EndLoop','Set','Get','CheckerSignOn','CheckerSignOff','BeginOrder','EndOrder',
     'Expect','NotExpect','Wait','WaitUntil','Execute','ExecuteAndWait',
     'Store','Increment','Decrement','Description','Initialize','Timeout','Delete','Var','TransactionComplete','IncrementPostTransactionNumber',
     'Reset','ValidateData','Run','Test','TicketLookup','Timer','LoadDLLs','UnloadDLLs','Comment','Kill','ShowVersion');
  SetValueString: array[enumSetValue] of string =
    ('Unknown','TenderType','TransactionType','PAN','Expiration','Currency','TrxAmount','CashierID','Lane',
     'SeqNum','PostTransactionNumber','CustomerOK','TenderTypeStatus','Timeout','ConfigureReady','ScatReady','PostVersion',
     'OverrideFlag','ChangeAmountAllowed','TransactionTimeout','ResponseCode','ApprovedAmount','TenderTypeMTX','ReceiptRequired',
     'CashbackAmount','ManagerID','PIN','ManagerIDOrSecondaryID','FeeAmount','AccountType','AccountBalance','FSAAmount','FSADental',
     'FSAMedical','FSAVision','HIPAmount','RxAmount','EBTCashBalance','AuthorizationNumber','ManualEntryTrack2Flag','CheckType',
     'CompanyNumber','StoreNumber',
     'CheckTransitRoutingNumber','CheckAccountNumber','CheckNumber','HostDeclineMessage','CustomerDisplay','CashierDisplay',
     'PurchaseAmount','TaxAmount','EBTFoodStampBalance','Track1Data','Track2Data','CustomerName','VoucherNumber',
     'PrimaryIDType','PrimaryID','StateCode','DateOfBirth','ManualEntryMICRFlag','TrainingTransaction',
     'ManualEntryIDFlag','SecondaryIDType','SecondaryID','PhoneNumber',
     'FleetData','Odometer','VehicleID','DriverID','POSEnteredRestrictionCode','ProductRestrictionCode',
     'CashierOKCashback','HostSlotNumber','UPCCode','PONumber','ZipCode','LanguageID','CVV2','BioDOB','BioFreqShop',
     'ItemExpDate','WirelessPIN','ControlNumber','ECCProductCode','VisaTranID','SocialSecurityNumber','PayrollCheckIssueDate',
     'ServerInformation','CheckManualMICRString','LoyaltyCardFlag','PrintEBTCashBalance','PrintEBTFoodStampBalance',
     'HostRetrievalReferenceNumber','HostReferenceNumber','MerchantIDNumber','SVSAuthorizationNumber','RewardsVoucherRedeemedDate',
     'ProductType','RewardsVoucherNumber','RewardsKeyCode','RewardsVoucherIssuer','DukptKeySerialNumber','FrequentShopperData',
     'NewPIN','TransactionDate','TransactionTime','MICRFields','eWICRx','CustomerFirstName','CustomerLastName','RawMICR',
     'ExtendedLaneType','TaxExempt');
  ExpectValueString: array[enumExpectValue] of string =
    ('Unknown','ApprovedAmount','ResponseCode','CashbackAmount','T');
  StatusString: array[enumStatus] of string =
    ('Unknown','ScatStatus','ScatReady','HostStatus','TenderTypeStatus','ConfigureReady');
  UserVarSettingString: array[enumUserVarSetting] of string =
    ('Unknown','SeqNum','TrxAmount','CashbackAmount');
  MAX_USER_STR_VARS = 26;
  BAD = -1234567;

implementation

end.
