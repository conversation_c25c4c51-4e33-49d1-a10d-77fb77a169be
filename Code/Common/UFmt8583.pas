// (c) MTXEPS, Inc. 1988-2008
Unit UFmt8583; { Unformat 8583 }
{
v825.2 01-21-09 TSL Some clean up, add Structure_Var_LLLL
v815.3 04-28-05 TSL-H Change parm list of TruncIfNeeded call
v815.2 03-14-05 TSL-G Add Structure_Var_LLLsBcd
v814.1 06-24-04 TSL-F add Get_Length
v813.1 01-02-04 TSL-E add Truncating of track2
** 06-30-00 TSL-D Fix printing in Unformat by adding Bit_Data_In[I]
** 02-15-00 TSL-C Comment out unused var
** 12-29-99 TSL-B Add BCD stuff
** 11-09-99 TSL-A Take out a few bit map messages there were too many
}
Interface

Uses
   FinalizationLog,
   {$IFDEF MSWINDOWS}
   Windows,
   {$ENDIF}
   sysutils, Base8583, MTX_Constants, MTX_Lib,MRTypes;

function  UnBCDtoString(anyVar:AnsiString) : AnsiString;    // TSL-B
function  Unformat_8583(Var In_Ptr : P_ISO; Var In_Data : B_Record; var PrintData: arrayByte) : Integer;
Function  Get_Bit(Var In_Ptr : P_ISO; In_Bit : Integer) : AnsiString;
Function  Get_Length(var In_Ptr: P_ISO; In_Bit : integer) : integer;

Implementation

uses
{$IFDEF HOSTSIM}
   Fmt_8583,smToDisplay;
{$ELSE HOSTSIM}
  {$IFDEF ISOMSGTEST}
     Fmt_8583,smToDisplay;
  {$ELSE ISOMSGTEST}
     Fmt_8583;
  {$ENDIF ISOMSGTEST}
{$ENDIF HOSTSIM}

function UnBCDtoString(anyVar:AnsiString) : AnsiString; { only send BCD to this routine }
var
  tempStr : AnsiString;
  i,
  tempChr1,
  tempChr2 : byte;
begin
  i := 1;
  tempStr := '';
  while (i <= length(AnyVar)) do            { turn 1 hex bytes }
  begin                                     { into 2 decimal values }
    tempChr1 := ord(AnyVar[i]) shr 4;       { move left 4 bits to right }
    if (tempChr1 > 9) then
      tempChr1 := ((tempChr1 - 9) or $40)   { A..F }
    else
      tempChr1 := tempChr1 or $30;          { 0..9 }
    tempChr2 := ord(AnyVar[i]) and $0F;     { keep only right 4 bits }
    if (tempChr2 > 9) then
      tempChr2 := (tempChr2 - 9) or $40     { A..F }
    else
      tempChr2 := tempChr2 or $30;          { 0..9 }
    tempStr := tempStr + AnsiChar(tempChr1) + AnsiChar(tempChr2); { put them together }
    inc(i);                                        { bump counter }
  end;
  result := tempStr;
end;

function Unformat_8583(var In_Ptr: P_ISO; var In_Data: B_Record; var PrintData: arrayByte): integer;
const
  SIGNATURE_BIT = 63;           //75534
var
  I,Bit_Map_Len,BadBit,Current_BitMapNum,MsgPointer,MsgLen: integer;
  Map_Error,Bit_Error: boolean;
  Debug_Msg: AnsiString;

  procedure Convert_To_Bin(Var In_Ptr: P_ISO; Num: integer);
  var i,j,Pos1,Pos2: integer;
  begin
    i := 1;
    j := 1;
    try
      if Bit_Error or Map_Error then
        exit;
      while i <= 16 do
      begin
        Pos1 := Pos(In_Ptr.In_Bit_Map_16[Num][I+0], Hex_Set);
        Pos2 := Pos(In_Ptr.In_Bit_Map_16[Num][I+1], Hex_Set);
        if (Pos1 = 0) or (Pos2 = 0) then
        begin
          SM('****WARNING! Incoming Bit Map is Invalid >' + In_Ptr.In_Bit_Map_16[Num]+'<');
          Map_Error := true;
          exit;
        end;
        In_Ptr.In_Bit_Map_08[((Num-1)*8)+J] := ((Pos1-1) shl 4) + (Pos2-1);
        inc(i,2);
        inc(j);
      end;          //DList('Bit Map ' + Str_(Num) + ' is >' + In_Bit_Map_16[Num] + '<  Hex >' + Hex(In_Bit_Map_08[((Num-1)*8)+1], 8)+'<');
    except on e: exception do
      SM(format('Unformat_8583.Convert_To_Bin (Num=%d, i=%d, j=%d) EXCEPTION: %s',[Num,i,j,e.message]));    //JTG 34508
    end;
  end;

  procedure Set_BadBit(aBit: integer);
  begin
    try
      if aBit = 0 then
      begin
        SM(format('****WARNING! Map %d has error (uFmt8583)',[Current_BitMapNum]));
        Map_Error := True;
      end
      else
      begin
        BadBit := aBit;
        SM(format('****WARNING! Bit %d (or %d) is in ERROR! (uFmt8583)',[BadBit,pred(BadBit)]));
        Bit_Error := True;
      end;
      MsgLen := 0;
    except on e: exception do
      SM(format('Unformat_8583.Set_BadBit (aBit=%d) EXCEPTION: %s',[aBit,e.message]));   //JTG 34508
    end;
  end;

  function CopyFixedBytes(aBit, Num: Integer): AnsiString;   { func to get "N" bytes }
  var TmpStr: AnsiString;
      printStr: AnsiString;
  begin
    try
      Result := '';
      If Bit_Error or Map_Error then
        exit;
      if (MsgLen >= Num) then
      begin
        tmpStr := A_Copy(In_Data.B_Data[MsgPointer], Num);
        //SM(format('Unformat_8583.CopyFixedBytes aBit[%d] Num[%d] data[%s]',[aBit,Num,TmpStr]));  //7934
        if aBit > 0
          then printStr := truncIfNeeded(In_Ptr, aBit, tmpStr)
          else printStr := tmpStr;
        if printStr = '' then
          printStr := tmpStr;
        if length(PrintStr) > 0 then            // JTG only access PrintStr[1] if it exists   34508
          move(printStr[1], PrintData[msgPointer], Num);
        inc(MsgPointer, Num);
        Dec(MsgLen, Num);
      end
      else
        Set_BadBit(aBit);

      if (not Bit_Error) and (not Map_Error) then
      begin
        Result := TmpStr;
        In_Ptr.Bit_Length[aBit] := Num;
      end;
    except on e: exception do
      SM(format('Unformat_8583.CopyFixedBytes (aBit=%d, Num=%d) EXCEPTION: %s',[aBit,Num,e.message]));  //JTG 34508
    end;
  end;  { CopyFixedBytes }

  Function CopyVarBytes(aBit, Num_Len_Bytes: Integer) : AnsiString;
  Var Len: Integer;
      sLen,TmpStr: AnsiString;
  begin
    try
      Result := '';
      If Bit_Error or Map_Error then
        exit;
      sLen := CopyFixedBytes(0, Num_Len_Bytes);  { use bit zero so we don't truncate the length chars }
      len := StrToIntDef(sLen, 0);
      If ((len > 0) and (MsgLen >= Len)) then       { and msg long enough   }
        TmpStr := CopyFixedBytes(aBit,Len)
      else Set_BadBit(aBit);

      If (not Bit_Error) then
      begin
        Result := TmpStr;
        In_Ptr.Bit_Length[aBit] := Len;
      end;
    except on e: exception do
      SM(format('Unformat_8583.CopyVarBytes (aBit=%d, Num_Len_Bytes=%d) EXCEPTION: %s',[aBit,Num_Len_Bytes,e.message]));//JTG 34508
    end;
  end; { CopyVarBytes }

begin { Unformat_8583 }
  result := 0; // TODO: reconfirm! // [Warning] UFmt8583.pas(368): Return value of function 'Unformat_8583' might be undefined
  i := 0;
  try
    for i := 1 to (Max_BitMaps * 64) do    { Clear out old arrays }
    begin
      In_Ptr.Bit_Data_In[i] := '';
      In_Ptr.Bit_Length[i]  := 0;
    end;
    FillChar(PrintData, sizeOf(PrintData), 0);
    FillChar(In_Ptr.In_Bit_Map_16, sizeOf(In_Ptr.In_Bit_Map_16), 0);
    FillChar(In_Ptr.In_Bit_Map_08, sizeOf(In_Ptr.In_Bit_Map_08), 0);
    Bit_Error := False;
    Map_Error := False;
    MsgLen    := In_Data.B_Len;
    MsgPointer:= 1;
    BadBit    := 0;
    Debug_Msg := '';
    Debug_Msg := '[Var Inits OK]';         //JTG 34508

    If (MsgType_Structure = MsgDisplay)
      then In_Ptr.Msg_Type := CopyFixedBytes(0,4)
      else In_Ptr.Msg_Type := UnBcdToString(CopyFixedBytes(0,2));
    DList('UFmt8583.Unformat_8583: Message Type=>' + In_Ptr.Msg_Type +'<');

    { Get Bit Map 1 }
    Current_BitMapNum := 1;

    If (In_Ptr.Bit_Map_Type = Bit_Map_Display)
      then Bit_Map_Len := 16
      else Bit_Map_Len := 8;      // Hex Bit Maps

    In_Ptr.In_Bit_Map_16[Current_BitMapNum] := CopyFixedBytes(0, Bit_Map_Len);

    If (In_Ptr.Bit_Map_Type = Bit_Map_Display)
      then DList('Bit Map=>' + In_Ptr.In_Bit_Map_16[Current_BitMapNum] +'<')
      else DList('Bit Map=>' + UnBCDToString(In_Ptr.In_Bit_Map_16[Current_BitMapNum]) +'<');

    If (In_Ptr.Bit_Map_Type = Bit_Map_Display)
      then Convert_To_Bin(In_Ptr, Current_BitMapNum)
      else Move(In_Ptr.In_Bit_Map_16[1][1], In_Ptr.In_Bit_Map_08[((Current_BitMapNum-1)*8)+1], 8);
    Debug_Msg := '[Get Bit Map 1 OK]';         //JTG 34508

    { Get Balance of Bit Maps }
    I := 1;
    Current_BitMapNum := 2;
    while (I < (Max_BitMaps * 64)) do
    begin
      If (In_Ptr.Bit_Format[I] = Structure_BitMap) then
      begin
        If (TestBit(In_Ptr.In_Bit_Map_08, I) <> 0) then
        begin
          In_Ptr.In_Bit_Map_16[Current_BitMapNum] := CopyFixedBytes(0,Bit_Map_Len);
          If (In_Ptr.Bit_Map_Type = Bit_Map_Display) then
          begin
            DList('Bit Map=>' + In_Ptr.In_Bit_Map_16[Current_BitMapNum] +'<');
            Convert_To_Bin(In_Ptr, Current_BitMapNum);
          end
          Else
          begin
            DList('Bit Map=>' + UnBCDToString(In_Ptr.In_Bit_Map_16[Current_BitMapNum]) +'<');
            Move(In_Ptr.In_Bit_Map_16[Current_BitMapNum][1], In_Ptr.In_Bit_Map_08[((Current_BitMapNum-1)*8)+1], 8);
          end;
          I := ((Current_BitMapNum-1) * 64) + 1;
          Inc(Current_BitMapNum);
        end
        Else
          Break;
      end
      Else
        Inc(I);
    end;
    Debug_Msg := '[Get Balance of Bit Maps OK]';    //JTG 34508

    { Unformat bits }
    Current_BitMapNum := 0;
    I := 1;
    while (I <= (Max_BitMaps * 64)) and (MsgLen > 0) do
      begin
        If (TestBit(In_Ptr.In_Bit_Map_08, I) <> 0) then  { If bit is in map }
          Case In_Ptr.Bit_Format[I] of                   { TSL-B highly modified }
            Structure_Unknown :
              begin
                SM('****WARNING! Bit Structure '+Str_(I)+' is UNKNOWN');
                Set_BadBit(i);
              end;

            Structure_Fixed :
              begin
                Debug_Msg := '[Structure_Fixed]';   //JTG 34508
                In_Ptr.Bit_Data_In[I] := CopyFixedBytes(i,In_Ptr.Bit_Min_Len[I]);
                DList('Bit '+ZFill(Str_(I), 3)+' (F/'+Str_(In_Ptr.Bit_Min_Len[I])+')=>' +
                      truncIfNeeded(In_Ptr,i,In_Ptr.Bit_Data_In[I]) + '<');
                If (Length(In_Ptr.Bit_Data_In[I]) <> In_Ptr.Bit_Min_Len[I]) and (Length(In_Ptr.Bit_Data_In[I]) <> 0) then
                  Set_BadBit(i);
              end;
            Structure_FixedBcd:
              begin
                Debug_Msg := '[Structure_FixedBcd]';    //JTG 34508
                Bit_Map_Len := In_Ptr.Bit_Min_Len[I];
                if (odd(Bit_Map_Len)) then
                  inc(Bit_Map_Len);                   { make sure it's even } { TSL-A }
                Bit_Map_Len := Bit_Map_Len div 2;
                In_Ptr.Bit_Data_In[I] := UnBcdToString(CopyFixedBytes(i,Bit_Map_Len));
                If (odd(In_Ptr.Bit_Min_Len[I])) then            // delete extra even byte
                  delete(In_Ptr.Bit_Data_In[I], 1, 1);
                DList('Bit '+ZFill(Str_(I), 3)+' (FBcd/'+Str_(In_Ptr.Bit_Min_Len[I])+')=>' +
                      truncIfNeeded(In_Ptr,i,In_Ptr.Bit_Data_In[I]) + '<');
                If (Length(In_Ptr.Bit_Data_In[I]) <> In_Ptr.Bit_Min_Len[I]) and (Length(In_Ptr.Bit_Data_In[I]) <> 0) then
                  Set_BadBit(i);
              end;
            Structure_Var_L,
            Structure_Var_LL,
            Structure_Var_LLL,
            Structure_Var_LLLL:
              begin
                Debug_Msg := '[Structure_Var_L]';     //JTG 34508
                case In_Ptr.Bit_Format[I] of
                  Structure_Var_L   : In_Ptr.Bit_Data_In[I] := CopyVarBytes(i,1);
                  Structure_Var_LL  : In_Ptr.Bit_Data_In[I] := CopyVarBytes(i,2);
                  Structure_Var_LLL : In_Ptr.Bit_Data_In[I] := CopyVarBytes(i,3);
                  Structure_Var_LLLL:
                    if i = SIGNATURE_BIT      //length of signature length field increased from 3 to 4..
                      then In_Ptr.Bit_Data_In[I] :=CopyVarBytes(i,BytesInSignatureLengthField)
                      else In_Ptr.Bit_Data_In[I] :=CopyVarBytes(i,4);
                end;
                DList('Bit '+ZFill(Str_(I), 3)+' (V/'+Str_(In_Ptr.Bit_Min_Len[I])+'/'+ Str_(In_Ptr.Bit_Max_Len[I])+')=>' +
                  truncIfNeeded(In_Ptr,i,In_Ptr.Bit_Data_In[I]) + '<('+Str_(Length(In_Ptr.Bit_Data_In[I]))+')');
                If (Length(In_Ptr.Bit_Data_In[I]) < In_Ptr.Bit_Min_Len[I]) or
                   (Length(In_Ptr.Bit_Data_In[I]) > In_Ptr.Bit_Max_Len[I]) then
                begin
                  Set_BadBit(i);
                end;
              end;

            Structure_Var_LLBcd,
            Structure_Var_LLLBcd,
            Structure_Var_LLLsBcd:
              begin
                Debug_Msg := '[Structure_Var_LLBcd]';     //JTG 34508
                If (In_Ptr.Bit_Format[I] = Structure_Var_LLBcd)
                  then Bit_Map_Len := strToInt(UnBcdToString(CopyFixedBytes(0,1)))
                  else Bit_Map_Len := strToInt(UnBcdToString(CopyFixedBytes(0,2)));
                if (In_Ptr.Bit_Format[i] = Structure_Var_LLLsBcd) then
                  In_Ptr.Bit_Data_In[i] := UnBcdToString(CopyFixedBytes(i,Bit_Map_Len))
                else
                if (odd(Bit_Map_Len))
                  then In_Ptr.Bit_Data_In[I] := UnBcdToString(CopyFixedBytes(i,(Bit_Map_Len+1) div 2))
                  else In_Ptr.Bit_Data_In[I] := UnBcdToString(CopyFixedBytes(i,Bit_Map_Len div 2));

                //SetLength(Bit_Data_In[I], Bit_Map_Len);
                DList('Bit '+ZFill(Str_(I), 3)+' (VBcd/'+Str_(In_Ptr.Bit_Min_Len[I])+')=>'
                  + truncIfNeeded(In_Ptr,i,In_Ptr.Bit_Data_In[I]) +'<('+Str_(Length(In_Ptr.Bit_Data_In[I]))+')');
                If (Length(In_Ptr.Bit_Data_In[I]) < In_Ptr.Bit_Min_Len[I]) or
                   (Length(In_Ptr.Bit_Data_In[I]) > In_Ptr.Bit_Max_Len[I]) then
                  Set_BadBit(i);
              end;

            Structure_HexBcd:
              begin
                Debug_Msg := '[Structure_HexBcd]';       //JTG 34508
                Bit_Map_Len := In_Ptr.Bit_Min_Len[I] div 2;
                In_Ptr.Bit_Data_In[I] := UnBcdToString(CopyFixedBytes(i,Bit_Map_Len));
                DList('Bit '+ZFill(Str_(I), 3)+' (HexBcd/'+Str_(In_Ptr.Bit_Min_Len[I])+')=>'
                  + truncIfNeeded(In_Ptr,i,In_Ptr.Bit_Data_In[I]) +'<('+Str_(Length(In_Ptr.Bit_Data_In[I]))+')');
              end;

            Structure_LLOnlyBcd,
            Structure_LLLOnlyBcd:
              begin
                Debug_Msg := '[Structure_LLOnlyBcd]';    //JTG 34508
                If (In_Ptr.Bit_Format[I] = Structure_LLOnlyBcd)
                  then Bit_Map_Len := strToInt(UnBcdToString(CopyFixedBytes(0,1)))
                  else Bit_Map_Len := strToInt(UnBcdToString(CopyFixedBytes(0,2)));

                In_Ptr.Bit_Data_In[I] := CopyFixedBytes(i,Bit_Map_Len);
                DList('Bit '+ZFill(Str_(I), 3)+' (LBcd/'+Str_(In_Ptr.Bit_Min_Len[I])+')=>'
                  + truncIfNeeded(In_Ptr,i,In_Ptr.Bit_Data_In[I]) +'<('+Str_(Length(In_Ptr.Bit_Data_In[I]))+')');
              end;

            Structure_BitMap:;   // do nothing?
          else
            begin
              SM('****WARNING! Bit Structure '+Str_(I)+'=>'+Str_(In_Ptr.Bit_Format[I])+'< is Mis-Defined');
              Set_BadBit(i);
            end;
          end;
        inc(I);
      end;
    if Map_Error
      then result := 999
      else result := BadBit;
  except on e: exception do
    SM(format('Unformat_8583 (i=%d) %s EXCEPTION: %s',[i,Debug_Msg,e.message]));  //JTG 34508, added info to this log line
  end;
end;

function  Get_Bit(Var In_Ptr : P_ISO; In_Bit : Integer) : AnsiString;
begin
  result := '';
  if Range(In_Bit) then
    result := In_Ptr.Bit_Data_In[In_Bit];
end;

function  Get_Length(var In_Ptr: P_ISO; In_Bit : integer) : integer;
begin
  result := 0;
  if Range(In_Bit) then
    result := In_Ptr.Bit_Length[In_Bit];
end;

initialization
  ExtendedLog('UFmt8583 Initialization');
finalization
  ExtendedLog('UFmt8583 Finalization');
end.
