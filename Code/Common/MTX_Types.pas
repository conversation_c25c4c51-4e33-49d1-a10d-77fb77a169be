// (c) MTXEPS, Inc. 1988-2008
unit MTX_Types;
(******************************************************************************
 *
 *  MTX_TYPES unit
 *
 *  This unit contains type declarations for the base of the OpenEPS object
 *  system.
 *
 ******************************************************************************
  Revision History
  ================
  04-11-06 SRM N/A
    - Added type type declarations for TMTXByteArray .. TMTXCardinalArray.
    - Added header block.
 ******************************************************************************)

interface

uses
  Classes,
//  {$IFDEF DXE2}
//  JclAnsiStrings,
//  {$ENDIF DXE2}
  SysUtils;

type

(******************************************************************************
 * Data Type Declarations
 ******************************************************************************)

  TMTXByte                                 = Byte;
  TMTXInteger                              = Integer;
  TMTXCardinal                             = Cardinal;
  TMTXInt64                                = Int64;
  TMTXString                               = AnsiString;

//  {$IFDEF DXE2}
//  TMTXStrings                              = TAnsiStrings;
//  TMTXStringList                           = TAnsiStringList;
//  {$ELSE}
  TMTXStrings                              = TStrings;
  TMTXStringList                           = TStringList;
//  {$ENDIF DXE2}

  TMTXChar                                 = AnsiChar;
  PMTXChar                                 = PAnsiChar;

  TMTXCharArray                            = array of TMTXChar;
  PMTXCharArray                            = ^TMTXCharArray;
  
  TMTXByteArray                            = array of TMTXByte;
  TMTXIntegerArray                         = array of TMTXInteger;
  TMTXCardinalArray                        = array of TMTXCardinal;

  TMTXBoolean                              = Boolean;
  TMTXVariant                              = Variant;
  TMTXTimestamp                            = TDateTime;

  TMTXLogType =
    (
      ltError,
      ltWarning,
      ltInfo
    );

  TMTXLogLevel =
    (
      llNone,
      llVerbose,
      llNormal,
      llTerse,
      llDebug
    );

    enumDCC = (DCCUnknown,DCCNo,DCCYes);     // TFS-21467 moved from SCAT_Classes
  TMtxKey128 = array [0..15] of Byte;
  TMtxKey192 = array [0..23] of Byte;
  TMtxKey256 = array [0..31] of Byte;

(******************************************************************************
 * Interface Declarations
 ******************************************************************************)

  IMTXObject = interface ['{9DFCCB62-EA2F-4390-A48B-2810844DE6F6}']
    function GetInterface(const AIID: TGUID; out Obj): TMTXBoolean;
  end;

  IMTXThread = interface ['{B0536294-FEDF-4C4B-A52B-C96D6D731C07}']
    procedure Suspend;
    procedure Resume;
    procedure Terminate;
    function IsSuspended: Boolean;
    function IsTerminared: Boolean;
  end;

  IMTXLogger = interface ['{7AE47773-74A8-4EE5-8039-F0D7CDCB94F7}']
    procedure Log(const ALogMsg: TMTXString; const ALogMsgType: TMTXLogType; const ALogMsgLevel: TMTXLogLevel); overload;
    procedure Log(const ALogFmt: TMTXString; ALogParams: array of const); overload;
    procedure Log(const AException: Exception); overload;
  end;

implementation

end.
