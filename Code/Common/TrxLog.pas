// (c) MTXEPS, Inc. 1988-2008
unit TrxLog;

//===== Revision History =====================================================
//    DAEv823 05-01-07    Add function GetTotalTrxAmt
//    TSL-04  09-01-06    Add function FindRecordButNotVoid for ACI host
//============================================================================

interface

uses
  FinalizationLog,
  Classes,
  Sysutils, MTX_Constants, MdMsg;

type

  TRptInfoType = (rtRptInfo, rtRptBlock);                                       

  TTrxLog = class
  public
    {$IFDEF TEST}
    PersistentFile: boolean;  // stops Teardown from deleting file, if true
    function DeleteTestFile: boolean; virtual; abstract;
    {$ENDIF}

    constructor  Create(QFileName: string); virtual; abstract;
    destructor   Destroy; override;
    function  AppendRecord(QRecord : MDMsgRec) : Boolean; virtual; abstract;
    function ResetFile: boolean; virtual; abstract;
    function  ClearDeletedRecords : integer; virtual; abstract;
    function  ChangeOfflinePreAuthCompToPurch : boolean; virtual; abstract;
    function  ClearVisaFields : boolean; virtual; abstract;
    procedure DeleteTORsAtEOD(aSuffix: string); virtual; abstract;
    procedure DeleteExistingOfflineAndTORFiles; virtual; abstract;
    procedure EncryptFields(var QRecord: mdMsgRec; newKEK3: string = ''); virtual; abstract;
    function DecryptFields(var QRecord: MdMsgRec; newKEK3: string = ''): boolean; virtual; abstract;
    function  SaveOutstandingPreauths(QPreAuthFileName: string) : boolean; virtual; abstract;
    function  MovePreAuthsToActlog(QPreAuthFileName: string) : boolean; virtual; abstract;
    function  CopyOfflineFilesToActlog : boolean; virtual; abstract;
    function  SetTORTypeFromTORFiles: boolean; virtual; abstract; // DEV-24478
    function  MakeOfflinesAndTORsFromActlog(var QList: TStringList; MakeTORs: boolean = true) : boolean; virtual; abstract;
    function  RptInfoExists(RptInfoType: TRptInfoType): boolean; virtual; abstract; 
    function  FindDeclAdvBySeqNo(QSeqNo: string6): integer; virtual; abstract;
    function  FindRecord(var QRecord : MDMsgRec; QSeqNo : string6; QSeqType : TSeqNumType) : boolean; virtual; abstract;
    function  FindRecordNotDeleted(var QRecord : MDMsgRec; QSeqNo : string6; QSeqType : TSeqNumType) : boolean; virtual; abstract;
    function  FindRecordButNotVoid(var QRecord : MDMsgRec; QSeqNo : string6; QSeqType : TSeqNumType) : boolean; virtual; abstract;
    function  FindPreAuth(var QRecord: MDMsgRec; QSeqNo: string6; QAcctNoLast4: string) : boolean; virtual; abstract;
    function  WriteSettlementRecord(QRecord : MDMsgRec; var QFound:boolean) : boolean; virtual; abstract;
    function  FindRecordByAcctNoAndReqCode(var QRecord: MdMsgRec; QAcctNoLast4: string; QReqCode: string): boolean; virtual; abstract;
    function  FindRecordByAcctNoAndTrxAmtN(var QRecord: MDMsgRec; QAcctNoLast4: string; QTrxAmtN: int64) : boolean; virtual; abstract;
    function  FindRecordByAcctNo(var QRecord : MDMsgRec; QAcctNoLast4: string) : boolean; virtual; abstract;
    function  FindRecordNo(QSeqNo : string6; QSeqType : TSeqNumType) : integer; virtual; abstract;
    function  FindRecordNoNew(QSeqNo: integer; QSeqType: TSeqNumType): integer; virtual; abstract;
    function  FindRecordNotVoidByOldSeqNo(var QRecord : MDMsgRec; QOldSeqNo : string6) : boolean; virtual; abstract;
    function  FindRecordNotVoidByOldSeqNoAndReqCode(var QRecord : MDMsgRec; QOldSeqNo : string6; QReqCode: string) : boolean; virtual; abstract;
    function  FindRecordNotVoidNotInFlightByOldSeqNo(var QRecord : MDMsgRec; QOldSeqNo : string6) : boolean; virtual; abstract;
    function  FindRecordVoidByOldSeqNo(var QRecord : MDMsgRec; QOldSeqNo : string6) : boolean; virtual; abstract;
    function  FindRecordToMakeTOR(var QRecord: MDMsgRec; OldSeqNo, SeqNo: string6): boolean; virtual; abstract;     //JTG 33934 33801 requires a new method to scan the Actlog
    {$IFNDEF HOSTSIM} // Constructing instance of 'TRecFile' containing abstract method 'TTrxLog.TCPIPComFindRecord'
    {$IFNDEF MTXEPSDLL}
    {$IFNDEF MTXPOSDLL}
    {$IFNDEF RPT}
    function  TCPIPComFindRecord(var QRecord: MDMsgRec; QSeqNo: string255; QVoidType: char; QMsgTypeID: string4) : boolean; virtual; abstract;
    {$ENDIF RPT}
    {$ENDIF MTXPOSDLL}
    {$ENDIF MTXEPSDLL}
    {$ENDIF HOSTSIM}
    function  GetFileSize : integer; overload; virtual; abstract;
    function  GetFileSize(QTrxType: integer) : integer; overload; virtual; abstract;
    function  GetRecordCount(QShowDeleted: Boolean = False) : integer; virtual; abstract;
    function  GetRecordCountApproved(aHost: string3; aIncludeLocalApproved: boolean=false) : integer; virtual; abstract; // DEV-11499: add aIncludeLocalApproved
    function  GetRecordCountDeclined(aHost: string3) : integer; virtual; abstract;
    function  GetRecordSequenceNumbers(QSeqType : TSeqNumType; var QSeqNums : TStringList; QShowDeleted : Boolean = False) : integer; virtual; abstract;
    function  MarkRecordDeleted(QSeqNo : string6; QSeqType : TSeqNumType) : Boolean; virtual; abstract;
    function  MoveRecordToEOF(QSeqNo : string6; QSeqType : TSeqNumType) : Boolean; virtual; abstract;
    function  ReadByRecordNum(RecordNum: integer; var QRecord : MDMsgRec): integer; overload; virtual; abstract;
    function  WriteByRecordNum(RecordNum: integer; QRecord : MDMsgRec): Boolean; virtual; abstract;
    function  WriteByRecordNumNoEncrypt(RecordNum: integer; QRecord : MDMsgRec): boolean; virtual; abstract;
    function  RemoveRecord(QSeqNo : string6; QSeqType : TSeqNumType) : Boolean; virtual; abstract;
    function  UpdateRecord(QRecord : MDMsgRec; QSeqType : TSeqNumType) : Boolean; virtual; abstract;
    function  WriteRecord(QRecord : MDMsgRec; newKEK3: string=''; encryptIt: boolean=true) : Boolean; virtual; abstract;
    function RewriteFile: boolean; virtual; abstract;
    function  InitLaneMsgFile: boolean; virtual; abstract;
    function  GetExt:string; virtual; abstract;
    function  GetFullName: string; virtual; abstract;
    procedure SetFullName(aName: string); virtual; abstract;
    function  GetTotalTrxAmt(aHost: string3 = '') : integer; virtual; abstract;
    function  GetStoreMonitoringTotalsForEngine(aHost: string3; var approvedCount, approvedAmount, declinedCount, offlineCount, offlineAmount, torCount, torAmount,SignatureCount: integer) : boolean; virtual; abstract;
    function  TruncateCardData: boolean; virtual; abstract;                     
    function  BlankOfflines: boolean; virtual; abstract;  // DEV-29582
    function  IsReportBlock(var QRecord: mdMsgRec): boolean; virtual; abstract; // DEV-28982
    function  IsValidTransaction(QRecord : MDMsgRec): boolean; virtual; abstract; 
    procedure GetKeyFromTrxFile(var QRecord : MDMsgRec); virtual; abstract;
    function  GetVoucherHeldCountAndAmount({aHost: string3; }var aTotalHeldCount: integer; var aTotalHeldAmount: integer; var aTotalWaitingCount: integer; var aTotalWaitingAmount: integer): boolean; virtual; abstract;// DEV-8000
    function  GetVoucherHeldTrxs(aStartingNum: integer): string; virtual; abstract; // DEV-8000
    function  VoucherUpdate(aXML: string): string; virtual; abstract; // DEV-8000

    function  TranslateErrorCode(QErrorCode : integer) : string; virtual;
    procedure GetCountAndAmount(var Count,Amount: integer); virtual; abstract;    // JTG 20061

    {$IFDEF TEST}
//    procedure    SetupTest(dir: string); virtual; abstract;
//    function     TearDownTest: boolean; virtual; abstract;
    {$ENDIF TEST}

  protected
//    FKeyType: TEncryptionKey;
//    procedure DecryptFields(var QRecord: MdMsgRec); virtual;

//  published
//    property FullName: string read FFullName write FFullName; virtual; abstract;
  private

  end;

const
  { trx types }
  TT_ALL       = 0;
  TT_IN_FLIGHT = 1;
  TT_OFFLINE   = 2;
  TT_TOR       = 3;

implementation

destructor TTrxLog.Destroy;
begin
  inherited Destroy;
end;

(*
  function TranslateErrorCode()

  Description:
    Translate the error code, QErrorCode, from a numeric value to a string
    value that gives a more understandable/meaningful explaination of the error.

  Notes:
    Currently, this function supports I/O errors (and EInOutError exception
    error values).

  Parameter(s): QErrorCode - integer indicating the error code to translate

  Return Value(s): string containing a textual explaination of QErrorCode

  The input is the value returned by ClearDeletedRecords.
*)
function TTrxLog.TranslateErrorCode(QErrorCode: integer) : string;
begin
  case QErrorCode of
      0: result := 'The deleted records were cleared from the transaction log';
     -1: result := 'The deleted records were not cleared from the transaction log';
      1: result := 'The records were cleared from the transaction log, but the ' +
                   'temporary file has not yet been renamed/deleted';
      2: result := 'The transaction log was not found';
      3: result := 'Invalid file name';
      4: result := 'Too many open files';
      5: result := 'Access denied';
     32: result := 'Sharing violation';
    100: result := 'End of file reached';
    101: result := 'The disk is full';
    106: result := 'Invalid input received';
    else result := 'Unknown error code received';
  end;
end;

initialization
  ExtendedLog('TrxLog Initialization');
finalization
  ExtendedLog('TrxLog Finalization');
end.
