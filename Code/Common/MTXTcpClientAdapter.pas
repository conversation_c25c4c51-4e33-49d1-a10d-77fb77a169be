unit MTXTcpClientAdapter;

interface

uses MTX_Constants;

const
  DEFAULT_CONNECT_TIMEOUT = 10*1000;

type
  TSocketKind = (skUnknown, skIndy, skBorland, skIPWorks);
  
  TMTXTcpClientAdapter = class
  protected
    FActive: Boolean;
    FHost: string;
    FPort: Integer;
    FUseSSL: Boolean;
    FSSLEstablished: Boolean;
    //FInternalSocket: TObject;
    FInactiveTimer: LongInt;
    FSSLEstablishTimeout: LongInt;
    FConnectTimeout: LongInt;
  private
    procedure SetHost(Value: string);
    procedure SetPort(Value: integer);
    procedure SetUseSSL(Value: Boolean);
  public
    constructor Create;
    destructor Destroy; override;

    procedure CreateSocket(aUseSSL: boolean=false); virtual; abstract;
    procedure DestroySocket; virtual; abstract;  
    //procedure Connect(Timeout: integer = DEFAULT_CONNECT_TIMEOUT); virtual; abstract;
    //procedure Disconnect; virtual; abstract;
    procedure CheckIfTimeToConnect; virtual; abstract;
    function BlockIn(Len: integer; var Data: ArrayByte): integer; virtual; abstract;
    procedure BlockOut(Len: integer; var aData: ArrayByte; var PrintBuf: B_Record); virtual; abstract;
    procedure PrintBlockIn(Len: integer; var PrintData: ArrayByte); virtual; abstract;

    property Active: Boolean read FActive; // Connected & SSLEstablished
    property Host: string read FHost write SetHost;
    property Port: Integer read FPort write SetPort;
    property UseSSL: Boolean read FUseSSL write SetUseSSL default True;
    property SSLEstablished: Boolean read FSSLEstablished;

    property InactiveTimer: LongInt read FInactiveTimer write FInactiveTimer;
    property ConnectTimeout: LongInt read FConnectTimeout write FConnectTimeout;
    property SSLEstablishTimeout: LongInt read FSSLEstablishTimeout write FSSLEstablishTimeout;
  end;

//function CreateMTXTcpClientAdapter(SocketKind: TSocketKind): TMTXTcpClientAdapter; // XE: Remove WinEPS

implementation

uses
  FinalizationLog,
  OpenEPSTcpCli,
  MTX_Lib;

{ // XE: Remove WinEPS
function CreateMTXTcpClientAdapter(SocketKind: TSocketKind): TMTXTcpClientAdapter;
begin
  result := nil;
  case SocketKind of
    skIndy: result := TMTXIndyTcpClientAdapter.Create;
    skBorland: ; //result := TMTXBorlandTcpClientAdapter.Create;
    skIPWorks: ; //result := TMTXIPWorksTcpClientAdapter.Create;
  end;
end;
}

constructor TMTXTcpClientAdapter.Create;
begin
  inherited;
  SM('TMTXTcpClientAdapter.Create');
end;

destructor TMTXTcpClientAdapter.Destroy;
begin
  SM('TMTXTcpClientAdapter.Destroy');
end;

procedure TMTXTcpClientAdapter.SetHost(Value: string);
begin
  if NOT FActive and (FHost <> Value) then
    FHost := Value;
end;

procedure TMTXTcpClientAdapter.SetPort(Value: integer);
begin
  if NOT FActive and (FPort <> Value) then
    FPort := Value;
end;

procedure TMTXTcpClientAdapter.SetUseSSL(Value: Boolean);
begin
  if NOT FActive and (FUseSSL <> Value) then
    FUseSSL := Value;
end;

initialization
  ExtendedLog('MTXTcpClientAdapter Initialization');
finalization
  ExtendedLog('MTXTcpClientAdapter Finalization');

end.
