object dXMLTables: TdXMLTables
  OldCreateOrder = False
  OnCreate = DataModuleCreate
  Height = 200
  Width = 327
  object cHost: TClientDataSet
    PersistDataPacket.Data = {
      8E1100009619E0BD0100000018000000830000000000030000008E1108486F73
      744E616D6501004900100001000557494454480200020010000C486F7374506F
      6C6C4368617201004900100001000557494454480200020004000D486F737450
      6F6C6C496E74766C01004900100001000557494454480200020004000D486F73
      7453757370656E64437401004900100001000557494454480200020004000B48
      6F7374466174616C437401004900100001000557494454480200020004000C48
      6F7374526573756D65437401004900100001000557494454480200020004000C
      486F73744F6E544F787878780100490010000100055749445448020002000400
      09486F73744C6F63616C01004900100001000557494454480200020002000948
      6F73744F6666544F01004900100001000557494454480200020004000C486F73
      745F535343505F494401004900100001000557494454480200020020000E486F
      73745F49735F4542434449430100490010000100055749445448020002000200
      0E486F73745F436F6D6D5F4E525A490100490010000100055749445448020002
      00020011486F73745F4D61696E5F53657373696F6E0100490010000100055749
      44544802000200040010486F73745F544F525F53657373696F6E010049001000
      010005574944544802000200040011486F73745F4F66666C5F53657373696F6E
      01004900100001000557494454480200020004000E486F73744D65726368616E
      74494401004900100001000557494454480200020020000C486F737450617373
      776F726401004900100001000557494454480200020010000E486F7374436B41
      7574685479706501004900100001000557494454480200020002000B486F7374
      436B43436F6465010049001000010005574944544802000200040010486F7374
      44656661756C7453746174650100490010000100055749445448020002000400
      0A486F73744F6E4462436B01004900100001000557494454480200020002000B
      486F73744F66664462436B010049001000010005574944544802000200020008
      486F7374506F7274010049001000010005574944544802000200020008486F73
      7442617564010049001000010005574944544802000200060008486F73744269
      747301004900100001000557494454480200020002000A486F73745061726974
      79010049001000010005574944544802000200020008486F737453746F700100
      49001000010005574944544802000200020007486F7374495251010049001000
      01000557494454480200020002000F486F737453746F72655465726D49440100
      490010000100055749445448020002000C000E486F73744D7367536563436F64
      6501004900100001000557494454480200020010000F486F73744D6774496E66
      6F4461746101004900100001000557494454480200020024000F486F73745365
      6E644D6774496E666F01004900100001000557494454480200020002000D486F
      73744D657263685479706501004900100001000557494454480200020008000E
      486F7374496E73744944436F6465010049001000010005574944544802000200
      16000D486F73744D657263684E616D6501004900100001000557494454480200
      0200320010486F73744D65726368416464726573730100490010000100055749
      445448020002002E000D486F73744D6572636843697479010049001000010005
      5749445448020002001A000E486F73744D657263685374617465010049001000
      01000557494454480200020004000B486F73745A6970436F6465010049001000
      01000557494454480200020012000B486F7374436F756E747279010049001000
      010005574944544802000200040010486F737443757272656E6379436F646501
      004900100001000557494454480200020006000D486F73745374617465436F64
      6501004900100001000557494454480200020004000E486F7374436F756E7479
      436F6465010049001000010005574944544802000200060010486F73744F6666
      736574487273474D5401004900100001000557494454480200020006000D486F
      7374436C65726B4C616E6501004900100001000557494454480200020002000B
      486F7374544F524C61746501004900100001000557494454480200020002000E
      486F7374544F5254696D656F7574010049001000010005574944544802000200
      02000B486F7374544F524E6F5432010049001000010005574944544802000200
      02000F486F73745F54726163655F446174610100490010000100055749445448
      0200020002000F486F73744F666652657375626D697401004900100001000557
      4944544802000200020010486F73745465726D696E616C547970650100490010
      0001000557494454480200020004000F486F73744D65726368616E744E756D01
      00490010000100055749445448020002000C000F486F73745465726D696E616C
      4E756D01004900100001000557494454480200020006000F486F737452657472
      7950686F6E653101004900100001000557494454480200020002000A486F7374
      50686F6E653101004900100001000557494454480200020028000A486F73744C
      6F676F6E3101004900100001000557494454480200020050000F486F73745265
      74727950686F6E653201004900100001000557494454480200020002000A486F
      737450686F6E653201004900100001000557494454480200020028000A486F73
      744C6F676F6E3201004900100001000557494454480200020050000F486F7374
      526574727950686F6E653301004900100001000557494454480200020002000A
      486F737450686F6E653301004900100001000557494454480200020028000A48
      6F73744C6F676F6E3301004900100001000557494454480200020050000D486F
      73744D6F64656D496E697401004900100001000557494454480200020050000C
      486F7374486F6C6454696D650100490010000100055749445448020002000A00
      0C486F7374496E69744D61696E01004900100001000557494454480200020010
      000B486F7374496E6974544F5201004900100001000557494454480200020010
      000F486F7374496E69744F66666C696E65010049001000010005574944544802
      00020010000D486F7374436F6E6669674E756D01004900100001000557494454
      4802000200040011486F73745365636F6E645363724E616D6501004900100001
      0005574944544802000200060010486F73745472616E73616374696F6E730100
      4900100001000557494454480200020002000B486F737454696D656F75740100
      4900100001000557494454480200020004000F486F73744F666646776444656C
      61790100490010000100055749445448020002000A0010486F73744F66665061
      636544656C61790100490010000100055749445448020002000A000E486F7374
      544F5257616974496E740100490010000100055749445448020002000A000E48
      6F737454727857616974496E740100490010000100055749445448020002000A
      000A486F73745375666669780100490010000100055749445448020002000600
      11486F737453656E644865616C74684D73670100490010000100055749445448
      02000200020010486F73744865616C74684D7367496E74010049001000010005
      57494454480200020008000C486F73745769634C6F63616C0100490010000100
      0557494454480200020002000A486F737446747042696E010049001000010005
      57494454480200020002000C486F73744469766973696F6E0100490010000100
      05574944544802000200060009486F7374506F72743201004900100001000557
      4944544802000200020009486F73744261756432010049001000010005574944
      544802000200060009486F737442697473320100490010000100055749445448
      0200020002000B486F7374506172697479320100490010000100055749445448
      02000200020009486F737453746F703201004900100001000557494454480200
      0200020008486F73744952513201004900100001000557494454480200020002
      0009486F7374506F727433010049001000010005574944544802000200020009
      486F73744261756433010049001000010005574944544802000200060009486F
      7374426974733301004900100001000557494454480200020002000B486F7374
      50617269747933010049001000010005574944544802000200020009486F7374
      53746F7033010049001000010005574944544802000200020008486F73744952
      5133010049001000010005574944544802000200020012486F7374536574466F
      724461794C69676874010049001000010005574944544802000200020011486F
      737453617665436865636B496E666F0100490010000100055749445448020002
      00020011486F73745061727469616C42494E446C6C0100490010000100055749
      44544802000200020011486F73744143485374617465436F6465730100490010
      00010005574944544802000200620012486F73745472756E6361746541636374
      4E6F01004900100001000557494454480200020002000F486F73744654505573
      65724E616D6501004900100001000557494454480200020028000D486F737446
      545049704164647201004900100001000557494454480200020028000F486F73
      7446545050617373776F72640100490010000100055749445448020002002800
      0F486F737446545046696C654E616D6501004900100001000557494454480200
      0200180010486F7374504F4E756D626572557365640100490010000100055749
      4454480200020002000E486F7374506C6174666F726D49440100490010000100
      05574944544802000200020010486F7374436C656172696E67436F6465010049
      00100001000557494454480200020006000C486F73744D6572636842494E0100
      490010000100055749445448020002000C000C486F73744D6572636849434101
      00490010000100055749445448020002000C000E486F73745369674361704D6F
      646501004900100001000557494454480200020002000E486F7374506572736F
      6E616C436B01004900100001000557494454480200020002000D486F73745061
      79726F6C6C436B010049001000010005574944544802000200020010486F7374
      49504C6F63616C506F7274310100490010000100055749445448020002002800
      10486F737449504C6F63616C506F727432010049001000010005574944544802
      000200280010486F737449504C6F63616C506F72743301004900100001000557
      4944544802000200280010486F73744552434C6F63616C506F72740100490010
      000100055749445448020002000A000E486F737446545042494E446179730100
      49001000010005574944544802000200060012486F73744C61737442494E4674
      704461746508000800100000000D486F7374536574746C655270740100490010
      00010005574944544802000200020011486F737454656E6465724F66666C696E
      6501004900100001000557494454480200020014000B486F7374556E75736564
      3101004900100001000557494454480200020014000D486F737450726F677261
      6D496401004900100001000557494454480200020006000A486F737446545044
      6972010049001000010005574944544802000200C8000A486F73744552435573
      65010049001000010005574944544802000200020013486F7374455243547261
      6E7366657254797065010049001000010005574944544802000200020010486F
      7374455243465450497041646472010049001000010005574944544802000200
      28000E486F7374455243465450506F7274010049001000010005574944544802
      0002000A0016486F73744552434654504261636B757049704164647201004900
      1000010005574944544802000200280014486F73744552434654504261636B75
      70506F72740100490010000100055749445448020002000A0012486F73744552
      43465450557365724E616D650100490010000100055749445448020002002800
      12486F737445524346545050617373776F726401004900100001000557494454
      4802000200280012486F737445524346545046696C654E616D65010049001000
      01000557494454480200020018000D486F737445524346545044697201004900
      1000010005574944544802000200C8000000}
    Active = True
    Aggregates = <>
    FieldDefs = <
      item
        Name = 'HostName'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 16
      end
      item
        Name = 'HostPollChar'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 4
      end
      item
        Name = 'HostPollIntvl'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 4
      end
      item
        Name = 'HostSuspendCt'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 4
      end
      item
        Name = 'HostFatalCt'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 4
      end
      item
        Name = 'HostResumeCt'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 4
      end
      item
        Name = 'HostOnTOxxxx'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 4
      end
      item
        Name = 'HostLocal'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostOffTO'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 4
      end
      item
        Name = 'Host_SSCP_ID'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 32
      end
      item
        Name = 'Host_Is_EBCDIC'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'Host_Comm_NRZI'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'Host_Main_Session'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 4
      end
      item
        Name = 'Host_TOR_Session'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 4
      end
      item
        Name = 'Host_Offl_Session'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 4
      end
      item
        Name = 'HostMerchantID'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 32
      end
      item
        Name = 'HostPassword'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 16
      end
      item
        Name = 'HostCkAuthType'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostCkCCode'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 4
      end
      item
        Name = 'HostDefaultState'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 4
      end
      item
        Name = 'HostOnDbCk'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostOffDbCk'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostPort'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostBaud'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 6
      end
      item
        Name = 'HostBits'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostParity'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostStop'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostIRQ'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostStoreTermID'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 12
      end
      item
        Name = 'HostMsgSecCode'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 16
      end
      item
        Name = 'HostMgtInfoData'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 36
      end
      item
        Name = 'HostSendMgtInfo'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostMerchType'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 8
      end
      item
        Name = 'HostInstIDCode'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 22
      end
      item
        Name = 'HostMerchName'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 50
      end
      item
        Name = 'HostMerchAddress'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 46
      end
      item
        Name = 'HostMerchCity'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 26
      end
      item
        Name = 'HostMerchState'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 4
      end
      item
        Name = 'HostZipCode'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 18
      end
      item
        Name = 'HostCountry'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 4
      end
      item
        Name = 'HostCurrencyCode'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 6
      end
      item
        Name = 'HostStateCode'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 4
      end
      item
        Name = 'HostCountyCode'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 6
      end
      item
        Name = 'HostOffsetHrsGMT'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 6
      end
      item
        Name = 'HostClerkLane'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostTORLate'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostTORTimeout'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostTORNoT2'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'Host_Trace_Data'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostOffResubmit'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostTerminalType'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 4
      end
      item
        Name = 'HostMerchantNum'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 12
      end
      item
        Name = 'HostTerminalNum'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 6
      end
      item
        Name = 'HostRetryPhone1'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostPhone1'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 40
      end
      item
        Name = 'HostLogon1'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 80
      end
      item
        Name = 'HostRetryPhone2'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostPhone2'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 40
      end
      item
        Name = 'HostLogon2'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 80
      end
      item
        Name = 'HostRetryPhone3'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostPhone3'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 40
      end
      item
        Name = 'HostLogon3'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 80
      end
      item
        Name = 'HostModemInit'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 80
      end
      item
        Name = 'HostHoldTime'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 10
      end
      item
        Name = 'HostInitMain'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 16
      end
      item
        Name = 'HostInitTOR'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 16
      end
      item
        Name = 'HostInitOffline'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 16
      end
      item
        Name = 'HostConfigNum'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 4
      end
      item
        Name = 'HostSecondScrName'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 6
      end
      item
        Name = 'HostTransactions'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostTimeout'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 4
      end
      item
        Name = 'HostOffFwdDelay'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 10
      end
      item
        Name = 'HostOffPaceDelay'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 10
      end
      item
        Name = 'HostTORWaitInt'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 10
      end
      item
        Name = 'HostTrxWaitInt'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 10
      end
      item
        Name = 'HostSuffix'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 6
      end
      item
        Name = 'HostSendHealthMsg'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostHealthMsgInt'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 8
      end
      item
        Name = 'HostWicLocal'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostFtpBin'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostDivision'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 6
      end
      item
        Name = 'HostPort2'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostBaud2'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 6
      end
      item
        Name = 'HostBits2'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostParity2'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostStop2'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostIRQ2'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostPort3'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostBaud3'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 6
      end
      item
        Name = 'HostBits3'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostParity3'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostStop3'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostIRQ3'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostSetForDayLight'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostSaveCheckInfo'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostPartialBINDll'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostACHStateCodes'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 98
      end
      item
        Name = 'HostTruncateAcctNo'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostFTPUserName'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 40
      end
      item
        Name = 'HostFTPIpAddr'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 40
      end
      item
        Name = 'HostFTPPassword'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 40
      end
      item
        Name = 'HostFTPFileName'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 24
      end
      item
        Name = 'HostPONumberUsed'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostPlatformID'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostClearingCode'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 6
      end
      item
        Name = 'HostMerchBIN'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 12
      end
      item
        Name = 'HostMerchICA'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 12
      end
      item
        Name = 'HostSigCapMode'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostPersonalCk'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostPayrollCk'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostIPLocalPort1'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 40
      end
      item
        Name = 'HostIPLocalPort2'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 40
      end
      item
        Name = 'HostIPLocalPort3'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 40
      end
      item
        Name = 'HostERCLocalPort'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 10
      end
      item
        Name = 'HostFTPBINDays'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 6
      end
      item
        Name = 'HostLastBINFtpDate'
        Attributes = [faUnNamed]
        DataType = ftDateTime
      end
      item
        Name = 'HostSettleRpt'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostTenderOffline'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 20
      end
      item
        Name = 'HostUnused1'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 20
      end
      item
        Name = 'HostProgramId'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 6
      end
      item
        Name = 'HostFTPDir'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 200
      end
      item
        Name = 'HostERCUse'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostERCTransferType'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 2
      end
      item
        Name = 'HostERCFTPIpAddr'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 40
      end
      item
        Name = 'HostERCFTPPort'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 10
      end
      item
        Name = 'HostERCFTPBackupIpAddr'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 40
      end
      item
        Name = 'HostERCFTPBackupPort'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 10
      end
      item
        Name = 'HostERCFTPUserName'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 40
      end
      item
        Name = 'HostERCFTPPassword'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 40
      end
      item
        Name = 'HostERCFTPFileName'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 24
      end
      item
        Name = 'HostERCFTPDir'
        Attributes = [faUnNamed]
        DataType = ftString
        Size = 200
      end>
    IndexDefs = <
      item
        Name = 'DEFAULT_ORDER'
      end
      item
        Name = 'CHANGEINDEX'
      end>
    Params = <>
    StoreDefs = True
    Left = 16
    Top = 16
  end
  object cProc: TClientDataSet
    Aggregates = <>
    FieldDefs = <
      item
        Name = 'DSProcAuto'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcAoff'
        DataType = ftString
        Size = 4
      end
      item
        Name = 'DSProcStartHH'
        DataType = ftString
        Size = 4
      end
      item
        Name = 'DSProcStartMM'
        DataType = ftString
        Size = 4
      end
      item
        Name = 'DSProcTrainID'
        DataType = ftString
        Size = 18
      end
      item
        Name = 'DSProcStopHH'
        DataType = ftString
        Size = 4
      end
      item
        Name = 'DSProcStopMM'
        DataType = ftString
        Size = 4
      end
      item
        Name = 'DSProcArchDays'
        DataType = ftString
        Size = 4
      end
      item
        Name = 'DSProcCutHH'
        DataType = ftString
        Size = 4
      end
      item
        Name = 'DSProcCutMM'
        DataType = ftString
        Size = 4
      end
      item
        Name = 'DSProcTrackCkr'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcLPort'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcBaud'
        DataType = ftString
        Size = 6
      end
      item
        Name = 'DSProcLBits'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcLParity'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcLStop'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcLIRQ'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcTermIPport'
        DataType = ftString
        Size = 16
      end
      item
        Name = 'DSProcPrintLog'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcPrintTrx'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcPrintRpt'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcTermTrace'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcFormFeedUnused'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcDupAcctOnly'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProc490type'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcReportDone'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcHide'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcDisableAlarm'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcCmdMsgIPportUnused'
        DataType = ftString
        Size = 10
      end
      item
        Name = 'DSProcExportTranData'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcOnlineVerify'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcOnlineConvert'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcOfflineChoice'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcOfflineConvert'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcEncryption'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcJournalDaysPOSUnused'
        DataType = ftString
        Size = 4
      end
      item
        Name = 'DSProcJournalDaysEPSUnused'
        DataType = ftString
        Size = 4
      end
      item
        Name = 'DSProcUseJournalService'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcJournalPort'
        DataType = ftString
        Size = 10
      end
      item
        Name = 'DSProcSuspendJournaling'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcPumpIncrement'
        DataType = ftString
        Size = 4
      end
      item
        Name = 'DSProcSigLineYN'
        DataType = ftString
        Size = 2
      end
      item
        Name = 'DSProcSigLineAmt'
        DataType = ftString
        Size = 6
      end
      item
        Name = 'DSProcPrinterNorm'
        DataType = ftString
        Size = 8
      end
      item
        Name = 'DSProcPumpAllowedTenders'
        DataType = ftString
        Size = 12
      end
      item
        Name = 'DSProcPumpDefaultTender'
        DataType = ftString
        Size = 15
      end
      item
        Name = 'DSProcSSLPort'
        DataType = ftString
        Size = 20
      end
      item
        Name = 'DSProcOfflineBlackhawkReceiptVerbiage'
        DataType = ftString
        Size = 20
      end>
    IndexDefs = <>
    Params = <>
    StoreDefs = True
    Left = 64
    Top = 16
    object cProcDSProcAuto: TStringField
      FieldName = 'DSProcAuto'
      Size = 2
    end
    object cProcDSProcAoff: TStringField
      FieldName = 'DSProcAoff'
      Size = 4
    end
    object cProcDSProcStartHH: TStringField
      FieldName = 'DSProcStartHH'
      Size = 4
    end
    object cProcDSProcStartMM: TStringField
      FieldName = 'DSProcStartMM'
      Size = 4
    end
    object cProcDSProcTrainID: TStringField
      FieldName = 'DSProcTrainID'
      Size = 18
    end
    object cProcDSProcStopHH: TStringField
      FieldName = 'DSProcStopHH'
      Size = 4
    end
    object cProcDSProcStopMM: TStringField
      FieldName = 'DSProcStopMM'
      Size = 4
    end
    object cProcDSProcArchDays: TStringField
      FieldName = 'DSProcArchDays'
      Size = 4
    end
    object cProcDSProcCutHH: TStringField
      FieldName = 'DSProcCutHH'
      Size = 4
    end
    object cProcDSProcCutMM: TStringField
      FieldName = 'DSProcCutMM'
      Size = 4
    end
    object cProcDSProcTrackCkr: TStringField
      FieldName = 'DSProcTrackCkr'
      Size = 2
    end
    object cProcDSProcLPort: TStringField
      FieldName = 'DSProcLPort'
      Size = 2
    end
    object cProcDSProcBaud: TStringField
      FieldName = 'DSProcBaud'
      Size = 6
    end
    object cProcDSProcLBits: TStringField
      FieldName = 'DSProcLBits'
      Size = 2
    end
    object cProcDSProcLParity: TStringField
      FieldName = 'DSProcLParity'
      Size = 2
    end
    object cProcDSProcLStop: TStringField
      FieldName = 'DSProcLStop'
      Size = 2
    end
    object cProcDSProcLIRQ: TStringField
      FieldName = 'DSProcLIRQ'
      Size = 2
    end
    object cProcDSProcTermIPport: TStringField
      FieldName = 'DSProcTermIPport'
      Size = 16
    end
    object cProcDSProcPrintLog: TStringField
      FieldName = 'DSProcPrintLog'
      Size = 2
    end
    object cProcDSProcPrintTrx: TStringField
      FieldName = 'DSProcPrintTrx'
      Size = 2
    end
    object cProcDSProcPrintRpt: TStringField
      FieldName = 'DSProcPrintRpt'
      Size = 2
    end
    object cProcDSProcTermTrace: TStringField
      FieldName = 'DSProcTermTrace'
      Size = 2
    end
    object cProcDSProcFormFeedUnused: TStringField
      FieldName = 'DSProcFormFeedUnused'
      Size = 2
    end
    object cProcDSProcDupAcctOnly: TStringField
      FieldName = 'DSProcDupAcctOnly'
      Size = 2
    end
    object cProcDSProc490type: TStringField
      FieldName = 'DSProc490type'
      Size = 2
    end
    object cProcDSProcReportDone: TStringField
      FieldName = 'DSProcReportDone'
      Size = 2
    end
    object cProcDSProcHide: TStringField
      FieldName = 'DSProcHide'
      Size = 2
    end
    object cProcDSProcDisableAlarm: TStringField
      FieldName = 'DSProcDisableAlarm'
      Size = 2
    end
    object cProcDSProcCmdMsgIPportUnused: TStringField
      FieldName = 'DSProcCmdMsgIPportUnused'
      Size = 10
    end
    object cProcDSProcExportTranData: TStringField
      FieldName = 'DSProcExportTranData'
      Size = 2
    end
    object cProcDSProcOnlineVerify: TStringField
      FieldName = 'DSProcOnlineVerify'
      Size = 2
    end
    object cProcDSProcOnlineConvert: TStringField
      FieldName = 'DSProcOnlineConvert'
      Size = 2
    end
    object cProcDSProcOfflineChoice: TStringField
      FieldName = 'DSProcOfflineChoice'
      Size = 2
    end
    object cProcDSProcOfflineConvert: TStringField
      FieldName = 'DSProcOfflineConvert'
      Size = 2
    end
    object cProcDSProcEncryption: TStringField
      FieldName = 'DSProcEncryption'
      Size = 2
    end
    object cProcDSProcJournalDaysPOSUnused: TStringField
      FieldName = 'DSProcJournalDaysPOSUnused'
      Size = 4
    end
    object cProcDSProcJournalDaysEPSUnused: TStringField
      FieldName = 'DSProcJournalDaysEPSUnused'
      Size = 4
    end
    object cProcDSProcUseJournalService: TStringField
      FieldName = 'DSProcUseJournalService'
      Size = 2
    end
    object cProcDSProcJournalPort: TStringField
      FieldName = 'DSProcJournalPort'
      Size = 10
    end
    object cProcDSProcSuspendJournaling: TStringField
      FieldName = 'DSProcSuspendJournaling'
      Size = 2
    end
    object cProcDSProcPumpIncrement: TStringField
      FieldName = 'DSProcPumpIncrement'
      Size = 4
    end
    object cProcDSProcSigLineYN: TStringField
      FieldName = 'DSProcSigLineYN'
      Size = 2
    end
    object cProcDSProcSigLineAmt: TStringField
      FieldName = 'DSProcSigLineAmt'
      Size = 6
    end
    object cProcDSProcPrinterNorm: TStringField
      FieldName = 'DSProcPrinterNorm'
      Size = 8
    end
    object cProcDSProcPumpAllowedTenders: TStringField
      FieldName = 'DSProcPumpAllowedTenders'
      Size = 12
    end
    object cProcDSProcPumpDefaultTender: TStringField
      FieldName = 'DSProcPumpDefaultTender'
      Size = 15
    end
    object cProcDSProcSSLPort: TStringField
      FieldName = 'DSProcSSLPort'
    end
    object cProcDSProcRemoveOfflinesFromActlogAtEOD: TStringField
      FieldKind = fkCalculated
      FieldName = 'DSProcRemoveOfflinesFromActlogAtEOD'
      Calculated = True
    end
  end
end
