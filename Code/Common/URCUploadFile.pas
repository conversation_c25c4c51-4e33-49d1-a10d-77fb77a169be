// (c) MTXEPS, Inc. 1988-2008
Unit URCUploadFile;
(*
v815.3 04-20-05 TSL-02 Add TenderType and TransactionType
v815.1 01-11-05 TSL-01 Add CardType
*)
interface

uses
  FinalizationLog,
  Classes, SysUtils, UXMLCommon, mtx_lib;

const
  SReceiptCaptureUploadFile = 'ReceiptCaptureUploadFile';
  SHeader = 'Header';
  SCompany = 'Company';
  SStore = 'Store';
  SWinEPSIPAddress = 'WinEPSIPAddress';
  SReceipts = 'Receipts';
  SCount = 'count';
  SRecord = 'Record';
  SAcct = 'Acct';
  SExp = 'Exp';
  SAmt = 'Amt';
  SSeq = 'Seq';
  STime = 'Time';
  SDate = 'Date';
  SLane = 'Lane';
  SRetrieval = 'Retrieval';
  SAuthCode = 'AuthCode';
  SHostRef = 'HostRef';
  STenderType = 'TenderType';
  STransactionType = 'TransactionType';
  SCashier = 'Cashier';
  SReceipt = 'Receipt';
  SSig = 'Sig';
  SbinaryLength = 'binaryLength';
  Sxmlns = 'xmlns';
  SEncoded = 'Encoded';
  SRECVersion = 'RECVersion';
  SSigType = 'sigType';
  SEODTime = 'EODTime';
  SNumTrans = 'NumTrans';
  SComments = 'Comments';
  SCardType = 'CardType';

type
  TRCItem = class;
  TRCUploadFile = class(TXMLConfiguration)
  private
    FItems: TList;
    function GetItem(Index: Integer): TRCItem;
    function GetCount: Integer;
  public
    Company: string;
    Store: string;
    WinEPSIPAddress: string;
    RECVersion: string;
    EODTime: string;
    NumTrans: string;
    Comments: string;
    constructor Create;
    destructor Destroy; override;
    //procedure SaveToXML(AFileName: string); // XE: Remove WinEPS - not for OpenEPS
    function CreateReceiptsNode(Root: TXMLParserNode): TXMLParserNode;
    function GetReceiptsXMLStr: string;                                         
    procedure Clear;
    procedure Add(ARCItem: TRCItem);
    procedure Remove(ARCItem: TRCItem);
    property Items[Index: Integer]: TRCItem read GetItem; default;
    property Count: Integer read GetCount;
  end;

  TRCItem = class(TObject)
  private
    FRCUploadFile: TRCUploadFile;
  public
    Acct: string;
    Exp: string;
    Amt: string;
    Seq: string;
    Time: string;
    Date: string;
    Lane: string;
    Retrieval: string;
    AuthCode: string;
    HostRef: string;
    Cashier: string;
    TenderType: string;
    TransactionType: string;
    Receipt: string;
    Sig: string;
    CardType: string;
    SigLength: Integer;
    SigType: Integer;
    constructor Create(ARCUploadFile: TRCUploadFile);
    destructor Destroy; override;
  end;

implementation

{ TRCUploadFile }

procedure TRCUploadFile.Add(ARCItem: TRCItem);
begin
  FItems.Add(ARCItem);
end;

procedure TRCUploadFile.Clear;
begin
  while FItems.Count > 0 do TRCItem(FItems.Last).Free;
end;

constructor TRCUploadFile.Create;
begin
  inherited;
  FItems := TList.Create;
end;

destructor TRCUploadFile.Destroy;
begin
  Clear;
  FItems.Free;
  inherited;
end;

function TRCUploadFile.GetCount: Integer;
begin
  Result := FItems.Count;
end;

function TRCUploadFile.GetItem(Index: Integer): TRCItem;
begin
  Result := FItems[Index];
end;

procedure TRCUploadFile.Remove(ARCItem: TRCItem);
begin
  FItems.Remove(ARCItem);
end;

{ // XE: Remove WinEPS - not for OpenEPS
procedure TRCUploadFile.SaveToXML(AFileName: string);
var
  Root, Node, Node2: TXMLParserNode;
begin
  Root := TXMLParserNode.Create(nil);
  try
    Root.Name := SReceiptCaptureUploadFile;
    Root.Attr.Values[Sxmlns] := 'xsdReceiptFile';
    Root.Attr.Values[SRECVersion] := RECVersion;
    Node := Root.AddChild(SHeader);
    Node2 := Node.AddChild(SCompany);
    Node2.Text := Company;
    Node2 := Node.AddChild(SStore);
    Node2.Text := Store;
    Node2 := Node.AddChild(SWinEPSIPAddress);
    Node2.Text := WinEPSIPAddress;
    Node2 := Node.AddChild(SEODTime);
    Node2.Text := EODTime;
    Node2 := Node.AddChild(SNumTrans);
    Node2.Text := NumTrans;
    Node2 := Node.AddChild(SComments);
    Node2.Text := Comments;
    CreateReceiptsNode(Root);
    Root.SaveToFile(AFileName);
  finally
    Root.Free;
  end;
end;
}

function TRCUploadFile.CreateReceiptsNode(Root: TXMLParserNode): TXMLParserNode;
var
  Node, Node2, Node3, Node4: TXMLParserNode;
  i: Integer;
begin
  result := nil;
  try
    Node := Root.AddChild(SReceipts);
    result := Node;
    Node.Attr.Values[SCount] := IntToStr(Count);
    for i := 0 to Count - 1 do
    begin
      Node2 := Node.AddChild(SRecord);
      Node3 := Node2.AddChild(SAcct);
      Node3.Text := Items[i].Acct;
      Node3 := Node2.AddChild(SExp);
      Node3.Text := Items[i].Exp;
      Node3 := Node2.AddChild(SAmt);
      Node3.Text := Items[i].Amt;
      Node3 := Node2.AddChild(SSeq);
      Node3.Text := Items[i].Seq;
      Node3 := Node2.AddChild(STime);
      Node3.Text := Items[i].Time;
      Node3 := Node2.AddChild(SDate);
      Node3.Text := Items[i].Date;
      Node3 := Node2.AddChild(SLane);
      Node3.Text := Items[i].Lane;
      Node3 := Node2.AddChild(SRetrieval);
      Node3.Text := Items[i].Retrieval;
      Node3 := Node2.AddChild(SAuthCode);
      Node3.Text := Items[i].AuthCode;
      Node3 := Node2.AddChild(SHostRef);
      Node3.Text := Items[i].HostRef;
      Node3 := Node2.AddChild(SCashier);
      Node3.Text := Items[i].Cashier;
      Node3 := Node2.AddChild(SCardType);
      Node3.Text := Items[i].CardType;
      Node3 := Node2.AddChild(STenderType);
      Node3.Text := Items[i].TenderType;
      Node3 := Node2.AddChild(STransactionType);
      Node3.Text := Items[i].TransactionType;
      Node3 := Node2.AddChild(SReceipt);
      Node3.Text := Items[i].Receipt;
      Node3 := Node2.AddChild(SSig);
      Node3.Attr.Values[SbinaryLength] := IntToStr(Items[i].SigLength);
      Node3.Attr.Values[SSigType] := IntToStr(Items[i].SigType);

      Node4 := Node3.AddChild(SEncoded);
      Node4.Text := Items[i].Sig;
    end;
  except
    on e: exception do
      sm('Try..Except: URCUploadFile.CreateReceiptsNode ' + e.message);
  end;
end;

function TRCUploadFile.GetReceiptsXMLStr: string;                               
var
  Root, Node: TXMLParserNode;
begin
  result := '';
  try
    Root := TXMLParserNode.Create(nil);
    try
      Node := CreateReceiptsNode(Root);
      result := Node.GetXMLStr;
    finally
      Root.Free;
    end;
  except
    on e: exception do
      sm('Try..Except: URCUploadFile.GetReceiptsXMLStr ' + e.message);
  end;
end;

{ TRCItem }

constructor TRCItem.Create(ARCUploadFile: TRCUploadFile);
begin
  inherited Create;
  FRCUploadFile := ARCUploadFile;
  if Assigned(FRCUploadFile) then FRCUploadFile.Add(Self);
end;

destructor TRCItem.Destroy;
begin
  if Assigned(FRCUploadFile) then FRCUploadFile.Remove(Self);
  inherited;
end;

initialization
  ExtendedLog('URCUploadFile Initialization');
finalization
  ExtendedLog('URCUploadFile Finalization');

end.

