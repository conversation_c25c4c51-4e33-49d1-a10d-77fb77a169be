// (c) MTXEPS, Inc. 1988-2008
unit UPLUMessageFile;

interface

uses
  FinalizationLog,
  Classes, SysUtils, LibXmlParser, mtx_constants;

const
  SPLUMessageFileName = 'PrepaidWirelessText.xml';
  SPLU = 'PLU';
  SId = 'Id';
  SLine = 'Line';
  
function GetPLUMessage(const PLU: string; AMessage: TStrings): Boolean; { return False if message can not be found }

implementation

type
  TXMLNode = class(TObject)
  private
    FParent: TXMLNode;
    FChildren: TList;
    FAttr: TStrings;
    FText: string;
    FName: string;
  public
    constructor Create(AParent: TXMLNode);
    destructor Destroy; override;
    procedure FreeChildren;
    property Name: string read FName write FName;
    property Text: string read FText write FText;
    property Attr: TStrings read FAttr;
    property Children: TList read FChildren;
  end;

{ TXMLNode }

constructor TXMLNode.Create(AParent: TXMLNode);
begin
  inherited Create;
  FParent := AParent;
  if Assigned(FParent) then FParent.Children.Add(Self);
  FAttr := TStringList.Create;
  FChildren := TList.Create;
end;

destructor TXMLNode.Destroy;
begin
  if Assigned(FParent) then FParent.Children.Remove(Self);
  FreeChildren;
  FreeAndNil(FChildren);
  FreeAndNil(FAttr);
  inherited;
end;

procedure TXMLNode.FreeChildren;
begin
  while FChildren.Count > 0 do TXMLNode(FChildren.Last).Free; 
end;

function GetPLUMessage(const PLU: string; AMessage: TStrings): Boolean;
var
  XMLParser: TXMLParser;
  Root, Node, Node2: TXMLNode;
  i, j: Integer;

  procedure ScanElement(AParent: TXMLNode);
  var
    Node: TXMLNode;
    i: Integer;
  begin
    Node := nil;
    while XMLParser.Scan do
    begin
      case XmlParser.CurPartType of
        ptXmlProlog : ;
        ptDtdc      : ;
        ptStartTag,
        ptEmptyTag  : begin
                        Node := TXMLNode.Create(AParent);
                        Node.Name := XmlParser.CurName;
                        if AParent = nil then Root := Node;

                        if XmlParser.CurAttr.Count > 0 then
                        begin
                          for i := 0 to XmlParser.CurAttr.Count - 1 do
                          begin
                            Node.Attr.Values[XmlParser.CurAttr.Name(i)] := XmlParser.CurAttr.Value(i);
                          end;
                        end;

                        if XmlParser.CurPartType = ptStartTag then ScanElement(Node);
                      end;
        ptEndTag    : break;
        ptContent,
        ptCData     : begin
                        Node.Text := XmlParser.CurContent;
                      end;
        ptComment   : ;
        ptPI        : ;
      end;
    end;
  end;

begin
  Result := False;

  XMLParser := TXMLParser.Create;
  try
    XMLParser.LoadFromFile(WinEPSDir + SPLUMessageFileName);
    XMLParser.StartScan;
    ScanElement(nil);

    for i := 0 to Root.Children.Count - 1 do
    begin
      Node := Root.Children[i];
      if Node.Name = SPLU then
      begin
        if Node.Attr.Values[SId] = PLU then
        begin
          for j := 0 to Node.Children.Count - 1 do
          begin
            Node2 := Node.Children[j];
            if Node2.Name = SLine then AMessage.Add(Node2.Text);
          end;
          Result := True;
          break;
        end;
      end;
    end;

    FreeAndNil(Root);
  finally
    FreeAndNil(XMLParser);
  end;

end;

initialization
  ExtendedLog('UPLUMessageFile Initialization');
finalization
  ExtendedLog('UPLUMessageFile Finalization');

end.

