// (c) MTXEPS, Inc. 1988-2008
unit FleetU;

interface
{
v824.0 03-31-08 TSL-01 Add procedure SetMaxFuelAndNonFuelItems to be used by WEX, too
************************************************************************* }

uses
  FinalizationLog,
  SysUtils, Classes, Math, MTX_Lib, MTX_Constants;

type
  TFleetCard = class
    Track2Data: string;
    ManualEntry: boolean;
    ExpDate: string;          { YYMM }
    RestrictionCode: integer;
    VehicleID: string;
    FleetType: integer;

    CallFleetAgain,
    PromptForExpDate,
    PromptForVehicleID,
    PromptForOdometer,
    PromptForDriverID,
    PromptForRestrictionCode: boolean;

    MaxFuelItems,
    MaxNonFuelItems,
    MaxTotalItems: integer;

    constructor Create(inFleetType: integer; inTrack2data: string);
    destructor Destroy; override;
    procedure Resubmit;
    function TranslateRestrictionCode(code: integer): integer;
    //function ConvertFleetProductData(host: string3; var productData: array500c): array500c; // XE: Remove WinEPS - not for OpenEPS
    //function GetFuelAmountATL(var productData: array500c): integer; // XE: Remove WinEPS - not for OpenEPS
  private
    Separator: integer;
    EndSentinel: integer;
    fleetProducts: TStringList;
    procedure Reset;
    procedure SetMaxFuelAndNonFuelItems(aCode: integer);
    procedure Translate;
  end;

const
  { OpenEPS Product Restriction Codes }
  FUEL_ONLY = 1;
  FUEL_AND_OTHER = 2;

  { Fleet Types }
  ftNone          = 0;
  ftMasterCard    = 1;
  ftVoyager       = 2;
  ftWrightExpress = 3;
  ftVisa          = 4;
  ftFleetOne      = 5;
  ftFleetwide     = 6;
  MAX_FLEET_TYPES = 7;

  FleetNames: array [0..MAX_FLEET_TYPES-1] of string =
    ('None','MasterCard','Voyager','Wright Express','Visa','FleetOne','Fleetwide');

  { decline codes }
  dcOK                   = 0;
  dcAcctNumLengthFailure = 1;
  dcMod10Failure         = 2;

  Hex1D = #$1D;

var
  validChars : set of char = ['0','1','2','3','4','5','6','7','8','9'];

implementation

constructor TFleetCard.Create(inFleetType: integer; inTrack2data: string);
begin
  inherited Create;
  Reset;
  FleetType := inFleetType;
  Track2Data := inTrack2data;
  Separator := pos('=',Track2Data);
  ManualEntry := Separator <= 0;
  EndSentinel := pos('?',Track2Data);
  if EndSentinel = 0                             // JTG we are missing the End Sentinel
    then EndSentinel := length(Track2Data) + 1;  // JTG where it would have been ...
  Translate;
end;

destructor TFleetCard.Destroy;
begin
  Reset;
  inherited;
end;

procedure TFleetCard.Reset;
begin
  Track2Data := StringOfChar(' ', 40);
  FleetType := ftNone;
  ManualEntry := false;
  ExpDate := '';
  RestrictionCode := -1;
  VehicleID := '';
  CallFleetAgain := false;
  PromptForExpDate := false;
  PromptForVehicleID := false;
  PromptForOdometer := false;
  PromptForDriverID := false;
  PromptForRestrictionCode := false;
  MaxFuelItems := 0;
  MaxNonFuelItems := 0;
  MaxTotalItems := 0;
end;

procedure TFleetCard.SetMaxFuelAndNonFuelItems(aCode: integer);
begin
  MaxFuelItems := 999;
  if (aCode = FUEL_ONLY)
    then MaxNonFuelItems := 0
    else MaxNonFuelItems := 999;
  MaxTotalItems := 999;
end;

procedure TFleetCard.Translate;
begin
  try
    case FleetType of
      ftVoyager:
      begin
        if ManualEntry then
        begin   // perform 2 checks
          PromptForExpDate := true;
          PromptForRestrictionCode := true;
        end
        else
        begin   { change MMYY to YYMM (internal use only) }
          ExpDate := Copy(Track2Data, 23, 2) + Copy(Track2Data, 21, 2);
          case StrToIntDef(Copy(Track2Data, 25, 1), -1) of  { prompt restriction }
            0:  ;
            1:  PromptForDriverID := true;
            2:  PromptForOdometer := true;
            3:  begin
                PromptForDriverID := true;
                PromptForOdometer := true;
                end;
          end;
          case StrToIntDef(Copy(Track2Data, 26, 1), -1) of  { fuel restriction }
            0:  RestrictionCode := FUEL_AND_OTHER;
            1:  RestrictionCode := FUEL_ONLY;
          end;
        end;
        SetMaxFuelAndNonFuelItems(RestrictionCode);
      end;
      ftWrightExpress:
        begin
        if StrToIntDef(Copy(Track2Data, 11, 2), -1) = 0 then
          begin
          PromptForDriverID := true;
          PromptForOdometer := true;
          end;
        if ManualEntry then
          begin
          // verify account # is 13 digits - done with card prefix table
          // mod10 check is done in fct.pas

          PromptForExpDate := true;
          RestrictionCode := FUEL_AND_OTHER;
          PromptForVehicleID := true;
//              Track2Data := '690046' + Copy(Track2Data, 7, 13);
          end
        else
          begin
          ExpDate := Copy(Track2Data, 21, 4);
          if StrToIntDef(Copy(Track2Data, 26, 2), 0) = 0
            then RestrictionCode := FUEL_ONLY
            else RestrictionCode := FUEL_AND_OTHER;
          VehicleID := Copy(Track2Data, 28, 5);
          end;
        SetMaxFuelAndNonFuelItems(RestrictionCode);
        end;
      ftMastercard:
        begin
        if ManualEntry then
          begin
          PromptForExpDate := true;
          RestrictionCode := FUEL_AND_OTHER;
          PromptForDriverID := true;
          PromptForOdometer := true;
          end
        else
          begin
          ExpDate := Copy(Track2Data, 18, 4);
          case StrToIntDef(Copy(Track2Data, 36, 1), 0) of
            1:  RestrictionCode := FUEL_AND_OTHER;
            2:  RestrictionCode := FUEL_ONLY
          end;
          case StrToIntDef(Copy(Track2Data, 37, 1), 0) of
            1:  begin
                PromptForDriverID := true;
                PromptForOdometer := true;
                end;
            2:  begin
                PromptForVehicleID := true;
                PromptForOdometer := true;
                end;
            3:  begin
                PromptForDriverID := true;
                PromptForOdometer := true;
                end;
            4:  begin
                PromptForOdometer := true;
                end;
            5:  ; { nothing needed }
          end;
          end;
        MaxFuelItems := 999;
        if RestrictionCode = FUEL_ONLY
          then MaxNonFuelItems := 0
          else MaxNonFuelItems := 999;
        MaxTotalItems := 999;
        end;
      ftVisa:     // JTG: Dev-4536
        begin
        if ManualEntry then
          begin
          PromptForExpDate := true;
          RestrictionCode := FUEL_AND_OTHER;
          PromptForDriverID := true;
          PromptForOdometer := true;
          end
        else
          begin
          ExpDate := Copy(Track2Data,Separator+1,4);
          case StrToIntDef(Copy(Track2Data,EndSentinel-2,1),0) of
            0:    RestrictionCode := FUEL_AND_OTHER;
            1,2:  RestrictionCode := FUEL_ONLY
          end;
          case StrToIntDef(Copy(Track2Data,EndSentinel-1,1),0) of
            1:  begin
                PromptForDriverID := true;
                PromptForOdometer := true;
                end;
            2:  begin
                PromptForVehicleID := true;
                PromptForOdometer := true;
                end;
            3:  begin
                PromptForDriverID := true;
                PromptForOdometer := true;
                end;
            4:  PromptForOdometer := true;
            5:  ; { nothing needed }
            6:  PromptForDriverID := true;
          end;
          end;
        MaxFuelItems := 999;
        if RestrictionCode = FUEL_ONLY
          then MaxNonFuelItems := 0
          else MaxNonFuelItems := 999;
        MaxTotalItems := 999;
        end;
      ftFleetOne: // JMR: DOEP-38607
        begin
        RestrictionCode := FUEL_AND_OTHER;
        PromptForVehicleID := true;
        PromptForOdometer := true;
        if ManualEntry then
          begin

          end
        else
          begin
          ExpDate := Copy(Track2Data,Separator+1,4);
          end;
        MaxFuelItems := 999;
        MaxNonFuelItems := 999;
        MaxTotalItems := 999;
        end;
      ftFleetwide: // JRM: DOEP-38607
        begin
        RestrictionCode := FUEL_AND_OTHER;
        PromptForDriverID := true;
        PromptForOdometer := true;
        if ManualEntry then
          begin
          PromptForExpDate := true;
          end
        else
          begin
          ExpDate := Copy(Track2Data,Separator+1,4);
          end;
        MaxFuelItems := 999;
        MaxNonFuelItems := 999;
        MaxTotalItems := 999;
        end;
    end;
  except
    on e : Exception do
    ;
//      SM('TFleetCard.Translate exception.  ' + e.message);
  end;
end; { TFleetCard.Translate }

(* // XE: Remove WinEPS - not for OpenEPS
function TFleetCard.GetFuelAmountATL(var productData: array500c): integer;
var
  oldLine,
  tempData : string;
  productCode,
  total: integer;
begin
  result := 0;
  tempData := productData;
  while Length(tempData) >= 34 do
  begin
    oldLine := Copy(tempData, 1, 34);
    tempData := Copy(tempData, 35, Length(tempdata)-34);
    if Length(tempData) > 0 then
      if (tempData[1] = Hex1D) then
        tempData := Copy(tempData, 2, Length(tempdata)-1);
    productCode := StrToIntDef(Copy(oldLine, 1, 4), 0);

    if ((productCode >= 1) and
        (productCode <= 99)) then
      begin
        total := strtointdef(Copy(oldLine, 23, 11), 0); { product amount (2 decimal places, so leave off 3rd decimal place) }
        result := result + total;
      end;
  end;
end;
*)

(* // XE: Remove WinEPS - not for OpenEPS
function TFleetCard.ConvertFleetProductData(host: {DLLTypes.}string3; var productData: {DLLTypes.}array500c): {DLLTypes.}array500c;
var
  oldLine,
  newLine,
  resultStr,
  header,
  tempData: string;

  function GetNACS(fleetCode: integer) : integer;
  var
    i,
    j: integer;
    first,
    second,
    answer: string;
  begin
    result := fleetCode;            // if we don't find it, this will return what came in as a pass thru
    if fleetProducts.Count > 0 then
    begin
      for i := 0 to (fleetProducts.Count - 1) do
      begin
        j := 1;
        first := '';
        while fleetProducts[i][j] in validChars do
        begin
          first := first + fleetProducts[i][j];
          Inc(j);
        end;
        if fleetCode < StrToIntDef(first, 0) then
          exit;
        if fleetCode = StrToIntDef(first, 0) then
        begin
          while fleetProducts[i][j] <> ':' do
            Inc(j);
          Inc(j);
          answer := '';
          while fleetProducts[i][j] in validChars do
          begin
            answer := answer + fleetProducts[i][j];
            Inc(j);
          end;
          result := StrToIntDef(answer, 0);
          break;
        end
        else
          if fleetProducts[i][j] = '-' then
          begin
            Inc(j);
            second := '';
            while fleetProducts[i][j] in validChars do
            begin
              second := second + fleetProducts[i][j];
              Inc(j);
            end;
            if fleetCode <= StrToIntDef(second, -1) then
            begin
              Inc(j);
              answer := '';
              while fleetProducts[i][j] in validChars do
              begin
                answer := answer + fleetProducts[i][j];
                Inc(j);
              end;
              result := StrToIntDef(answer, -1);
              break;
            end;
          end;
      end;
    end;
  end;

begin { ConvertFleetProductData }
  result := '';
  resultStr := '';
  fleetProducts := TStringList.Create;
  try
    fleetProducts.Sorted := true;
    if FileExists('FleetProduct.' + host) then
      fleetProducts.LoadFromFile('FleetProduct.' + host);
    tempData := productData;
    if UpperCase(host) = 'ACI' then
    begin
      while Length(tempData) >= 34 do
      begin
        oldLine := Copy(tempData, 1, 34);
        tempData := Copy(tempData, 35, Length(tempdata)-34);
        newLine := ZFill(IntToStr(GetNACS(StrToIntDef(Copy(oldLine, 0, 4), 0))), 3);     { category code }
        newLine := newLine + ZFill(IntToStr(StrToIntDef(Copy(oldLine, 5, 8), 0)), 6);    { quantity }
        newLine := newLine + Copy(oldLine, 22, 1);                                       { sign }
        newLine := newLine + ZFill(IntToStr(StrToIntDef(Copy(oldLine, 14, 8), 0)), 12);  { unit price }
        newLine := newLine + Copy(oldLine, 22, 1);                                       { sign }
        newLine := newLine + ZFill(IntToStr(StrToIntDef(Copy(oldLine, 23, 12), 0)), 12); { amount }
        resultStr := resultStr + newLine;
        if Length(tempData) > 0 then
          if tempData[1] = Hex1D then
            tempData := Copy(tempData, 2, Length(tempdata)-1);
      end;
    end;
    if UpperCase(host) = 'ATL' then
    begin
      header := 'S';                                                     { service level } // todo: 'S' if pump, else 'O'.
      header := header + ZFill(IntToStr(Length(tempData) div 34), 2);    { # of products in this trx }
      while Length(tempData) >= 34 do
      begin
        newLine := '';
        oldLine := Copy(tempData, 1, 34);
        tempData := Copy(tempData, 35, Length(tempdata)-34);
        if Length(resultStr) > 0 then
          newLine := '\';
        newLine := newLine + ZFill(IntToStr(GetNACS(StrToIntDef(Copy(oldLine, 1, 4), 0))), 3);  { product code }
        newLine := newLine + Copy(oldLine, 13, 1);                                  { unit of measure }
        newLine := newLine + '3' + IntToStr(StrToIntDef(Copy(oldLine, 5, 8), 0));   { quantity (always 3 decimal places) }
        newLine := newLine + '\';
        newLine := newLine + '3' + IntToStr(StrToIntDef(Copy(oldLine, 14, 8), 0));  { unit price (always 3 decimal places) }
        newLine := newLine + '\';
        newLine := newLine + Copy(oldLine, 23, 11);  { product amount (2 decimal places, so leave off 3rd decimal place) }
        if resultStr = '' then
          resultStr := header;
        resultStr := resultStr + newLine;
        if Length(tempData) > 0 then
          if (tempData[1] = Hex1D) then
            tempData := Copy(tempData, 2, Length(tempdata)-1);
      end;
    end;
    Move(resultStr[1], result, Length(resultStr));
  finally
    fleetProducts.Free;
  end;
end; { ConvertFleetProductData }
*)

function TFleetCard.TranslateRestrictionCode(code: integer): integer;
{ Translate host specific Product Restriction Code to OpenEPS code. }
begin
  result := -1;
  try
    case FleetType of
      ftVoyager:
        begin
          case (code div 10) of
            0:  ;
            1:  PromptForDriverID := true;
            2:  PromptForOdometer := true;
            3:
              begin
                PromptForDriverID := true;
                PromptForOdometer := true;
              end;
          end;
          case (code mod 10) of
            0:  result := FUEL_AND_OTHER;
            1:  result := FUEL_ONLY;
          end;
          SetMaxFuelAndNonFuelItems(result);
        end;
      ftWrightExpress:
        begin
          case code of
            0: result := FUEL_ONLY;
            else
               result := FUEL_AND_OTHER;
          end;
          SetMaxFuelAndNonFuelItems(result);
        end;
      ftMastercard:
        case code of
          1:  result := FUEL_AND_OTHER;
          2:  result := FUEL_ONLY;
        end;
      ftVisa:          // JTG Dev-4536
        case code of
          0:    result := FUEL_AND_OTHER;
          1,2:  result := FUEL_ONLY;
        end;
    end;
    RestrictionCode := result;
  except
    on e : Exception do
    ;
//      SM('TFleetCard.TranslateRestrictionCode exception.  ' + e.message);
  end;
end; { TranslateRestrictionCode }


procedure TFleetCard.Resubmit;
begin

end;

initialization
  ExtendedLog('FleetU Initialization');
finalization
  ExtendedLog('FleetU Finalization');

end.
