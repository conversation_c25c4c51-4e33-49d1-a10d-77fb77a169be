// (c) MTXEPS, Inc. 1988-2008
unit encryptISO;
{
v825.1 11-21-08 TSL Take out printing to get rid of MTX_lib - so much garbage comes with it.
v820.0 09-20-06 TSL-01  Add encrypt and decrypt B_Record procedures
}

interface

uses
  {$IFNDEF LINUX}
  windows,
  {$ENDIF}
  sysutils, strUtils, 
  MTX_Constants, MTXEncryptionUtils;

function encryptIt(aBuf: B_Record): B_Record;
//function encryptItFile(aBuf: B_Record; aFileName: string): B_Record; // XE: Remove WinEPS - not for OpenEPS 
function decryptIt(aBuf: B_Record): B_Record;
{$IFNDEF WOLF}
function decryptItOld(aBuf: B_Record): B_Record;
{$ENDIF}
function setEncBuf(WinBuf: B_Record): B_Record;
function setWinBuf(EncBuf: B_Record): B_Record;
//function encryptB_Record(var EncBuf: B_Record): boolean; // XE: Remove WinEPS - not for OpenEPS
//function encryptB_RecordFile(var EncBuf: B_Record; aFileName: string): boolean; // XE: Remove WinEPS - not for OpenEPS
//procedure decryptB_Record(var DecBuf: B_Record); // XE: Remove WinEPS - not for OpenEPS

implementation

uses
  FinalizationLog,
  MRTypes;

{ note:  aBuf in each function assumes there are no sentinels or length bytes
  on the front.  For IP messageing between WinEPS and OpenEPS there are 4 bytes
  on the front of a message that are never encrypted.  These are
            sentinels (to distinguish the start of a message)
                MX    ( for unencrypted msg prior to ver 815.3 )
                EX    ( for encrypted msg ver 815.3 and later )
                ll    where ll are two length bytes in shor int PC format

  The routines below encrypt or decrypt an entire msg in B_Data array for a
  length of B_len.  So B_Len is the entire length of the msg to en/decrypt
  starting at element 1 of the one (not zero) based array.

  They return the encrypted or decrypted msg in B_Data for a length of B_len.
}

function encryptIt(aBuf: B_Record): B_Record;
var
  encryptStr: AnsiString;
  i: integer;
begin
  try
    try
      setLength(encryptStr, aBuf.B_Len);
      move(aBuf.B_data[1], encryptStr[1], aBuf.B_Len);
//      if sameText('MTX', leftStr(encryptStr, 3))      // i.e. is it a ServerEPS msg?
//        then encryptStr := MTXEncryptionUtils.EncryptStringOld(encryptStr)
//        else
      encryptStr := MTXEncryptionUtils.EncryptString(encryptStr);
      if ((aBuf.B_Len > 0) and (encryptStr = '')) then { encryption failed }
      begin
        for i := 1 to 3 do { try a few times to see if it will work }
          begin
          //SM('****WARNING: EncryptISO.EncryptIt.MTXEncryption.EncryptString returned a blank string!  Attempting to encrypt again. (' + IntToStr(i) + ')');
          encryptStr := MTXEncryptionUtils.EncryptString(encryptStr);
          if encryptStr <> '' then { it worked }
            break;
          end;
        //if encryptStr = '' then { multiple encryption attempts failed }
        //  SM('****ERROR: EncryptISO.EncryptIt FAILED TO ENCRYPT STRING!!!');
      end;
      fillChar(result, sizeOf(B_Record), 0);  { init result buffer in case len is zero }
      result.B_Len := length(encryptStr);
      move(encryptStr[1], result.B_data[1], result.B_Len);
    except
      on e : Exception do
        ;//SM('****Try..Except: EncryptISO.EncryptIt : ' + e.Message);
    end;
  finally
    fillchar(aBuf,sizeOf(aBuf), 0);     // PCI destroy incoming buffer, encryptStr is OK because it is encrypted
    aBuf.B_Data[1] := 0;
  end;
end;   { encryptIt }

(* // XE: Remove WinEPS - not for OpenEPS
function encryptItFile(aBuf: B_Record; aFileName: string): B_Record; 
var
  encryptStr : string;
  i: integer;
begin
  try
    try
      setLength(encryptStr, aBuf.B_Len);
      move(aBuf.B_data[1], encryptStr[1], aBuf.B_Len);

      encryptStr := MTXEncryptionUtils.EncryptString(encryptStr);
      if (encryptStr = '') then { encryption failed }
        begin
        for i := 1 to 3 do { try a few times to see if it will work }
          begin
          //SM('****WARNING: EncryptISO.encryptItFile.MTXEncryption.EncryptString returned a blank string!  Attempting to encrypt again. (' + IntToStr(i) + ')');
          encryptStr := MTXEncryptionUtils.EncryptString(encryptStr);
          if encryptStr <> '' then { it worked }
            break;
          end;
        //if encryptStr = '' then { multiple encryption attempts failed }
        //  SM('****ERROR: EncryptISO.EncryptIt FAILED TO ENCRYPT STRING!!!');
        end;
      fillChar(result, sizeOf(B_Record), 0);  { init result buffer in case len is zero }
      result.B_Len := length(encryptStr);
      move(encryptStr[1], result.B_data[1], result.B_Len);
    except
      on e : Exception do
        ;//SM('****Try..Except: EncryptISO.EncryptItFile : ' + e.Message);
    end;
  finally
    {$OPTIMIZATION OFF} // make sure optimizer doesn't ignore the cleanup code
    fillchar(aBuf,sizeOf(aBuf), 0);     // PCI destroy buffer
    {$OPTIMIZATION ON}
    //i := aBuf.B_Len;                    // to defeat optimization and make sure previous line is implemented by compiler // [Hint] encryptISO.pas(117): Value assigned to 'i' never used
  end;
end;   { encryptItFile }
*)

function decryptIt(aBuf: B_Record): B_Record;
var
  decryptStr: AnsiString;  //7934
begin
  setLength(decryptStr, aBuf.B_Len);
  move(aBuf.B_data[1], decryptStr[1], aBuf.B_Len);
  decryptStr := MTXEncryptionUtils.DecryptString(decryptStr);
  result.B_Len := length(decryptStr);
  move(decryptStr[1], result.B_data[1], result.B_Len);
  decryptStr := stringOfChar('X', length(decryptStr));        // PCI destroy buffer
end;   { decryptIt }

{$IFNDEF WOLF}
function decryptItOld(aBuf: B_Record): B_Record;
var decryptStr: AnsiString;          //7934
begin
  setLength(decryptStr, aBuf.B_Len);
  move(aBuf.B_data[1], decryptStr[1], aBuf.B_Len);
  decryptStr := MTXEncryptionUtils.DecryptStringOld(decryptStr);
  result.B_Len := length(decryptStr);
  move(decryptStr[1], result.B_data[1], result.B_Len);
  decryptStr := stringOfChar('X', length(decryptStr));        // PCI destroy buffer
end;   { decryptIt }
{$ENDIF}

function setEncBuf(WinBuf: B_Record): B_Record;
begin  { makes a buffer for en/decrypting from an OpenEPS/WinEPS send buffer }
  fillChar(result, sizeOf(B_Record), 0);  { first, init result buffer }
  move(WinBuf.B_data[5], result.B_Data[1], WinBuf.B_Len);
  result.B_Len := WinBuf.B_Len;  { the B_len is already less the sent and len bytes }
  fillChar(WinBuf, sizeOf(WinBuf), 0);    // PCI destroy buffer
  WinBuf.B_Len := ord(WinBuf.B_Data[1]);  // to defeat optimization and make sure previous line is implemented by compiler
end;

function setWinBuf(EncBuf: B_Record): B_Record;
begin  { make a WinEPS send Buffer from en/decrypt buffer by adding 4 bytes to front }
  fillChar(result, sizeOf(B_Record), 0);  { first, init result buffer }
  move(EncBuf.B_Data[1], result.B_Data[5], EncBuf.B_Len);
  result.B_Len := encBuf.B_Len;
  fillChar(encBuf, sizeOf(encBuf), 0);    // PCI destroy buffer
  encBuf.B_Len := ord(encBuf.B_Data[1]);  // to defeat optimization and make sure previous line is implemented by compiler
end;

(* // XE: Remove WinEPS - not for OpenEPS
function encryptB_Record(var EncBuf: B_Record): boolean; 
var encryptBuf : B_Record;   { return EncBuf has 4 blank bytes on front for EXnn }
begin
  result := true;
  encryptBuf := setEncBuf(EncBuf);     { set a buffer for encrypting }
  encryptBuf := encryptIt(encryptBuf); { encrypt it }
  if (encryptBuf.B_Len > 0) then
    EncBuf := setWinBuf(encryptBuf)        { set EncBuf with encrypted msg }
  else
  begin
    fillChar(EncBuf, sizeOf(B_Record), 0); { null buffer if no encryption  }
    encBuf.B_Len := ord(encBuf.B_Data[1]);  // to defeat optimization and make sure previous line is implemented by compiler
    result := false;
  end;
end;
*)

(* // XE: Remove WinEPS - not for OpenEPS
function encryptB_RecordFile(var EncBuf: B_Record; aFileName: string): boolean; 
var encryptBuf : B_Record;   { return EncBuf has 4 blank bytes on front for EXnn }
begin
  result := true;
  encryptBuf := setEncBuf(EncBuf);     { set a buffer for encrypting }
  encryptBuf := encryptItFile(encryptBuf, aFileName); { encrypt it }
  if (encryptBuf.B_Len > 0) then
    EncBuf := setWinBuf(encryptBuf)        { set EncBuf with encrypted msg }
  else
  begin
    fillChar(EncBuf, sizeOf(B_Record), 0); { null buffer if no encryption  }
    encBuf.B_Len := ord(encBuf.B_Data[1]);  // to defeat optimization and make sure previous line is implemented by compiler
    result := false;
  end;
end;
*)

(* // XE: Remove WinEPS - not for OpenEPS
procedure decryptB_Record(var DecBuf: B_Record); 
var decryptBuf : B_Record;  { return DecBuf has 4 blank bytes on front for EXnn}
begin
  decryptBuf := setEncBuf(DecBuf);
  decryptBuf := decryptIt(decryptBuf);
  DecBuf     := setWinBuf(decryptBuf);
  fillChar(decryptBuf, sizeOf(decryptBuf), 0);        // PCI destroy buffer
  decryptBuf.B_Len := ord(decryptBuf.B_Data[1]);  // to defeat optimization and make sure previous line is implemented by compiler
end;
*)

initialization
  ExtendedLog('encryptISO Initialization');
finalization
  ExtendedLog('encryptISO Finalization');
end.
