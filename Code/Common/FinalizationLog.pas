unit FinalizationLog;

interface

type
  TInitOrFinalProcedure = reference to procedure;

procedure ExtendedLog(const ASectionName: String; const AInitOrFinalProcedure: TInitOrFinalProcedure = nil);

implementation

uses
  <PERSON>ys<PERSON>tils, SyncObjs, DateUtils;

{$IFDEF XLOG}
procedure WriteToLog(const AMessage: string);
const
  Filename = 'FinalizationLog.txt';
var
  LogFile: textfile;
  Exist: boolean;
  Timestamp: string;
begin
  try
    Exist := FileExists(Filename);
    AssignFile(LogFile, Filename);
    try
      if Exist then
        Append(LogFile)
      else
        Rewrite(LogFile);
      Timestamp := FormatDateTime('mm/dd/yyyy hh:nn:ss.zzz ', Now);
      WriteLn(LogFile, Timestamp, AMessage);
    finally
      CloseFile(LogFile);
    end;
  except
    on e: exception do
  end;
end;
{$ENDIF XLOG}

{$IFNDEF XLOG}
procedure WriteToLog(const AMessage: string); inline;
begin
  // Inline NOOP if XLOG is undefined.
end;
{$ENDIF XLOG}

procedure ExtendedLog(const ASectionName: String; const AInitOrFinalProcedure: TInitOrFinalProcedure = nil);
begin
  if Assigned(AInitOrFinalProcedure) then
  begin
    WriteToLog(ASectionName + ' Start');
    try
      AInitOrFinalProcedure;
    except
      on e:Exception do
      begin
        // TODO: An exception during finalization.  May wish to actually log this in debug mode.
        WriteToLog(ASectionName + ' Exception: ' + e.Message);
        raise
      end;
    end;
    WriteToLog(ASectionName + ' Finish');
  end
  else
    WriteToLog(ASectionName + ' Complete');
end;

initialization

ExtendedLog('------------------------------');
ExtendedLog('FinalizationLog Initialization');

finalization

ExtendedLog('FinalizationLog Finalization');
ExtendedLog('------------------------------');

end.
