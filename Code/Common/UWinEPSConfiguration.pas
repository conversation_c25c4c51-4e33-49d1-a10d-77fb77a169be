// (c) MTXEPS, Inc. 1988-2014
unit UWinEPSConfiguration;
{**
V825.0 12-12-07 DAE Dev-6686 Support Hypercom 4150 GUI
v825.0 12-04-07 DAE DEV-4855 Support Vx570 - GUI
v822.0 01-25-06 DEV-4434 Support Mx850/830 - GUI
v820.0 07-05-06 JTG     DEV-3015 add interval msg chkbox to form & XML files
v819.0 04-14-06 DEV-2308 Signature Capture suport with ECC (GUI)
v819.0 04-14-06 DEV-2312 Add a new option to allow the End of Order message to kick off the interval Pin Pad message (GUI)
v819.0 04-12-06 DEV-2303 PUBLIX - Retrieve the data from swiping of a drivers License (GUI)
v818.0 03-08-06 DEV-1987 Need to add the MX870 onto our GUI as a supported terminal
v818.0 03-08-06 DEV-1984 Need to add the L4250 onto our GUI as a supported terminal
v818.0 02-14-06 DEV-924 BlackHawk support of Redeem, Reload, Balance Inq (GUI) -Rollback
v818.0 02-14-06 DEV-924 BlackHawk support of Redeem, Reload, Balance Inq (GUI)
v817.0 11-18-05 DEV-830 Add WIC State Code in terminal configuration
v817.0 11-16-05 Add Ingenico eN-Crypt2100 Terminal
v817.0 11-15-05 Add Ingenico Entouch 1000
v816.1 10-06-05 JMR   Make "other" triple language
v815.1 02-15-05 JMR   Added conditional define UPDATE related to JMR-A.  Only upd815to8151.exe uses it.
v815.1 01-10-05 JMR-A Move CardSlideManualActionList from tender to transaction.
**}

interface

uses
  FinalizationLog,
  Classes, SysUtils, LibXmlParser, MTX_Constants, UXMLCommon;

{$IFDEF MTXEPSDLL}
  {$DEFINE MTXEPSDLL_OR_GUIJR}
{$ENDIF MTXEPSDLL}

{$IFDEF GUIJR}
  {$DEFINE MTXEPSDLL_OR_GUIJR}
{$ENDIF GUIJR}

const
  SConfigTypeName = 'WinEPSConfiguration';
  SVersion = 'Version';
  SLastModified = 'LastModified';
  SParameterVersion = 'ParameterVersion';
  SOpenEPSParameters = 'OpenEPSParameters';
  SStandInAllowed = 'StandInAllowed';
  STerminal = 'Terminal';
  STerminal2 = 'Terminal2';
  SValidateCashier = 'ValidateCashier';
  STerminalPort = 'TerminalPort';
  STerminalPort2 = 'TerminalPort2';
  STerminalIPPort = 'TerminalIPPort';
  SVerishieldEnabled = 'VerishieldEnabled';
  SSREDEnable = 'SREDEnable';  // TFS-25351
  SSendReceiptToPINPad = 'SendReceiptToPINPad';                                 
  SDLLExTrace = 'DLLExTrace';
  STCPIPTrace = 'TCPIPTrace';
  SSerialTrace = 'SerialTrace';
  SCardReaderAttached = 'CardReaderAttached';
  SPINPadAttached = 'PINPadAttached';
  SPOSGiftCardReturn = 'POSGiftCardReturn';
  SOpenEPSControlledMSR = 'OpenEPSControlledMSR'; // DEV-12874

  SPenUPTimeOutValue = 'PenUPTimeOutValue';
  SGeneralParameters = 'GeneralParameters';
  SKeyAssignments = 'KeyAssignments';
  SScreenKeys = 'ScreenKeys';
  SScreenKey = 'ScreenKey';
  SFunctionKeys = 'FunctionKeys';
  SEnterKeyIsYes = 'EnterKeyIsYes';
  SReplaceEMVAppLabels = 'ReplaceEMVAppLabels';   // TFS-24615
  SCTLSConfig = 'CTLSConfig';                     // TFS-30049
  SManualButton = 'ManualButton';                 // TFS-35484
  SFunctionKey = 'FunctionKey';
  SLayerPrompts = 'LayerPrompts';
  SPrimaryActions = 'PrimaryActions';
  SReceiptHeader = 'ReceiptHeader';
  SReceiptFooter = 'ReceiptFooter';
  SManagerIDValidationType = 'ManagerIDValidationType';
  SWICStateCode = 'WICStateCode';
  SCustCashBackInCents = 'CustCashBackInCents'; // DEV-23642
  SICEScreenFileName = 'ICEScreenFileName';
  STerminalCode = 'TerminalCode';
  SRKIFileName = 'RKIFileName'; // TFS-9773
  SCashierDisplaySize = 'CashierDisplaySize';
  SMiscPrompts = 'MiscPrompts';
  SMiscProperties = 'MiscProperties';
  SIntervalMessage = 'IntervalMessage';
  SPleaseWait = 'PleaseWait';
  SWrongCard = 'WrongCard';
  SEBTTypePrompt = 'EBTTypePrompt';
  STerminalClosedPrompt = 'TerminalClosedPrompt';
  STransactionCancelled = 'TransactionCancelled';
  SProcessingPromptForVoid = 'ProcessingPromptForVoid';
  SEndOrderIntervalMessage = 'EndOrderIntervalMessage';
  SKeySlot = 'KeySlot';
  SEMVforceCreditOffline = 'EMVforceCreditOffline';     // TFS-30442
  SStartWICSession = 'StartWICSession';                                         
  SEnterWICPIN = 'EnterWICPIN';
  SSmartWICCardRemoved = 'SmartWICCardRemoved';
  SRetrievingPrescription = 'RetrievingPrescription';
  SUpdatingPrescription = 'UpdatingPrescription';
  SLockingWICCard = 'LockingWICCard';
  SRemoveWICCard = 'RemoveWICCard';
  SDataEntryCashier = 'DataEntryCashier';                                       
  SDataEntryPOSTrackingNumber = 'DataEntryPOSTrackingNumber';
  SDataEntryDOB = 'DataEntryDOB';
  SPONumber = 'PONumber';
  STokenPinPrompt = 'TokenPinPrompt'; // DOEP-72983
  SPinReEntryPrompt = 'PinReEntryPrompt';
  SGiftSecurityCodeReEntryPrompt = 'GiftSecurityCodeReEntryPrompt'; // CPCLIENTS-2930
  SFallbackPrompt = 'FallbackPrompt';
  SReinsertCardPrompt = 'ChipCardReInsertPrompt';

  STransactionDefinitions = 'TransactionDefinitions';
  STenderList = 'TenderList';
  STender = 'Tender';
  SName = 'Name';
  SVoidAllowed = 'VoidAllowed';
  SCustomerIdLookup = 'CustomerIdLookup'; //CPCLIENTS-7494
  SIgnoreTacB = 'IgnoreTacB';
  SCode = 'Code';
  STransactionList = 'TransactionList';
  STransaction = 'Transaction';
  SActionList = 'ActionList';
  SAction = 'Action';
  STAC = 'TAC';
  SDescription = 'Description';
  SCustomerLines = 'CustomerLines';
  SCashierLines = 'CashierLines';
  SCardSlideManualActionList = 'CardSlideManualActionList';
  SMICRManualActionList = 'MICRManualActionList';

  SNumberOfBadSlidesAllowed = 'NumberOfBadSlidesAllowed';
  SNumberOfBadChipReadAllowed = 'NumberOfBadChipReadAllowed'; 
  SBadCardSlideErrorPromptDisplayTime = 'BadCardSlideErrorPromptDisplayTime';
  SDigitalID = 'DigitalID'; // DOEP-26548 <
  SSuppressLanguageOnVAS = 'SuppressLanguageOnVAS';    //CPCLIENTS-8202
  SActivateCardReader = 'ActivateCardReader'; // 6285
  SDigitalIDButtonTitle = 'DigitalIDButtonTitle';
  SDigitalIDCustomerDisplay = 'DigitalIDCustomerDisplay';
  SDigitalIDCashierDisplay = 'DigitalIDCashierDisplay'; // DOEP-26548 >
  SDigitalIDMinLength = 'DigitalIDMinLength'; //  DEV-39393 (827.2's DEV-39198)
  SDigitalIDMaxLength = 'DigitalIDMaxLength'; //  DEV-39393 (827.2's DEV-39198)
  SPromptForCashbackByCheckType = 'PromptForCashbackByCheckType'; // DEV-18499
  SDisplayNACHAText = 'DisplayNACHAText'; // DEV-18499
  SSVSAuthNum = 'SVSAuthNum'; // DEV-28404

  SCardSlideFail = 'CardSlideFail';
  SDisableNoZeroKey = 'DisableNoZeroKey';
  SPromptForCashbackOnDiscover = 'PromptForCashbackOnDiscover';
  SProhibitCancellingSignatureCapture = 'ProhibitCancellingSignatureCapture';
  SGetFreqShopperNumFromPINPad = 'GetFreqShopperNumFromPINPad';
  SCustomerPromptForOtherAmount = 'CustomerPromptForOtherAmount';
  SCashierPromptForOtherAmount = 'CashierPromptForOtherAmount';
  SOtherAmountKeyLabel = 'OtherAmountKeyLabel';
  SNoTipAmountKeyLabel = 'NoTipAmountKeyLabel';
  SMaxCashBackKeyLabel = 'MaxCashBackKeyLabel'; // DEV-10520
  SCustomerPromptAfterEFTKey = 'CustomerPromptAfterEFTKey';
  SCashierPromptAfterEFTKey = 'CashierPromptAfterEFTKey';
  SCustomerPromptNonMatchingDigits = 'CustomerPromptNonMatchingDigits'; // DEV-17192
  SCashierPromptNonMatchingDigits = 'CashierPromptNonMatchingDigits'; // DEV-17192
  SBadCardSlideCustomerDisplay = 'BadCardSlideCustomerDisplay';
  SBadCardSlideCashierDisplay = 'BadCardSlideCashierDisplay';
  SErrorConditionPromptDisplayTime = 'ErrorConditionPromptDisplayTime';
  SErrorConditionCustomerDisplay = 'ErrorConditionCustomerDisplay';
  SErrorConditionCashierDisplay = 'ErrorConditionCashierDisplay';
  SBalanceInquiryActionList = 'BalanceInquiryActionList';
  SCashbackAmounts = 'CashbackAmounts';
  SSkipForManualEntry = 'SkipForManualEntry';
  SLine = 'Line';
  SCheckTypesRequiringSecondaryID = 'CheckTypesRequiringSecondaryID';
  SCheckTypesRequiringSecondaryIDSlide = 'CheckTypesRequiringSecondaryIDSlide';
  SCheckTypesRequiring2DBarcode = 'CheckTypesRequiring2DBarcode';
  SRequireVerificationNo = 'RequireVerificationNo';
  SRequireVerificationNoCustomerDisplay = 'RequireVerificationNoCustomerDisplay';
  SRequireVerificationNoCashierDisplay = 'RequireVerificationNoCashierDisplay'; 
  SIDTypesRequiringStateCode = 'IDTypesRequiringStateCode';
  SCheckTypesRequiringManagerID = 'CheckTypesRequiringManagerID';
  SCheckTypesRequiringPhoneNumber = 'CheckTypesRequiringPhoneNumber';
  SIDTypesRequiringManagerID = 'IDTypesRequiringManagerID';
  SPOSDoesSignaturesAfterAllTendersCompleted = 'POSDoesSignaturesAfterAllTendersCompleted';
  //SShowCashbackCustomerDisplay = 'ShowCashbackCustomerDisplay';
  SCheckTypesRequiringSSN = 'CheckTypesRequiringSSN';
  SCheckTypesRequiringPayrollIssueDate = 'CheckTypesRequiringPayrollIssueDate';
  SPromptFreqShopAltID = 'PromptFreqShopAltID'; // DEV-29738
  SPromptForCustomerOK = 'PromptForCustomerOK';
  SPromptAfterFeeAmtSet = 'PromptAfterFeeAmtSet';
  SPromptCustomerOKForPartialAuth = 'PromptCustomerOKForPartialAuth';
  SCustomerOKPartialAuth = 'CustomerOKPartialAuth';
  SDualEBTPrompt = 'DualEBTPrompt';
  SDualEBTPromptSet = 'DualEBTPromptSet';
  SBadCardSlidePrompts = 'BadCardSlidePrompts'; // DOEP-71133
  SBadChipReadPrompts = 'BadChipReadPrompts';

  SCombineYesNoWithAmount = 'CombineYesNoWithAmount';
  SAllowOtherCashbackAmount = 'AllowOtherCashbackAmount';
  SAllowMaxCashBackButton = 'AllowMaxCashBackButton'; // DEV-10520
  SAmount1 = 'Amount1';
  SAmount2 = 'Amount2';
  SAmount3 = 'Amount3';
  SAmount4 = 'Amount4';
  SAmount5 = 'Amount5';
  SAmount6 = 'Amount6';

  //tip attributes fields
  SAllowOtherTipAmount = 'AllowOtherTipAmount';
  SAllowSuggestedTip = 'AllowSuggestedTip';
  SAllowNoTipAmount = 'AllowNoTipAmount';
  SDisplayWholeDollarsOnPinpad = 'DisplayWholeDollarsOnPinpad';      //18606
  STipAmounts = 'TipAmounts';
  STipPercentAmount1 = 'TipPercentAmount1';
  STipPercentAmount2 = 'TipPercentAmount2';
  STipPercentAmount3 = 'TipPercentAmount3';
  STipPercentAmount4 = 'TipPercentAmount4';
  STipPercentAmount5 = 'TipPercentAmount5';
  STipDollarAmount1 = 'TipDollarAmount1';
  STipDollarAmount2 = 'TipDollarAmount2';
  STipDollarAmount3 = 'TipDollarAmount3';
  STipDollarAmount4 = 'TipDollarAmount4';
  STipDollarAmount5 = 'TipDollarAmount5';
  SIsTipPercent = 'IsTipPercent';
  SOnlyPromptWithZeroAmount = 'OnlyPromptWithZeroAmount'; //CPCLIENTS-837

  SSoftDeclineActions = 'SoftDeclineActions';
  STerminalResponse = 'TerminalResponse';
  SCheckConfiguration = 'CheckConfiguration';
  SMICRReaderType = 'MICRReaderType';
  SMICRResultCodes = 'MICRResultCodes';
  SMICRResult = 'MICRResult';
  STranslatedCode = 'TranslatedCode';
  SCheckTypes = 'CheckTypes';
  SCheck = 'Check';
  SType = 'Type';
  SCaption = 'Caption';
  SLayer = 'Layer';
  SVerifyXML = 'VerifyXML';
  SLanguageCode = 'LanguageCode';

  SBioMTXServerPort = 'BioMTXServerPort';
  SGetFingerPrintPrompt = 'GetFingerPrintPrompt';
  SRetryGetFingerPrintPrompt = 'RetryGetFingerPrintPrompt';
  SAfterGetFingerPrintPrompt = 'AfterGetFingerPrintPrompt';
  SRetryAfterGetFingerPrintPrompt = 'RetryAfterGetFingerPrintPrompt';
  SGetCustIDPrompt = 'GetCustIDPrompt';
  SRetryGetCustIDPrompt = 'RetryGetCustIDPrompt';
  SAfterGetCustIDPrompt = 'AfterGetCustIDPrompt';
  SRetryAfterGetCustIDPrompt = 'RetryAfterGetCustIDPrompt';
  SBioMTXUnavailablePrompt = 'BioMTXUnavailablePrompt';
  SECCOnly = 'ECCOnly';
  SFloorLimit = 'FloorLimit';
  SECCProductCode = 'ECCProductCode';
  SPromptForStateCode = 'PromptForStateCode';
  SPINPadEntry = 'PINPadEntry';
  SPinPadEntryRetryCount = 'PinPadEntryRetryCount';  
  SPinPadEntryCustomerDisplay = 'PinPadEntryCustomerDisplay';
  SPinPadEntryCashierDisplay = 'PinPadEntryCashierDisplay';
  SPinPadEntryRetryCustomerDisplay = 'PinPadEntryRetryCustomerDisplay';
  SPinPadEntryRetryCashierDisplay = 'PinPadEntryRetryCashierDisplay';
  SBioDOBLength6Prompt = 'BioDOBLength6Prompt';
  SBioDOBLength8Prompt = 'BioDOBLength8Prompt';
  SBioZipCodePrompt = 'BioZipCodePrompt';
  SBioSSNPrompt = 'BioSSNPrompt';                                               
  SOverride2ndID = 'Override2ndID'; // DEV-8143
  SPOSSupplySignature = 'POSSupplySignature'; // DEV-12747
  SPrintPaperReceiptOnManual = 'PrintPaperReceiptOnManual'; // DOEP-16038
  SCustomSignature = 'CustomSignature'; // DEV-19583
  SVerifyLast4 = 'VerifyLast4'; // DEV-17192
  SEnableWICButton = 'EnableWICButton';
  SRequestTokenData = 'RequestTokenData';
  SAllowManualAutoTender = 'AllowManualAutoTender'; // DOEP-41273
  SShowDisclaimer = 'ShowDisclaimer';
  SDisclaimerLines = 'DisclaimerLines';
  SCustomSwipeForm = 'CustomSwipeForm'; // DOEP-71378
  SCustomBadCardSlideForm = 'CustomBadCardSlideForm'; // DOEP-71389
  SSubScreenKey = 'SubScreenKey'; // DOEP-71681
  SCHDKeySlot = 'CHDKeySlot'; // TFS-8022
  SP2PManualEncryption = 'P2PManualEncryption';
  SDoNotAllowPINBypass = 'DoNotAllowPINBypass'; // TFS-26084
  SWaitForPOSTotalToConvert = 'WaitForPOSTotalToConvert';
  SPCI4 = 'PCI4';
  SRebootTime24Hour = 'RebootTime24Hour';
  SEMVMinCreditToDebit = 'EMVMinCreditToDebit';   // TFS-35080
  SEnableQuickChip = 'EnableQuickChip';
  SDisableNFC = 'DisableNFC';
  SNoPINCVMOption = 'NoPINCVMOption';
  SGiftSecurityCodeRetryCount = 'GiftSecurityCodeRetryCount'; // CPCLIENTS-2930
  SVASMode = 'VASMode';   // CPCLIENTS-5358
  SEnablePayWithQRCode = 'EnablePayWithQRCode'; // CPCLIENTS-11227
  SPromptOnlyOnPOSRequest = 'PromptOnlyOnPOSRequest';  // CPCLIENTS-11904

  // CPCLIENTS-5956 Charity Donations constant Values
  SCharityDonation = 'CharityDonation';
  SCharityDonationOptions = 'CharityDonationOptions';
  SAllowOtherAmount = 'AllowOtherAmount';
  SCustomerDisplay = 'CustomerDisplay';
  SCashierDisplay = 'CashierDisplay';
  SAmountOptions = 'AmountOptions';
  SRoundToNearestDollarAmount = 'RoundToNearestDollarAmount';
  sDelayDonationUntilRoundUpSetByPOS = 'DelayDonationUntilRoundUpSetByPOS'; // CPCLIENTS-7473
  SDonationAmounts = 'DonationAmounts';
  SRoundUpLabel = 'RoundUpLabel';
  SSkipDonationLabel = 'SkipDonationLabel';
  SOtherAmountOptions = 'OtherAmountOptions';
  SOtherAmountLabel = 'OtherAmountLabel';
  SAmountConfirmCustomerDisplay = 'AmountConfirmCustomerDisplay';
  SAmountConfirmCashierDisplay = 'AmountConfirmCashierDisplay';

  // CPCLIENTS-6548 - DeCA Fee Amount changes - start
  SFeeAmountOptions = 'FeeAmountOptions';
  SPercent = 'Percent';
  SAmount = 'Amount';
  SPercentEnable = 'PercentEnable';
  SFeeAmountOption = 'FeeAmountOption';
  // CPCLIENTS-6548 - DeCA Fee Amount changes - end

  STruRating = 'TruRating'; // 6330 <
  STruRatingActive = 'TruRatingActive';
  STruRatingServerAddress = 'TruRatingServerAddress';
  STruRatingPartnerID = 'TruRatingPartnerID';
  STruRatingTimeoutInSeconds = 'TruRatingTimeoutInSeconds';
  STruRatingMaxFailureCount = 'TruRatingMaxFailureCount';
  SDefaultCashierLines = 'DefaultCashierLines'; // 6330 >

  sNY: array[boolean] of string[1] = ('N','Y');

type
  TCustomerAndCashierLines = class;

  TOpenEPSParameters = class(TObject)
  private
    FStandInAllowed: Boolean;
    FTerminal: string;
    FTerminal2: string;
    FValidateCashier: Boolean;
    FTerminalPort: string;
    FTerminalPort2: string;
    FTerminalIPPort: string;
    FVerishieldEnabled: Boolean;
    FSREDEnable: Boolean;  // TFS-25351
    FSendReceiptToPINPad: Boolean;
    FSerialTrace: Boolean;
    FDLLExTrace: Boolean;
    FTCPIPTrace: Boolean;
    FCardReaderAttached: Boolean;
    FPINPadAttached: Boolean;
    FPOSGiftCardReturn: string;
    FPenUPTimeOutValue: integer;
    FPOSDoesSignaturesAfterAllTendersCompleted: Boolean;
    FEndOrderIntervalMessage: Boolean;                                          
    FKeySlot: string;
    FCHDKeySlot: string;
    FEMVforceCreditOffline: boolean;    // TFS-30442
    FEMVMinCreditToDebit: integer;	// TFS-35086
    FPCI4: boolean;
    FRebootTime24Hour: string;
    FEnableQuickChip: Boolean;
    FDisableNFC: Boolean;
    FNoPINCVMOption: Boolean;
    FVASMode: TVASMode;
    FEnablePayWithQRCode: Boolean; // CPCLIENTS-11227
  public
    OpenEPSControlledMSR: string; // DEV-12874
    property StandInAllowed: Boolean read FStandInAllowed write FStandInAllowed;
    property Terminal: string read FTerminal write FTerminal;
    property Terminal2: string read FTerminal2 write FTerminal2;
    property ValidateCashier: Boolean read FValidateCashier write FValidateCashier;
    property TerminalPort: string read FTerminalPort write FTerminalPort;
    property TerminalPort2: string read FTerminalPort2 write FTerminalPort2;
    property TerminalIPPort: string read FTerminalIPPort write FTerminalIPPort;
    property VerishieldEnabled: Boolean read FVerishieldEnabled write FVerishieldEnabled;
    property SREDEnable: boolean read FSREDEnable write FSREDEnable;           // TFS-25351
    property SendReceiptToPINPad: boolean read FSendReceiptToPINPad write FSendReceiptToPINPad;
    property SerialTrace: Boolean read FSerialTrace write FSerialTrace;
    property DLLExTrace: Boolean read FDLLExTrace write FDLLExTrace;
    property TCPIPTrace: Boolean read FTCPIPTrace write FTCPIPTrace;
    property CardReaderAttached: Boolean read FCardReaderAttached write FCardReaderAttached;
    property PINPadAttached: Boolean read FPINPadAttached write FPINPadAttached;
    property POSGiftCardReturn: string read FPOSGiftCardReturn write FPOSGiftCardReturn;
    property PenUPTimeOutValue: integer read FPenUPTimeOutValue write FPenUPTimeOutValue;
    property POSDoesSignaturesAfterAllTendersCompleted: Boolean read FPOSDoesSignaturesAfterAllTendersCompleted write FPOSDoesSignaturesAfterAllTendersCompleted;
    property EndOrderIntervalMessage: Boolean read FEndOrderIntervalMessage write FEndOrderIntervalMessage;
    property KeySlot: string read FKeySlot write FKeySlot;
    property CHDKeySlot: string read FCHDKeySlot write FCHDKeySlot;
    property EMVforceCreditOffline: boolean read FEMVforceCreditOffline write FEMVforceCreditOffline;  // TFS-30442
    property EMVMinCreditToDebit: integer read FEMVMinCreditToDebit write FEMVMinCreditToDebit;	// TFS-35086
    property PCI4: boolean read FPCI4 write FPCI4;
    property RebootTime24Hour: string read FRebootTime24Hour write FRebootTime24Hour;
    property EnableQuickChip: Boolean read FEnableQuickChip write FEnableQuickChip;
    property DisableNFC: Boolean read FDisableNFC write FDisableNFC;
    property NoPINCVMOption: Boolean read FNoPINCVMOption write FNoPINCVMOption;
    property VASMode: TVASMode read FVASMode write FVASMode default vmNone;
    property EnablePayWithQRCode: Boolean read FEnablePayWithQRCode write FEnablePayWithQRCode; // CPCLIENTS-11227
  end;

  TKeyList = class;

  TKey = class(TObject)
  private
    FKeyList: TKeyList;
    FLayer: Integer;
    FId: Integer;
    FValue: string;
    FCaption: string;
    FLanguageCode: string;
  public
    procedure Assign(AKey: TKey);
    constructor Create( AKeyList: TKeyList;
                        ALayer: Integer;
                        AId: Integer;
                        AValue: string;
                        ACaption: string;
                        ALanguageCode: string = '');
    destructor Destroy; override;
    property Layer: Integer read FLayer write FLayer;
    property Id: Integer read FId write Fid;
    property Value: string read FValue write FValue;
    property Caption: string read FCaption write FCaption;
    property LanguageCode: string read FLanguageCode write FLanguageCode;
  end;

  TKeyList = class(TObject)
  private
    FItemName: string;
    FItems: TList;
    function GetItem(Index: Integer): TKey;
    function GetCount: Integer;
  public
    procedure Assign(AKeyList: TKeyList);
    constructor Create(AItemName: string);
    destructor Destroy; override;
    procedure Clear;
    function Add(ALayer: Integer; AId: Integer; AValue: string; ACaption: string; ALanguageCode: string = ''): TKey;
    procedure Delete(Index: Integer);
    function KeyByLayerAndId(ALayer: Integer; AId: Integer): TKey;
    function KeyByValue(AValue: string): TKey;
    property Items[Index: Integer]: TKey read GetItem; default;
    property Count: Integer read GetCount;
  end;

  TLayerPromptList = class;

  TLayerPrompt = class(TObject)
  private
    FLayerPrompts: TLayerPromptList;
    FId: Integer;
    FPrompts: TCustomerLineList;
  public
    procedure Assign(ALayerPrompt: TLayerPrompt);
    constructor Create(ALayerPrompts: TLayerPromptList; AId: Integer);
    destructor Destroy; override;
    property Id: Integer read FId write Fid;
    property Prompts: TCustomerLineList read FPrompts;
  end;

  TLayerPromptList = class(TObject)
  private
    FItems: TList;
    function GetItem(Index: Integer): TLayerPrompt;
    function GetCount: Integer;
  public
    procedure Assign(ALayerPrompts: TLayerPromptList);
    constructor Create;
    destructor Destroy; override;
    procedure Clear;
    function Add(AId: Integer): TLayerPrompt;
    procedure Delete(Index: Integer);
    function LayerPromptById(AId: Integer): TLayerPrompt;
    property Items[Index: Integer]: TLayerPrompt read GetItem; default;
    property Count: Integer read GetCount;
  end;

  TScreenKey = class(TKey)
  private
    FSubScreenKeys: TKeyList;
  public
    constructor Create(AKeyList: TKeyList; ALayer: Integer; AId: Integer; AValue: string; ACaption: string; ALanguageCode : string = '');
    destructor Destroy; override;
    property SubScreenKeys: TKeyList read FSubScreenKeys;
  end;

  TScreenKeyList = class(TKeyList)
  private
    function GetItem(Index: Integer): TScreenKey;
  public
    function Add(ALayer: Integer; AId: Integer; AValue: string; ACaption: string; ALanguageCode : string = ''): TScreenKey;
    procedure Delete(Index: Integer);
    procedure Clear;
    function KeyByLayerAndId(ALayer: Integer; AId: Integer): TScreenKey;
    function KeyByValue(AValue: string): TScreenKey;
    property Items[Index: Integer]: TScreenKey read GetItem; default;
  end;

  TKeyAssignments = class(TObject)
  private
    FScreenKeys: TScreenKeyList;
    FFunctionKeys: TKeyList;
    FLayerPrompts: TLayerPromptList;
    FEnterKeyIsYes: Boolean;
    FReplaceEMVAppLabels: Boolean; // TFS-24615
    FCTLSConfig: TCTLSConfigType;
    FManualButton: boolean;     // TFS-35484
  public
    property ScreenKeys: TScreenKeyList read FScreenKeys;
    property FunctionKeys: TKeyList read FFunctionKeys;
    property LayerPrompts: TLayerPromptList read FLayerPrompts; 
    property EnterKeyIsYes: Boolean read FEnterKeyIsYes write FEnterKeyIsYes;
    property ReplaceEMVAppLabels: Boolean read FReplaceEMVAppLabels write FReplaceEMVAppLabels; // TFS-24615
    property CTLSConfig: TCTLSConfigType read FCTLSConfig write FCTLSConfig;
    property ManualButton: Boolean read FManualButton write FManualButton;      // TFS-35484
    procedure Assign(AKeyAssignments: TKeyAssignments);
    constructor Create;
    destructor Destroy; override;
  end;

  // DEV-29738, renamed TActionList to TTerminalActionList to remove confusion with Delphi's TActionList in the ActnList unit. Changed other code using the class name.
  TTerminalActionList = class;


  TTipAndCashback = class(TObject)
  private
    FCustomerLines: TCustomerLineList;
    FCashierLines: TLineCollection5;
    FCustomerPromptForOtherAmount: TCustomerLineList;
    FOtherAmountKeyLabel: TCustomerLineList;
    FNoTipAmountKeyLabel: TCustomerLineList;
    FCashierPromptForOtherAmount: TLineCollection5;
  public
    constructor Create;
    destructor Destroy; override;
    procedure Assign(ABaseAmounts: TTipAndCashback);

    property CustomerLines: TCustomerLineList read FCustomerLines;
    property CashierLines: TLineCollection5 read FCashierLines write FCashierLines;
    property CustomerPromptForOtherAmount: TCustomerLineList read FCustomerPromptForOtherAmount;
    property OtherAmountKeyLabel: TCustomerLineList read FOtherAmountKeyLabel;
    property NoTipAmountKeyLabel: TCustomerLineList read FNoTipAmountKeyLabel;
    property CashierPromptForOtherAmount: TLineCollection5 read FCashierPromptForOtherAmount write FCashierPromptForOtherAmount;
  end;

  TCashbackAmounts = class(TTipAndCashback)
  const
    MAX_CASHBACK_AMTS = 6;                                       // CPCLIENTS-10087
  private
    FMaxCashBackKeyLabel: TCustomerLineList;
  public
    CashbackAmount: array[1..MAX_CASHBACK_AMTS] of Integer;      // CPCLIENTS-10087
    AllowOtherCashbackAmount: Boolean;
    AllowMaxCashBackButton: boolean;
    CombineYesNoWithAmount: boolean;
    constructor Create;
    destructor Destroy; override;
    procedure Assign(ACashbackAmounts: TCashbackAmounts);
    property MaxCashBackKeyLabel: TCustomerLineList read FMaxCashBackKeyLabel;
    procedure CalculateBtns(var NumberOfValueBtns, MaxCashbackBtnNum, OtherCashbackBtnNum: integer; AllowMaxCashback: string);
  end;

  TTipAmounts = class(TTipAndCashback)
  private
  public
    TipPercentAmount: array[1..5] of integer;       // CPCLIENTS-13012
    TipDollarAmount: array[1..5] of integer;        // CPCLIENTS-13012
    TipWholeDollarAmount: array[1..3] of integer;   // 18769   introduced array to arrange non zero values of Tip at the start. None and other btns not included.

    AllowOtherTipAmount: Boolean;
    AllowSuggestedTip: Boolean;
    AllowNoTipAmount: Boolean;
    IsTipPercentage: Boolean;
    OnlyPromptWithZeroAmount: Boolean; //CPCLIENTS-837
    DisplayWholeDollarsOnPinpad: Boolean;   //18606

    constructor Create;
    destructor Destroy; override;
    procedure Assign(ATipAmounts: TTipAmounts);
    procedure CalculateNONEandOTHERbtns(var NONEbtn, OTHERbtn: byte; LanguageID: byte; SwapCustomNoTipBtnIndex: boolean = False);  // CPCLIENTS-13012     //18605 introduced default parameter SwapCustomNoTipBtnIndex
    function IncludeButton(TipIsPct: boolean; Pct, Amt: integer): boolean;    // CPCLIENTS-13012
    function ConvertTipToWholeDollarAmount(var TipAmount: integer): integer;  //18606
    procedure AssignWholeDollarAmountArray(TipAmounts: TTipAmounts);          //18769
  end;

  // CPCLIENTS-6548 - DecA Fee Amount changes - start
  TFeeAmounts = class(TObject)
  private
    FPercent : Integer;
    FAmount  : Integer;
    FPercentEnable : Boolean;

  public
    property Percent: Integer read FPercent;
    property Amount: Integer read FAmount;
    property PercentEnable: Boolean read FPercentEnable;

    constructor Create;
    destructor Destroy; override;
  end;
  // CPCLIENTS-6548 - DecA Fee Amount changes - end

    // CPCLIENTS-5956 Charity Donations Class
  TCharityDonationOptions = class(TObject)  // New One
  const
    MAX_DONATION_AMTS = 4;                        // CPCLIENTS-13012
  private
    FRoundToNearestDollarAmount: Boolean;
    FDelayDonationUntilRoundUpSetByPOS: Boolean;  // CPCLIENTS-7473
    FAllowOtherAmount: Boolean;

    FCustomerDisplay: TCustomerLineList;
    FCashierDisplay: TLineCollection5;

    FAOCustomerDisplay: TCustomerLineList;
    FAOCashierDisplay: TLineCollection5;
    FRoundUpLabel: TCustomerLineList;
    FSkipDonationLabel: TCustomerLineList;

    FOtherAmountLabel: TCustomerLineList;
    FOAOCustomerDisplay: TCustomerLineList;
    FOAOCashierDisplay: TLineCollection5;
    FAmountConfirmCustomerDisplay: TCustomerLineList;
    FAmountConfirmCashierDisplay: TLineCollection5;
  public

    DonationAmount: array[1..MAX_DONATION_AMTS] of Integer;   // CPCLIENTS-13012

    constructor Create;
    destructor Destroy; override;
    procedure Assign(ACharityDonations: TCharityDonationOptions);
    property RoundToNearestDollarAmount: Boolean read FRoundToNearestDollarAmount write FRoundToNearestDollarAmount;
    property DelayDonationUntilRoundUpSetByPOS: Boolean read FDelayDonationUntilRoundUpSetByPOS write FDelayDonationUntilRoundUpSetByPOS;  // CPCLIENTS-7473
    property AllowOtherAmount: Boolean read FAllowOtherAmount write FAllowOtherAmount;

    property CustomerDisplay: TCustomerLineList read FCustomerDisplay;
    property CashierDisplay: TLineCollection5 read FCashierDisplay write FCashierDisplay;

    property AOCustomerDisplay: TCustomerLineList read FAOCustomerDisplay;
    property AOCashierDisplay: TLineCollection5 read FAOCashierDisplay write FAOCashierDisplay;
    property RoundUpLabel: TCustomerLineList read FRoundUpLabel;
    property SkipDonationLabel: TCustomerLineList read FSkipDonationLabel;

    property OtherAmountLabel: TCustomerLineList read FOtherAmountLabel;
    property OAOCustomerDisplay: TCustomerLineList read FOAOCustomerDisplay;
    property OAOCashierDisplay: TLineCollection5 read FOAOCashierDisplay write FOAOCashierDisplay;
    property AmountConfirmCustomerDisplay: TCustomerLineList read FAmountConfirmCustomerDisplay;
    property AmountConfirmCashierDisplay: TLineCollection5 read FAmountConfirmCashierDisplay write FAmountConfirmCashierDisplay;

    function IncludeButton(Amt: integer): boolean;    // CPCLIENTS-13012
    procedure CalculateSKIPandOTHERandROUNDUPbtns(var SKIPbtn, OTHERbtn, ROUNDUPbtn: byte; LanguageID: byte; RoundupAmt: string);
  end;


  TMiscPrompts2 = class;

  // DEV-29738, renamed TAction to TTerminalAction to remove confusion with Delphi's TAction in the ActnList unit. Changed other code using the class name.
  TTerminalAction = class(TObject)
  private
    FActionList: TTerminalActionList;
    FTAC: string;
    FDescription: string;
    FCustomerLines: TCustomerLineList;
    FCashierLines: TLineCollection5;
    FNumberOfBadSlidesAllowed: Integer;
    FNumberOfBadChipReadAllowed: integer;
    FCardSlideFail: string;
    FBadCardSlideCustomerDisplay: TCustomerLineList;
    FBadCardSlideCashierDisplay: TLineCollection5;
    FDigitalID: boolean; // DOEP-26548
    FSuppressLanguageOnVAS : boolean; //CPCLIENTS-8202
    FActivateCardReader: boolean; // 6285
    FDisableNoZeroKey: Boolean;
    FCustomerPromptAfterEFTKey: TCustomerLineList;
    FCashierPromptAfterEFTKey: TLineCollection5;
    FCustomerPromptNonMatchingDigits: TCustomerLineList; // DEV-17192
    FCashierPromptNonMatchingDigits: TLineCollection5; // DEV-17192
    FActionProperties: TActionProperties;
    FBadCardSlideErrorPromptDisplayTime: Integer;
    FErrorConditionPromptDisplayTime: Integer;
    FErrorConditionCustomerDisplay: TCustomerLineList;
    FErrorConditionCashierDisplay: TLineCollection5;
    FBalanceInquiryActionList: TTerminalActionList;
    FPromptForCashbackOnDiscover: Boolean;
    FProhibitCancellingSignatureCapture: Boolean;
    FGetFreqShopperNumFromPINPad: Boolean;
    FCashbackAmounts: TCashbackAmounts;
    FTipAmounts:  TTipAmounts;
    FSkipForManualEntry: Boolean;
    FCheckTypesRequiringSecondaryID: TStringList;
    FCheckTypesRequiringSecondaryIDSlide: TStringList;
    FCheckTypesRequiring2DBarcode: TStringList;
    FRequireVerificationNo: Boolean;
    FRequireVerificationNoCustomerDisplay: TCustomerLineList;
    FRequireVerificationNoCashierDisplay: TLineCollection5;
    FCheckTypesRequiringManagerID: TStringList;
    FCheckTypesRequiringPhoneNumber: TStringList;
    FCheckTypesRequiringSSN: TStringList;
    FCheckTypesRequiringPayrollIssueDate: TStringList;
    FIDTypesRequiringManagerID: TStringList;
    FMiscPrompts: TMiscPrompts2;
    FKeyAssignments: TKeyAssignments;
    FBioMTXServerPort: Integer;
    FECCOnly: Boolean;
    FFloorLimit: Integer;
    FECCProductCode: Integer;
    FPromptForStateCode: Boolean;
    FPINPadEntry: Boolean;
    FOverride2ndID: Boolean; // DEV-8143
    FPOSSupplySignature: Boolean; // DEV-12747
    FPrintPaperReceiptOnManual: boolean; //DOEP-16038
    FCustomSignature: boolean; // DEV-19583
    FVerifyLast4: boolean; // DEV-17192
    FDisplayNACHAText: boolean; // DEV-18499
    FSVSAuthNum: boolean; // DEV-28404
    //FShowCashback: boolean;
    //FShowCashbackCustomerDisplay: TCashbackCustomerLineList;
    FEnableWICButton: boolean;
    FRequestTokenData: boolean;
    FAllowManualAutoTender: boolean; // DOEP-41273
    FPromptFreqShopAltID: Boolean; // DEV-29738
    FPromptForCustomerOK: boolean;
    FPromptAfterFeeAmtSet: TCustomerAndCashierLines;
    FPromptCustomerOKForPartialAuth: boolean;
    FCustomerOKPartialAuth: TCustomerAndCashierLines;
    FDualEBTPrompt: boolean;
    FDualEBTPromptSet: TCustomerAndCashierLines;
    FShowDisclaimer: boolean;
    FDisclaimerLines: TStringList;
    FBadCardSlidePrompts: TCustomerAndCashierLines; // DOEP-71133
    FBadChipReadPrompts: TCustomerAndCashierLines;
    FCustomSwipeForm: Boolean; // DOEP-71378
    FCustomBadCardSlideForm: Boolean; // DOEP-71389
    FP2PManualEncryption: boolean;
    FDoNotAllowPINBypass: Boolean; // TFS-26084
    FPromptOnlyOnPOSRequest: boolean;   // CPCLIENTS-11904
    FWaitForPOSTotalToConvert: boolean;
    FCharityDonation: Boolean;  // CPCLIENTS-5956 Charity Donations
    FCharityDonationOptions: TCharityDonationOptions;   // CPCLIENTS-5956 Charity Donations
    FFeeAmountOption: boolean; // CPCLIENTS-6548 - DecA Fee Amount changes
    FFeeAmounts: TFeeAmounts; // CPCLIENTS-6548 - DecA Fee Amount changes

    procedure SetCardSlideFail(const Value: string);
    procedure SetDisableNoZeroKey(const Value: Boolean);
    procedure SetNumberOfBadSlidesAllowed(const Value: Integer);
    procedure SetNumberOfBadChipReadAllowed(const Value: Integer);
    procedure SetBadCardSlideErrorPromptDisplayTime(const Value: Integer);
    procedure SetErrorConditionPromptDisplayTime(const Value: Integer);
    procedure SetPromptForCashbackOnDiscover(const Value: Boolean);
    procedure SetProhibitCancellingSignatureCapture(const Value: Boolean);
    procedure SetGetFreqShopperNumFromPINPad(const Value: Boolean);
    procedure SetSkipForManualEntry(const Value: Boolean);
    procedure SetBioMTXServerPort(const Value: Integer);
    procedure SetRequireVerificationNo(const Value: Boolean);
    procedure SetECCOnly(const Value: Boolean);
    procedure SetFloorLimit(const Value: Integer);
    procedure SetECCProductCode(const Value: Integer);                          
    procedure SetPromptForStateCode(const Value: Boolean); 
    procedure SetPINPadEntry(const Value: Boolean);
    procedure SetOverride2ndID(const Value: boolean);
    procedure SetPOSSupplySignature(const Value: boolean);
    procedure SetPrintPaperReceiptOnManual(const Value: boolean); //DOEP-16038
    procedure SetCustomSignature(const Value: boolean); // DEV-19583
    procedure SetVerifyLast4(const Value: boolean); // DEV-17192
    function GetDigitalID: boolean;
    procedure SetDigitalID(const Value: boolean); // DOEP-26548
    procedure SetSuppressLanguageOnVAS(const Value: boolean);    //CPCLIENTS-8202
    function GetActivateCardReader: boolean;
    procedure SetActivateCardReader(const Value: boolean); // 6285
    procedure SetDisplayNACHAText(const Value: boolean); // DEV-18499
    procedure SetSVSAuthNum(const Value: boolean); // DEV-28404
    //procedure SetShowCashback(const Value: boolean);
    procedure SetEnableWICButton(const Value: boolean);
    procedure SetRequestTokenData(const Value: boolean);
    procedure SetAllowManualAutoTender(const Value: Boolean); // DOEP-41273
    procedure SetPromptFreqShopAltID(const Value: Boolean); // DEV-29738
    procedure SetPromptForCustomerOK(const Value: Boolean);
    procedure SetPromptCustomerOKForPartialAuth(const Value: Boolean);
    procedure SetDualEBTPrompt(const Value: Boolean);
    procedure SetShowDisclaimer(const Value: Boolean);
    procedure SetCustomSwipeForm(const Value: Boolean); // DOEP-71378
    procedure SetCustomBadCardSlideForm(const Value: Boolean); // DOEP-71389
    procedure SetP2PManualEncryption(const Value: boolean);
    procedure SetDoNotAllowPINBypass(const Value: Boolean); // TFS-26084
    procedure SetPromptOnlyOnPOSRequest(const Value: boolean);  // CPCLIENTS-11904
    procedure SetWaitForPOSTotalToConvert(const Value: boolean);
    procedure SetCharityDonation(const Value: boolean); // CPCLIENTS-5956 Charity Donations
    procedure SetFeeAmountOption(const Value: Boolean); // CPCLIENTS-6548 - DeCA
    function GetIsActivateCardReader : boolean; // CPCLIENTS-19301
  public
    DigitalIDButtonTitle: string;
    DigitalIDCustomerDisplay: TCustomerLineList;
    DigitalIDCashierDisplay: TLineCollection5; // DOEP-26548 >
    DigitalIDMinLength: Integer;  // DEV-39393 (827.2's DEV-39198)
    DigitalIDMaxLength: Integer;  // DEV-39393 (827.2's DEV-39198)
    PromptForCashbackByCheckType: TStringList; // DEV-18499
    PinPadEntryRetryCount: integer;
    PinPadEntryCustomerDisplay: TCustomerLineList;
    PinPadEntryCashierDisplay: TLineCollection5;
    PinPadEntryRetryCustomerDisplay: TCustomerLineList;
    PinPadEntryRetryCashierDisplay: TLineCollection5;
    GiftSecurityCodeRetryCount : Integer; // CPCLIENTS-2930

    constructor Create(AActionList: TTerminalActionList; ATAC: string; IsNewTAC: boolean=false);
    destructor Destroy; override;
    procedure Assign(AAction: TTerminalAction);
    function GetIndex(): Integer;
    procedure SetDefaultValues; // DEV-18499

    property TAC: string read FTAC write FTAC;
    property Description: string read FDescription write FDescription;

    property ActionProperties: TActionProperties read FActionProperties write FActionProperties;

    property CustomerLines: TCustomerLineList read FCustomerLines;
    property CashierLines: TLineCollection5 read FCashierLines write FCashierLines;
    //property ShowCashbackCustomerDisplay: TCashbackCustomerLineList read FShowCashbackCustomerDisplay; 
    property NumberOfBadSlidesAllowed: Integer read FNumberOfBadSlidesAllowed write SetNumberOfBadSlidesAllowed;
    property NumberOfBadChipReadAllowed: integer read FNumberOfBadChipReadAllowed write SetNumberOfBadChipReadAllowed;
    property CardSlideFail: string read FCardSlideFail write SetCardSlideFail;
    property BadCardSlideErrorPromptDisplayTime: Integer read FBadCardSlideErrorPromptDisplayTime write SetBadCardSlideErrorPromptDisplayTime;
    property BadCardSlideCustomerDisplay: TCustomerLineList read FBadCardSlideCustomerDisplay;
    property BadCardSlideCashierDisplay: TLineCollection5 read FBadCardSlideCashierDisplay write FBadCardSlideCashierDisplay;
    property DigitalID: boolean read GetDigitalID write SetDigitalID; // DOEP-26548
    property SuppressLanguageOnVAS: boolean read FSuppressLanguageOnVAS write SetSuppressLanguageOnVAS;   //CPCLIENTS-8202 added property SuppressLanguageOnVAS
    property ActivateCardReader: boolean read GetActivateCardReader write SetActivateCardReader; // 6285
    property PromptFreqShopAltID: Boolean read FPromptFreqShopAltID write SetPromptFreqShopAltID; // DEV-29738
    property PromptForCustomerOK: Boolean read FPromptForCustomerOK write SetPromptForCustomerOK;
    property PromptAfterFeeAmtSet: TCustomerAndCashierLines read FPromptAfterFeeAmtSet;
    property PromptCustomerOKForPartialAuth: Boolean read FPromptCustomerOKForPartialAuth write SetPromptCustomerOKForPartialAuth;
    property CustomerOKPartialAuth: TCustomerAndCashierLines read FCustomerOKPartialAuth;
    property DualEBTPrompt: Boolean read FDualEBTPrompt write SetDualEBTPrompt;
    property DualEBTPromptSet: TCustomerAndCashierLines read FDualEBTPromptSet;

    property ErrorConditionPromptDisplayTime: Integer read FErrorConditionPromptDisplayTime write SetErrorConditionPromptDisplayTime;
    property ErrorConditionCustomerDisplay: TCustomerLineList read FErrorConditionCustomerDisplay;
    property ErrorConditionCashierDisplay: TLineCollection5 read FErrorConditionCashierDisplay write FErrorConditionCashierDisplay;

    property DisableNoZeroKey: Boolean read FDisableNoZeroKey write SetDisableNoZeroKey;
    property PromptForCashbackOnDiscover: Boolean read FPromptForCashbackOnDiscover write SetPromptForCashbackOnDiscover;
    property ProhibitCancellingSignatureCapture: Boolean read FProhibitCancellingSignatureCapture write SetProhibitCancellingSignatureCapture;
    property GetFreqShopperNumFromPINPad: Boolean read FGetFreqShopperNumFromPINPad write SetGetFreqShopperNumFromPINPad;
    property CustomerPromptAfterEFTKey: TCustomerLineList read FCustomerPromptAfterEFTKey;
    property CashierPromptAfterEFTKey: TLineCollection5 read FCashierPromptAfterEFTKey write FCashierPromptAfterEFTKey;
    property CustomerPromptNonMatchingDigits: TCustomerLineList read FCustomerPromptNonMatchingDigits; // DEV-17192
    property CashierPromptNonMatchingDigits: TLineCollection5 read FCashierPromptNonMatchingDigits write FCashierPromptNonMatchingDigits; // DEV-17192

    property BalanceInquiryActionList: TTerminalActionList read FBalanceInquiryActionList;

    property CashbackAmounts: TCashbackAmounts read FCashbackAmounts;
    property TipAmounts: TTipAmounts read FTipAmounts;
    property SkipForManualEntry: Boolean read FSkipForManualEntry write SetSkipForManualEntry;

    property CheckTypesRequiringSecondaryID: TStringList read FCheckTypesRequiringSecondaryID;
    property CheckTypesRequiringSecondaryIDSlide: TStringList read FCheckTypesRequiringSecondaryIDSlide;
    property CheckTypesRequiring2DBarcode: TStringList read FCheckTypesRequiring2DBarcode; 
    property RequireVerificationNo: Boolean read FRequireVerificationNo write SetRequireVerificationNo;
    property RequireVerificationNoCustomerDisplay: TCustomerLineList read FRequireVerificationNoCustomerDisplay;
    property RequireVerificationNoCashierDisplay: TLineCollection5 read FRequireVerificationNoCashierDisplay write FRequireVerificationNoCashierDisplay; 
    property ECCOnly: Boolean read FECCOnly write SetECCOnly;
    property FloorLimit: Integer read FFloorLimit write SetFloorLimit default -1;
    property ECCProductCode: Integer read FECCProductCode write SetECCProductCode default -1; 
    property PromptForStateCode: Boolean read FPromptForStateCode write SetPromptForStateCode default False; 
    property PINPadEntry: Boolean read FPINPadEntry write SetPINPadEntry default False; 
    property Override2ndID: boolean read FOverride2ndID write SetOverride2ndID default false; // DEV-8143
    property POSSupplySignature: boolean read FPOSSupplySignature write SetPOSSupplySignature default false; // DEV-12747
    property PrintPaperReceiptOnManual: boolean read FPrintPaperReceiptOnManual write SetPrintPaperReceiptOnManual default false; // DOEP-16038
    property CustomSignature: boolean read FCustomSignature write SetCustomSignature default false; // DEV-19583
    property VerifyLast4: boolean read FVerifyLast4 write SetVerifyLast4; // DEV-17192

    property CheckTypesRequiringManagerID: TStringList read FCheckTypesRequiringManagerID;
    property CheckTypesRequiringPhoneNumber: TStringList read FCheckTypesRequiringPhoneNumber;
    property CheckTypesRequiringSSN : TStringList read FCheckTypesRequiringSSN ;
    property CheckTypesRequiringPayrollIssueDate: TStringList read FCheckTypesRequiringPayrollIssueDate;
    property IDTypesRequiringManagerID: TStringList read FIDTypesRequiringManagerID;

    property BioMTXServerPort: Integer read FBioMTXServerPort write SetBioMTXServerPort;
    property KeyAssignments: TKeyAssignments read FKeyAssignments;
    property MiscPrompts: TMiscPrompts2 read FMiscPrompts;
    property DisplayNACHAText: boolean read FDisplayNACHAText write SetDisplayNACHAText; // DEV-18499
    property SVSAuthNum: boolean read FSVSAuthNum write SetSVSAuthNum default False; // DEV-28404
    property EnableWICButton: boolean read FEnableWICButton write SetEnableWICButton default False;
    property RequestTokenData: boolean read FRequestTokenData write SetRequestTokenData default False;
    property AllowManualAutoTender: boolean read FAllowManualAutoTender write SetAllowManualAutoTender default false; // DOEP-41273
    property ShowDisclaimer: boolean read FShowDisclaimer write SetShowDisclaimer default false;
    property DisclaimerLines: TStringList read FDisclaimerLines;
    property BadCardSlidePrompts: TCustomerAndCashierLines read FBadCardSlidePrompts;  // DOEP-71133
    property BadChipReadPrompts: TCustomerAndCashierLines read FBadChipReadPrompts; 
    property CustomSwipeForm: Boolean read FCustomSwipeForm write SetCustomSwipeForm default False; //DOEP-71378
    property CustomBadCardSlideForm: Boolean read FCustomBadCardSlideForm write SetCustomBadCardSlideForm default False; // DOEP-71389
    property P2PManualEncryption: boolean read FP2PManualEncryption write SetP2PManualEncryption default false;
    property DoNotAllowPINBypass: Boolean read FDoNotAllowPINBypass write SetDoNotAllowPINBypass default False;  // TFS-26084
    property PromptOnlyOnPOSRequest: boolean read FPromptOnlyOnPOSRequest write SetPromptOnlyOnPOSRequest;   // CPCLIENTS-11904
    property WaitForPOSTotalToConvert: boolean read FWaitForPOSTotalToConvert write SetWaitForPOSTotalToConvert default false;
    property CharityDonation: Boolean read FCharityDonation write SetCharityDonation;  // CPCLIENTS-5956 Charity Donations
    property CharityDonationOptions: TCharityDonationOptions read FCharityDonationOptions;  // CPCLIENTS-5956 Charity Donations
    property FeeAmountOption: Boolean read FFeeAmountOption write SetFeeAmountOption; // CPCLIENTS-6548 - DeCA Fee Amount changes
    property FeeAmounts: TFeeAmounts read FFeeAmounts;  // CPCLIENTS-6548 - DeCA Fee Amount changes
    property IsActivateCardReader: boolean read GetIsActivateCardReader; // CPCLIENTS-19301
  end;

  // DEV-29738, renamed TActionList to TTerminalActionList to remove confusion with Delphi's TActionList in the ActnList unit. Changed other code using the class name.
  TTerminalActionList = class(TObject)
  private
    FItems: TList;
    function GetItem(Index: Integer): TTerminalAction;
    function GetCount: Integer;
  public
    constructor Create;
    destructor Destroy; override;
    procedure Clear;
    function Add(ATAC: string; IsNewTAC: boolean=false): TTerminalAction;
    function ActionByTAC(ATAC: string): TTerminalAction;
    procedure Delete(Index: Integer);
    procedure Assign(AActionList: TTerminalActionList);
    function Insert(ATAC: string; Index: Integer; IsNewTAC: boolean=false): TTerminalAction;
    function IsTACExists(TAC: string): boolean; // DEV-19583 <
    function IsTACInOrder(TacA: string; TacB: string): boolean;
    function TacListStr: string; // DEV-19583 >

    property Items[Index: Integer]: TTerminalAction read GetItem; default;
    property Count: Integer read GetCount;
  end;

  TCustomerAndCashierLines = class(TObject)
  private
    FCustomerLines: TCustomerLineList;
    FCashierLines: TLineCollection5;
    FUseForm: boolean;
  public
    property CustomerLines: TCustomerLineList read FCustomerLines;
    property CashierLines: TLineCollection5 read FCashierLines;
    property UseForm: boolean read FUseForm write FUseForm;
    procedure Assign(ACustomerAndCashierLines: TCustomerAndCashierLines);
    constructor Create;
    destructor Destroy; override;
  end;

  TMiscPrompts = class(TObject)
  private
    FIntervalMessage: TCustomerAndCashierLines;
    FPleaseWait: TCustomerAndCashierLines;
    FWrongCard: TCustomerAndCashierLines;
    FEBTTypePrompt: TCustomerAndCashierLines;
    FTerminalClosedPrompt: TCustomerAndCashierLines;
    FTransactionCancelled: TCustomerAndCashierLines;
    FProcessingPromptForVoid: TCustomerAndCashierLines;
    FStartWICSession: TCustomerAndCashierLines;                                 
    FEnterWICPIN: TCustomerAndCashierLines;
    FSmartWICCardRemoved: TCustomerAndCashierLines;
    FRetrievingPrescription: TCustomerAndCashierLines;
    FUpdatingPrescription: TCustomerAndCashierLines;
    FLockingWICCard: TCustomerAndCashierLines;
    FRemoveWICCard: TCustomerAndCashierLines;
    FDataEntryCashier: TCustomerAndCashierLines;
    FDataEntryPOSTrackingNumber: TCustomerAndCashierLines;
    FDataEntryDOB: TCustomerAndCashierLines;
    FPONumber: TCustomerAndCashierLines;
    FTokenPinPrompt: TCustomerAndCashierLines; // DOEP-72983
    FPinReEntryPrompt: TCustomerAndCashierLines;
    FFallbackPrompt: TCustomerAndCashierLines;
    FInsertSwipedChipCard: TCustomerAndCashierLines;
    FGiftSecurityCodeReEntryPrompt: TCustomerAndCashierLines; // CPCLIENTS-2930
  public
    property IntervalMessage: TCustomerAndCashierLines read FIntervalMessage;
    property PleaseWait: TCustomerAndCashierLines read FPleaseWait;
    property WrongCard: TCustomerAndCashierLines read FWrongCard;
    property EBTTypePrompt: TCustomerAndCashierLines read FEBTTypePrompt;
    property TerminalClosedPrompt: TCustomerAndCashierLines read FTerminalClosedPrompt;
    property TransactionCancelled: TCustomerAndCashierLines read FTransactionCancelled;
    property ProcessingPromptForVoid: TCustomerAndCashierLines read FProcessingPromptForVoid;
    property StartWICSession: TCustomerAndCashierLines read FStartWICSession;
    property EnterWICPIN: TCustomerAndCashierLines read FEnterWICPIN;
    property SmartWICCardRemoved: TCustomerAndCashierLines read FSmartWICCardRemoved;
    property RetrievingPrescription: TCustomerAndCashierLines read FRetrievingPrescription;
    property UpdatingPrescription: TCustomerAndCashierLines read FUpdatingPrescription;
    property LockingWICCard: TCustomerAndCashierLines read FLockingWICCard;
    property RemoveWICCard: TCustomerAndCashierLines read FRemoveWICCard;
    property DataEntryCashier: TCustomerAndCashierLines read FDataEntryCashier;
    property DataEntryPOSTrackingNumber: TCustomerAndCashierLines read FDataEntryPOSTrackingNumber;
    property DataEntryDOB: TCustomerAndCashierLines read FDataEntryDOB;
    property PONumber: TCustomerAndCashierLines read FPONumber;
    property TokenPinPrompt: TCustomerAndCashierLines read FTokenPinPrompt; // DOEP-72983
    property PinReEntryPrompt: TCustomerAndCashierLines read FPinReEntryPrompt;
    property FallbackPrompt: TCustomerAndCashierLines read FFallbackPrompt;
    property InsertSwipedChipCard: TCustomerAndCashierLines read FInsertSwipedChipCard;
    property GiftSecurityCodeReEntryPrompt: TCustomerAndCashierLines read FGiftSecurityCodeReEntryPrompt; // CPCLIENTS-2930

    constructor Create;
    destructor Destroy; override;
  end;

  TMiscPrompts2 = class(TObject)
  private
    FGetFingerPrintPrompt: TCustomerAndCashierLines;
    FRetryGetFingerPrintPrompt: TCustomerAndCashierLines;
    FAfterGetFingerPrintPrompt: TCustomerAndCashierLines;
    FRetryAfterGetFingerPrintPrompt: TCustomerAndCashierLines;
    FGetCustIDPrompt: TCustomerAndCashierLines;
    FRetryGetCustIDPrompt: TCustomerAndCashierLines;
    FAfterGetCustIDPrompt: TCustomerAndCashierLines;
    FRetryAfterGetCustIDPrompt: TCustomerAndCashierLines;
    FBioDOBLength6Prompt: TCustomerAndCashierLines;
    FBioDOBLength8Prompt: TCustomerAndCashierLines;
    FBioZipCodePrompt: TCustomerAndCashierLines;
    FBioSSNPrompt: TCustomerAndCashierLines;                                    
    FBioMTXUnavailablePrompt: TCustomerAndCashierLines;
  public
    property GetFingerPrintPrompt: TCustomerAndCashierLines read FGetFingerPrintPrompt;
    property RetryGetFingerPrintPrompt: TCustomerAndCashierLines read FRetryGetFingerPrintPrompt;
    property AfterGetFingerPrintPrompt: TCustomerAndCashierLines read FAfterGetFingerPrintPrompt;
    property RetryAfterGetFingerPrintPrompt: TCustomerAndCashierLines read FRetryAfterGetFingerPrintPrompt;
    property GetCustIDPrompt: TCustomerAndCashierLines read FGetCustIDPrompt;
    property RetryGetCustIDPrompt: TCustomerAndCashierLines read FRetryGetCustIDPrompt;
    property AfterGetCustIDPrompt: TCustomerAndCashierLines read FAfterGetCustIDPrompt;
    property RetryAfterGetCustIDPrompt: TCustomerAndCashierLines read FRetryAfterGetCustIDPrompt;
    property BioMTXUnavailablePrompt: TCustomerAndCashierLines read FBioMTXUnavailablePrompt;
    property BioDOBLength6Prompt: TCustomerAndCashierLines read FBioDOBLength6Prompt;
    property BioDOBLength8Prompt: TCustomerAndCashierLines read FBioDOBLength8Prompt;
    property BioZipCodePrompt: TCustomerAndCashierLines read FBioZipCodePrompt;
    property BioSSNPrompt: TCustomerAndCashierLines read FBioSSNPrompt;         
    procedure Assign(AMiscPrompts: TMiscPrompts2);
    constructor Create;
    destructor Destroy; override;
  end;

  TMiscProperties = class(TObject)
  private
    FIDTypesRequiringStateCode: TStringList;
  public
    constructor Create;
    destructor Destroy; override;
    property IDTypesRequiringStateCode: TStringList read FIDTypesRequiringStateCode;
  end;

  TTruRatingProps = class // 6330
  private
    FTruRatingActive: boolean;
    FTruRatingDefaultCashierLines: TLineCollection5;
    FTruRatingMaxFailureCount: integer;
    FTruRatingPartnerId: string;
    FTruRatingServerAddress: string;
    FTruRatingTimeoutInSeconds: integer;
  public
    property TruRatingActive: boolean read FTruRatingActive write FTruRatingActive;
    property TruRatingDefaultCashierLines: TLineCollection5 read FTruRatingDefaultCashierLines write FTruRatingDefaultCashierLines;
    property TruRatingMaxFailureCount: integer read FTruRatingMaxFailureCount write FTruRatingMaxFailureCount;
    property TruRatingPartnerId: string read FTruRatingPartnerId write FTruRatingPartnerId;
    property TruRatingServerAddress: string read FTruRatingServerAddress write FTruRatingServerAddress;
    property TruRatingTimeoutInSeconds: integer read FTruRatingTimeoutInSeconds write FTruRatingTimeoutInSeconds;
    constructor Create;
    destructor Destroy; override;
  end;

  TGeneralParameters = class(TObject)
  private
    FKeyAssignments: TKeyAssignments;
    FPrimaryActions: TTerminalActionList;
    FReceiptHeader: TLineCollection4;
    FReceiptFooter: TLineCollection2;
    FManagerIDValidationType: string;
    FICEScreenFileName: string;
    FCashierDisplaySize: string;
    FMiscPrompts: TMiscPrompts;
    FMiscProperties: TMiscProperties;
    FWICStateCode: string;
    FRKIFileName: string;
    FTruRatingProps: TTruRatingProps; // 6330
  public
    TerminalCode: string;
    CustCashBackInCents: string; // DEV-23642
    property KeyAssignments: TKeyAssignments read FKeyAssignments;
    property PrimaryActions: TTerminalActionList read FPrimaryActions;
    property ReceiptHeader: TLineCollection4 read FReceiptHeader;
    property ReceiptFooter: TLineCollection2 read FReceiptFooter;
    property ManagerIDValidationType: string read FManagerIDValidationType write FManagerIDValidationType;
    property ICEScreenFileName: string read FICEScreenFileName write FICEScreenFileName;
    property RKIFileName: string read FRKIFileName write FRKIFileName; // TFS-9773
    property CashierDisplaySize: string read FCashierDisplaySize write FCashierDisplaySize;
    property MiscPrompts: TMiscPrompts read FMiscPrompts;
    property MiscProperties: TMiscProperties read FMiscProperties;
    property WICStateCode: string read FWICStateCode write FWICStateCode;
    property TruRatingProps: TTruRatingProps read FTruRatingProps write FTruRatingProps; // 6330
    constructor Create;
    destructor Destroy; override;
  end;

  TTenderList = class;

  TTransactionList = class;

  TTransaction = class(TObject)
  private
    FTransactionList: TTransactionList;
    FName: string;
    FActionList: TTerminalActionList;
    FVoidAllowed: string;
    FCustomerIdLookup: Boolean; // CPCLIENTS-7494
    FCardSlideManualActionList: TTerminalActionList;
  public
    constructor Create(ATransactionList: TTransactionList; AName: string);
    destructor Destroy; override;
    function TACExists(ATAC: string): Boolean;
    procedure Assign(ATransaction: TTransaction);
    property Name: string read FName write FName;
    property Actions: TTerminalActionList read FActionList;
    property VoidAllowed: string read FVoidAllowed write FVoidAllowed;
    property CustomerIdLookup: Boolean read FCustomerIdLookup write FCustomerIdLookup;  // CPCLIENTS-7494
    property CardSlideManualActionList: TTerminalActionList read FCardSlideManualActionList;
  end;

  TTransactionList = class(TObject)
  private
    FItems: TList;
    function GetItem(Index: Integer): TTransaction;
    function GetCount: Integer;
  public
    constructor Create;
    destructor Destroy; override;
    procedure Clear;
    function Add(AName: string): TTransaction;
    procedure Delete(Index: Integer);
    function TransactionByName(AName: string): TTransaction;
    procedure Assign(ATransactionList: TTransactionList);
    procedure Sort;
    property Items[Index: Integer]: TTransaction read GetItem; default;
    property Count: Integer read GetCount;
  end;

  TTender = class(TObject)
  private
    FTenderList: TTenderList;
    FName: string;
    FIgnoreTacB: Boolean;
    FTransactions: TTransactionList;
    FMICRManualActionList: TTerminalActionList;
  public
    constructor Create(ATenderList: TTenderList; AName: string);
    destructor Destroy; override;
    function TACExists(ATAC: string): Boolean;
    procedure Assign(ATender: TTender);
    property Name: string read FName write FName;
    property IgnoreTacB: Boolean read FIgnoreTacB write FIgnoreTacB;
    property Transactions: TTransactionList read FTransactions;
    property MICRManualActionList: TTerminalActionList read FMICRManualActionList;
  end;

  TTenderList = class(TObject)
  private
    FItems: TList;
    function GetItem(Index: Integer): TTender;
    function GetCount: Integer;
  public
    constructor Create;
    destructor Destroy; override;
    procedure Clear;
    function Add(AName: string): TTender;
    procedure Delete(Index: Integer);
    function TACExists(ATAC: string): Boolean;
    function TenderByName(AName: string): TTender;
    procedure Assign(ATenderList: TTenderList);
    procedure Sort;
    property Items[Index: Integer]: TTender read GetItem; default;
    property Count: Integer read GetCount;
  end;

  TTransactionDefinitions = class(TObject)
  private
    FTenders: TTenderList;
  public
    constructor Create;
    destructor Destroy; override;
    property Tenders: TTenderList read FTenders;
  end;

  TTerminalResponseList = class;

  TTerminalResponse = class(TObject)
  private
    FTerminalResponseList: TTerminalResponseList;
    FCode: string;
    FActions: TTerminalActionList;
  public
    constructor Create(ATerminalResponseList: TTerminalResponseList; ACode: string);
    destructor Destroy; override;
    property Code: string read FCode write FCode;
    property Actions: TTerminalActionList read FActions;
  end;

  TTerminalResponseList = class(TObject)
  private
    FItems: TList;
    function GetItem(Index: Integer): TTerminalResponse;
    function GetCount: Integer;
  public
    constructor Create;
    destructor Destroy; override;
    procedure Clear;
    function Add(ACode: string): TTerminalResponse;
    function Find(ACode: string): TTerminalResponse;
    procedure Delete(Index: Integer);
    property Items[Index: Integer]: TTerminalResponse read GetItem; default;
    property Count: Integer read GetCount;
  end;

  TMICRResultList = class;

  TMICRResult = class(TObject)
  private
    FMICRResultList: TMICRResultList;
    FCode: string;
    FTranslatedCode: string;
    FCashierLines: TLineCollection2;
  public
    constructor Create(AMICRResultList: TMICRResultList; ACode: string; ATranslatedCode: string);
    destructor Destroy; override;
    property Code: string read FCode write FCode;
    property TranslatedCode: string read FTranslatedCode write FTranslatedCode;
    property CashierLines: TLineCollection2 read FCashierLines;
  end;

  TMICRResultList = class(TObject)
  private
    FItems: TList;
    function GetItem(Index: Integer): TMICRResult;
    function GetCount: Integer;
  public
    constructor Create;
    destructor Destroy; override;
    procedure Clear;
    function Add(ACode: string; ATranslatedCode: string): TMICRResult;
    function ResultByCode(ACode: string): TMICRResult;
    procedure Delete(Index: Integer);
    property Items[Index: Integer]: TMICRResult read GetItem; default;
    property Count: Integer read GetCount;
  end;

  TCheckList = class;

  TCheck = class(TObject)
  private
    FCheckList: TCheckList;
    FType: string;
    FManagerIdRequired: Boolean;
    FSecondaryIdRequired: Boolean;
    FDescription: string;
  public
    constructor Create(ACheckList: TCheckList; AType: string; AManagerIdRequired: Boolean; ASecondaryIdRequired: Boolean);
    destructor Destroy; override;
    property Type_: string read FType write FType;
    property ManagerIdRequired: Boolean read FManagerIdRequired write FManagerIdRequired;
    property SecondaryIdRequired: Boolean read FSecondaryIdRequired write FSecondaryIdRequired;
    property Description: string read FDescription write FDescription;
  end;

  TCheckList = class(TObject)
  private
    FItems: TList;
    function GetItem(Index: Integer): TCheck;
    function GetCount: Integer;
  public
    constructor Create;
    destructor Destroy; override;
    procedure Clear;
    function Add(AType: string; AManagerIdRequired: Boolean; ASecondaryIdRequired: Boolean): TCheck;
    function CheckByType(AType: string): TCheck;
    procedure Delete(Index: Integer);
    property Items[Index: Integer]: TCheck read GetItem; default;
    property Count: Integer read GetCount;
  end;

  TIdList = class;

  TIdReq = class(TObject)
  private
    FIdList: TIdList;
    FType: string;
    FStateCodeRequired: Boolean;
    FManagerIdRequired: Boolean;
  public
    constructor Create(AIdList: TIdList; AType: string; AStateCodeRequired: Boolean; AManagerIdRequired: Boolean);
    destructor Destroy; override;
    property Type_: string read FType write FType;
    property StateCodeRequired: Boolean read FStateCodeRequired write FStateCodeRequired;
    property ManagerIdRequired: Boolean read FManagerIdRequired write FManagerIdRequired;
  end;

  TIdList = class(TObject)
  private
    FItems: TList;
    function GetItem(Index: Integer): TIdReq;
    function GetCount: Integer;
  public
    constructor Create;
    destructor Destroy; override;
    procedure Clear;
    function Add(AType: string; AStateCodeRequired: Boolean; AManagerIdRequired: Boolean): TIdReq;
    function IdByType(AType: string): TIdReq;
    procedure Delete(Index: Integer);
    property Items[Index: Integer]: TIdReq read GetItem; default;
    property Count: Integer read GetCount;
  end;

  TCheckConfiguration = class(TObject)
  private
    FMICRReaderType: string;
    FMICRFormat: string;
    FMICRResultCodes: TMICRResultList;
  public
    property MICRReaderType: string read FMICRReaderType write FMICRReaderType;
    property MICRFormat: string read FMICRFormat write FMICRFormat;
    property MICRResultCodes: TMICRResultList read FMICRResultCodes;
    constructor Create;
    destructor Destroy; override;
  end;

  TWinEPSConfiguration = class(TXMLConfiguration)
  private
    FOpenEPSParameters: TOpenEPSParameters;
    FGeneralParameters: TGeneralParameters;
    FTransactionDefinitions: TTransactionDefinitions;
    FSoftDeclineActions: TTerminalResponseList;
    FSoftDeclineMICRManualActionList: TTerminalActionList; // DEV-18499
    FCheckConfiguration: TCheckConfiguration;
    FParameterVersion: Integer;
    FVerifyXML: string;
    procedure SortTendersAndTransactions;
  public
    fileName: string;
    Version: string;
    LastModified: string;                                                       
    constructor Create;
    destructor Destroy; override;
    procedure SaveToXML(AFileName: string);
    function LoadFromXML(AFileName: string): Boolean;
    function ValidateVersion: boolean;                                          

    procedure AddSequence(btnTag: string);
    procedure UpdateEWicConfig; // add
    procedure SetDefaultCardSlideManualActionList;

    property OpenEPSParameters: TOpenEPSParameters read FOpenEPSParameters;
    property GeneralParameters: TGeneralParameters read FGeneralParameters;
    property TransactionDefinitions: TTransactionDefinitions read FTransactionDefinitions;
    property SoftDeclineActions: TTerminalResponseList read FSoftDeclineActions;
    property CheckConfiguration: TCheckConfiguration read FCheckConfiguration;
    property ParameterVersion: Integer read FParameterVersion write FParameterVersion;
    property SoftDeclineMICRManualActionList: TTerminalActionList read FSoftDeclineMICRManualActionList; // DEV-18499
  end;

  TCardSlideFail = (csfManualSequence, csfErrorPrompts);

  TCashierDisplayType = (cdt2by16, cdt5by40);

  TSequenceType = (stCardSlideManual, stMICRManual);

  TLineCollectionRec2 = packed record
    Line1: string;
    Line2: string;
  end;

  TLineCollectionRec5 = packed record
    Line1: string;
    Line2: string;
    Line3: string;
    Line4: string;
    Line5: string;
  end;

const
  CardSlideFailStr: array [TCardSlideFail] of string = ('M', 'E');
  NOT_FOUND = -1;

function GetCardSlideFail(ACardSlideFail: string): TCardSlideFail;
function GetCTLSConfigType(aCTLSConfigType: string): TCTLSConfigType;
function VASModeStrToEnum(aVasMode: string): TVASMode;

implementation

uses
  GeneralUtilities,  // for SplitStr
  EMVManager,
  StringUtils,
  MTX_Utils,
  MTX_Lib;

function GetCardSlideFail(ACardSlideFail: string): TCardSlideFail;
var
  i: TCardSlideFail;
begin
  Result := Low(TCardSlideFail);
  for i := Low(TCardSlideFail) to High(TCardSlideFail) do
  begin
    if CardSlideFailStr[i] = ACardSlideFail then
    begin
      Result := i;
      break;
    end;
  end;
end;  

function GetCTLSConfigType(aCTLSConfigType: string): TCTLSConfigType;
var
  i: TCTLSConfigType;
begin
  Result := Low(TCTLSConfigType);
  for i := Low(TCTLSConfigType) to High(TCTLSConfigType) do
  begin
    if CTLSConfigTypeStr[i] = aCTLSConfigType then
    begin
      Result := i;
      break;
    end;
  end;
end;

function VASModeStrToEnum(aVasMode: string): TVASMode;
var
  i: TVasMode;
begin
  Result := vmNone;
  for i := Low(TVASMode) to High(TVASMode) do
  begin
    if SameText(VASModesStr[i], aVASMode) then
    begin
      Result := i;
      break;
    end;
  end;
end;

function TenderCompare(Item1, Item2: Pointer): Integer;
var
  Tender1, Tender2: string;
  i, Tender1Index, Tender2Index: Integer;
begin
  Result := 0;
  Tender1 := TTender(Item1).Name;
  Tender2 := TTender(Item2).Name;
  Tender1Index := -1;
  Tender2Index := -1;
  for i := Low(RealTenderDescrip) to High(RealTenderDescrip) do
  begin
    if RealTenderDescrip[i] = Tender1 then Tender1Index := i;
    if RealTenderDescrip[i] = Tender2 then Tender2Index := i;
  end;
  if (Tender1Index <> -1) and (Tender2Index <> -1) then
  begin
    if Tender1Index < Tender2Index then Result := -1
    else
    if Tender1Index > Tender2Index then Result := 1;
  end;
end;

function TranCompare(Item1, Item2: Pointer): Integer;
var
  Tran1, Tran2: string;
  i, Tran1Index, Tran2Index: Integer;
begin
  Result := 0;
  Tran1 := TTransaction(Item1).Name;
  Tran2 := TTransaction(Item2).Name;
  Tran1Index := -1;
  Tran2Index := -1;
  for i := Low(RealTranDescrip) to High(RealTranDescrip) do
  begin
    if RealTranDescrip[i] = Tran1 then Tran1Index := i;
    if RealTranDescrip[i] = Tran2 then Tran2Index := i;
  end;
  if (Tran1Index <> -1) and (Tran2Index <> -1) then
  begin
    if Tran1Index < Tran2Index then Result := -1
    else
    if Tran1Index > Tran2Index then Result := 1;
  end;
end;
{ TKeyAssignments }

procedure TKeyAssignments.Assign(AKeyAssignments: TKeyAssignments);
begin
  FScreenKeys.Assign(AKeyAssignments.ScreenKeys);
  FFunctionKeys.Assign(AKeyAssignments.FunctionKeys);
  FLayerPrompts.Assign(AKeyAssignments.LayerPrompts);
  FEnterKeyIsYes := AKeyAssignments.EnterKeyIsYes;
  FReplaceEMVAppLabels := AKeyAssignments.ReplaceEMVAppLabels;   // TFS-24615
  FCTLSConfig := AKeyAssignments.CTLSConfig;                     // TFS-30049
  FManualButton := AKeyAssignments.ManualButton;                 // TFS-35484
end;

constructor TKeyAssignments.Create;
begin
  inherited;
  FScreenKeys := TScreenKeyList.Create('ScreenKey');
  FFunctionKeys := TKeyList.Create('FunctionKey');
  FLayerPrompts := TLayerPromptList.Create;
end;

destructor TKeyAssignments.Destroy;
begin
  FreeAndNil(FLayerPrompts);
  FreeAndNil(FFunctionKeys);
  FreeAndNil(FScreenKeys);
  inherited;
end;

{ TCustomerAndCashierLines }

procedure TCustomerAndCashierLines.Assign(
  ACustomerAndCashierLines: TCustomerAndCashierLines);
begin
  FCustomerLines.Assign(ACustomerAndCashierLines.CustomerLines);
  FCashierLines.Assign(ACustomerAndCashierLines.CashierLines);
end;

constructor TCustomerAndCashierLines.Create;
begin
  inherited;
  FCustomerLines := TCustomerLineList.Create;
  FCashierLines := TLineCollection5.Create;
end;

destructor TCustomerAndCashierLines.Destroy;
begin
  FreeAndNil(FCashierLines);
  FreeAndNil(FCustomerLines);
  inherited;
end;

{ TMiscPrompts }

constructor TMiscPrompts.Create;
begin
  inherited;
  FIntervalMessage := TCustomerAndCashierLines.Create;
  FPleaseWait := TCustomerAndCashierLines.Create;
  FWrongCard := TCustomerAndCashierLines.Create;
  FEBTTypePrompt := TCustomerAndCashierLines.Create;
  FTerminalClosedPrompt := TCustomerAndCashierLines.Create;
  FTransactionCancelled := TCustomerAndCashierLines.Create;
  FProcessingPromptForVoid := TCustomerAndCashierLines.Create;
  FStartWICSession := TCustomerAndCashierLines.Create;                          
  FEnterWICPIN := TCustomerAndCashierLines.Create;
  FSmartWICCardRemoved := TCustomerAndCashierLines.Create;
  FRetrievingPrescription := TCustomerAndCashierLines.Create;
  FUpdatingPrescription := TCustomerAndCashierLines.Create;
  FLockingWICCard := TCustomerAndCashierLines.Create;
  FRemoveWICCard := TCustomerAndCashierLines.Create;
  FDataEntryCashier := TCustomerAndCashierLines.Create;
  FDataEntryPOSTrackingNumber := TCustomerAndCashierLines.Create;
  FDataEntryDOB := TCustomerAndCashierLines.Create;
  FPONumber := TCustomerAndCashierLines.Create;
  FTokenPinPrompt := TCustomerAndCashierLines.Create;
  FPinReEntryPrompt := TCustomerAndCashierLines.Create;
  FFallbackPrompt := TCustomerAndCashierLines.Create;
  FInsertSwipedChipCard := TCustomerAndCashierLines.Create;
  FGiftSecurityCodeReEntryPrompt := TCustomerAndCashierLines.Create;
end;

destructor TMiscPrompts.Destroy;
begin
  FreeAndNil(FProcessingPromptForVoid);
  FreeAndNil(FTransactionCancelled);
  FreeAndNil(FTerminalClosedPrompt);
  FreeAndNil(FEBTTypePrompt);
  FreeAndNil(FPleaseWait);
  FreeAndNil(FWrongCard);
  FreeAndNil(FIntervalMessage);
  FreeAndNil(FStartWICSession);
  FreeAndNil(FEnterWICPIN);
  FreeAndNil(FSmartWICCardRemoved);
  FreeAndNil(FRetrievingPrescription);
  FreeAndNil(FUpdatingPrescription);
  FreeAndNil(FLockingWICCard);
  FreeAndNil(FRemoveWICCard);
  FreeAndNil(FDataEntryCashier);
  FreeAndNil(FDataEntryPOSTrackingNumber);
  FreeAndNil(FDataEntryDOB);
  FreeAndNil(FPONumber);
  FreeAndNil(FTokenPinPrompt);
  FreeAndNil(FPinReEntryPrompt);
  FreeAndNil(FFallbackPrompt);
  FreeAndNil(FInsertSwipedChipCard);
  FreeAndNil(FGiftSecurityCodeReEntryPrompt);
  inherited;
end;

{ TGeneralParameters }

constructor TGeneralParameters.Create;
begin
  inherited;
  FKeyAssignments := TKeyAssignments.Create;
  FPrimaryActions := TTerminalActionList.Create;
  FReceiptHeader := TLineCollection4.Create;
  FReceiptFooter := TLineCollection2.Create;
  FMiscPrompts := TMiscPrompts.Create;
  FMiscProperties := TMiscProperties.Create;
  FTruRatingProps := TTruRatingProps.Create; // 6330
end;

destructor TGeneralParameters.Destroy;
begin
  FreeAndNil(FMiscProperties);
  FreeAndNil(FMiscPrompts);
  FreeAndNil(FReceiptFooter);
  FreeAndNil(FReceiptHeader);
  FreeAndNil(FPrimaryActions);
  FreeAndNil(FKeyAssignments);
  FreeAndNil(FTruRatingProps); // 6330
  inherited;
end;

{ TWinEPSConfiguration }

constructor TWinEPSConfiguration.Create;
begin
  inherited;
  FOpenEPSParameters := TOpenEPSParameters.Create;
  FGeneralParameters := TGeneralParameters.Create;
  FTransactionDefinitions := TTransactionDefinitions.Create;
  FSoftDeclineActions := TTerminalResponseList.Create;
  FCheckConfiguration := TCheckConfiguration.Create;
  FSoftDeclineMICRManualActionList := TTerminalActionList.Create; // DEV-18499
end;

destructor TWinEPSConfiguration.Destroy;
begin
  FreeAndNil(FSoftDeclineMICRManualActionList); // DEV-18499
  FreeAndNil(FCheckConfiguration);
  FreeAndNil(FSoftDeclineActions);
  FreeAndNil(FTransactionDefinitions);
  FreeAndNil(FGeneralParameters);
  FreeAndNil(FOpenEPSParameters);
  inherited;
end;

function TWinEPSConfiguration.LoadFromXML(AFileName: string): Boolean;
var
  Node: TXMLParserNode;
  i: Integer;
  {$IFDEF UPDATE}
    FCardSlideManualActionList: TTerminalActionList;
  {$ENDIF UPDATE}

  procedure GetOpenEPSParameters(AParent: TXMLParserNode);
  var
    i: Integer;
    Node: TXMLParserNode;
  begin
    for i := 0 to AParent.Children.Count - 1 do
    begin
      Node := AParent.Children[i];
      if Node.Name = SStandInAllowed then
        OpenEPSParameters.StandInAllowed := StrToBoolean(Node.Text)
      else
      if Node.Name = STerminal then
        OpenEPSParameters.Terminal := Node.Text
      else if Node.Name = STerminal2 then
        OpenEPSParameters.Terminal2 := Node.Text
      else if Node.Name = SValidateCashier then
        OpenEPSParameters.ValidateCashier := StrToBoolean(Node.Text)
      else
      if Node.Name = STerminalPort then
        OpenEPSParameters.TerminalPort := Node.Text
      else if Node.Name = STerminalPort2 then
        OpenEPSParameters.TerminalPort2 := Node.Text
      else if Node.Name = STerminalIPPort then
        OpenEPSParameters.TerminalIPPort := Node.Text
      else
      if Node.Name = SVerishieldEnabled then
        OpenEPSParameters.VerishieldEnabled := StrToBoolean(Node.Text)
      else
      if Node.Name = SSREDEnable then                            // TFS-25351
        OpenEPSParameters.SREDEnable := StrToBoolean(Node.Text)  // TFS-25351
      else                                                       // TFS-25351
      if Node.Name = SSendReceiptToPINPad then
        OpenEPSParameters.SendReceiptToPINPad := StrToBoolean(Node.Text)
      else
      if Node.Name = SDLLExTrace then
        OpenEPSParameters.DLLExTrace := StrToBoolean(Node.Text)
      else
      if Node.Name = STCPIPTrace then
        OpenEPSParameters.TCPIPTrace := StrToBoolean(Node.Text)
      else
      if Node.Name = SSerialTrace then
        OpenEPSParameters.SerialTrace := StrToBoolean(Node.Text)
      else
      if Node.Name = SCardReaderAttached then
        OpenEPSParameters.CardReaderAttached := StrToBoolean(Node.Text)
      else
      if Node.Name = SPINPadAttached then
        OpenEPSParameters.PINPadAttached := StrToBoolean(Node.Text)
      else
      if Node.Name = SPOSGiftCardReturn then
        OpenEPSParameters.POSGiftCardReturn := Node.Text
      else
      if Node.Name = SPenUPTimeOutValue then
        OpenEPSParameters.PenUPTimeOutValue := StrToIntDef(Node.Text, DefaultPenUPTimeOutValue)
      else
      if Node.Name = SPOSDoesSignaturesAfterAllTendersCompleted then
        OpenEPSParameters.POSDoesSignaturesAfterAllTendersCompleted := StrToBoolean(Node.Text)
      else
      if Node.Name = SEndOrderIntervalMessage then
        OpenEPSParameters.EndOrderIntervalMessage := StrToBoolean(Node.Text)
      else if SameText(Node.Name, SKeySlot) then
        OpenEPSParameters.KeySlot := Node.Text // DEV-9974
      else if SameText(Node.Name, SCHDKeySlot) then
        OpenEPSParameters.CHDKeySlot := Node.Text // DOEP-8022
      else if SameText(Node.Name, SEMVforceCreditOffline) then                // TFS-30442
        OpenEPSParameters.EMVforceCreditOffline := StrToBoolean(Node.Text)    // TFS-30442
      else if SameText(Node.Name, SEMVMinCreditToDebit) then				// TFS-35086
        OpenEPSParameters.EMVMinCreditToDebit := StrToIntDef(Node.Text, 0)	// TFS-35086
      else if SameText(Node.Name, SPCI4) then
        OpenEPSParameters.PCI4 := StrToBoolean(Node.Text)
      else if SameText(Node.Name, SRebootTime24Hour) then
        OpenEPSParameters.RebootTime24Hour := Node.Text
      else if SameText(Node.Name, SEnableQuickChip) then
        OpenEPSParameters.EnableQuickChip := StrToBoolean(Node.Text)
      else if SameText(Node.Name, SDisableNFC) then
        OpenEPSParameters.DisableNFC := StrToBoolean(Node.Text)
      else if SameText(Node.Name, SNoPINCVMOption) then
        OpenEPSParameters.NoPINCVMOption := StrToBoolean(Node.Text)
      else if SameText(Node.Name, SVASMode) then
        OpenEPSParameters.VASMode := VASModeStrToEnum(Node.Text)
      else if SameText(Node.Name, SEnablePayWithQRCode) then  // CPCLIENTS-11227
        OpenEPSParameters.EnablePayWithQRCode := StrToBoolean(Node.Text)

    end;
  end;

  procedure GetStringList(AParent: TXMLParserNode; Strings: TStringList);
  var
    k: Integer;
    Node3: TXMLParserNode;
  begin
    Strings.Clear;
    for k := 0 to AParent.Children.Count - 1 do
    begin
      Node3 := AParent.Children[k];
      if Node3.Name = SLine then Strings.Add(Node3.Text);
    end;
  end;

  procedure GetStringListFromNumberedLines(AParent: TXMLParserNode; Strings: TStringList);
  var
    k: Integer;
    Node3: TXMLParserNode;
  begin
    Strings.Clear;
    for k := 0 to AParent.Children.Count - 1 do
    begin
      Node3 := AParent.Children[k];
      if Copy(Node3.Name, 1, Length(SLine)) = SLine then Strings.Add(Node3.Text);
    end;
  end;  

  procedure GetKeyAssignments(AParent: TXMLParserNode; KeyAssignments: TKeyAssignments);
  var
    j, k, m: Integer;
    Node2, Node3, Node4: TXMLParserNode;
    screenKey: TScreenKey;
  begin
    for j := 0 to AParent.Children.Count - 1 do
    begin
      Node2 := AParent.Children[j];
      if Node2.Name = SScreenKeys then
      begin
        for k := 0 to Node2.Children.Count - 1 do
        begin
          Node3 := Node2.Children[k];
          if Node3.Name = SScreenKey then
          begin
            screenKey := KeyAssignments.ScreenKeys.Add( StrToIntDef(Node3.Attr.Values[SLayer], 1),
                                                        StrToIntDef(Node3.Attr.Values[SId], -1),
                                                        Node3.Text,
                                                        Node3.Attr.Values[SCaption],
                                                        Node3.Attr.Values[SLanguageCode]);
            for m := 0 to Node3.Children.Count - 1 do
            begin
              Node4 := Node3.Children[m];
              if Node4.Name = SSubScreenKey then
                screenKey.SubScreenKeys.Add(StrToIntDef(Node4.Attr.Values[SLayer], 1), StrToIntDef(Node4.Attr.Values[SId], -1), Node4.Text, Node4.Attr.Values[SCaption]);
            end;
          end;
        end;
      end
      else
      if Node2.Name = SFunctionKeys then
      begin
        for k := 0 to Node2.Children.Count - 1 do
        begin
          Node3 := Node2.Children[k];
          if Node3.Name = SFunctionKey then
            KeyAssignments.FunctionKeys.Add(StrToIntDef(Node3.Attr.Values[SLayer], 1), StrToIntDef(Node3.Attr.Values[SId], -1), Node3.Text, Node3.Attr.Values[SCaption]);
        end;
      end
      else
      if Node2.Name = SEnterKeyIsYes then
      begin
        KeyAssignments.EnterKeyIsYes := StrToBoolean(Node2.Text);
      end
      else                                                               // TFS-24615
      if Node2.Name = SReplaceEMVAppLabels then                          // TFS-24615
        KeyAssignments.ReplaceEMVAppLabels := StrToBoolean(Node2.Text)   // TFS-24615
      else
      if Node2.Name = SCTLSConfig then
        KeyAssignments.CTLSConfig := GetCTLSConfigType(Node2.Text)
      else
      if Node2.Name = SManualButton then                                 // TFS-35484
        KeyAssignments.ManualButton := StrToBoolean(Node2.Text)          // TFS-35484
      else
      if Node2.Name = SLayerPrompts then
      begin
        for k := 0 to Node2.Children.Count - 1 do
        begin
          Node3 := Node2.Children[k];
          if Node3.Name = SLayer then
          begin
            GetCustomerLines(Node3, KeyAssignments.LayerPrompts.Add(StrToIntDef(Node3.Attr.Values[SId], 0)).Prompts);
          end;
        end;
      end
    end;
  end;

  procedure GetCustomerAndCashierLines(AParent: TXMLParserNode; CustomerAndCashierLines: TCustomerAndCashierLines);
  var
    i: Integer;
    Node: TXMLParserNode;
  begin
    CustomerAndCashierLines.UseForm := AParent.Attr.Values[sUseForm] = 'Y';
    CustomerAndCashierLines.CustomerLines.Clear;
    for i := 0 to AParent.Children.Count - 1 do
    begin
      Node := AParent.Children[i];
      if Node.Name = SCustomerLines then
        GetCustomerLines(Node, CustomerAndCashierLines.CustomerLines)
      else
      if Node.Name = SCashierLines then
        GetLineCollection5(Node, CustomerAndCashierLines.CashierLines);
    end;
  end;

  procedure GetMiscPrompts2(AParent: TXMLParserNode; MiscPrompts: TMiscPrompts2);
  var
    i: Integer;
    Node: TXMLParserNode;
  begin
    for i := 0 to AParent.Children.Count - 1 do
    begin
      Node := AParent.Children[i];
      if Node.Name = SGetFingerPrintPrompt then
        GetCustomerAndCashierLines(Node, MiscPrompts.GetFingerPrintPrompt)
      else
      if Node.Name = SRetryGetFingerPrintPrompt then
        GetCustomerAndCashierLines(Node, MiscPrompts.RetryGetFingerPrintPrompt)
      else
      if Node.Name = SAfterGetFingerPrintPrompt then
        GetCustomerAndCashierLines(Node, MiscPrompts.AfterGetFingerPrintPrompt)
      else
      if Node.Name = SRetryAfterGetFingerPrintPrompt then
        GetCustomerAndCashierLines(Node, MiscPrompts.RetryAfterGetFingerPrintPrompt)
      else
      if Node.Name = SGetCustIDPrompt then
        GetCustomerAndCashierLines(Node, MiscPrompts.GetCustIDPrompt)
      else
      if Node.Name = SRetryGetCustIDPrompt then
        GetCustomerAndCashierLines(Node, MiscPrompts.RetryGetCustIDPrompt)
      else
      if Node.Name = SAfterGetCustIDPrompt then
        GetCustomerAndCashierLines(Node, MiscPrompts.AfterGetCustIDPrompt)
      else
      if Node.Name = SRetryAfterGetCustIDPrompt then
        GetCustomerAndCashierLines(Node, MiscPrompts.RetryAfterGetCustIDPrompt)
      else
      if Node.Name = SBioDOBLength6Prompt then                                  
        GetCustomerAndCashierLines(Node, MiscPrompts.BioDOBLength6Prompt)
      else
      if Node.Name = SBioDOBLength8Prompt then
        GetCustomerAndCashierLines(Node, MiscPrompts.BioDOBLength8Prompt)
      else
      if Node.Name = SBioZipCodePrompt then
        GetCustomerAndCashierLines(Node, MiscPrompts.BioZipCodePrompt)
      else
      if Node.Name = SBioSSNPrompt then
        GetCustomerAndCashierLines(Node, MiscPrompts.BioSSNPrompt)
      else                                                                      
      if Node.Name = SBioMTXUnavailablePrompt then
        GetCustomerAndCashierLines(Node, MiscPrompts.BioMTXUnavailablePrompt);
    end;
  end;

  procedure GetCharityDonationOptions(AParent: TXMLParserNode; CharityDonationOptions: TCharityDonationOptions);  // CPCLIENTS-5956 Charity Donations
  var
    i, j, k: Integer;
    Node, Node2, Node3: TXMLParserNode;
  begin
    if Assigned(AParent) then
      if AParent.Attr.Values[SAllowOtherAmount] <> '' then
          CharityDonationOptions.AllowOtherAmount := StrToBoolean(AParent.Attr.Values[SAllowOtherAmount]);

    for i := 0 to AParent.Children.Count - 1 do
    begin
      Node := AParent.Children[i];
      if Node.Name = SCustomerDisplay then
        GetCustomerLines(Node, CharityDonationOptions.CustomerDisplay)
      else if Node.Name = SCashierDisplay then
        GetLineCollection5(Node, CharityDonationOptions.CashierDisplay)
      else if Node.Name = SAmountOptions then // Amount Options
      begin
        if Node.Attr.Values[SRoundToNearestDollarAmount] <> '' then
          CharityDonationOptions.RoundToNearestDollarAmount := StrToBoolean(Node.Attr.Values[SRoundToNearestDollarAmount]);
        if Node.Attr.Values[SDelayDonationUntilRoundUpSetByPOS] <> '' then  // CPCLIENTS-7473
          CharityDonationOptions.DelayDonationUntilRoundUpSetByPOS := StrToBoolean(Node.Attr.Values[SDelayDonationUntilRoundUpSetByPOS]);
        for j := 0 to Node.Children.Count - 1 do
        begin
          Node2 := Node.Children[j];
          if Node2.Name = SCustomerDisplay then
            GetCustomerLines(Node2, CharityDonationOptions.AOCustomerDisplay)
          else if Node2.Name = SCashierDisplay then
            GetLineCollection5(Node2, CharityDonationOptions.AOCashierDisplay)
          else if Node2.Name = SDonationAmounts then
          begin
            for k := 0 to Node2.Children.Count - 1 do
            begin
              Node3 := Node2.Children[k];
              if Node3.Name = SAmount1 then
                CharityDonationOptions.DonationAmount[1] := StrToIntDef(Node3.Text, 0)       // CPCLIENTS-13012 start
              else if Node3.Name = SAmount2 then
                CharityDonationOptions.DonationAmount[2] := StrToIntDef(Node3.Text, 0)
              else if Node3.Name = SAmount3 then
                CharityDonationOptions.DonationAmount[3] := StrToIntDef(Node3.Text, 0)
              else if Node3.Name = SAmount4 then
                CharityDonationOptions.DonationAmount[4] := StrToIntDef(Node3.Text, 0)       // CPCLIENTS-13012 end
            end;
          end
          else if Node2.Name = SRoundUpLabel then
            GetCustomerLines(Node2, CharityDonationOptions.RoundUpLabel)
          else if Node2.Name = SSkipDonationLabel then
            GetCustomerLines(Node2, CharityDonationOptions.SkipDonationLabel);
        end;
      end
      else if Node.Name = SOtherAmountOptions then // Other Amount Options
      begin
        for j := 0 to Node.Children.Count - 1 do
        begin
          Node2 := Node.Children[j];
          if Node2.Name = SOtherAmountLabel then
            GetCustomerLines(Node2, CharityDonationOptions.OtherAmountLabel)
          else if Node2.Name = SCustomerDisplay then
            GetCustomerLines(Node2, CharityDonationOptions.OAOCustomerDisplay)
          else if Node2.Name = SCashierDisplay then
            GetLineCollection5(Node2, CharityDonationOptions.OAOCashierDisplay)
          else if Node2.Name = SAmountConfirmCustomerDisplay then
            GetCustomerLines(Node2, CharityDonationOptions.AmountConfirmCustomerDisplay)
          else if Node2.Name = SAmountConfirmCashierDisplay then
            GetLineCollection5(Node2, CharityDonationOptions.AmountConfirmCashierDisplay)
        end;
      end;
    end;
  end;

  procedure GetTruRatingInfo(AParent: TXMLParserNode; ATruRating: TTruRatingProps); // 6330
  var
    i: Integer;
    Node: TXMLParserNode;
  begin
    for i := 0 to AParent.Children.Count - 1 do
    begin
      Node := AParent.Children[i];
      if Node.Name = STruRatingActive then
        ATruRating.FTruRatingActive := StrToBoolean(Node.Text)
      else if Node.Name = sTruRatingServerAddress then
        ATruRating.FTruRatingServerAddress := Node.Text
      else if Node.Name = sTruRatingPartnerID then
        ATruRating.FTruRatingPartnerId := Node.Text
      else if Node.Name = sTruRatingTimeoutInSeconds then
        ATruRating.FTruRatingTimeoutInSeconds := StrToIntDef(Node.Text, 5)
      else if Node.Name = sTruRatingMaxFailureCount then
        ATruRating.FTruRatingMaxFailureCount := StrToIntDef(Node.Text, 5)
      else if Node.Name = sDefaultCashierLines then
        GetLineCollection5(Node, ATruRating.TruRatingDefaultCashierLines)
    end;
  end;

  procedure GetActions(AParent: TXMLParserNode; Actions: TTerminalActionList);
  var
    i, j, k: Integer;
    Node, Node2, Node3: TXMLParserNode;
    Action: TTerminalAction;
    tipNCash: TTipAndCashback;
    tmpTAC: string;
    tmpInt: integer;
  begin
    Actions.Clear;
    for i := 0 to AParent.Children.Count - 1 do
    begin
      Node := AParent.Children[i];

      if Node.Name = SAction then
      begin
        tmpTAC := Node.Attr.Values[STAC];
        if tmpTAC.StartsWith('#') then // CPCLIENTS-1411 CPCLIENTS-4711
        begin
          tmpInt := StrToIntDef(tmpTAC.Substring(1, tmpTAC.Length-2),0);
          if tmpInt > 0 then
            Action := Actions.Add((AnsiChar(tmpInt)));
        end
        else
        Action := Actions.Add(tmpTAC);

        Action.Description := Node.Attr.Values[SDescription];

        if Node.Attr.Values[SNumberOfBadSlidesAllowed] <> '' then
          Action.NumberOfBadSlidesAllowed := StrToIntDef(Node.Attr.Values[SNumberOfBadSlidesAllowed], -1);

        if Node.Attr.Values[SNumberOfBadChipReadAllowed] <> '' then
          Action.NumberOfBadChipReadAllowed := StrToIntDef(Node.Attr.Values[SNumberOfBadChipReadAllowed], DEFAULT_BAD_CHIP_READ_COUNT);

        if Node.Attr.Values[SBadCardSlideErrorPromptDisplayTime] <> '' then
          Action.BadCardSlideErrorPromptDisplayTime := StrToIntDef(Node.Attr.Values[SBadCardSlideErrorPromptDisplayTime], -1);

        if Node.Attr.Values[SCardSlideFail] <> '' then
          Action.CardSlideFail := Node.Attr.Values[SCardSlideFail];

        if Node.Attr.Values[SDisableNoZeroKey] <> '' then
          Action.DisableNoZeroKey := StrToBoolean(Node.Attr.Values[SDisableNoZeroKey]);

        if Node.Attr.Values[SPromptForCashbackOnDiscover] <> '' then
          Action.PromptForCashbackOnDiscover := StrToBoolean(Node.Attr.Values[SPromptForCashbackOnDiscover]);

        if Node.Attr.Values[SProhibitCancellingSignatureCapture] <> '' then
          Action.ProhibitCancellingSignatureCapture := StrToBoolean(Node.Attr.Values[SProhibitCancellingSignatureCapture]);

        if Node.Attr.Values[SGetFreqShopperNumFromPINPad] <> '' then
          Action.GetFreqShopperNumFromPINPad := StrToBoolean(Node.Attr.Values[SGetFreqShopperNumFromPINPad]);

        if Node.Attr.Values[SErrorConditionPromptDisplayTime] <> '' then
          Action.ErrorConditionPromptDisplayTime := StrToIntDef(Node.Attr.Values[SErrorConditionPromptDisplayTime], -1);

        if Node.Attr.Values[SSkipForManualEntry] <> '' then
          Action.SkipForManualEntry := StrToBoolean(Node.Attr.Values[SSkipForManualEntry]);
          
        if Node.Attr.Values[SRequireVerificationNo] <> '' then
          Action.RequireVerificationNo := StrToBoolean(Node.Attr.Values[SRequireVerificationNo]);
        if Node.Attr.Values[SECCOnly] <> '' then                                
          Action.ECCOnly := StrToBoolean(Node.Attr.Values[SECCOnly]);
        if Node.Attr.Values[SVerifyLast4] <> '' then // DEV-17192
          Action.VerifyLast4 := StrToBoolean(Node.Attr.Values[SVerifyLast4]);
        if Node.Attr.Values[SFloorLimit] <> '' then
          Action.FloorLimit := StrToIntDef(Node.Attr.Values[SFloorLimit], -1);
        if Node.Attr.Values[SECCProductCode] <> '' then
          Action.ECCProductCode := StrToIntDef(Node.Attr.Values[SECCProductCode], -1);
        if Node.Attr.Values[SPromptForStateCode] <> '' then
          Action.PromptForStateCode := StrToBoolean(Node.Attr.Values[SPromptForStateCode]);
        if Node.Attr.Values[SPINPadEntry] <> '' then
          Action.PINPadEntry := StrToBoolean(Node.Attr.Values[SPINPadEntry]);
        if Node.Attr.Values[SPINPadEntryRetryCount] <> '' then
          Action.PINPadEntryRetryCount := StrToIntDef(Node.Attr.Values[SPINPadEntryRetryCount], 1);
        if Node.Attr.Values[SOverride2ndID] <> '' then
          Action.Override2ndID := StrToBoolean(Node.Attr.Values[SOverride2ndID]); // DEV-8143
        if Node.Attr.Values[SPOSSupplySignature] <> '' then
          Action.POSSupplySignature := StrToBoolean(Node.Attr.Values[SPOSSupplySignature]); // DEV-12747
        if Node.Attr.Values[SPrintPaperReceiptOnManual] <> '' then
          Action.PrintPaperReceiptOnManual := StrToBoolean(Node.Attr.Values[SPrintPaperReceiptOnManual]); // DOEP-16038
        if Node.Attr.Values[SCustomSignature] <> '' then
          Action.CustomSignature := StrToBoolean(Node.Attr.Values[SCustomSignature]); // DEV-19583

        if Node.Attr.Values[SDigitalID] <> '' then // DOEP-26548
          Action.DigitalID := StrToBoolean(Node.Attr.Values[SDigitalID]);
        if Node.Attr.Values[SSuppressLanguageOnVAS] <> '' then        //CPCLIENTS-8202
          Action.SuppressLanguageOnVAS := StrToBoolean(Node.Attr.Values[SSuppressLanguageOnVAS]);
        if Node.Attr.Values[SDigitalIDButtonTitle] <> '' then
          Action.DigitalIDButtonTitle := Node.Attr.Values[SDigitalIDButtonTitle];
        if Node.Attr.Values[SActivateCardReader] <> '' then
          Action.ActivateCardReader := StrToBoolean(Node.Attr.Values[SActivateCardReader]);
        if Node.Attr.Values[SPromptFreqShopAltID] <> '' then // DEV-29738
          Action.PromptFreqShopAltID := StrToBoolean(Node.Attr.Values[SPromptFreqShopAltID]);
        if Node.Attr.Values[SPromptForCustomerOK] <> '' then
          Action.PromptForCustomerOK := StrToBoolean(Node.Attr.Values[SPromptForCustomerOK]);
        if Node.Attr.Values[SPromptCustomerOKForPartialAuth] <> '' then
          Action.PromptCustomerOKForPartialAuth := StrToBoolean(Node.Attr.Values[SPromptCustomerOKForPartialAuth]);
        if Node.Attr.Values[SDualEBTPrompt] <> '' then
          Action.DualEBTPrompt := StrToBoolean(Node.Attr.Values[SDualEBTPrompt]);
        if Node.Attr.Values[SShowDisclaimer] <> '' then
          Action.ShowDisclaimer := StrToBoolean(Node.Attr.Values[SShowDisclaimer]);

        //  DEV-39393 (827.2's DEV-39198), min and max digital ID lengths
        Action.DigitalIDMinLength := StrToIntDef(Node.Attr.Values[SDigitalIDMinLength], DIGITAL_ID_DEFAULT_LENGTH);
        if (Action.DigitalIDMinLength < DIGITAL_ID_MIN_LENGTH) or (Action.DigitalIDMinLength > DIGITAL_ID_MAX_LENGTH) then
          // Prevent any values outside the spec'd boundaries due to corrupted or hand-edited file
          Action.DigitalIDMinLength := DIGITAL_ID_DEFAULT_LENGTH;
        Action.DigitalIDMaxLength := StrToIntDef(Node.Attr.Values[SDigitalIDMaxLength], DIGITAL_ID_DEFAULT_LENGTH);
        if (Action.DigitalIDMaxLength < DIGITAL_ID_MIN_LENGTH) or (Action.DigitalIDMaxLength > DIGITAL_ID_MAX_LENGTH) then
          // Prevent any values outside the spec'd boundaries due to corrupted or hand-edited file
          Action.DigitalIDMaxLength := DIGITAL_ID_DEFAULT_LENGTH;

        if Node.Attr.Values[SDisplayNACHAText] <> '' then // DEV-18499
          Action.DisplayNACHAText := StrToBoolean(Node.Attr.Values[SDisplayNACHAText]);
        if Node.Attr.Values[SSVSAuthNum] <> '' then // DEV-28404
          Action.SVSAuthNum := StrToBoolean(Node.Attr.Values[SSVSAuthNum]);
        if Node.Attr.Values[SEnableWICButton] <> '' then
          Action.EnableWICButton := StrToBoolean(Node.Attr.Values[SEnableWICButton]);
        if Node.Attr.Values[SRequestTokenData] <> '' then
          Action.RequestTokenData := StrToBoolean(Node.Attr.Values[SRequestTokenData]);
        if Node.Attr.Values[SAllowManualAutoTender] <> '' then // DEV-41273
          Action.AllowManualAutoTender := StrToBoolean(Node.Attr.Values[SAllowManualAutoTender]);
        if Node.Attr.Values[SCustomSwipeForm] <> '' then
          Action.CustomSwipeForm := StrToBoolean(Node.Attr.values[SCustomSwipeForm]); // DOEP-71378
        if Node.Attr.Values[SCustomBadCardSlideForm] <> '' then
          Action.CustomBadCardSlideForm := StrToBoolean(Node.Attr.values[SCustomBadCardSlideForm]); // DOEP-71389
        if Node.Attr.Values[SP2PManualEncryption] <> '' then
          Action.P2PManualEncryption := StrToBoolean(Node.Attr.Values[SP2PManualEncryption]);
        if Node.Attr.Values[SDoNotAllowPINBypass] <> '' then // TFS-26084
          Action.DoNotAllowPINBypass := StrToBoolean(Node.Attr.Values[SDoNotAllowPINBypass]);
        if Node.Attr.Values[SWaitForPOSTotalToConvert] <> '' then
          Action.WaitForPOSTotalToConvert := StrToBoolean(Node.Attr.Values[SWaitForPOSTotalToConvert]);
        if Node.Attr.Values[SGiftSecurityCodeRetryCount] <> '' then
          Action.GiftSecurityCodeRetryCount := StrToIntDef(Node.Attr.Values[SGiftSecurityCodeRetryCount], MAX_GIFT_SECURITY_RETRY_COUNT); // CPCLIENTS-2390
        if Node.Attr.Values[SPromptOnlyOnPOSRequest] <> '' then
          Action.PromptOnlyOnPOSRequest := StrToBoolean(Node.Attr.Values[SPromptOnlyOnPOSRequest]); // CPCLIENTS-11904
        if Node.Attr.Values[SCharityDonation] <> '' then
          Action.CharityDonation := StrToBoolean(Node.Attr.Values[SCharityDonation]); // CPCLIENTS-5956 Charity Donations

        if Node.Attr.Values[SFeeAmountOption] <> '' then  // CPCLIENTS-6548 - DeCA Fee Amount changes
          Action.FeeAmountOption := StrToBoolean(Node.Attr.Values[SFeeAmountOption]);

        if Action.TAC = 'j' then // DEV-18499 <
        begin
          if SameText(Trim(Node.Attr.Values[SPromptForCashbackByCheckType]), '') then
            Node.Attr.Values[SPromptForCashbackByCheckType] := 'Personal';
          SplitStr(Node.Attr.Values[SPromptForCashbackByCheckType], ',', Action.PromptForCashbackByCheckType, true);
        end; // DEV-18499 >

        for j := 0 to Node.Children.Count - 1 do
        begin
          Node2 := Node.Children[j];
          if Node2.Name = SCustomerLines then
            GetCustomerLines(Node2, Action.CustomerLines)
          else
          if Node2.Name = SCashierLines then
            GetLineCollection5(Node2, Action.CashierLines)
          else
          if Node2.Name = SCustomerPromptAfterEFTKey then
            GetCustomerLines(Node2, Action.CustomerPromptAfterEFTKey)
          else
          if Node2.Name = SCashierPromptAfterEFTKey then
            GetLineCollection5(Node2, Action.CashierPromptAfterEFTKey)
          else // DEV-17192 <
          if Node2.Name = SCustomerPromptNonMatchingDigits then
            GetCustomerLines(Node2, Action.CustomerPromptNonMatchingDigits)
          else
          if Node2.Name = SCashierPromptNonMatchingDigits then
            GetLineCollection5(Node2, Action.CashierPromptNonMatchingDigits) // DEV-17192 >
          else
          if Node2.Name = SBadCardSlideCustomerDisplay then
            GetCustomerLines(Node2, Action.BadCardSlideCustomerDisplay)
          else
          if Node2.Name = SBadCardSlideCashierDisplay then
            GetLineCollection5(Node2, Action.BadCardSlideCashierDisplay)
          else
          if Node2.Name = SErrorConditionCustomerDisplay then
            GetCustomerLines(Node2, Action.ErrorConditionCustomerDisplay)
          else
          if Node2.Name = SErrorConditionCashierDisplay then
            GetLineCollection5(Node2, Action.ErrorConditionCashierDisplay)
          else
          if Node2.Name = SDigitalIDCustomerDisplay then // DOEP-26548 <
            GetCustomerLines(Node2, Action.DigitalIDCustomerDisplay)
          else
          if Node2.Name = SDigitalIDCashierDisplay then
            GetLineCollection5(Node2, Action.DigitalIDCashierDisplay) // DOEP-26548 >
          else
          if Node2.Name = SPinPadEntryCustomerDisplay then
            GetCustomerLines(Node2, Action.PinPadEntryCustomerDisplay)
          else
          if Node2.Name = SPinPadEntryCashierDisplay then
            GetLineCollection5(Node2, Action.PinPadEntryCashierDisplay) 
          else
          if Node2.Name = SPinPadEntryRetryCustomerDisplay then
            GetCustomerLines(Node2, Action.PinPadEntryRetryCustomerDisplay)
          else
          if Node2.Name = SPinPadEntryRetryCashierDisplay then
            GetLineCollection5(Node2, Action.PinPadEntryRetryCashierDisplay)
          else
          if Node2.Name = SBalanceInquiryActionList then
            GetActions(Node2, Action.BalanceInquiryActionList)
          else
          if (Node2.Name = SCashbackAmounts) or
              (Node2.Name = STipAmounts) or  // CPCLIENTS-6548
              (Node2.Name = SFeeAmountOptions) then
          //since cashback and tip for other amounts are super similair
          begin
            if (Node2.Name = SCashbackAmounts) then
              tipNCash := Action.CashbackAmounts as TTipAndCashback
            else if(Node2.Name = STipAmounts) then
            begin
              tipNCash := Action.TipAmounts as TTipAndCashback;
              Action.TipAmounts.IsTipPercentage := StrToBoolean(Node2.Attr.Values[SIsTipPercent]);
              Action.TipAmounts.AllowSuggestedTip := StrToBoolean(Node2.Attr.Values[SAllowSuggestedTip]);
              Action.TipAmounts.AllowNoTipAmount := StrToBoolean(Node2.Attr.Values[SAllowNoTipAmount]);
              Action.TipAmounts.OnlyPromptWithZeroAmount := StrToBoolean(Node2.Attr.Values[SOnlyPromptWithZeroAmount]); //CPCLIENTS-837
              Action.TipAmounts.DisplayWholeDollarsOnPinpad := StrToBoolean(Node2.Attr.Values[SDisplayWholeDollarsOnPinpad]);     //18606
            end
            // CPCLIENTS-6548 - DeCA Fee Amount changes - start
            else if(Node2.Name = SFeeAmountOptions) then
            begin
              Action.FeeAmounts.FPercentEnable := StrToBoolean(Node2.Attr.Values[SPercentEnable]);
            end
            // CPCLIENTS-6548 - DeCA Fee Amount changes - end
            else
              continue;

            for k := 0 to Node2.Children.Count - 1 do
            begin
              //for all the common fields and attributes
              Node3 := Node2.Children[k];
              if Node3.Name = SCustomerLines then
                GetCustomerLines(Node3, tipNCash.CustomerLines)
              else
              if Node3.Name = SCashierLines then
                GetLineCollection5(Node3, tipNCash.CashierLines)
              else
              if Node3.Name = SCustomerPromptForOtherAmount then
                GetCustomerLines(Node3, tipNCash.CustomerPromptForOtherAmount)
              else
              if Node3.Name = SOtherAmountKeyLabel then
                GetCustomerLines(Node3, tipNCash.OtherAmountKeyLabel)
              else
              if Node3.Name = SNoTipAmountKeyLabel then
                GetCustomerLines(Node3, tipNCash.NoTipAmountKeyLabel)
              else
              if Node3.Name = SCashierPromptForOtherAmount then
                GetLineCollection5(Node3, tipNCash.CashierPromptForOtherAmount)
              else
              if Action.TAC = 'j' then
              begin
                if Node3.Name = SAllowOtherCashbackAmount then
                  Action.CashbackAmounts.AllowOtherCashbackAmount := StrToBoolean(Node3.Text)
                else
                if Node3.Name = SCombineYesNoWithAmount then
                  Action.CashbackAmounts.CombineYesNoWithAmount := StrToBoolean(Node3.Text)
                else
                if Node3.Name = SMaxCashBackKeyLabel then // DEV-10520 <
                  GetCustomerLines(Node3, Action.CashbackAmounts.MaxCashBackKeyLabel)
                else
                if Node3.Name = SAllowMaxCashBackButton then
                  Action.CashbackAmounts.AllowMaxCashBackButton := StrToBoolean(Node3.Text) // DEV-10520 >
                else
                if Node3.Name = SAmount1 then
                  Action.CashbackAmounts.CashbackAmount[1] := StrToIntDef(Node3.Text, 0)    // CPCLIENTS-10087
                else
                if Node3.Name = SAmount2 then
                  Action.CashbackAmounts.CashbackAmount[2] := StrToIntDef(Node3.Text, 0)    // CPCLIENTS-10087
                else
                if Node3.Name = SAmount3 then
                  Action.CashbackAmounts.CashbackAmount[3] := StrToIntDef(Node3.Text, 0)    // CPCLIENTS-10087
                else
                if Node3.Name = SAmount4 then
                  Action.CashbackAmounts.CashbackAmount[4] := StrToIntDef(Node3.Text, 0)    // CPCLIENTS-10087
                else
                if Node3.Name = SAmount5 then
                  Action.CashbackAmounts.CashbackAmount[5] := StrToIntDef(Node3.Text, 0)    // CPCLIENTS-10087
                else
                if Node3.Name = SAmount6 then
                  Action.CashbackAmounts.CashbackAmount[6] := StrToIntDef(Node3.Text, 0);   // CPCLIENTS-10087
              end
              else if Action.TAC = Chr_Tip then
              begin
                if Node3.Name = SAllowOtherTipAmount then
                  Action.TipAmounts.AllowOtherTipAmount := StrToBoolean(Node3.Text)
                else if Node3.Name = STipPercentAmount1 then
                  Action.TipAmounts.TipPercentAmount[1] := StrToIntDef(Node3.Text, 0)
                else if Node3.Name = STipPercentAmount2 then
                  Action.TipAmounts.TipPercentAmount[2] := StrToIntDef(Node3.Text, 0)
                else if Node3.Name = STipPercentAmount3 then
                  Action.TipAmounts.TipPercentAmount[3] := StrToIntDef(Node3.Text, 0)
                else if Node3.Name = STipPercentAmount4 then
                  Action.TipAmounts.TipPercentAmount[4] := StrToIntDef(Node3.Text, 0)
                else if Node3.Name = STipPercentAmount5 then
                  Action.TipAmounts.TipPercentAmount[5] := StrToIntDef(Node3.Text, 0)
                else if Node3.Name = STipDollarAmount1 then
                begin
                  Action.TipAmounts.TipDollarAmount[1] := StrToIntDef(Node3.Text, 0);
                  if Action.TipAmounts.DisplayWholeDollarsOnPinpad then          //18606 if true then convert the tip amount to whole dollar amount
                    Action.TipAmounts.TipDollarAmount[1] := Action.TipAmounts.ConvertTipToWholeDollarAmount(Action.TipAmounts.TipDollarAmount[1]);
                end
                else if Node3.Name = STipDollarAmount2 then
                begin
                  Action.TipAmounts.TipDollarAmount[2] := StrToIntDef(Node3.Text, 0);
                  if Action.TipAmounts.DisplayWholeDollarsOnPinpad then          //18606 if true then convert the tip amount to whole dollar amount
                    Action.TipAmounts.TipDollarAmount[2] := Action.TipAmounts.ConvertTipToWholeDollarAmount(Action.TipAmounts.TipDollarAmount[2]);
                end
                else if Node3.Name = STipDollarAmount3 then
                begin
                  Action.TipAmounts.TipDollarAmount[3] := StrToIntDef(Node3.Text, 0);
                  if Action.TipAmounts.DisplayWholeDollarsOnPinpad then          //18606 if true then convert the tip amount to whole dollar amount
                    Action.TipAmounts.TipDollarAmount[3] := Action.TipAmounts.ConvertTipToWholeDollarAmount(Action.TipAmounts.TipDollarAmount[3]);
                end
		            else if Node3.Name = STipDollarAmount4 then
                  Action.TipAmounts.TipDollarAmount[4] := StrToIntDef(Node3.Text, 0)
                else if Node3.Name = STipDollarAmount5 then
                  Action.TipAmounts.TipDollarAmount[5] := StrToIntDef(Node3.Text, 0);
              end
              else if Action.TAC = Chr_FeeAmount then // CPCLIENTS-6548 - DeCA Fee Amount changes
              begin
                 if Node3.Name = SPercent then
                   Action.FeeAmounts.FPercent := StrToIntDef(Node3.Text, 0)
                 else if Node3.Name = SAmount then
                   Action.FeeAmounts.FAmount := StrToIntDef(Node3.Text, 0);
              end;
            end;

            if Action.TipAmounts.DisplayWholeDollarsOnPinpad then        //18769 assign tip in the array excluding zero tip if present.
              Action.TipAmounts.AssignWholeDollarAmountArray(Action.TipAmounts);
          end
          else
          if Node2.Name = SCheckTypesRequiringSecondaryID then
            GetStringList(Node2, Action.CheckTypesRequiringSecondaryID)
          else
          if Node2.Name = SCheckTypesRequiringSecondaryIDSlide then
            GetStringList(Node2, Action.CheckTypesRequiringSecondaryIDSlide)
          else
          if Node2.Name = SCheckTypesRequiring2DBarcode then
            GetStringList(Node2, Action.CheckTypesRequiring2DBarcode)
          else
          if Node2.Name = SRequireVerificationNoCustomerDisplay then
            GetCustomerLines(Node2, Action.RequireVerificationNoCustomerDisplay)
          else
          if Node2.Name = SRequireVerificationNoCashierDisplay then
            GetLineCollection5(Node2, Action.RequireVerificationNoCashierDisplay)
          else                                                                  
          if Node2.Name = SCheckTypesRequiringManagerID then
            GetStringList(Node2, Action.CheckTypesRequiringManagerID)
          else
          if Node2.Name = SCheckTypesRequiringPhoneNumber then
            GetStringList(Node2, Action.CheckTypesRequiringPhoneNumber)
          else
          if Node2.Name = SCheckTypesRequiringSSN  then
            GetStringList(Node2, Action.CheckTypesRequiringSSN )
          else
          if Node2.Name = SCheckTypesRequiringPayrollIssueDate then
            GetStringList(Node2, Action.CheckTypesRequiringPayrollIssueDate)
          else                    
          if Node2.Name = SIDTypesRequiringManagerID then
            GetStringList(Node2, Action.IDTypesRequiringManagerID)
          else
          if Node2.Name = SBioMTXServerPort then
            Action.BioMTXServerPort := StrToIntDef(Node2.Text, 0)
          else
          if Node2.Name = SKeyAssignments then
            GetKeyAssignments(Node2, Action.KeyAssignments)
          else
          if Node2.Name = SMiscPrompts then
            GetMiscPrompts2(Node2, Action.MiscPrompts)
          else if Node2.Name = SPromptAfterFeeAmtSet then
            GetCustomerAndCashierLines(Node2, Action.PromptAfterFeeAmtSet)
          else if Node2.Name = SCustomerOKPartialAuth then // DOEP-48492 - Fixed to read from the correct xml node
            GetCustomerAndCashierLines(Node2, Action.CustomerOKPartialAuth)
          else if Node2.Name = SDualEBTPromptSet then
            // DOEP-45073
            GetCustomerAndCashierLines(Node2, Action.DualEBTPromptSet)
          else if Node2.Name = SDisclaimerLines then
            GetStringListFromNumberedLines(Node2, Action.DisclaimerLines)
          else if Node2.Name = SBadCardSlidePrompts then
            GetCustomerAndCashierLines(Node2, Action.BadCardSlidePrompts) // DOEP-71133
          else if Node2.Name = SBadChipReadPrompts then
            GetCustomerAndCashierLines(Node2, Action.BadChipReadPrompts)
          else if Node2.Name = SCharityDonationOptions then // CPCLIENTS-5956
            GetCharityDonationOptions(Node2, Action.CharityDonationOptions);

          if Action.TAC = Chr_Tip then
          begin
            if (Node2.Attr.IndexOfName(SAllowSuggestedTip) = NOT_FOUND) then
              Action.TipAmounts.AllowSuggestedTip := True;
          end;

        end;
      end;
    end;
  end;

  procedure GetMiscProperties(AParent: TXMLParserNode; MiscProperties: TMiscProperties);
  var
    i: Integer;
    Node: TXMLParserNode;
  begin
    for i := 0 to AParent.Children.Count - 1 do
    begin
      Node := AParent.Children[i];
      if Node.Name = SIDTypesRequiringStateCode then
        GetStringList(Node, MiscProperties.IDTypesRequiringStateCode);
    end;
  end;

  procedure GetMiscPrompts(AParent: TXMLParserNode; MiscPrompts: TMiscPrompts);
  var
    i, lang: Integer;
    Node: TXMLParserNode;
    FoundWrongCard: boolean;
    CustomerLine : TCustomerLine;
  begin
    FoundWrongCard := false;
    for i := 0 to AParent.Children.Count - 1 do
    begin
      Node := AParent.Children[i];
      if Node.Name = SIntervalMessage then
        GetCustomerAndCashierLines(Node, MiscPrompts.IntervalMessage)
      else
      if Node.Name = SPleaseWait then
        GetCustomerAndCashierLines(Node, MiscPrompts.PleaseWait)
      else
      if Node.Name = SWrongCard then
      begin
        GetCustomerAndCashierLines(Node, MiscPrompts.WrongCard);
        FoundWrongCard := true;
      end
      else
      if Node.Name = SEBTTypePrompt then
        GetCustomerAndCashierLines(Node, MiscPrompts.EBTTypePrompt)
      else
      if Node.Name = STerminalClosedPrompt then
        GetCustomerAndCashierLines(Node, MiscPrompts.TerminalClosedPrompt)
      else
      if Node.Name = STransactionCancelled then
        GetCustomerAndCashierLines(Node, MiscPrompts.TransactionCancelled)
      else
      if Node.Name = SProcessingPromptForVoid then
        GetCustomerAndCashierLines(Node, MiscPrompts.ProcessingPromptForVoid)
      else if Node.Name = SStartWICSession then                                 
        GetCustomerAndCashierLines(Node, MiscPrompts.StartWICSession)
      else if Node.Name = SEnterWICPIN then
        GetCustomerAndCashierLines(Node, MiscPrompts.EnterWICPIN)
      else if Node.Name = SSmartWICCardRemoved then
        GetCustomerAndCashierLines(Node, MiscPrompts.SmartWICCardRemoved)
      else if Node.Name = SRetrievingPrescription then
        GetCustomerAndCashierLines(Node, MiscPrompts.RetrievingPrescription)
      else if Node.Name = SUpdatingPrescription then
        GetCustomerAndCashierLines(Node, MiscPrompts.UpdatingPrescription)
      else if Node.Name = SLockingWICCard then
        GetCustomerAndCashierLines(Node, MiscPrompts.LockingWICCard)
      else if Node.Name = SRemoveWICCard then
        GetCustomerAndCashierLines(Node, MiscPrompts.RemoveWICCard)
      else if Node.Name = SDataEntryCashier then                                
        GetCustomerAndCashierLines(Node, MiscPrompts.DataEntryCashier)
      else if Node.Name = SDataEntryPOSTrackingNumber then
        GetCustomerAndCashierLines(Node, MiscPrompts.DataEntryPOSTrackingNumber)
      else if Node.Name = SDataEntryDOB then
        GetCustomerAndCashierLines(Node, MiscPrompts.DataEntryDOB)
      else if Node.Name = SPONumber then
        GetCustomerAndCashierLines(Node, MiscPrompts.PONumber)
      else if Node.Name = STokenPinPrompt then
        GetCustomerAndCashierLines(Node, MiscPrompts.TokenPinPrompt) // DOEP-72983
      else if Node.Name = SPinReEntryPrompt then
        GetCustomerAndCashierLines(Node, MiscPrompts.PinReEntryPrompt)
      else if Node.Name = SFallbackPrompt then
        GetCustomerAndCashierLines(Node, MiscPrompts.FallbackPrompt)
      else if Node.Name = SReinsertCardPrompt then
        GetCustomerAndCashierLines(Node, MiscPrompts.InsertSwipedChipCard)
      else if Node.Name = SGiftSecurityCodeReEntryPrompt then
        GetCustomerAndCashierLines(Node, MiscPrompts.GiftSecurityCodeReEntryPrompt); // CPCLIENTS-2390

    end;
    // set default
    if MiscPrompts.PONumber.CustomerLines.Count = 0 then
    begin
      for lang := 1 to 3 do
      begin
        CustomerLine := MiscPrompts.PONumber.CustomerLines.Add(lang);
        if Assigned(CustomerLine) then
          CustomerLine.Line1 := 'Enter P.O. Number';
      end;
      MiscPrompts.PONumber.CashierLines.Line1 := 'Please wait...';
    end;

    if NOT FoundWrongCard and Assigned(MiscPrompts.WrongCard) then
    begin
      MiscPrompts.WrongCard.UseForm := false;
      for lang := 1 to 3 do
      begin
        CustomerLine := MiscPrompts.WrongCard.CustomerLines.Add(lang);
        if Assigned(CustomerLine) then // DEV-60148
          CustomerLine.Line1 := 'Wrong Prompt';
      end;
      MiscPrompts.WrongCard.CashierLines.Line1 := 'Wrong Prompt';
    end;
    // DOEP-72983
    if MiscPrompts.TokenPinPrompt.CustomerLines.Count = 0 then
    begin
      for lang := 1 to 3 do
      begin
        CustomerLine := MiscPrompts.TokenPinPrompt.CustomerLines.Add(lang);
        if Assigned(CustomerLine) then
        begin
          CustomerLine.Line1 := 'Please Enter PIN';
          CustomerLine.Line2 := 'and Press ENTER';
        end;
      end;
      MiscPrompts.TokenPinPrompt.CashierLines.Line1 := 'Cust Enter PIN';
    end;
    if MiscPrompts.PinReEntryPrompt.CustomerLines.Count = 0 then
    begin
      for lang := 1 to 3 do
      begin
        CustomerLine := MiscPrompts.PinReEntryPrompt.CustomerLines.Add(lang);
        if Assigned(CustomerLine) then
        begin
          CustomerLine.Line1 := 'Please Re-Enter PIN';
          CustomerLine.Line2 := 'and Press ENTER';
        end;
      end;
      MiscPrompts.PinReEntryPrompt.CashierLines.Line1 := 'Cust Re-Enter PIN';
    end;
    if MiscPrompts.FallbackPrompt.CustomerLines.Count = 0 then
    begin
      for lang := 1 to 3 do
      begin
        CustomerLine := MiscPrompts.FallbackPrompt.CustomerLines.Add(lang);
        if Assigned(CustomerLine) then
        begin
          CustomerLine.Line1 := 'Fallback required';
          CustomerLine.Line2 := 'Please swipe Card';

          //FOR TESTING ONLY  XXX123
          if lang = 2 then
            CustomerLine.Line1 := 'es Fallback required';
          if lang = 3 then
            CustomerLine.Line1 := 'fr Fallback required';
        end;
      end;
      MiscPrompts.FallbackPrompt.CashierLines.Line1 := 'Cust Swipe Card';
    end;
    if MiscPrompts.InsertSwipedChipCard.CustomerLines.Count = 0 then
    begin
      for lang := 1 to 3 do
      begin
        CustomerLine := MiscPrompts.InsertSwipedChipCard.CustomerLines.Add(lang);
        if Assigned(CustomerLine) then
        begin
          CustomerLine.Line1 := 'Please Insert Card';
          CustomerLine.Line2 := 'This is a chip card';

          //FOR TESTING ONLY   XXX123
          if lang = 2 then
            CustomerLine.Line1 := 'es Please Insert Card';
          if lang = 3 then
            CustomerLine.Line1 := 'fr Please Insert Card';
        end;
      end;
      MiscPrompts.InsertSwipedChipCard.CashierLines.Line1 := 'Cust Insert Card';
    end;
    if MiscPrompts.GiftSecurityCodeReEntryPrompt.CustomerLines.Count = 0 then // CPCLIENTS-2390
    begin
      for lang := 1 to 3 do
      begin
        CustomerLine := MiscPrompts.GiftSecurityCodeReEntryPrompt.CustomerLines.Add(lang);
        if Assigned(CustomerLine) then
        begin
          CustomerLine.Line1 := 'Please Re-Enter Gift Security Code';
          CustomerLine.Line2 := 'and Press ENTER';
        end;
      end;
      MiscPrompts.GiftSecurityCodeReEntryPrompt.CashierLines.Line1 := 'Cust Re-Enter Gift Security Code';
    end;
  end;

  procedure GetGeneralParameters(AParent: TXMLParserNode);
  var
    i: Integer;
    Node: TXMLParserNode;
  begin
    GeneralParameters.KeyAssignments.ScreenKeys.Clear;
    GeneralParameters.KeyAssignments.FunctionKeys.Clear;
    GeneralParameters.KeyAssignments.LayerPrompts.Clear;
    for i := 0 to AParent.Children.Count - 1 do
    begin
      Node := AParent.Children[i];

      if Node.Name = SKeyAssignments then
        GetKeyAssignments(Node, GeneralParameters.KeyAssignments);

      if Node.Name = SPrimaryActions then
        GetActions(Node, GeneralParameters.PrimaryActions);

      if Node.Name = SReceiptHeader then
        GetLineCollection4(Node, GeneralParameters.ReceiptHeader);

      if Node.Name = SReceiptFooter then
        GetLineCollection2(Node, GeneralParameters.ReceiptFooter);

      if Node.Name = SManagerIDValidationType then
        GeneralParameters.ManagerIDValidationType := Node.Text;

      if Node.Name = SWICStateCode then                                         
        GeneralParameters.WICStateCode := Node.Text;

      if Node.Name = SICEScreenFileName then
        GeneralParameters.ICEScreenFileName := Node.Text;
      if SameText(Node.Name, STerminalCode) then
        GeneralParameters.TerminalCode := Node.Text;
      if SameText(Node.Name, SRKIFileName) then
        GeneralParameters.RKIFileName := Node.Text; // TFS-9773

      if SameText(Node.Name, SCustCashBackInCents) then // DEV-23642
        GeneralParameters.CustCashBackInCents := Node.Text; // DEV-23642

      if Node.Name = SCashierDisplaySize then
        GeneralParameters.CashierDisplaySize := Node.Text;

      if Node.Name = SMiscPrompts then
        GetMiscPrompts(Node, GeneralParameters.MiscPrompts);

      if Node.Name = SMiscProperties then
        GetMiscProperties(Node, GeneralParameters.MiscProperties);

      if Node.Name = STruRating then // 6330
        GetTruRatingInfo(Node, GeneralParameters.TruRatingProps); // 6330

    end;
  end;

  procedure GetTransactionDefinitions(AParent: TXMLParserNode);
  var
    Node, Node2, Node3, Node4, Node5: TXMLParserNode;
    i, j, k, m, n: Integer;
    Tender: TTender;
    Transaction: TTransaction;
    cp: integer;
  begin
    cp := 1;
    try
    TransactionDefinitions.Tenders.Clear;
    for i := 0 to AParent.Children.Count - 1 do
    begin
      cp := 2;
      Node := AParent.Children[i];
      if Node.Name = STenderList then
      begin
        cp := 4;
        for j := 0 to Node.Children.Count - 1 do
        begin
          cp := 5;
          Node2 := Node.Children[j];
          if Node2.Name = STender then
          begin
            cp := 7;
            Tender := TransactionDefinitions.Tenders.Add(Node2.Attr.Values[SName]);
            Tender.IgnoreTacB := StrToBoolean(Node2.Attr.Values[SIgnoreTacB]);
            for k := 0 to Node2.Children.Count - 1 do
            begin
              cp := 10;
              Node3 := Node2.Children[k];
              if Node3.Name = STransactionList then
              begin
                cp := 11;
                for m := 0 to Node3.Children.Count - 1 do
                begin
                  cp := 12;
                  Node4 := Node3.Children[m];
                  if Node4.Name = STransaction then
                  begin
                    cp := 14;
                    Transaction := Tender.Transactions.Add(Node4.Attr.Values[SName]);
                    Transaction.VoidAllowed := Node4.Attr.Values[SVoidAllowed];
                    Transaction.CustomerIdLookup := iif(Node4.Attr.Values[SCustomerIdLookup] = 'Y', True, False); // CPCLIENTS-7494
                    for n := 0 to Node4.Children.Count - 1 do
                    begin
                      cp := 17;
                      Node5 := Node4.Children[n];
                      if Node5.Name = SActionList then
                        GetActions(Node5, Transaction.Actions)
                      else
                      if Node5.Name = SCardSlideManualActionList then
                        GetActions(Node5, Transaction.CardSlideManualActionList);
                      cp := 19;
                    end;
                  end;
                end;
              end
              else
              {$IFDEF UPDATE}
                if Node3.Name = SCardSlideManualActionList then
                  begin
                    GetActions(Node3, FCardSlideManualActionList);
                    for n := 0 to tender.Transactions.Count-1 do
                      tender.Transactions[n].CardSlideManualActionList.Assign(FCardSlideManualActionList);
                  end
                else
              {$ENDIF UPDATE}
              if Node3.Name = SMICRManualActionList then
                GetActions(Node3, Tender.MICRManualActionList);
            end;
          end;
        end;
      end;
    end;

    except on e: exception do
      begin
        SM('Try..Except UWinEPSConfiguration - GetTransactionDefinitions cp=' + Inttostr(cp) + ' - ' + e.message);
        raise;
      end;
    end;

  end;

  procedure GetSoftDeclineActions(AParent: TXMLParserNode);
  var
    Node: TXMLParserNode;
    i: Integer;
    TerminalResponse: TTerminalResponse;
  begin
    SoftDeclineActions.Clear;
    for i := 0 to AParent.Children.Count - 1 do
    begin
      Node := AParent.Children[i];
      if Node.Name = STerminalResponse then
      begin
        TerminalResponse := SoftDeclineActions.Add(Node.Attr.Values[SCode]);
        GetActions(Node, TerminalResponse.Actions);
      end
      else if SameText(Node.Name, SMICRManualActionList) then // DEV-18499 <
      begin
        SoftDeclineMICRManualActionList.Clear;
        GetActions(Node, SoftDeclineMICRManualActionList);
      end; // DEV-18499 >      
    end;
  end;

  procedure GetCheckConfiguration(AParent: TXMLParserNode);
  var
    Node, Node2, Node3: TXMLParserNode;
    i, j, k: Integer;
    MICRResult: TMICRResult;
  begin
    for i := 0 to AParent.Children.Count - 1 do
    begin
      Node := AParent.Children[i];
      if Node.Name = SMICRReaderType then
        CheckConfiguration.MICRReaderType := Node.Text
      else
      if Node.Name = SMICRResultCodes then
      begin
        CheckConfiguration.MICRResultCodes.Clear;
        for j := 0 to Node.Children.Count - 1 do
        begin
          Node2 := Node.Children[j];
          if Node2.Name = SMICRResult then
          begin
            MICRResult := CheckConfiguration.MICRResultCodes.Add(Node2.Attr.Values[SCode], Node2.Attr.Values[STranslatedCode]);
            for k := 0 to Node2.Children.Count - 1 do
            begin
              Node3 := Node2.Children[k];
              if Node3.Name = SCashierLines then
                GetLineCollection2(Node3, MICRResult.CashierLines);
            end;
          end;
        end;
      end;
    end;
  end;

begin // LoadFromXML
  result := false;
  try
{$IFDEF MTXEPSDLL}
    if OpenEpsLockDown then
    begin
      SM('OpenEpsLockDown. Skip WinEPSConfiguration.LoadFromXML');
      exit;
    end;
    SM('wc.LoadFromFile');
{$ENDIF}

    FVerifyXML := '';
    if NOT FXMLParser.LoadFromFile(AFileName) then                         // 828.5
      Exit;                                                                // 828.5
    FXMLParser.StartScan;                                                  // 828.5
    ScanElement(nil);
    fileName := AFileName;

{$IFDEF UPDATE}
    FCardSlideManualActionList := TTerminalActionList.Create;
{$ENDIF UPDATE}

    if Root.Name = SConfigTypeName then
    begin
      Version := Root.Attr.Values[SVersion];
      if Version = '' then Version := DEFAULT_XML_VERSION;
      LastModified := Root.Attr.Values[SLastModified];           
      for i := 0 to Root.Children.Count - 1 do
      begin
        Node := Root.Children[i];
        if Node.Name = SParameterVersion then ParameterVersion := StrToIntDef(Node.Text, 0)
        else
        if Node.Name = SOpenEPSParameters then GetOpenEPSParameters(Node)
        else
        if Node.Name = SGeneralParameters then GetGeneralParameters(Node)
        else
        if Node.Name = STransactionDefinitions then GetTransactionDefinitions(Node)
        else
        if Node.Name = SSoftDeclineActions then GetSoftDeclineActions(Node)
        else
        if Node.Name = SCheckConfiguration then GetCheckConfiguration(Node)
        else
        if Node.Name = SVerifyXML then FVerifyXML := Node.Text;
      end;
      Result := FVerifyXML = 'OK';
    end
    else
      Result := False;
    ValidateVersion;
{$IFDEF UPDATE}
    FreeAndNil(FCardSlideManualActionList);
{$ENDIF UPDATE}
    FreeAndNil(Root);
  finally
    try
      // Workaround - Hospitality -  TFS 126118
      // We are doing this since the ConMan changes wont be in for the release
      // requested by Honza
      if (AnsiCompareText(OpenEPSParameters.Terminal, 'XPI-VX690') = 0) and
       (Length(OpenEPSParameters.TerminalPort) = 0) and
       (Length(OpenEPSParameters.TerminalIPPort) > 0) then
      begin
        OpenEPSParameters.TerminalPort := 'TCP/IP';
        SaveToXML('TerminalConfiguration.xml');
      end;
    except
    end;
  end;
end; // LoadFromXML

function TWinEPSConfiguration.ValidateVersion: boolean;          
var
  ver: Double;
  sVer: string;
begin
  // TODO: need to convert XMLObj to double
  ver := GetValidXMLVersion(xfWinEPSConfiguration);
  sVer := ConvertToDecimalSeparator(Version);     // 33046
  result := StrToFloatDef(sVer, 0) = ver;
  if not result then
    SM(Format('%s%s Version (%s) is %s valid (should be %.1f)',
        [iif(not result, '***WARNING ',''), ExtractFileName(fileName), sVer, iif(not result, 'not', ''), ver]));
end;

procedure TWinEPSConfiguration.AddSequence(btnTag: string); // DEV-22476: moved from IntegratedU
var
  tenderNum,
  trxNum,
  lang,
  a,
  i: integer;
  tempTender: TTender;
  tempTrx : TTransaction;
  tenderName,
  trxName,
  manualSeq,
  seq: string;
  CustomerLine : TCustomerLine;
begin // AddSequence
  tenderNum := StrToIntDef(copy(btnTag, 1, 2), 1);
  trxNum := StrToIntDef(copy(btnTag, 3, 2), 1);

  tenderName := RealTenderDescrip[tenderNum];
  trxName := TranDescrip[tenderNum, trxNum];
  tempTender := TransactionDefinitions.Tenders.TenderByName(tenderName);
  if tempTender = nil then
    begin
    tempTender := TransactionDefinitions.Tenders.Add(tenderName);
    if ((tempTender.Name = tnCheck) or (tempTender.Name = tnACH)) then
      tempTender.IgnoreTacB := true;
    end;
  tempTender.Transactions.TransactionByName(trxName).Free;
  tempTrx := tempTender.Transactions.Add(trxName);

  seq := DefaultTacSeq[tenderNum, trxNum];
  for i := 1 to Length(seq) do
    begin
    with tempTrx.Actions.Add(seq[i]) do
      begin
      for lang := 1 to 3 do
        begin
        CustomerLine := CustomerLines.Add(lang);
        if CustomerLine <> nil then
          begin
          CustomerLine.Line1 := DefaultTacPrompts[Pos(seq[i], tacList), 1];
          CustomerLine.Line2 := DefaultTacPrompts[Pos(seq[i], tacList), 2];
          end;
        end;
      CashierLines.Line1 := DefaultTacPrompts[Pos(seq[i], tacList), 3];
      CashierLines.Line2 := DefaultTacPrompts[Pos(seq[i], tacList), 4];
      if seq[i] = 'J' then
        begin
        CheckTypesRequiringPhoneNumber.Clear;
        for a := Low(CheckTypeDescrip) to High(CheckTypeDescrip) do
          CheckTypesRequiringPhoneNumber.Add(CheckTypeDescrip[a]);
        end;
      if seq[i] = 'x' then
        begin
        CheckTypesRequiringSSN.Clear;
        CheckTypesRequiringSSN.Add(cPayroll);
        end;
      if seq[i] = 'r' then
        begin
        CheckTypesRequiringPayrollIssueDate.Clear;
        CheckTypesRequiringPayrollIssueDate.Add(cPayroll);
        end;
      if seq[i] = 'W' then
        with GeneralParameters.MiscProperties.IDTypesRequiringStateCode do
          begin
          Clear;
          Add(IDTypeDescrip[iDL]);
          Add(IDTypeDescrip[iST]);
          end;
      if seq[i] = 'S' then
        begin
          CheckTypesRequiringSecondaryID.Clear;
          CheckTypesRequiringSecondaryID.Add(cPayroll);
          CheckTypesRequiringSecondaryID.Add(cGovernment);
          CheckTypesRequiringSecondaryID.Add(cBusiness);
        end;
      if seq[i] = '#' then
        begin
          CheckTypesRequiring2DBarcode.Clear;
          CheckTypesRequiring2DBarcode.Add(cPayroll);
        end;
      if seq[i] = 'T' then
        begin
          CheckTypesRequiringManagerID.Clear;
          for a := Low(CheckTypeDescrip) to High(CheckTypeDescrip) do
            CheckTypesRequiringManagerID.Add(CheckTypeDescrip[a]);
          IDTypesRequiringManagerID.Clear;
          for a := Low(IDTypeDescrip) to High(IDTypeDescrip) do
            IDTypesRequiringManagerID.Add(IDTypeDescrip[a]);
        end;
      if seq[i] = 'H' then
        PromptForCashbackOnDiscover := true;
      if seq[i] = 'U' then
      begin
        begin
          with tempTender.MICRManualActionList do
            begin
            Clear;
            ManualSeq := iif(SameText(tempTender.Name, tnCheck), DefaultMICRManualSeqForCheck, DefaultMICRManualSeq); // DEV-18499
            for a := 1 to Length(ManualSeq) do
              begin
              with Add(ManualSeq[a]) do
                begin
                for lang := 1 to 3 do
                begin
                  CustomerLine := CustomerLines.Add(lang);
                  if CustomerLine <> nil then
                  begin
                    if SameText(tempTender.Name, tnCheck) then // DEV-18499 <
                    begin
                      CustomerLine.Line1 := 'Hand Your Check';
                      CustomerLine.Line2 := 'To the Cashier';
                    end else // DEV-18499 >
                    begin
                      CustomerLine.Line1 := DefaultTacPrompts[Pos(ManualSeq[a], tacList), 1];
                      CustomerLine.Line2 := DefaultTacPrompts[Pos(ManualSeq[a], tacList), 2];
                    end;
                  end;
                end;
                if SameText(tempTender.Name, tnCheck) then // DEV-18499 <
                begin
                  CashierLines.Line1 := 'Enter check data';
                  CashierLines.Line2 := 'as one field';
                end else // DEV-18499 >
                begin
                  CashierLines.Line1 := DefaultTacPrompts[Pos(ManualSeq[a], tacList), 3];
                  CashierLines.Line2 := DefaultTacPrompts[Pos(ManualSeq[a], tacList), 4];
                end;

                end;
              end;
            end;
        end;
      end;

    end;
  end;

  with tempTrx.CardSlideManualActionList do
    begin
    Clear;
    manualSeq := DefaultManualSeq[tenderNum, trxNum];
    for a := 1 to Length(manualSeq) do
      with Add(manualSeq[a]) do
        begin
        for lang := 1 to 3 do
          begin
          CustomerLine := CustomerLines.Add(lang);
          if CustomerLine <> nil then
            begin
            CustomerLine.Line1 := DefaultTacPrompts[Pos(manualSeq[a], tacList), 1];
            CustomerLine.Line2 := DefaultTacPrompts[Pos(manualSeq[a], tacList), 2];
            end;
          end;
        CashierLines.Line1 := DefaultTacPrompts[Pos(manualSeq[a], tacList), 3];
        CashierLines.Line2 := DefaultTacPrompts[Pos(manualSeq[a], tacList), 4];
        end;
    end;
        
  // EBT FS/CA "Balnace Inquiry" requires "Purchase"
  // Gift Card "Balnace Inquiry" requires "Redeem"
  if (trxName = trBalanceInquiry) and NOT SameText(tenderName, tnEWic) then { must ensure that there is a '$' in the purchase sequence } { part 1 }
  begin
    if tempTender.Transactions.TransactionByName(TranDescrip[tenderNum,1]) = nil then
    begin
      AddSequence(FormatFloat('00', tenderNum) + '01');
    end
    else
      if not tempTender.Transactions.TransactionByName(TranDescrip[tenderNum,1]).TACExists('$') then
        with tempTender.Transactions.TransactionByName(TranDescrip[tenderNum,1]).Actions.Insert('$',0) do
          begin
          for lang := 1 to 3 do
            begin
            CustomerLine := CustomerLines.Add(lang);
            if CustomerLine <> nil then
              begin
              CustomerLine.Line1 := DefaultTacPrompts[Pos('$', tacList), 1];
              CustomerLine.Line2 := DefaultTacPrompts[Pos('$', tacList), 2];
              end;
            end;
          CashierLines.Line1 := DefaultTacPrompts[Pos('$', tacList), 3];
          CashierLines.Line2 := DefaultTacPrompts[Pos('$', tacList), 4];
          end;
  end;

  if ((trxNum = 1) and
      NOT SameText(tenderName, tnEWic) and
      (tempTender.Transactions.TransactionByName(trBalanceInquiry) <> nil)) then { part 2 }
  begin
    if (not tempTrx.TACExists('$')) then
    begin
      with tempTrx.Actions.Insert('$', 0) do
      begin
        for lang := 1 to 3 do
        begin
          CustomerLine := CustomerLines.Add(lang);
          if CustomerLine <> nil then
          begin
            CustomerLine.Line1 := DefaultTacPrompts[Pos('$', tacList), 1];
            CustomerLine.Line2 := DefaultTacPrompts[Pos('$', tacList), 2];
          end;  
        end;
        CashierLines.Line1 := DefaultTacPrompts[Pos('$', tacList), 3];
        CashierLines.Line2 := DefaultTacPrompts[Pos('$', tacList), 4];
      end;
    end;
  end;
end; { AddSequence }

procedure TWinEPSConfiguration.UpdateEWicConfig;
var
  Tender: TTender;
  Trans: TTransaction;
  TransName: string;
  i: integer;
begin
  Tender := TransactionDefinitions.Tenders.TenderByName(tnEWic);
  if NOT Assigned(Tender) then
    Tender := TransactionDefinitions.Tenders.Add(tnEWic);
  for i := 1 to 10 do
  begin
    TransName := Trim(TranDescrip[WinEPS_eWIC, i]);
    if TransName <> '' then
    begin
      Trans := Tender.Transactions.TransactionByName(TransName);
      if NOT Assigned(Trans) then
      begin
        AddSequence(FormatFloat('00',WinEPS_eWIC) + FormatFloat('00', i));
        Trans := Tender.Transactions.TransactionByName(TransName);
      end;
      if Assigned(Trans) and SameText(Trans.Name, 'Pre Auth') then // for both BYL and Publix
        Trans.VoidAllowed := 'N';
    end;
  end;
  // remove force trans for eWIC - we don't support/ for both BYL and Publix
  for i := 0 to Tender.Transactions.Count -1 do
  begin
    if SameText(Tender.Transactions[i].Name, 'Force') then
      begin
        Tender.Transactions.Delete(i);
        Break;
      end;
  end;
end;

procedure TWinEPSConfiguration.SetDefaultCardSlideManualActionList;
var
  Tender: TTender;
  Trans: TTransaction;
  tmpAction: TTerminalAction;
  CustomerLine : TCustomerLine;
  manualSeq: string;
  tenderNum, trxNum, lang, idx: integer;
  i, j, k1, k2: integer;
begin
  for i := 0 to TransactionDefinitions.Tenders.Count-1 do
  begin
    Tender := TransactionDefinitions.Tenders[i];
    for j := 0 to Tender.Transactions.Count -1 do
    begin
      Trans := Tender.Transactions[j];

      // get tender, trx index
      tenderNum := 0;
      trxNum := 0;
      for k1 := 1 to MaxRealTenders do
        if SameText(RealTenderDescrip[k1], Tender.Name) then
        begin
          tenderNum := k1;
          for k2 := 1 to 10 do
            if SameText(TranDescrip[tenderNum, k2], Trans.Name) then
            begin
              trxNum := k2;
              Break;
            end;
        end;
      if (tenderNum = 0) or (trxNum = 0) then
        Continue; 

      if Assigned(Trans.CardSlideManualActionList) then
      begin
        Trans.CardSlideManualActionList.Clear;
        manualSeq := DefaultManualSeq[tenderNum, trxNum];
        for idx := 1 to Length(manualSeq) do
        begin
          tmpAction := Trans.CardSlideManualActionList.Add(manualSeq[idx]);
          for lang := 1 to 3 do
          begin
            CustomerLine := tmpAction.CustomerLines.Add(lang);
            if CustomerLine <> nil then
            begin
              CustomerLine.Line1 := DefaultTacPrompts[Pos(manualSeq[idx], tacList), 1];
              CustomerLine.Line2 := DefaultTacPrompts[Pos(manualSeq[idx], tacList), 2];
            end;
          end;
          tmpAction.CashierLines.Line1 := DefaultTacPrompts[Pos(manualSeq[idx], tacList), 3];
          tmpAction.CashierLines.Line2 := DefaultTacPrompts[Pos(manualSeq[idx], tacList), 4];
        end;
      end;
    end;
  end;
end;

procedure TWinEPSConfiguration.SaveToXML(AFileName: string);
var
  Root, Node, Node2, Node3, Node4, Node5, Node6{, Child}: TXMLParserNode;
  i, j: Integer;

  procedure AddStringNode(ParentNode: TXMLParserNode; NodeName: string; NodeText: string);
  var
    Child: TXMLParserNode;
  begin
    Child := ParentNode.AddChild(NodeName);
    Child.Text := NodeText;
  end;

  procedure AddIntegerNode(ParentNode: TXMLParserNode; NodeName: string; NodeData: Integer);
  var
    Child: TXMLParserNode;
  begin
    Child := ParentNode.AddChild(NodeName);
    Child.Text := IntToStr(NodeData);
  end;

  procedure AddBooleanNode(ParentNode: TXMLParserNode; NodeName: string; NodeData: Boolean);
  var
    Child: TXMLParserNode;
  begin
    Child := ParentNode.AddChild(NodeName);
    Child.Text := BooleanStr[NodeData];
  end;

  procedure AddLineCollection4(ParentNode: TXMLParserNode; Line: TLineCollection4);
  var
    Child: TXMLParserNode;
  begin
    Child := ParentNode.AddChild(SLine1);
    Child.Text := Line.Line1;
    Child := ParentNode.AddChild(SLine2);
    Child.Text := Line.Line2;
    Child := ParentNode.AddChild(SLine3);
    Child.Text := Line.Line3;
    Child := ParentNode.AddChild(SLine4);
    Child.Text := Line.Line4;
  end;

  procedure AddLineCollection2(ParentNode: TXMLParserNode; Line: TLineCollection2);
  var
    Child: TXMLParserNode;
  begin
    Child := ParentNode.AddChild(SLine1);
    Child.Text := Line.Line1;
    Child := ParentNode.AddChild(SLine2);
    Child.Text := Line.Line2;
  end;

  procedure AddStringList(ParentNode: TXMLParserNode; Strings: TStringList);
  var
    j: Integer;
    Line: TXMLParserNode;
  begin
    for j := 0 to Strings.Count - 1 do
    begin
      Line := ParentNode.AddChild(SLine);
      Line.Text := Strings[j];
    end;
  end;

  procedure AddStringListToNumberedLines(ParentNode: TXMLParserNode; Strings: TStringList);
  var
    j: Integer;
    Line: TXMLParserNode;
  begin
    for j := 0 to Strings.Count - 1 do
    begin
      Line := ParentNode.AddChild(SLine + IntToStr(j+1));
      Line.Text := Strings[j];
    end;
  end;  

  procedure AddKeyAssignments(Node: TXMLParserNode; KeyAssignments: TKeyAssignments);
  var
    Node3, Child, Child2: TXMLParserNode;
    i, j: Integer;
  begin
    Node3 := Node.AddChild(SScreenKeys);
    for i := 0 to KeyAssignments.ScreenKeys.Count - 1 do
    begin
      Child := Node3.AddChild(SScreenKey);
      Child.Text := KeyAssignments.ScreenKeys[i].Value;
      Child.Attr.Values[SLayer] := IntToStr(KeyAssignments.ScreenKeys[i].Layer);
      Child.Attr.Values[SId] := IntToStr(KeyAssignments.ScreenKeys[i].Id);
      Child.Attr.Values[SCaption] := KeyAssignments.ScreenKeys[i].Caption;
      Child.Attr.Values[SLanguageCode] := KeyAssignments.ScreenKeys[i].LanguageCode;
      for j := 0 to KeyAssignments.ScreenKeys[i].SubScreenKeys.Count - 1 do
      begin
        Child2 := Child.AddChild(SSubScreenKey);
        Child2.Text := KeyAssignments.ScreenKeys[i].SubScreenKeys[j].Value;
        Child2.Attr.Values[SLayer] := IntToStr(KeyAssignments.ScreenKeys[i].SubScreenKeys[j].Layer);
        Child2.Attr.Values[SId] := IntToStr(KeyAssignments.ScreenKeys[i].SubScreenKeys[j].Id);
        Child2.Attr.Values[SCaption] := KeyAssignments.ScreenKeys[i].SubScreenKeys[j].Caption;
      end;
    end;
    Node3 := Node.AddChild(SFunctionKeys);
    for i := 0 to KeyAssignments.FunctionKeys.Count - 1 do
    begin
      Child := Node3.AddChild(SFunctionKey);
      Child.Text := KeyAssignments.FunctionKeys[i].Value;
      Child.Attr.Values[SLayer] := IntToStr(KeyAssignments.FunctionKeys[i].Layer);
      Child.Attr.Values[SId] := IntToStr(KeyAssignments.FunctionKeys[i].Id);
      Child.Attr.Values[SCaption] := KeyAssignments.FunctionKeys[i].Caption;
    end;
    Node3 := Node.AddChild(SLayerPrompts);
    for i := 0 to KeyAssignments.LayerPrompts.Count - 1 do
    begin
      Child := Node3.AddChild(SLayer);
      Child.Attr.Values[SId] := IntToStr(KeyAssignments.LayerPrompts[i].Id);
      AddCustomerLines(Child, KeyAssignments.LayerPrompts[i].Prompts);
    end;
    Node3 := Node.AddChild(SEnterKeyIsYes);
    Node3.Text := BooleanStr[KeyAssignments.EnterKeyIsYes];
    Node3 := Node.AddChild(SReplaceEMVAppLabels);                   // TFS-24615
    Node3.Text := BooleanStr[KeyAssignments.ReplaceEMVAppLabels];   // TFS-24615
    Node3 := Node.AddChild(SManualButton);                           // TFS-35484
    Node3.Text := BooleanStr[KeyAssignments.ManualButton];           // TFS-35484
    Node3 := Node.AddChild(SCTLSConfig);                             // TFS-30049
    Node3.Text := CTLSConfigTypeStr[KeyAssignments.CTLSConfig];
  end;

  procedure AddCustomerAndCashierLines(ParentNode: TXMLParserNode; CustomerAndCashierLines: TCustomerAndCashierLines);
  begin
    if CustomerAndCashierLines.UseForm  // only add if 'UseForm="Y"  leave others empty
      then ParentNode.Attr.Values[sUseForm] := sNY[CustomerAndCashierLines.UseForm];

    AddCustomerLines(ParentNode.AddChild(SCustomerLines), CustomerAndCashierLines.CustomerLines);
    AddLineCollection5(ParentNode.AddChild(SCashierLines), CustomerAndCashierLines.CashierLines);
  end;

  procedure AddMiscPrompts2(ParentNode: TXMLParserNode; MiscPrompts: TMiscPrompts2);
  begin
    AddCustomerAndCashierLines(ParentNode.AddChild(SGetFingerPrintPrompt), MiscPrompts.GetFingerPrintPrompt);
    AddCustomerAndCashierLines(ParentNode.AddChild(SRetryGetFingerPrintPrompt), MiscPrompts.RetryGetFingerPrintPrompt);
    AddCustomerAndCashierLines(ParentNode.AddChild(SAfterGetFingerPrintPrompt), MiscPrompts.AfterGetFingerPrintPrompt);
    AddCustomerAndCashierLines(ParentNode.AddChild(SRetryAfterGetFingerPrintPrompt), MiscPrompts.RetryAfterGetFingerPrintPrompt);
    AddCustomerAndCashierLines(ParentNode.AddChild(SGetCustIDPrompt), MiscPrompts.GetCustIDPrompt);
    AddCustomerAndCashierLines(ParentNode.AddChild(SRetryGetCustIDPrompt), MiscPrompts.RetryGetCustIDPrompt);
    AddCustomerAndCashierLines(ParentNode.AddChild(SAfterGetCustIDPrompt), MiscPrompts.AfterGetCustIDPrompt);   
    AddCustomerAndCashierLines(ParentNode.AddChild(SBioDOBLength6Prompt), MiscPrompts.BioDOBLength6Prompt); 
    AddCustomerAndCashierLines(ParentNode.AddChild(SBioDOBLength8Prompt), MiscPrompts.BioDOBLength8Prompt);
    AddCustomerAndCashierLines(ParentNode.AddChild(SBioZipCodePrompt), MiscPrompts.BioZipCodePrompt);
    AddCustomerAndCashierLines(ParentNode.AddChild(SBioSSNPrompt), MiscPrompts.BioSSNPrompt); 
    AddCustomerAndCashierLines(ParentNode.AddChild(SRetryAfterGetCustIDPrompt), MiscPrompts.RetryAfterGetCustIDPrompt);
    AddCustomerAndCashierLines(ParentNode.AddChild(SBioMTXUnavailablePrompt), MiscPrompts.BioMTXUnavailablePrompt);
  end;

  procedure AddActions(ParentNode: TXMLParserNode; Actions: TTerminalActionList);
  var
    Child, NextChild, CustLines, CashLines, AllowOther, AllowMax, Amount, NewChild, CombineYesNo: TXMLParserNode;
    i: Integer;
    Action: TTerminalAction;
  begin

    for i := 0 to Actions.Count - 1 do
    begin
      Action := Actions[i];
      Child := ParentNode.AddChild(SAction);
      Child.Attr.Values[STAC] := Action.TAC;
      if Action.Description <> '' then Child.Attr.Values[SDescription] := Action.Description;

      if apNumberOfBadSlidesAllowed in Action.ActionProperties then
        Child.Attr.Values[SNumberOfBadSlidesAllowed] := IntToStr(Action.NumberOfBadSlidesAllowed);
        
      if apNumberOfBadChipReadAllowed in Action.ActionProperties then
        Child.Attr.Values[SNumberOfBadChipReadAllowed] := IntToStr(Action.NumberOfBadChipReadAllowed);

      if apBadCardSlideErrorPromptDisplayTime in Action.ActionProperties then
        Child.Attr.Values[SBadCardSlideErrorPromptDisplayTime] := IntToStr(Action.BadCardSlideErrorPromptDisplayTime);

      if apDigitalID in Action.ActionProperties then // DOEP-26548 <
      begin
        Child.Attr.Values[SDigitalID] := BooleanStr[Action.DigitalID]; // DOEP-26548
        Child.Attr.Values[SDigitalIDButtonTitle] := Action.DigitalIDButtonTitle;

        //  DEV-39393 (827.2's DEV-39198), addition of digital ID min/max lengths
        Child.Attr.Values[SDigitalIDMinLength] := IntToStr(Action.DigitalIDMinLength);
        Child.Attr.Values[SDigitalIDMaxLength] := IntToStr(Action.DigitalIDMaxLength);
      end; // DOEP-26548 >

      if apActivateCardReader in Action.ActionProperties then // 6285
        Child.Attr.Values[SActivateCardReader] := BooleanStr[Action.ActivateCardReader];

      if (apPinPadEntry in Action.ActionProperties) and (Action.TAC = 'x') then
        Child.Attr.Values[SPinPadEntryRetryCount] := IntToStr(Action.PinPadEntryRetryCount);

      if apPromptForCashbackByCheckType in Action.ActionProperties then // DEV-18499
        Child.Attr.Values[SPromptForCashbackByCheckType] := ReplaceString(Action.PromptForCashbackByCheckType.CommaText, '"', ''); // DEV-18499

      if apDisplayNACHAText in Action.ActionProperties then // DEV-18499
        Child.Attr.Values[SDisplayNACHAText] := BooleanStr[Action.DisplayNACHAText];

      if apSVSAuthNum in Action.ActionProperties then // DEV-28404
        Child.Attr.Values[SSVSAuthNum] := BooleanStr[Action.SVSAuthNum];

      if apEnableWICButton in Action.ActionProperties then
        Child.Attr.Values[SEnableWICButton] := BooleanStr[Action.EnableWICButton];

      if apRequestTokenData in Action.ActionProperties then
        Child.Attr.Values[SRequestTokenData] := BooleanStr[Action.RequestTokenData];

      if apAllowManualAutoTender in Action.ActionProperties then
        Child.Attr.Values[SAllowManualAutoTender] := BooleanStr[Action.AllowManualAutoTender]; // DOEP-41273

      if apCardSlideFail in Action.ActionProperties then
        Child.Attr.Values[SCardSlideFail] := Action.CardSlideFail;

      if apDisableNoZeroKey in Action.ActionProperties then
        Child.Attr.Values[SDisableNoZeroKey] := BooleanStr[Action.DisableNoZeroKey];

      if apPromptForCashbackOnDiscover in Action.ActionProperties then
        Child.Attr.Values[SPromptForCashbackOnDiscover] := BooleanStr[Action.PromptForCashbackOnDiscover];

      if apProhibitCancellingSignatureCapture in Action.ActionProperties then
        Child.Attr.Values[SProhibitCancellingSignatureCapture] := BooleanStr[Action.ProhibitCancellingSignatureCapture];

      if apGetFreqShopperNumFromPINPad in Action.ActionProperties then
        Child.Attr.Values[SGetFreqShopperNumFromPINPad] := BooleanStr[Action.GetFreqShopperNumFromPINPad];

      if apErrorConditionPromptDisplayTime in Action.ActionProperties then
        Child.Attr.Values[SErrorConditionPromptDisplayTime] := IntToStr(Action.ErrorConditionPromptDisplayTime);

      if apSkipForManualEntry in Action.ActionProperties then
        Child.Attr.Values[SSkipForManualEntry] := BooleanStr[Action.SkipForManualEntry];
      if apVerifyLast4 in Action.ActionProperties then // DEV-17102
        Child.Attr.Values[SVerifyLast4] := BooleanStr[Action.VerifyLast4]; // DEV-17102        
      if apRTagProperties in Action.ActionProperties then
        Child.Attr.Values[SRequireVerificationNo] := BooleanStr[Action.RequireVerificationNo];
      if apECCOnly in Action.ActionProperties then
        Child.Attr.Values[SECCOnly] := BooleanStr[Action.ECCOnly];
      if apFloorLimit in Action.ActionProperties then
        Child.Attr.Values[SFloorLimit] := IntToStr(Action.FloorLimit);
      if apECCProductCode in Action.ActionProperties then
        Child.Attr.Values[SECCProductCode] := IntToStr(Action.ECCProductCode);
      if apPromptForStateCode in Action.ActionProperties then
        Child.Attr.Values[SPromptForStateCode] := BooleanStr[Action.PromptForStateCode];
      if apPINPadEntry in Action.ActionProperties then
        Child.Attr.Values[SPINPadEntry] := BooleanStr[Action.PINPadEntry];
      if apOverride2ndID in Action.ActionProperties then
        Child.Attr.Values[SOverride2ndID] := BooleanStr[Action.Override2ndID]; // DEV-8143
      if apPOSSupplySignature in Action.ActionProperties then
        Child.Attr.Values[SPOSSupplySignature] := BooleanStr[Action.POSSupplySignature]; // DEV-12747
      if apPrintPaperReceiptOnManual in Action.ActionProperties then
        Child.Attr.Values[SPrintPaperReceiptOnManual] := BooleanStr[Action.PrintPaperReceiptOnManual]; // DOEP-16038
      if apCustomSignature in Action.ActionProperties then
        Child.Attr.Values[SCustomSignature] := BooleanStr[Action.CustomSignature and Actions.IsTACInOrder('O', '>')]; // DEV-19583
      if apFreqShopAltID in Action.ActionProperties then
        Child.Attr.Values[SPromptFreqShopAltID] := BooleanStr[Action.PromptFreqShopAltID]; // DEV-29738
      if apPromptForCustomerOK in Action.ActionProperties then
        Child.Attr.Values[SPromptForCustomerOK] := BooleanStr[Action.PromptForCustomerOK];
      if apPromptCustomerOKForPartialAuth in Action.ActionProperties then
        Child.Attr.Values[SPromptCustomerOKForPartialAuth] := BooleanStr[Action.PromptCustomerOKForPartialAuth];
      if apDualEBTPrompt in Action.ActionProperties then
        Child.Attr.Values[SDualEBTPrompt] := BooleanStr[Action.DualEBTPrompt];
      if apShowDisclaimer in Action.ActionProperties then
        Child.Attr.Values[SShowDisclaimer] := BooleanStr[Action.ShowDisclaimer];
      if apCustomSwipeForm in Action.ActionProperties then
        Child.Attr.Values[SCustomSwipeForm] := BooleanStr[Action.CustomSwipeForm];
      if apCustomBadCardSlideForm in Action.ActionProperties then
        Child.Attr.Values[SCustomBadCardSlideForm] := BooleanStr[Action.CustomBadCardSlideForm];
      if apP2PManualEncryption in Action.ActionProperties then
        Child.Attr.Values[SP2PManualEncryption] := BooleanStr[Action.P2PManualEncryption];
      if apDoNotAllowPINBypass in Action.ActionProperties then
        Child.Attr.Values[SDoNotAllowPINBypass] := BooleanStr[Action.DoNotAllowPINBypass]; // TFS-26084
      if apWaitForPOSTotalToConvert in Action.ActionProperties then
        Child.Attr.Values[SWaitForPOSTotalToConvert] := BooleanStr[Action.WaitForPOSTotalToConvert];
      if aGiftSecurityCodeRetryCount in Action.ActionProperties then
        Child.Attr.Values[SGiftSecurityCodeRetryCount] := IntToStr(Action.GiftSecurityCodeRetryCount); // CPCLIENTS-2390
      if apPromptOnlyOnPOSRequest in Action.ActionProperties then                    // CPCLIENTS-3574
        Child.Attr.Values[SPromptOnlyOnPOSRequest] := BooleanStr[Action.PromptOnlyOnPOSRequest];  // CPCLIENTS-11904
      if apCharityDonation in Action.ActionProperties then
        Child.Attr.Values[SCharityDonation] := BooleanStr[Action.CharityDonation]; // CPCLIENTS-5956 Charity Donations *-*

      NextChild := Child.AddChild(SCustomerLines);
      AddCustomerLines(NextChild, Action.CustomerLines);

      NextChild := Child.AddChild(SCashierLines);
      AddLineCollection5(NextChild, Action.CashierLines);

      if Action.CustomerPromptAfterEFTKey.Count > 0 then
      begin
        NextChild := Child.AddChild(SCustomerPromptAfterEFTKey);
        AddCustomerLines(NextChild, Action.CustomerPromptAfterEFTKey);
      end;

      if Action.CashierPromptAfterEFTKey.Line1 <> '' then
      begin
        NextChild := Child.AddChild(SCashierPromptAfterEFTKey);
        AddLineCollection5(NextChild, Action.CashierPromptAfterEFTKey);
      end;

      if Action.CustomerPromptNonMatchingDigits.Count > 0 then // DEV-17192 <
      begin
        NextChild := Child.AddChild(SCustomerPromptNonMatchingDigits);
        AddCustomerLines(NextChild, Action.CustomerPromptNonMatchingDigits);
      end;

      if Action.CashierPromptNonMatchingDigits.Line1 <> '' then
      begin
        NextChild := Child.AddChild(SCashierPromptNonMatchingDigits);
        AddLineCollection5(NextChild, Action.CashierPromptNonMatchingDigits);
      end; // DEV-17192 >      

      if Action.BadCardSlideCustomerDisplay.Count > 0 then
      begin
        NextChild := Child.AddChild(SBadCardSlideCustomerDisplay);
        AddCustomerLines(NextChild, Action.BadCardSlideCustomerDisplay);
      end;

      if Action.BadCardSlideCashierDisplay.Line1 <> '' then
      begin
        NextChild := Child.AddChild(SBadCardSlideCashierDisplay);
        AddLineCollection5(NextChild, Action.BadCardSlideCashierDisplay);
      end;

      if Action.DigitalIDCustomerDisplay.Count > 0 then // DOEP-26548 <
      begin
        NextChild := Child.AddChild(SDigitalIDCustomerDisplay);
        AddCustomerLines(NextChild, Action.DigitalIDCustomerDisplay);
      end;

      if Action.DigitalIDCashierDisplay.Line1 <> '' then
      begin
        NextChild := Child.AddChild(SDigitalIDCashierDisplay);
        AddLineCollection5(NextChild, Action.DigitalIDCashierDisplay);
      end; // DOEP-26548 >

      if Action.PinPadEntryCustomerDisplay.Count > 0 then
      begin
        NextChild := Child.AddChild(SPinPadEntryCustomerDisplay);
        AddCustomerLines(NextChild, Action.PinPadEntryCustomerDisplay);
      end;

      if Action.PinPadEntryCashierDisplay.Line1 <> '' then
      begin
        NextChild := Child.AddChild(SPinPadEntryCashierDisplay);
        AddLineCollection5(NextChild, Action.PinPadEntryCashierDisplay);
      end;

      if Action.PinPadEntryRetryCustomerDisplay.Count > 0 then
      begin
        NextChild := Child.AddChild(SPinPadEntryRetryCustomerDisplay);
        AddCustomerLines(NextChild, Action.PinPadEntryRetryCustomerDisplay);
      end;

      if Action.PinPadEntryRetryCashierDisplay.Line1 <> '' then
      begin
        NextChild := Child.AddChild(SPinPadEntryRetryCashierDisplay);
        AddLineCollection5(NextChild, Action.PinPadEntryRetryCashierDisplay);
      end;

      if Action.ErrorConditionCustomerDisplay.Count > 0 then
      begin
        NextChild := Child.AddChild(SErrorConditionCustomerDisplay);
        AddCustomerLines(NextChild, Action.ErrorConditionCustomerDisplay);
      end;

      if Action.ErrorConditionCashierDisplay.Line1 <> '' then
      begin
        NextChild := Child.AddChild(SErrorConditionCashierDisplay);
        AddLineCollection5(NextChild, Action.ErrorConditionCashierDisplay);
      end;

      if Action.BalanceInquiryActionList.Count > 0 then
      begin
        NextChild := Child.AddChild(SBalanceInquiryActionList);
        AddActions(NextChild, Action.BalanceInquiryActionList);
      end;

      if Action.TAC = 'j' then
      begin
        NextChild := Child.AddChild(SCashbackAmounts);
        CustLines := NextChild.AddChild(SCustomerLines);
        AddCustomerLines(CustLines, Action.CashbackAmounts.CustomerLines);
        CashLines := NextChild.AddChild(SCashierLines);
        AddLineCollection5(CashLines, Action.CashbackAmounts.CashierLines);
        NewChild := NextChild.AddChild(SCustomerPromptForOtherAmount);
        AddCustomerLines(NewChild, Action.CashbackAmounts.CustomerPromptForOtherAmount);
        NewChild := NextChild.AddChild(SCashierPromptForOtherAmount);
        AddLineCollection5(NewChild, Action.CashbackAmounts.CashierPromptForOtherAmount);
        NewChild := NextChild.AddChild(SOtherAmountKeyLabel);
        AddCustomerLines(NewChild, Action.CashbackAmounts.OtherAmountKeyLabel);
        NewChild := NextChild.AddChild(SNoTipAmountKeyLabel);
        AddCustomerLines(NewChild, Action.CashbackAmounts.NoTipAmountKeyLabel);
        AllowOther := NextChild.AddChild(SAllowOtherCashbackAmount);
        AllowOther.Text := BooleanStr[Action.CashbackAmounts.AllowOtherCashbackAmount];
        NewChild := NextChild.AddChild(SMaxCashBackKeyLabel); // DEV-10520 <
        AddCustomerLines(NewChild, Action.CashbackAmounts.MaxCashBackKeyLabel);
        AllowMax := NextChild.AddChild(SAllowMaxCashBackButton);
        AllowMax.Text := BooleanStr[Action.CashbackAmounts.AllowMaxCashBackButton]; // DEV-10520 >
        CombineYesNo := NextChild.AddChild(SCombineYesNoWithAmount);
        CombineYesNo.Text := BooleanStr[Action.CashbackAmounts.CombineYesNoWithAmount];        

        Amount := NextChild.AddChild(SAmount1);
        Amount.Text := IntToStr(Action.CashbackAmounts.CashbackAmount[1]);
        Amount := NextChild.AddChild(SAmount2);
        Amount.Text := IntToStr(Action.CashbackAmounts.CashbackAmount[2]);
        Amount := NextChild.AddChild(SAmount3);
        Amount.Text := IntToStr(Action.CashbackAmounts.CashbackAmount[3]);
        Amount := NextChild.AddChild(SAmount4);
        Amount.Text := IntToStr(Action.CashbackAmounts.CashbackAmount[4]);
        Amount := NextChild.AddChild(SAmount5);
        Amount.Text := IntToStr(Action.CashbackAmounts.CashbackAmount[5]);
        Amount := NextChild.AddChild(SAmount6);
        Amount.Text := IntToStr(Action.CashbackAmounts.CashbackAmount[6]);
      end;

      if Action.CheckTypesRequiringSecondaryID.Count > 0 then
      begin
        NextChild := Child.AddChild(SCheckTypesRequiringSecondaryID);
        AddStringList(NextChild, Action.CheckTypesRequiringSecondaryID);
      end;
      if Action.CheckTypesRequiringSecondaryIDSlide.Count > 0 then              
      begin
        NextChild := Child.AddChild(SCheckTypesRequiringSecondaryIDSlide);
        AddStringList(NextChild, Action.CheckTypesRequiringSecondaryIDSlide);
      end;
      if Action.CheckTypesRequiring2DBarcode.Count > 0 then
      begin
        NextChild := Child.AddChild(SCheckTypesRequiring2DBarcode);
        AddStringList(NextChild, Action.CheckTypesRequiring2DBarcode);
      end;      
      
      if Action.RequireVerificationNoCustomerDisplay.Count > 0 then
      begin
        NextChild := Child.AddChild(SRequireVerificationNoCustomerDisplay);
        AddCustomerLines(NextChild, Action.RequireVerificationNoCustomerDisplay);
      end;

      if Action.RequireVerificationNoCashierDisplay.Line1 <> '' then
      begin
        NextChild := Child.AddChild(SRequireVerificationNoCashierDisplay);
        AddLineCollection5(NextChild, Action.RequireVerificationNoCashierDisplay);
      end;                                                                      

      if Action.CheckTypesRequiringManagerID.Count > 0 then
      begin
        NextChild := Child.AddChild(SCheckTypesRequiringManagerID);
        AddStringList(NextChild, Action.CheckTypesRequiringManagerID);
      end;

      if Action.CheckTypesRequiringPhoneNumber.Count > 0 then
      begin
        NextChild := Child.AddChild(SCheckTypesRequiringPhoneNumber);
        AddStringList(NextChild, Action.CheckTypesRequiringPhoneNumber);
      end;

      if Action.CheckTypesRequiringSSN.Count > 0 then
      begin
        NextChild := Child.AddChild(SCheckTypesRequiringSSN);
        AddStringList(NextChild, Action.CheckTypesRequiringSSN);
      end;

      if Action.CheckTypesRequiringPayrollIssueDate.Count > 0 then
      begin
        NextChild := Child.AddChild(SCheckTypesRequiringPayrollIssueDate);
        AddStringList(NextChild, Action.CheckTypesRequiringPayrollIssueDate);
      end;            

      if Action.IDTypesRequiringManagerID.Count > 0 then
      begin
        NextChild := Child.AddChild(SIDTypesRequiringManagerID);
        AddStringList(NextChild, Action.IDTypesRequiringManagerID);
      end;

      if Action.TAC = 'd' then
      begin
        NextChild := Child.AddChild(SBioMTXServerPort);
        NextChild.Text := IntToStr(Action.BioMTXServerPort);
        NextChild := Child.AddChild(SKeyAssignments);
        AddKeyAssignments(NextChild, Action.KeyAssignments);
        NextChild := Child.AddChild(SMiscPrompts);
        AddMiscPrompts2(NextChild, Action.MiscPrompts);
      end;

      if (Action.TAC = 'g') and Action.PromptForCustomerOK then
        AddCustomerAndCashierLines(Child.AddChild(SPromptAfterFeeAmtSet), Action.PromptAfterFeeAmtSet);

      if (Action.TAC = '>') and Action.PromptCustomerOKForPartialAuth then
        AddCustomerAndCashierLines(Child.AddChild(SCustomerOKPartialAuth), Action.CustomerOKPartialAuth);

      if (Action.TAC = 'L') and Action.DualEBTPrompt then
        AddCustomerAndCashierLines(Child.AddChild(SDualEBTPromptSet), Action.DualEBTPromptSet);

      if ((Action.TAC = 'O') or (Action.TAC = 'L')) and Action.ShowDisclaimer then
      begin
        NextChild := Child.AddChild(SDisclaimerLines);
        AddStringListToNumberedLines(NextChild, Action.DisclaimerLines);
      end;

      if Action.TAC = 'Q' then
      begin
        NextChild := Child.AddChild(SKeyAssignments);
        AddKeyAssignments(NextChild, Action.KeyAssignments);
      end;

      if (Action.BadCardSlidePrompts.CustomerLines.Count > 0) then
      begin
        NextChild := Child.AddChild(SBadCardSlidePrompts);
        AddCustomerAndCashierLines(NextChild, Action.BadCardSlidePrompts);
      end;

      if (Action.BadCardSlidePrompts.CustomerLines.Count > 0) then
      begin
        NextChild := Child.AddChild(SBadChipReadPrompts);
        AddCustomerAndCashierLines(NextChild, Action.BadChipReadPrompts);
      end;
	  
      // CPCLIENTS-5956  Starting *-*
      NextChild := Child.AddChild(SCharityDonationOptions);
      NextChild.Attr.Values[SAllowOtherAmount] := BooleanStr[Action.CharityDonationOptions.AllowOtherAmount];
      CustLines := NextChild.AddChild(SCustomerDisplay);
      AddCustomerLines(CustLines, Action.CharityDonationOptions.CustomerDisplay);
      CashLines := NextChild.AddChild(SCashierDisplay);
      AddLineCollection5(CashLines, Action.CharityDonationOptions.CashierDisplay);
    end;
  end;

  procedure AddMiscPrompts(Node: TXMLParserNode; NodeName: string; MiscPrompts: TMiscPrompts);
  var
    Child: TXMLParserNode;
  begin
    Child := Node.AddChild(NodeName);
    AddCustomerAndCashierLines(Child.AddChild(SIntervalMessage), MiscPrompts.IntervalMessage);
    AddCustomerAndCashierLines(Child.AddChild(SPleaseWait), MiscPrompts.PleaseWait);
    AddCustomerAndCashierLines(Child.AddChild(SWrongCard), MiscPrompts.WrongCard);
    AddCustomerAndCashierLines(Child.AddChild(SEBTTypePrompt), MiscPrompts.EBTTypePrompt);
    AddCustomerAndCashierLines(Child.AddChild(STerminalClosedPrompt), MiscPrompts.TerminalClosedPrompt);
    AddCustomerAndCashierLines(Child.AddChild(STransactionCancelled), MiscPrompts.TransactionCancelled);
    AddCustomerAndCashierLines(Child.AddChild(SProcessingPromptForVoid), MiscPrompts.ProcessingPromptForVoid);
    AddCustomerAndCashierLines(Child.AddChild(SStartWICSession), MiscPrompts.StartWICSession);
    AddCustomerAndCashierLines(Child.AddChild(SEnterWICPIN), MiscPrompts.EnterWICPIN);
    AddCustomerAndCashierLines(Child.AddChild(SSmartWICCardRemoved), MiscPrompts.SmartWICCardRemoved);
    AddCustomerAndCashierLines(Child.AddChild(SRetrievingPrescription), MiscPrompts.RetrievingPrescription);
    AddCustomerAndCashierLines(Child.AddChild(SUpdatingPrescription), MiscPrompts.UpdatingPrescription);
    AddCustomerAndCashierLines(Child.AddChild(SLockingWICCard), MiscPrompts.LockingWICCard);
    AddCustomerAndCashierLines(Child.AddChild(SRemoveWICCard), MiscPrompts.RemoveWICCard);
    AddCustomerAndCashierLines(Child.AddChild(SDataEntryCashier), MiscPrompts.DataEntryCashier);
    AddCustomerAndCashierLines(Child.AddChild(SDataEntryPOSTrackingNumber), MiscPrompts.DataEntryPOSTrackingNumber);
    AddCustomerAndCashierLines(Child.AddChild(SDataEntryDOB), MiscPrompts.DataEntryDOB);
    AddCustomerAndCashierLines(Child.AddChild(SPONumber), MiscPrompts.PONumber);
    AddCustomerAndCashierLines(Child.AddChild(STokenPinPrompt), MiscPrompts.TokenPinPrompt); // DOEP-72983
    AddCustomerAndCashierLines(Child.AddChild(SPinReEntryPrompt), MiscPrompts.PinReEntryPrompt);
    AddCustomerAndCashierLines(Child.AddChild(SFallbackPrompt), MiscPrompts.FallbackPrompt);
    AddCustomerAndCashierLines(Child.AddChild(SReinsertCardPrompt), MiscPrompts.InsertSwipedChipCard);
    AddCustomerAndCashierLines(Child.AddChild(SGiftSecurityCodeReEntryPrompt), MiscPrompts.GiftSecurityCodeReEntryPrompt);
  end;

  procedure AddMiscProperties(Node: TXMLParserNode; NodeName: string; MiscProperties: TMiscProperties);
  var
    Child, NextChild: TXMLParserNode;
  begin
    Child := Node.AddChild(NodeName);
    if MiscProperties.IDTypesRequiringStateCode.Count > 0 then
    begin
      NextChild := Child.AddChild(SIDTypesRequiringStateCode);
      AddStringList(NextChild, MiscProperties.IDTypesRequiringStateCode);
    end;
  end;

begin  // SaveToXML
  FVerifyXML := 'OK';
  SortTendersAndTransactions;

  Root := TXMLParserNode.Create(nil);
  try
    Root.Name := SConfigTypeName;
    Root.Attr.Values[SVersion] := FormatFloat(FORMAT_XML_VERSION, GetValidXMLVersion(xfWinEPSConfiguration));   
    Root.Attr.Values[SLastModified] := FormatDateTime(FORMAT_LASTMODIFIED, Now); 
    //add ParameterVersion
    Node := Root.AddChild(SParameterVersion);
    Node.Text := IntToStr(ParameterVersion);

    //add OpenEPSParameters
    Node := Root.AddChild(SOpenEPSParameters);
    AddBooleanNode(Node, SStandInAllowed, OpenEPSParameters.StandInAllowed);
    AddStringNode(Node, STerminal, OpenEPSParameters.Terminal);
    AddStringNode(Node, STerminal2, OpenEPSParameters.Terminal2);
    AddBooleanNode(Node, SValidateCashier, OpenEPSParameters.ValidateCashier);
    AddStringNode(Node, STerminalPort, OpenEPSParameters.TerminalPort);
    AddStringNode(Node, STerminalPort2, OpenEPSParameters.TerminalPort2);
    AddStringNode(Node, STerminalIPPort, OpenEPSParameters.TerminalIPPort);
    AddBooleanNode(Node, SVerishieldEnabled, OpenEPSParameters.VerishieldEnabled);
    AddBooleanNode(Node, SSendReceiptToPINPad, OpenEPSParameters.SendReceiptToPINPad); 
    AddBooleanNode(Node, SDLLExTrace, OpenEPSParameters.DLLExTrace);
    AddBooleanNode(Node, STCPIPTrace, OpenEPSParameters.TCPIPTrace);
    AddBooleanNode(Node, SSerialTrace, OpenEPSParameters.SerialTrace);
    AddBooleanNode(Node, SCardReaderAttached, OpenEPSParameters.CardReaderAttached);
    AddBooleanNode(Node, SPINPadAttached, OpenEPSParameters.PINPadAttached);
    AddStringNode(Node, SPOSGiftCardReturn, OpenEPSParameters.POSGiftCardReturn);
    AddIntegerNode(Node, SPenUPTimeOutValue, OpenEPSParameters.PenUPTimeOutValue);
    AddBooleanNode(Node, SPOSDoesSignaturesAfterAllTendersCompleted, OpenEPSParameters.POSDoesSignaturesAfterAllTendersCompleted); 
    AddBooleanNode(Node, SEndOrderIntervalMessage, OpenEPSParameters.EndOrderIntervalMessage); 
    AddStringNode(Node, SKeySlot, OpenEPSParameters.KeySlot);
    AddStringNode(Node, SCHDKeySlot, OpenEPSParameters.CHDKeySlot); // TFS-8022
    AddBooleanNode(Node, SEMVforceCreditOffline, OpenEPSParameters.EMVforceCreditOffline);// TFS-30442
    AddIntegerNode(Node, SEMVMinCreditToDebit, OpenEPSParameters.EMVMinCreditToDebit);	// TFS-35086
    //AddStringNode(Node, SOpenEPSControlledMSR, OpenEPSParameters.OpenEPSControlledMSR); // DEV-12874
    if OpenEPSParameters.PCI4 then begin
      AddBooleanNode(Node, SPCI4, OpenEPSParameters.PCI4);
      AddStringNode(Node, SRebootTime24Hour, OpenEPSParameters.RebootTime24Hour);
    end;
    AddBooleanNode(Node, SEnableQuickChip, OpenEPSParameters.EnableQuickChip);
    AddBooleanNode(Node, SDisableNFC, OpenEPSParameters.DisableNFC);
    AddBooleanNode(Node, SNoPINCVMOption, OpenEPSParameters.NoPINCVMOption);
    AddStringNode(Node, SVASMode, VASModesStr[OpenEPSParameters.VASMode]);
    AddBooleanNode(Node, SEnablePayWithQRCode, OpenEPSParameters.EnablePayWithQRCode);  // CPCLIENTS-11227
    
    //add GeneralParameters
    Node := Root.AddChild(SGeneralParameters);
    Node2 := Node.AddChild(SKeyAssignments);
    AddKeyAssignments(Node2, GeneralParameters.KeyAssignments);
    Node2 := Node.AddChild(SPrimaryActions);
    AddActions(Node2, GeneralParameters.PrimaryActions);
    Node2 := Node.AddChild(SReceiptHeader);
    AddLineCollection4(Node2, GeneralParameters.ReceiptHeader);
    Node2 := Node.AddChild(SReceiptFooter);
    AddLineCollection2(Node2, GeneralParameters.ReceiptFooter);
    AddStringNode(Node, SICEScreenFileName, GeneralParameters.ICEScreenFileName);
    //if NOT (SameText(GeneralParameters.Terminal, 'NONE') and NOT SameText(GeneralParameters.Terminal, 'SCAT-MAGP90')) then
      AddStringNode(Node, STerminalCode, GeneralParameters.TerminalCode);
    AddStringNode(Node, SRKIFileName, GeneralParameters.RKIFileName); // TFS-9773
    AddStringNode(Node, SManagerIDValidationType, GeneralParameters.ManagerIDValidationType);
    AddStringNode(Node, SWICStateCode, GeneralParameters.WICStateCode);         
    AddStringNode(Node, SCashierDisplaySize, GeneralParameters.CashierDisplaySize);
    AddMiscPrompts(Node, SMiscPrompts, GeneralParameters.MiscPrompts);
    AddMiscProperties(Node, SMiscProperties, GeneralParameters.MiscProperties);
    AddStringNode(Node, SCustCashBackInCents, GeneralParameters.CustCashBackInCents); // DEV-23642

    //add TransactionDefinitions
    Node := Root.AddChild(STransactionDefinitions);
    Node2 := Node.AddChild(STenderList);
    for i := 0 to TransactionDefinitions.Tenders.Count - 1 do
    begin
      Node3 := Node2.AddChild(STender);
      Node3.Attr.Values[SName] := TransactionDefinitions.Tenders[i].Name;
      Node3.Attr.Values[SIgnoreTacB] := BooleanStr[TransactionDefinitions.Tenders[i].IgnoreTacB];
      Node4 := Node3.AddChild(STransactionList);
      for j := 0 to TransactionDefinitions.Tenders[i].Transactions.Count - 1 do
      begin
        Node5 := Node4.AddChild(STransaction);
        Node5.Attr.Values[SName] := TransactionDefinitions.Tenders[i].Transactions[j].Name;
        Node5.Attr.Values[SVoidAllowed] := iif(TransactionDefinitions.Tenders[i].Transactions[j].VoidAllowed='',
            'Y', TransactionDefinitions.Tenders[i].Transactions[j].VoidAllowed);
        Node5.Attr.Values[SCustomerIdLookup] := iif(TransactionDefinitions.Tenders[i].Transactions[j].CustomerIdLookup,
            'Y', 'N'); // CPCLIENTS-7494
        Node6 := Node5.AddChild(SActionList);
        AddActions(Node6, TransactionDefinitions.Tenders[i].Transactions[j].Actions);
        Node6 := Node5.AddChild(SCardSlideManualActionList);
        AddActions(Node6, TransactionDefinitions.Tenders[i].Transactions[j].CardSlideManualActionList);
      end;
      if TransactionDefinitions.Tenders[i].MICRManualActionList.Count > 0 then
      begin
        Node4 := Node3.AddChild(SMICRManualActionList);
        AddActions(Node4, TransactionDefinitions.Tenders[i].MICRManualActionList);
      end;
    end;

    //add SoftDeclineActions 
    Node := Root.AddChild(SSoftDeclineActions);
    for i := 0 to SoftDeclineActions.Count - 1 do
    begin
      Node2 := Node.AddChild(STerminalResponse);
      Node2.Attr.Values[SCode] := SoftDeclineActions[i].Code;
      AddActions(Node2, SoftDeclineActions[i].Actions);
    end;
    if SoftDeclineMICRManualActionList.Count > 0 then // DEV-18499 <
    begin
      Node2 := Node.AddChild(SMICRManualActionList);
      AddActions(Node2, SoftDeclineMICRManualActionList);
    end; // DEV-18499 >

    //add CheckConfiguration
    Node := Root.AddChild(SCheckConfiguration);
    AddStringNode(Node, SMICRReaderType, CheckConfiguration.MICRReaderType);
    Node2 := Node.AddChild(SMICRResultCodes);
    for i := 0 to CheckConfiguration.MICRResultCodes.Count - 1 do
    begin
      Node3 := Node2.AddChild(SMICRResult);
      Node3.Attr.Values[SCode] := CheckConfiguration.MICRResultCodes[i].Code;
      Node3.Attr.Values[STranslatedCode] := CheckConfiguration.MICRResultCodes[i].TranslatedCode;
      Node4 := Node3.AddChild(SCashierLines);
      AddLineCollection2(Node4, CheckConfiguration.MICRResultCodes[i].CashierLines);
    end;

    //add VeryfyXML
    Node := Root.AddChild(SVerifyXML);
    Node.Text := FVerifyXML;
    Root.SaveToFile(AFileName);
  finally
    Root.Free;
  end;
end;

procedure TWinEPSConfiguration.SortTendersAndTransactions;
var
  i: Integer;
begin
  TransactionDefinitions.Tenders.Sort;
  for i := 0 to TransactionDefinitions.Tenders.Count - 1 do
    TransactionDefinitions.Tenders[i].Transactions.Sort;
end;

{ TKey }

procedure TKey.Assign(AKey: TKey);
begin
  FLayer := AKey.FLayer;
  FId := AKey.FId;
  FValue := AKey.Value;
  FCaption := AKey.Caption;
end;

constructor TKey.Create( AKeyList: TKeyList; ALayer: Integer; AId: Integer; AValue: string; ACaption: string; ALanguageCode : string );
begin
  inherited Create;
  FKeyList := AKeyList;
  FKeyList.FItems.Add(Self);
  FLayer := ALayer;
  FId := AId;
  FValue := AValue;
  FCaption := ACaption;
  FLanguageCode := ALanguageCode;
end;

destructor TKey.Destroy;
begin
  FKeyList.FItems.Remove(Self);
  inherited;
end;

{ TKeyList }

function TKeyList.Add(ALayer: Integer; AId: Integer; AValue: string; ACaption: string; ALanguageCode: string ): TKey;
begin
  result := TKey.Create(Self, ALayer, AId, AValue, ACaption, ALanguageCode);
end;

procedure TKeyList.Assign(AKeyList: TKeyList);
var i: integer;
begin
  FItemName := AKeyList.FItemName;
  Clear;
  for i := 0 to AKeyList.Count - 1 do
    Add(0,0,'','').Assign(AKeyList[i]);

end;

procedure TKeyList.Clear;
begin
  while (FItems.Count > 0) do TKey(FItems.Last).Free;
end;

constructor TKeyList.Create(AItemName: string);
begin
  inherited Create;
  FItemName := AItemName;
  FItems := TList.Create;
end;

procedure TKeyList.Delete(Index: Integer);
begin
  TKey(FItems[Index]).Free;
end;

destructor TKeyList.Destroy;
begin
  Clear;
  FreeAndNil(FItems);
  inherited;
end;

function TKeyList.GetCount: Integer;
begin
  if Assigned(FItems) then
    Result := FItems.Count;
end;

function TKeyList.GetItem(Index: Integer): TKey;
begin
  Result := FItems[Index];
end;

function TKeyList.KeyByLayerAndId(ALayer: Integer; AId: Integer): TKey;
var
  i: Integer;
begin
  Result := nil;
  for i := 0 to Count - 1 do
  begin
    if (Items[i].Layer = ALayer) and (Items[i].Id = AId) then
    begin
      Result := Items[i];
      break;
    end;
  end;
end;

function TKeyList.KeyByValue(AValue: string): TKey;
var
  i: Integer;
begin
  Result := nil;
  for i := 0 to Count - 1 do
  begin
    if Items[i].Value = AValue then
    begin
      Result := Items[i];
      break;
    end;
  end;
end;

{ TTerminalActionList }

function TTerminalActionList.ActionByTAC(ATAC: string): TTerminalAction;
var
  i: Integer;
begin
  Result := nil;
  for i := 0 to Count - 1 do
  begin
    if Items[i].TAC = ATAC then
    begin
      Result := Items[i];
      break;
    end;
  end;
end;

function TTerminalActionList.Add(ATAC: string; IsNewTAC: boolean=false): TTerminalAction;
begin
  if ATAC.Contains(chr_GiftSecurityCode) and (Length(ATAC) > 1) then //CPCLIENTS-1186 - fixed issue with XML parser not loading correctly the value of new TAC
    ATAC := chr_GiftSecurityCode;
  Result := TTerminalAction.Create(Self, ATAC, IsNewTAC); // DEV-17192
end;

procedure TTerminalActionList.Assign(AActionList: TTerminalActionList);
var
  i: Integer;
begin
  Clear;
  for i := 0 to AActionList.Count - 1 do
    Add('').Assign(AActionList[i]);
end;

procedure TTerminalActionList.Clear;
begin
  while (FItems.Count > 0) do TTerminalAction(FItems.Last).Free;
end;

constructor TTerminalActionList.Create;
begin
  inherited;
  FItems := TList.Create;
end;

procedure TTerminalActionList.Delete(Index: Integer);
begin
  TTerminalAction(FItems[Index]).Free;
end;

destructor TTerminalActionList.Destroy;
begin
  Clear;
  FreeAndNil(FItems);
  inherited;
end;

function TTerminalActionList.GetCount: Integer;
begin
  Result := FItems.Count;
end;

function TTerminalActionList.GetItem(Index: Integer): TTerminalAction;
begin
  Result := FItems[Index];
end;

function TTerminalActionList.Insert(ATAC: string; Index: Integer; IsNewTAC: boolean=false): TTerminalAction;
var
  i: Integer;
  A: TTerminalAction;
begin
  Result := TTerminalAction.Create(Self, ATAC, IsNewTAC); // DEV-17192
  for i := Count - 1 downto Index+1 do
  begin
    A := Items[i];
    FItems[i] := FItems[i - 1];
    FItems[i - 1] := A;
  end;
end;

function TTerminalActionList.IsTACExists(TAC: string): boolean; // DEV-19583 <
begin
  result := Pos(TAC, TacListStr) > 0;
end;

function TTerminalActionList.IsTACInOrder(TacA: string; TacB: string): boolean;
begin
  result := IsTACExists(TacA) and IsTACExists(TacB) and (Pos(TacA, TacListStr) < Pos(TacB, TacListStr));
end;

function TTerminalActionList.TacListStr: string; // DEV-19583 >
var i: integer;
begin
  result := '';
  for i := 0 to Count -1 do
    result := result + Items[i].TAC;
end;

{ TTransactionDefinitions }

constructor TTransactionDefinitions.Create;
begin
  inherited;
  FTenders := TTenderList.Create;
end;

destructor TTransactionDefinitions.Destroy;
begin
  FreeAndNil(FTenders);
  inherited;
end;

{ TTenderList }

function TTenderList.Add(AName: string): TTender;
begin
  Result := TTender.Create(Self, AName);
end;

procedure TTenderList.Assign(ATenderList: TTenderList);
var
  i: Integer;
begin
  Clear;
  for i := 0 to ATenderList.Count - 1 do
    Add('').Assign(ATenderList[i]);
end;

procedure TTenderList.Clear;
begin
  while (FItems.Count > 0) do TTender(FItems.Last).Free;
end;

constructor TTenderList.Create;
begin
  inherited;
  FItems := TList.Create;
end;

procedure TTenderList.Delete(Index: Integer);
begin
  TTender(FItems[Index]).Free;
end;

destructor TTenderList.Destroy;
begin
  Clear;
  FreeAndNil(FItems);
  inherited;
end;

function TTenderList.GetCount: Integer;
begin
  Result := FItems.Count;
end;

function TTenderList.GetItem(Index: Integer): TTender;
begin
  Result := FItems[Index];
end;

procedure TTenderList.Sort;
begin
  FItems.Sort(TenderCompare);
end;

function TTenderList.TACExists(ATAC: string): Boolean;
var
  i, j, k: Integer;
begin
  Result := False;
  for i := 0 to Count - 1 do
  begin
    for j := 0 to Items[i].Transactions.Count - 1 do
    begin
      for k := 0 to Items[i].Transactions[j].Actions.Count - 1 do
      begin
        if Items[i].Transactions[j].Actions[k].TAC = ATAC then
        begin
          Result := True;
          break;
        end;
      end;
      if Result then break;
    end;
    if Result then break;
  end;
end;

function TTenderList.TenderByName(AName: string): TTender;
var
  i: Integer;
begin
  Result := nil;
  for i := 0 to Count - 1 do
  begin
    if Items[i].Name = AName then
    begin
      Result := Items[i];
      break;
    end;
  end;
end;

{ TTender }

procedure TTender.Assign(ATender: TTender);
begin
  Name := ATender.Name;
  IgnoreTacB := ATender.IgnoreTacB;
  Transactions.Assign(ATender.Transactions);
//JMR-A  CardSlideManualActionList.Assign(ATender.CardSlideManualActionList);
  MICRManualActionList.Assign(ATender.MICRManualActionList);
end;

constructor TTender.Create(ATenderList: TTenderList; AName: string);
begin
  inherited Create;
  FTenderList := ATenderList;
  FTenderList.FItems.Add(Self);
  FName := AName;
  FTransactions := TTransactionList.Create;
  FMICRManualActionList := TTerminalActionList.Create;
end;

destructor TTender.Destroy;
begin
  FreeAndNil(FMICRManualActionList);
  FreeAndNil(FTransactions);
  FTenderList.FItems.Remove(Self);
  inherited;
end;

function TTender.TACExists(ATAC: string): Boolean;
var
  j, k: Integer;
begin
  Result := False;
  for j := 0 to Transactions.Count - 1 do
  begin
    for k := 0 to Transactions[j].Actions.Count - 1 do
    begin
      if Transactions[j].Actions[k].TAC = ATAC then
      begin
        Result := True;
        break;
      end;
    end;
    if Result then break;
  end;
end;

{ TTransactionList }

function TTransactionList.Add(AName: string): TTransaction;
begin
  Result := TTransaction.Create(Self, AName);
end;

procedure TTransactionList.Assign(ATransactionList: TTransactionList);
var
  i: Integer;
begin
  Clear;
  for i := 0 to ATransactionList.Count - 1 do
    Add('').Assign(ATransactionList[i]);
end;

procedure TTransactionList.Clear;
begin
  while (FItems.Count > 0) do TTransaction(FItems.Last).Free;
end;

constructor TTransactionList.Create;
begin
  inherited;
  FItems := TList.Create;
end;

procedure TTransactionList.Delete(Index: Integer);
begin
  TTransaction(FItems[Index]).Free;
end;

destructor TTransactionList.Destroy;
begin
  Clear;
  FreeAndNil(FItems);
  inherited;
end;

function TTransactionList.GetCount: Integer;
begin
  Result := FItems.Count;
end;

function TTransactionList.GetItem(Index: Integer): TTransaction;
begin
  Result := FItems[Index];
end;

procedure TTransactionList.Sort;
begin
  FItems.Sort(TranCompare);
end;

function TTransactionList.TransactionByName(AName: string): TTransaction;
var
  i: Integer;
begin
  Result := nil;
  for i := 0 to Count - 1 do
  begin
    if Items[i].Name = AName then
    begin
      Result := Items[i];
      break;
    end;
  end;
end;

{ TTransaction }

procedure TTransaction.Assign(ATransaction: TTransaction);
begin
  Name := ATransaction.Name;
  Actions.Assign(ATransaction.Actions);
  VoidAllowed := ATransaction.VoidAllowed;
  CardSlideManualActionList.Assign(ATransaction.CardSlideManualActionList);
end;

constructor TTransaction.Create(ATransactionList: TTransactionList;
  AName: string);
begin
  inherited Create;
  FTransactionList := ATransactionList;
  FTransactionList.FItems.Add(Self);
  FName := AName;
  FActionList := TTerminalActionList.Create;
  FCardSlideManualActionList := TTerminalActionList.Create;
end;

destructor TTransaction.Destroy;
begin
  FreeAndNil(FCardSlideManualActionList);
  FreeAndNil(FActionList);
  FTransactionList.FItems.Remove(Self);
  inherited;
end;

function TTransaction.TACExists(ATAC: string): Boolean;
var
  k: Integer;
begin
  Result := False;
  try
    if NOT Assigned(Actions) then
      Exit;  
    for k := 0 to Actions.Count - 1 do
    begin
      if Actions[k].TAC = ATAC then
      begin
        Result := True;
        break;
      end;
    end;
  except
    ;
  end;
end;

{ TTerminalResponse }

constructor TTerminalResponse.Create(
  ATerminalResponseList: TTerminalResponseList; ACode: string);
begin
  inherited Create;
  FTerminalResponseList := ATerminalResponseList;
  FTerminalResponseList.FItems.Add(Self);
  FCode := ACode;
  FActions := TTerminalActionList.Create;
end;

destructor TTerminalResponse.Destroy;
begin
  FreeAndNil(FActions);
  FTerminalResponseList.FItems.Remove(Self);
  inherited;
end;

{ TTerminalResponseList }

function TTerminalResponseList.Add(ACode: string): TTerminalResponse;
begin
  Result := TTerminalResponse.Create(Self, ACode);
end;

function TTerminalResponseList.Find(ACode: string): TTerminalResponse;
var i: integer;
begin
  result := nil;
  for i := 0 to Count -1 do
    if SameText(Items[i].Code, ACode) then
    begin
      Result := Items[i];
      Exit;
    end;
end;

procedure TTerminalResponseList.Clear;
begin
  while (FItems.Count > 0) do TTerminalResponse(FItems.Last).Free;
end;

constructor TTerminalResponseList.Create;
begin
  inherited;
  FItems := TList.Create;
end;

procedure TTerminalResponseList.Delete(Index: Integer);
begin
  TTerminalResponse(FItems[Index]).Free;
end;

destructor TTerminalResponseList.Destroy;
begin
  Clear;
  FreeAndNil(FItems);
  inherited;
end;

function TTerminalResponseList.GetCount: Integer;
begin
  Result := FItems.Count;
end;

function TTerminalResponseList.GetItem(Index: Integer): TTerminalResponse;
begin
  Result := FItems[Index];
end;

{ TCheckConfiguration }

constructor TCheckConfiguration.Create;
begin
  inherited;
  FMICRResultCodes := TMICRResultList.Create;
end;

destructor TCheckConfiguration.Destroy;
begin
  FreeAndNil(FMICRResultCodes);
  inherited;
end;

{ TMICRResult }

constructor TMICRResult.Create(AMICRResultList: TMICRResultList; ACode,
  ATranslatedCode: string);
begin
  inherited Create;
  FMICRResultList := AMICRResultList;
  FMICRResultList.FItems.Add(Self);
  FCode := ACode;
  FTranslatedCode := ATranslatedCode;
  FCashierLines := TLineCollection2.Create;
end;

destructor TMICRResult.Destroy;
begin
  FreeAndNil(FCashierLines);
  FMICRResultList.FItems.Remove(Self);
  inherited;
end;

{ TMICRResultList }

function TMICRResultList.Add(ACode, ATranslatedCode: string): TMICRResult;
begin
  Result := TMICRResult.Create(Self, ACode, ATranslatedCode);
end;

procedure TMICRResultList.Clear;
begin
  while (FItems.Count > 0) do TMICRResult(FItems.Last).Free;
end;

constructor TMICRResultList.Create;
begin
  inherited;
  FItems := TList.Create;
end;

procedure TMICRResultList.Delete(Index: Integer);
begin
  TMICRResult(FItems[Index]).Free;
end;

destructor TMICRResultList.Destroy;
begin
  Clear;
  FreeAndNil(FItems);
  inherited;
end;

function TMICRResultList.GetCount: Integer;
begin
  Result := FItems.Count;
end;

function TMICRResultList.GetItem(Index: Integer): TMICRResult;
begin
  Result := FItems[Index];
end;

function TMICRResultList.ResultByCode(ACode: string): TMICRResult;
var
  i: Integer;
begin
  Result := nil;
  for i := 0 to Count - 1 do
  begin
    if Items[i].Code = ACode then
    begin
      Result := Items[i];
      break;
    end;
  end;
end;

{ TCheck }

constructor TCheck.Create(ACheckList: TCheckList; AType: string;
  AManagerIdRequired, ASecondaryIdRequired: Boolean);
begin
  inherited Create;
  FCheckList := ACheckList;
  FCheckList.FItems.Add(Self);
  FType := AType;
  FManagerIdRequired := AManagerIdRequired;
  FSecondaryIdRequired := ASecondaryIdRequired;
end;

destructor TCheck.Destroy;
begin
  FCheckList.FItems.Remove(Self);
  inherited;
end;

{ TCheckList }

function TCheckList.Add(AType: string; AManagerIdRequired,
  ASecondaryIdRequired: Boolean): TCheck;
begin
  Result := TCheck.Create(Self, AType, AManagerIdRequired, ASecondaryIdRequired);
end;

function TCheckList.CheckByType(AType: string): TCheck;
var
  i: Integer;
begin
  Result := nil;
  for i := 0 to Count - 1 do
  begin
    if Items[i].Type_ = AType then
    begin
      Result := Items[i];
      break;
    end;
  end;
end;

procedure TCheckList.Clear;
begin
  while (FItems.Count > 0) do TCheck(FItems.Last).Free;
end;

constructor TCheckList.Create;
begin
  inherited;
  FItems := TList.Create;
end;

procedure TCheckList.Delete(Index: Integer);
begin
  TCheck(FItems[Index]).Free;
end;

destructor TCheckList.Destroy;
begin
  Clear;
  FreeAndNil(FItems);
  inherited;
end;

function TCheckList.GetCount: Integer;
begin
  Result := FItems.Count;
end;

function TCheckList.GetItem(Index: Integer): TCheck;
begin
  Result := FItems[Index];
end;

{ TIdReq }

constructor TIdReq.Create(AIdList: TIdList; AType: string; AStateCodeRequired,
  AManagerIdRequired: Boolean);
begin
  inherited Create;
  FIdList := AIdList;
  FIdList.FItems.Add(Self);
  FType := AType;
  FStateCodeRequired := AStateCodeRequired;
  FManagerIdRequired := AManagerIdRequired;
end;

destructor TIdReq.Destroy;
begin
  FIdList.FItems.Remove(Self);
  inherited;
end;

{ TIdList }

function TIdList.Add(AType: string; AStateCodeRequired,
  AManagerIdRequired: Boolean): TIdReq;
begin
  Result := TIdReq.Create(Self, AType, AStateCodeRequired, AManagerIdRequired);
end;

procedure TIdList.Clear;
begin
  while (FItems.Count > 0) do TIdReq(FItems.Last).Free;
end;

constructor TIdList.Create;
begin
  inherited;
  FItems := TList.Create;
end;

procedure TIdList.Delete(Index: Integer);
begin
  TIdReq(FItems[Index]).Free;
end;

destructor TIdList.Destroy;
begin
  Clear;
  FreeAndNil(FItems);
  inherited;
end;

function TIdList.GetCount: Integer;
begin
  Result := FItems.Count;
end;

function TIdList.GetItem(Index: Integer): TIdReq;
begin
  Result := FItems[Index];
end;

function TIdList.IdByType(AType: string): TIdReq;
var
  i: Integer;
begin
  Result := nil;
  for i := 0 to Count - 1 do
  begin
    if Items[i].Type_ = AType then
    begin
      Result := Items[i];
      break;
    end;
  end;
end;

{ TTipAndCashback }
constructor TTipAndCashback.Create;
begin
  inherited;
  FCustomerLines := TCustomerLineList.Create;
  FCashierLines := TLineCollection5.Create;
  FCustomerPromptForOtherAmount := TCustomerLineList.Create;
  FCashierPromptForOtherAmount := TLineCollection5.Create;
  FOtherAmountKeyLabel := TCustomerLineList.Create;
  FNoTipAmountKeyLabel := TCustomerLineList.Create;
end;

destructor TTipAndCashback.Destroy;
begin
  FreeAndNil(FCashierLines);
  FreeAndNil(FCustomerLines);
  FreeAndNil(FCashierPromptForOtherAmount);
  FreeAndNil(FCustomerPromptForOtherAmount);
  FreeAndNil(FOtherAmountKeyLabel);
  FreeAndNil(FNoTipAmountKeyLabel);
  inherited Destroy;
end;

procedure TTipAndCashback.Assign(ABaseAmounts: TTipAndCashback);
begin
  CustomerLines.Assign(ABaseAmounts.CustomerLines);
  CashierLines.Assign(ABaseAmounts.CashierLines);
  FCustomerPromptForOtherAmount.Assign(ABaseAmounts.CustomerPromptForOtherAmount);
  FCashierPromptForOtherAmount.Assign(ABaseAmounts.CashierPromptForOtherAmount);
  FOtherAmountKeyLabel.Assign(ABaseAmounts.OtherAmountKeyLabel);
  FNoTipAmountKeyLabel.Assign(ABaseAmounts.NoTipAmountKeyLabel);
end;

{ TCashbackAmounts }

procedure TCashbackAmounts.Assign(ACashbackAmounts: TCashbackAmounts);
var                                                       // CPCLIENTS-10087
  i: byte;                                                // CPCLIENTS-10087
begin
  inherited Assign(ACashbackAmounts);
  for i := 1 to MAX_CASHBACK_AMTS do                           // CPCLIENTS-10087
    CashbackAmount[i] := ACashbackAmounts.CashbackAmount[i];   // CPCLIENTS-10087
  AllowOtherCashbackAmount := ACashbackAmounts.AllowOtherCashbackAmount;
  CombineYesNoWithAmount := ACashbackAmounts.CombineYesNoWithAmount;
  FMaxCashBackKeyLabel.Assign(ACashbackAmounts.MaxCashBackKeyLabel); // DEV-10520
end;

procedure TCashbackAmounts.CalculateBtns(var NumberOfValueBtns, MaxCashbackBtnNum, OtherCashbackBtnNum: integer; AllowMaxCashback: string);
var
  OtherAmtUsed, MaxCashbackUsed: boolean;
  i: byte;
begin
  if AllowOtherCashbackAmount then
    OtherAmtUsed := True;
  OtherAmtUsed := AllowOtherCashbackAmount;
  MaxCashBackUsed := AllowMaxCashBackButton and sameText(AllowMaxCashBack, 'Y');

  MaxCashbackBtnNum := 0;
  OtherCashbackBtnNum := 0;
  NumberOfValueBtns := 0;
  for i := 1 to MAX_CASHBACK_AMTS do
    if CashbackAmount[i] > 0 then
      Inc(NumberOfValueBtns);
  if OtherAmtUsed or MaxCashbackUsed then
  begin
    if OtherAmtUsed and MaxCashbackUsed then               // Need to replace buttons for OTHER or FULL is either or both is used
    begin
      if NumberOfValueBtns > 4 then
        NumberOfValueBtns := 4;
      Inc(NumberOfValueBtns);
      MaxCashbackBtnNum := NumberOfValueBtns;
      Inc(NumberOfValueBtns);
      OtherCashbackBtnNum := NumberOfValueBtns;
      NumberOfValueBtns := Pred(MaxCashbackBtnNum);
    end
    else
    begin
      if NumberOfValueBtns > 5 then
        NumberOfValueBtns := 5;
      Inc(NumberOfValueBtns);
      if MaxCashbackUsed then
        MaxCashbackBtnNum := NumberOfValueBtns
      else
        OtherCashbackBtnNum := NumberOfValueBtns;
      NumberOfValueBtns := Pred(NumberOfValueBtns);
    end;
  end;
end;

constructor TCashbackAmounts.Create;
begin
  inherited;
  FMaxCashBackKeyLabel := TCustomerLineList.Create; // DEV-10520
end;

destructor TCashbackAmounts.Destroy;
begin
  FreeAndNil(FMaxCashBackKeyLabel); // DEV-10520
  inherited Destroy;
end;

{ TTipAmounts }
procedure TTipAmounts.Assign(ATipAmounts: TTipAmounts);
var
  i: integer;
begin
  inherited Assign(ATipAmounts);
  for i := 1 to 5 do
  begin
    TipPercentAmount[i] := ATipAmounts.TipPercentAmount[i];       // CPCLIENTS-13012
    TipDollarAmount[i] := ATipAmounts.TipDollarAmount[i];         // CPCLIENTS-13012
  end;

  AllowOtherTipAmount := ATipAmounts.AllowOtherTipAmount;
  AllowSuggestedTip := ATipAmounts.AllowSuggestedTip;
  AllowNoTipAmount := ATipAmounts.AllowNoTipAmount;
  IsTipPercentage := ATipAmounts.IsTipPercentage;
  OnlyPromptWithZeroAmount := ATipAmounts.OnlyPromptWithZeroAmount; //CPCLIENTS-837
  DisplayWholeDollarsOnPinpad := ATipAmounts.DisplayWholeDollarsOnPinpad;     //18606
end;

procedure TTipAmounts.AssignWholeDollarAmountArray(TipAmounts: TTipAmounts);    //18769 assign tip in the array excluding zero tip if present.
var
  i, j: integer;
begin
  j:=1;
  for i := 1 to 3 do         //None and other btns not included
  begin
    if (TipAmounts.TipDollarAmount[i] > 0) then
    begin
      TipWholeDollarAmount[j]:= TipAmounts.TipDollarAmount[i];
      inc(j);
    end;
  end;
end;

procedure TTipAmounts.CalculateNONEandOTHERbtns(var NONEbtn, OTHERbtn: byte; LanguageID: byte; SwapCustomNoTipBtnIndex: boolean = False);    // CPCLIENTS-13012    //18605 introduced default parameter SwapCustomNoTipBtnIndex
var
  BtnNumber: byte;
  i: byte;
begin
  BtnNumber := 0;
  for i := 1 to 5 do
    if IncludeButton(IsTipPercentage, TipPercentAmount[i], TipDollarAmount[i]) then
      Inc(BtnNumber) ;
  // if 5 buttons are used for values and we need both No Tip and Other Tip buttons then we need to rewrite button 5
  if AllowNoTipAmount and AllowOtherTipAmount and (BtnNumber = 5) then
    Dec(BtnNumber);

  if SwapCustomNoTipBtnIndex then      //18605 swap custom and no button position
  begin
    if Assigned(OtherAmountKeyLabel.CustomerLineById(LanguageID)) and (AllowOtherTipAmount) then
    begin
      Inc(BtnNumber) ;
      OTHERbtn := BtnNumber;
    end;
    if Assigned(NoTipAmountKeyLabel.CustomerLineById(LanguageID)) and (AllowNoTipAmount) then
    begin
      Inc(BtnNumber);
      NoneBtn := BtnNumber;
    end;
  end
  else
  begin
    if Assigned(NoTipAmountKeyLabel.CustomerLineById(LanguageID)) and (AllowNoTipAmount) then
    begin
      Inc(BtnNumber);
      NoneBtn := BtnNumber;
    end;
    if Assigned(OtherAmountKeyLabel.CustomerLineById(LanguageID)) and (AllowOtherTipAmount) then
    begin
      Inc(BtnNumber) ;
      OTHERbtn := BtnNumber;
    end;
  end;
end;

function TTipAmounts.ConvertTipToWholeDollarAmount(              //18606 replace the cent value with zero cents
  var TipAmount: integer): integer;
var
  tmpStr : String;
const
  ZERO_CENTS = '00';
begin
  tmpStr := '';
  tmpStr := format('%d',[Trunc((tipAmount / 100))]) + ZERO_CENTS;
  result := StrToInt(tmpStr);
end;

constructor TTipAmounts.Create;
begin
  inherited;
  
end;

destructor TTipAmounts.Destroy;
begin
  inherited;
end;

function TTipAmounts.IncludeButton(TipIsPct: boolean; Pct, Amt: integer): boolean;
begin
  if TipIsPct then
    Result := Pct > 0
  else
    Result := Amt > 0;
end;

{ TTerminalAction }

procedure TTerminalAction.Assign(AAction: TTerminalAction);
begin
  TAC := AAction.TAC;
  Description := AAction.Description;
  ActionProperties := AAction.ActionProperties;

  CustomerLines.Assign(AAction.CustomerLines);
  CashierLines.Assign(AAction.CashierLines);
  FNumberOfBadSlidesAllowed := AAction.NumberOfBadSlidesAllowed;
  FNumberOfBadChipReadAllowed := AAction.NumberOfBadChipReadAllowed;
  FCardSlideFail := AAction.CardSlideFail;

  FBadCardSlideErrorPromptDisplayTime := AAction.BadCardSlideErrorPromptDisplayTime;
  FBadCardSlideCustomerDisplay.Assign(AAction.BadCardSlideCustomerDisplay);
  FBadCardSlideCashierDisplay.Assign(AAction.BadCardSlideCashierDisplay);
  
  FDigitalID := AAction.DigitalID; // DOEP-26548 <
  FSuppressLanguageOnVAS := AAction.SuppressLanguageOnVAS;  //CPCLIENTS-8202
  FActivateCardReader := AAction.ActivateCardReader; // 6285
  DigitalIDButtonTitle := AAction.DigitalIDButtonTitle;
  DigitalIDCustomerDisplay.Assign(AAction.DigitalIDCustomerDisplay);
  DigitalIDCashierDisplay.Assign(AAction.DigitalIDCashierDisplay); // DOEP-26548 >
  //  DEV-39393 (827.2's DEV-39198), addition of digital ID min/max lengths
  DigitalIDMinLength := AAction.DigitalIDMinLength;
  DigitalIDMaxLength := AAction.DigitalIDMaxLength;

  FPINPadEntry := AAction.PINPadEntry;
  PinPadEntryRetryCount := AAction.PinPadEntryRetryCount;
  PinPadEntryCustomerDisplay.Assign(AAction.PinPadEntryCustomerDisplay);
  PinPadEntryCashierDisplay.Assign(AAction.PinPadEntryCashierDisplay);
  PinPadEntryRetryCustomerDisplay.Assign(AAction.PinPadEntryRetryCustomerDisplay);
  PinPadEntryRetryCashierDisplay.Assign(AAction.PinPadEntryRetryCashierDisplay);

  FErrorConditionPromptDisplayTime := AAction.ErrorConditionPromptDisplayTime;
  FErrorConditionCustomerDisplay.Assign(AAction.ErrorConditionCustomerDisplay);
  FErrorConditionCashierDisplay.Assign(AAction.ErrorConditionCashierDisplay);
  FDisableNoZeroKey := AAction.DisableNoZeroKey;
  FPromptForCashbackOnDiscover := AAction.PromptForCashbackOnDiscover;
  FProhibitCancellingSignatureCapture := AAction.ProhibitCancellingSignatureCapture;
  FSkipForManualEntry := AAction.SkipForManualEntry;
  FGetFreqShopperNumFromPINPad := AAction.GetFreqShopperNumFromPINPad;
  FCustomerPromptAfterEFTKey.Assign(AAction.CustomerPromptAfterEFTKey);
  FCashierPromptAfterEFTKey.Assign(AAction.CashierPromptAfterEFTKey);
  FCustomerPromptNonMatchingDigits.Assign(AAction.CustomerPromptNonMatchingDigits); // DEV-17192
  FCashierPromptNonMatchingDigits.Assign(AAction.CashierPromptNonMatchingDigits); // DEV-17192
  FBalanceInquiryActionList.Assign(AAction.BalanceInquiryActionList);
  FCashbackAmounts.Assign(AAction.CashbackAmounts);
  FCheckTypesRequiringSecondaryID.Assign(AAction.CheckTypesRequiringSecondaryID);
  FCheckTypesRequiringSecondaryIDSlide.Assign(AAction.CheckTypesRequiringSecondaryIDSlide);
  FCheckTypesRequiring2DBarcode.Assign(AAction.CheckTypesRequiring2DBarcode);
  FRequireVerificationNo := AAction.RequireVerificationNo;
  FRequireVerificationNoCustomerDisplay.Assign(AAction.RequireVerificationNoCustomerDisplay);
  FRequireVerificationNoCashierDisplay.Assign(AAction.RequireVerificationNoCashierDisplay);
  FCheckTypesRequiringManagerID.Assign(AAction.CheckTypesRequiringManagerID);
  FCheckTypesRequiringPhoneNumber.Assign(AAction.CheckTypesRequiringPhoneNumber);
  FCheckTypesRequiringSSN.Assign(AAction.CheckTypesRequiringSSN);
  FCheckTypesRequiringPayrollIssueDate.Assign(AAction.CheckTypesRequiringPayrollIssueDate);
  FIDTypesRequiringManagerID.Assign(AAction.IDTypesRequiringManagerID);
  FMiscPrompts.Assign(AAction.MiscPrompts);
  FBioMTXServerPort := AAction.BioMTXServerPort;
  FKeyAssignments.Assign(AAction.KeyAssignments);
  FECCOnly := AAction.ECCOnly;
  FFloorLimit := AAction.FloorLimit;
  FECCProductCode := AAction.ECCProductCode;
  FPromptForStateCode := AAction.PromptForStateCode;
  FOverride2ndID := AAction.Override2ndID; // DEV-8143
  FCustomSignature := AAction.CustomSignature; // DEV-19583
  FVerifyLast4 := AAction.VerifyLast4; // DEV-17192
  FDisplayNACHAText := AAction.DisplayNACHAText; // DEV-18499
  FSVSAuthNum := AAction.SVSAuthNum; // DEV-28404
  FEnableWICButton := AAction.EnableWICButton;
  FRequestTokenData := AAction.RequestTokenData;
  FAllowManualAutoTender := AAction.AllowManualAutoTender; // DOEP-41273

  PromptForCashbackByCheckType.Assign(AAction.PromptForCashbackByCheckType); // DEV-18499 >
  FDisclaimerLines.Assign(AAction.FDisclaimerLines);

  // TFS ????, the following action properties were not getting copied/assigned, causing issues
  //  with saving especially after updating bad card slide B TAC properties
  FShowDisclaimer := AAction.ShowDisclaimer;
  FPromptCustomerOKForPartialAuth := AAction.PromptCustomerOKForPartialAuth;
  FPOSSupplySignature := AAction.POSSupplySignature;
  FPrintPaperReceiptOnManual := AAction.PrintPaperReceiptOnManual;
  FPromptFreqShopAltID := AAction.PromptFreqShopAltID;
  FPromptForCustomerOK := AAction.PromptForCustomerOK;
  FDualEBTPrompt := AAction.DualEBTPrompt;
  FDualEBTPromptSet.Assign(AAction.DualEBTPromptSet);
  FPromptAfterFeeAmtSet.Assign(AAction.PromptAfterFeeAmtSet); 

  FBadCardSlidePrompts.Assign(AAction.BadCardSlidePrompts); // DOEP-71133
  FBadChipReadPrompts.Assign(AAction.BadChipReadPrompts);
  FCustomSwipeForm := AAction.CustomSwipeForm; // DOEP-71378
  FCustomBadCardSlideForm := AAction.CustomBadCardSlideForm; // DOEP-71389
  FP2PManualEncryption := AAction.P2PManualEncryption;
  FDoNotAllowPINBypass := AAction.DoNotAllowPINBypass; // TFS-26084
  FWaitForPOSTotalToConvert := AAction.WaitForPOSTotalToConvert;
  FCharityDonationOptions.Assign(AAction.CharityDonationOptions); // CPCLIENTS-5956 Charity Donations
end;

constructor TTerminalAction.Create(AActionList: TTerminalActionList; ATAC: string; IsNewTAC: boolean=false);
var
  i : integer;
begin
  inherited Create;
  FActionList := AActionList;
  if FActionList <> nil then FActionList.FItems.Add(Self);
  FTAC := ATAC;
  FCustomerLines := TCustomerLineList.Create;
  FCashierLines := TLineCollection5.Create;

  FCustomerPromptAfterEFTKey := TCustomerLineList.Create;
  FCashierPromptAfterEFTKey := TLineCollection5.Create;

  FCustomerPromptNonMatchingDigits := TCustomerLineList.Create; // DEV-17192
  FCashierPromptNonMatchingDigits := TLineCollection5.Create; // DEV-17192

  FBadCardSlideCustomerDisplay := TCustomerLineList.Create;
  FBadCardSlideCashierDisplay := TLineCollection5.Create;

  FErrorConditionCustomerDisplay := TCustomerLineList.Create;
  FErrorConditionCashierDisplay := TLineCollection5.Create;
  FCharityDonationOptions := TCharityDonationOptions.Create;  // CPCLIENTS-5956

  FBalanceInquiryActionList := TTerminalActionList.Create;

  FCashbackAmounts := TCashbackAmounts.Create;
  FTipAmounts := TTipAmounts.Create;
  FFeeAmounts := TFeeAmounts.Create; // CPCLIENTS-6548 - DeCA Fee Amount changes

  FCheckTypesRequiringSecondaryID := TStringList.Create;
  FCheckTypesRequiringSecondaryIDSlide := TStringList.Create;
  FCheckTypesRequiring2DBarcode := TStringList.Create;
  FRequireVerificationNoCustomerDisplay := TCustomerLineList.Create;
  FRequireVerificationNoCashierDisplay := TLineCollection5.Create;
  FCheckTypesRequiringManagerID := TStringList.Create;
  FCheckTypesRequiringPhoneNumber := TStringList.Create;
  FCheckTypesRequiringSSN := TStringList.Create;
  FCheckTypesRequiringPayrollIssueDate := TStringList.Create;
  FIDTypesRequiringManagerID := TStringList.Create;

  FKeyAssignments := TKeyAssignments.Create;
  FMiscPrompts := TMiscPrompts2.Create;
  FPromptAfterFeeAmtSet := TCustomerAndCashierLines.Create;
  FCustomerOKPartialAuth := TCustomerAndCashierLines.Create;
  FDualEBTPromptSet := TCustomerAndCashierLines.Create;

  FActionProperties := [];
  FFloorLimit := -1;
  FECCProductCode := -1;

  FDigitalID := false; // DOEP-26548 <
  FSuppressLanguageOnVAS := false; //CPCLIENTS-8202
  FActivateCardReader := false; // 6285
  DigitalIDCustomerDisplay := TCustomerLineList.Create;
  DigitalIDCashierDisplay := TLineCollection5.Create; // DOEP-26548 >
  // DEV-29738, Make sure that if the digital ID min/max length is never set anywhere else it at least defaults to length 10
  DigitalIDMinLength := DIGITAL_ID_DEFAULT_LENGTH;
  DigitalIDMaxLength := DIGITAL_ID_DEFAULT_LENGTH;
  FActivateCardReader := true; // 6285: true by default for Publix

  FPinPadEntry := false;
  PinPadEntryRetryCount := 0;
  PinPadEntryCustomerDisplay := TCustomerLineList.Create;
  PinPadEntryCashierDisplay := TLineCollection5.Create;
  PinPadEntryRetryCustomerDisplay := TCustomerLineList.Create;
  PinPadEntryRetryCashierDisplay := TLineCollection5.Create;

  PromptForCashbackByCheckType := TStringList.Create; // DEV-18499 <
  FDisclaimerLines := TStringList.Create;
  FBadCardSlidePrompts := TCustomerAndCashierLines.Create; // DOEP-71133
  // TFS-8031 - set default text for Bad Card Slide prompts
  for i := 1 to 3 do
    FBadCardSlidePrompts.CustomerLines.Add(i).Line1 := 'Swipe Card Again';
  FBadCardSlidePrompts.CashierLines.Line1 := 'Swipe Card Again';

  FBadChipReadPrompts := TCustomerAndCashierLines.Create;
  for i := 1 to 3 do
    FBadChipReadPrompts.CustomerLines.Add(i).Line1 := DEFAULT_BAD_CHIP_PROMPT1;
  FBadChipReadPrompts.CashierLines.Line1 := DEFAULT_BAD_CHIP_PROMPT1;

  if TAC='j' then // DEV-18499
    Include(FActionProperties, apPromptForCashbackByCheckType);
  if IsNewTAC then // DEV-17192
    SetDefaultValues;
end;

destructor TTerminalAction.Destroy;
begin
  FreeAndNil(FDualEBTPromptSet);
  FreeAndNil(FCustomerOKPartialAuth);
  FreeAndNil(FPromptAfterFeeAmtSet);
  FreeAndNil(FMiscPrompts);
  FreeAndNil(FKeyAssignments);
  FreeAndNil(FCheckTypesRequiringSecondaryID);
  FreeAndNil(FCheckTypesRequiringSecondaryIDSlide);
  FreeAndNil(FCheckTypesRequiring2DBarcode);
  FreeAndNil(FRequireVerificationNoCashierDisplay);
  FreeAndNil(FRequireVerificationNoCustomerDisplay);                            
  FreeAndNil(FCheckTypesRequiringManagerID);
  FreeAndNil(FCheckTypesRequiringPhoneNumber);
  FreeAndNil(FCheckTypesRequiringSSN);
  FreeAndNil(FCheckTypesRequiringPayrollIssueDate);
  FreeAndNil(FIDTypesRequiringManagerID);
  FreeAndNil(FCashbackAmounts);
  FreeAndNil(FBalanceInquiryActionList);
  FreeAndNil(FErrorConditionCashierDisplay);
  FreeAndNil(FErrorConditionCustomerDisplay);
  FreeAndNil(FBadCardSlideCashierDisplay);
  FreeAndNil(FBadCardSlideCustomerDisplay);
  FreeAndNil(FCustomerPromptAfterEFTKey);
  FreeAndNil(FCashierPromptAfterEFTKey);
  FreeAndNil(FCustomerPromptNonMatchingDigits); // DEV-17192
  FreeAndNil(FCashierPromptNonMatchingDigits); // DEV-17192
  FreeAndNil(FCashierLines);
  FreeAndNil(FCustomerLines);
  FreeAndNil(DigitalIDCustomerDisplay);
  FreeAndNil(DigitalIDCashierDisplay);
  FreeAndNil(PinPadEntryCustomerDisplay);
  FreeAndNil(PinPadEntryCashierDisplay);
  FreeAndNil(PinPadEntryRetryCustomerDisplay);
  FreeAndNil(PinPadEntryRetryCashierDisplay);
  FreeAndNil(PromptForCashbackByCheckType); // DEV-18499
  FreeAndNil(FDisclaimerLines);
  FreeAndNil(FBadCardSlidePrompts); // DOEP-71133
  FreeAndNil(FBadChipReadPrompts);
  FreeAndNil(FTipAmounts);
  FreeAndNil(FCharityDonationOptions);  // CPCLIENTS-5956
  FreeAndNil(FFeeAmounts); // CPCLIENTS-6548 - DecA

  if FActionList <> nil then FActionList.FItems.Remove(Self);
  inherited;
end;

function TTerminalAction.GetIndex: Integer;
var
  i: Integer;
begin
  Result := -1;
  if FActionList <> nil then
  begin
    for i := 0 to FActionList.Count - 1 do
    begin
      if FActionList[i] = Self then
      begin
        Result := i;
        break;
      end;
    end;
  end;
end;

function TTerminalAction.GetIsActivateCardReader: boolean;
begin
  Result := FActivateCardReader;
end;

procedure TTerminalAction.SetDefaultValues; // DEV-18499
var lang: integer;
begin
  if (TAC = 'j') and (PromptForCashbackByCheckType.Count = 0) then
    PromptForCashbackByCheckType.Add('Personal');

  if TAC = 'b' then // DEV-17192
  begin
    for lang := 1 to 3 do
    begin
      if NOT Assigned(CustomerPromptNonMatchingDigits.CustomerLineById(lang)) then
      begin
        CustomerPromptNonMatchingDigits.Add(lang).Line1 := 'Please Wait';
      end;
    end;
    if SameText(CashierPromptNonMatchingDigits.Line1, '') then
    begin
      CashierPromptNonMatchingDigits.Line1 := 'Last 4 Digits';
      CashierPromptNonMatchingDigits.Line2 := 'Do NOT Match';
    end;
  end;
end;

procedure TTerminalAction.SetBadCardSlideErrorPromptDisplayTime(
  const Value: Integer);
begin
  FBadCardSlideErrorPromptDisplayTime := Value;
  Include(FActionProperties, apBadCardSlideErrorPromptDisplayTime);
end;

procedure TTerminalAction.SetBioMTXServerPort(const Value: Integer);
begin
  FBioMTXServerPort := Value;
end;

procedure TTerminalAction.SetCardSlideFail(const Value: string);
begin
  FCardSlideFail := Value;
  Include(FActionProperties, apCardSlideFail);
end;

procedure TTerminalAction.SetCharityDonation(const Value: boolean); // CPCLIENTS-5956 Charity Donations
begin
  FCharityDonation := Value;
  Include(FActionProperties, apCharityDonation);
end;

procedure TTerminalAction.SetDisableNoZeroKey(const Value: Boolean);
begin
  FDisableNoZeroKey := Value;
  Include(FActionProperties, apDisableNoZeroKey);
end;

procedure TTerminalAction.SetDisplayNACHAText(const Value: Boolean); // DEV-18499
begin
  FDisplayNACHAText := Value;
  Include(FActionProperties, apDisplayNACHAText);
end;

procedure TTerminalAction.SetSVSAuthNum(const Value: Boolean); // DEV-28404
begin
  FSVSAuthNum := Value;
  Include(FActionProperties, apSVSAuthNum);
end;

procedure TTerminalAction.SetEnableWICButton(const Value: Boolean);
begin
  FEnableWICButton := Value;
  Include(FActionProperties, apEnableWICButton);
end;

procedure TTerminalAction.SetRequestTokenData(const Value: Boolean);
begin
  FRequestTokenData := Value;
  Include(FActionProperties, apRequestTokenData);
end;

procedure TTerminalAction.SetAllowManualAutoTender(const Value: Boolean);
begin
  FAllowManualAutoTender := Value;
  Include(FActionProperties, apAllowManualAutoTender);
end;

procedure TTerminalAction.SetErrorConditionPromptDisplayTime(const Value: Integer);
begin
  FErrorConditionPromptDisplayTime := Value;
  Include(FActionProperties, apErrorConditionPromptDisplayTime);
end;

procedure TTerminalAction.SetGetFreqShopperNumFromPINPad(const Value: Boolean);
begin
  FGetFreqShopperNumFromPINPad := Value;
  Include(FActionProperties, apGetFreqShopperNumFromPINPad);
end;

procedure TTerminalAction.SetNumberOfBadSlidesAllowed(const Value: Integer);
begin
  FNumberOfBadSlidesAllowed := Value;
  Include(FActionProperties, apNumberOfBadSlidesAllowed);
end;

procedure TTerminalAction.SetNumberOfBadChipReadAllowed(const Value: Integer);
begin
  FNumberOfBadChipReadAllowed := Value;
  Include(FActionProperties, apNumberOfBadChipReadAllowed);
end;

function TTerminalAction.GetDigitalID: boolean;
var NeedToEnableDID: boolean;
begin
  NeedToEnableDID := (FTAC = Chr_SelectLanguage) and EMVMgr.IsVASModeSet and not FDigitalID; // 8318
  result := NeedToEnableDID or FDigitalID;
  if NeedToEnableDID then
    SM('GetDigitalID - Force to set DigitalID=Y on TAC Q because VAS is on');
end;

procedure TTerminalAction.SetDigitalID(const Value: boolean);
begin
  FDigitalID := Value;
  Include(FActionProperties, apDigitalID);
end;

function TTerminalAction.GetActivateCardReader: boolean;
begin
  result := EMVMgr.IsVASModeSet or FActivateCardReader;
  if not FActivateCardReader and EMVMgr.IsVASModeSet then
    SM('GetActivateCardReader - Force ActivateCardReader=Y because VAS is on');
end;

procedure TTerminalAction.SetActivateCardReader(const Value: boolean);
begin
  FActivateCardReader := Value;
  Include(FActionProperties, apActivateCardReader);
end;

procedure TTerminalAction.SetProhibitCancellingSignatureCapture(
  const Value: Boolean);
begin
  FProhibitCancellingSignatureCapture := Value;
  Include(FActionProperties, apProhibitCancellingSignatureCapture);
end;

procedure TTerminalAction.SetPromptForCashbackOnDiscover(const Value: Boolean);
begin
  FPromptForCashbackOnDiscover := Value;
  Include(FActionProperties, apPromptForCashbackOnDiscover);
end;

procedure TTerminalAction.SetSkipForManualEntry(const Value: Boolean);
begin
  FSkipForManualEntry := Value;
  Include(FActionProperties, apSkipForManualEntry);
end;

procedure TTerminalAction.SetSuppressLanguageOnVAS(const Value: boolean);      //CPCLIENTS-8202
begin
  FSuppressLanguageOnVAS := Value;
  Include(FActionProperties, apSuppressLanguageOnVAS);
end;

procedure TTerminalAction.SetRequireVerificationNo(const Value: Boolean);
begin
  FRequireVerificationNo := Value;
  Include(FActionProperties, apRTagProperties);
end;

procedure TTerminalAction.SetECCOnly(const Value: Boolean);                             
begin
  FECCOnly := Value;
  Include(FActionProperties, apECCOnly);
end;

procedure TTerminalAction.SetVerifyLast4(const Value: boolean); // DEV-17192
begin
  FVerifyLast4 := Value;
  Include(FActionProperties, apVerifyLast4);
end;

// CPCLIENTS-6548 - DeCA Fee Amount changes
procedure TTerminalAction.SetFeeAmountOption(const Value: Boolean);
begin
  FFeeAmountOption := Value;
  Include(FActionProperties, apFeeAmountOption);
end;

procedure TTerminalAction.SetFloorLimit(const Value: Integer);
begin
  FFloorLimit := Value;
  Include(FActionProperties, apFloorLimit);
end;

procedure TTerminalAction.SetECCProductCode(const Value: Integer);                      
begin
  FECCProductCode := Value;
  Include(FActionProperties, apECCProductCode);
end;

procedure TTerminalAction.SetPromptForStateCode(const Value: Boolean);                  
begin
  FPromptForStateCode := Value;
  Include(FActionProperties, apPromptForStateCode);
end;

procedure TTerminalAction.SetOverride2ndID(const Value: boolean); // DEV-8143
begin
  FOverride2ndID := Value;
  Include(FActionProperties, apOverride2ndID);
end;

procedure TTerminalAction.SetCustomSignature(const Value: boolean); // DEV-19583
begin
  FCustomSignature := Value;
  Include(FActionProperties, apCustomSignature);
end;

procedure TTerminalAction.SetPOSSupplySignature(const Value: boolean); // DEV-12747
begin
  FPOSSupplySignature := Value;
  Include(FActionProperties, apPOSSupplySignature);
end;

procedure TTerminalAction.SetPrintPaperReceiptOnManual(const Value: boolean); // DOEP-16038
begin
  FPrintPaperReceiptOnManual := Value;
  Include(FActionProperties, apPrintPaperReceiptOnManual);
end;

procedure TTerminalAction.SetPINPadEntry(const Value: Boolean);                         
begin
  FPINPadEntry := Value;
  Include(FActionProperties, apPINPadEntry);
end;

procedure TTerminalAction.SetPromptFreqShopAltID(const Value: Boolean);
begin
  // DEV-29738
  FPromptFreqShopAltID := Value;
  Include(FActionProperties, apFreqShopAltID);
end;

procedure TTerminalAction.SetPromptForCustomerOK(const Value: Boolean);
begin
  FPromptForCustomerOK := Value;
  Include(FActionProperties, apPromptForCustomerOK);
end;

procedure TTerminalAction.SetPromptCustomerOKForPartialAuth(const Value: Boolean);
begin
  FPromptCustomerOKForPartialAuth := Value;
  Include(FActionProperties, apPromptCustomerOKForPartialAuth);
end;

procedure TTerminalAction.SetDualEBTPrompt(const Value: Boolean);
begin
  FDualEBTPrompt := Value;
  Include(FActionProperties, apDualEBTPrompt);
end;

procedure TTerminalAction.SetShowDisclaimer(const Value: Boolean);
begin
  FShowDisclaimer := Value;
  Include(FActionProperties, apShowDisclaimer);
end;

procedure TTerminalAction.SetCustomSwipeForm(const Value: Boolean);
begin
  FCustomSwipeForm := Value;
  Include(FActionProperties, apCustomSwipeForm);
end;

procedure TTerminalAction.SetCustomBadCardSlideForm(const Value: Boolean);
begin
  FCustomBadCardSlideForm := Value;
  Include(FActionProperties, apCustomBadCardSlideForm);
end;

procedure TTerminalAction.SetP2PManualEncryption(const Value: Boolean);
begin
  FP2PManualEncryption := Value;
  Include(FActionProperties, apP2PManualEncryption);
end;

procedure TTerminalAction.SetDoNotAllowPINBypass(const Value: Boolean); // TFS-26084
begin
  FDoNotAllowPINBypass := Value;
  Include(FActionProperties, apDoNotAllowPINBypass);
end;

procedure TTerminalAction.SetPromptOnlyOnPOSRequest(const Value: boolean);  // CPCLIENTS-11904
begin
  FPromptOnlyOnPOSRequest := Value;
end;

procedure TTerminalAction.SetWaitForPOSTotalToConvert(const Value: boolean);
begin
  FWaitForPOSTotalToConvert := Value;
  Include(FActionProperties, apWaitForPOSTotalToConvert);
end;

{ TMiscProperties }
constructor TMiscProperties.Create;
begin
  inherited;
  FIDTypesRequiringStateCode := TStringList.Create;
end;

destructor TMiscProperties.Destroy;
begin
  FreeAndNil(FIDTypesRequiringStateCode);
  inherited;
end;

{ TMiscPrompts2 }

procedure TMiscPrompts2.Assign(AMiscPrompts: TMiscPrompts2);
begin
  FGetFingerPrintPrompt.Assign(AMiscPrompts.GetFingerPrintPrompt);
  FRetryGetFingerPrintPrompt.Assign(AMiscPrompts.RetryGetFingerPrintPrompt);
  FAfterGetFingerPrintPrompt.Assign(AMiscPrompts.AfterGetFingerPrintPrompt);
  FRetryAfterGetFingerPrintPrompt.Assign(AMiscPrompts.RetryAfterGetFingerPrintPrompt);
  FGetCustIDPrompt.Assign(AMiscPrompts.GetCustIDPrompt);
  FRetryGetCustIDPrompt.Assign(AMiscPrompts.RetryGetCustIDPrompt);
  FAfterGetCustIDPrompt.Assign(AMiscPrompts.AfterGetCustIDPrompt);
  FBioDOBLength6Prompt.Assign(AMiscPrompts.BioDOBLength6Prompt);                
  FBioDOBLength8Prompt.Assign(AMiscPrompts.BioDOBLength8Prompt);
  FBioZipCodePrompt.Assign(AMiscPrompts.BioZipCodePrompt);
  FBioSSNPrompt.Assign(AMiscPrompts.BioSSNPrompt);                              
  FRetryAfterGetCustIDPrompt.Assign(AMiscPrompts.RetryAfterGetCustIDPrompt);
  FBioMTXUnavailablePrompt.Assign(AMiscPrompts.BioMTXUnavailablePrompt);
end;

constructor TMiscPrompts2.Create;
begin
  inherited;
  FGetFingerPrintPrompt := TCustomerAndCashierLines.Create;
  FRetryGetFingerPrintPrompt := TCustomerAndCashierLines.Create;
  FAfterGetFingerPrintPrompt := TCustomerAndCashierLines.Create;
  FRetryAfterGetFingerPrintPrompt := TCustomerAndCashierLines.Create;
  FGetCustIDPrompt := TCustomerAndCashierLines.Create;
  FRetryGetCustIDPrompt := TCustomerAndCashierLines.Create;
  FAfterGetCustIDPrompt := TCustomerAndCashierLines.Create;
  FBioDOBLength6Prompt := TCustomerAndCashierLines.Create;                      
  FBioDOBLength8Prompt := TCustomerAndCashierLines.Create;
  FBioZipCodePrompt := TCustomerAndCashierLines.Create;
  FBioSSNPrompt := TCustomerAndCashierLines.Create;                                     
  FRetryAfterGetCustIDPrompt := TCustomerAndCashierLines.Create;
  FBioMTXUnavailablePrompt := TCustomerAndCashierLines.Create;
end;

destructor TMiscPrompts2.Destroy;
begin
  FreeAndNil(FGetFingerPrintPrompt);
  FreeAndNil(FRetryGetFingerPrintPrompt);
  FreeAndNil(FAfterGetFingerPrintPrompt);
  FreeAndNil(FRetryAfterGetFingerPrintPrompt);
  FreeAndNil(FGetCustIDPrompt);
  FreeAndNil(FRetryGetCustIDPrompt);
  FreeAndNil(FAfterGetCustIDPrompt);
  FreeAndNil(FBioDOBLength6Prompt);                                             
  FreeAndNil(FBioDOBLength8Prompt);
  FreeAndNil(FBioZipCodePrompt);
  FreeAndNil(FBioSSNPrompt);                                                    
  FreeAndNil(FRetryAfterGetCustIDPrompt);
  FreeAndNil(FBioMTXUnavailablePrompt);
  inherited;
end;

{ TLayerPrompt }

procedure TLayerPrompt.Assign(ALayerPrompt: TLayerPrompt);
begin
  FId := ALayerPrompt.FId;
  FPrompts.Assign(ALayerPrompt.Prompts);
end;

constructor TLayerPrompt.Create(ALayerPrompts: TLayerPromptList; AId: Integer);
begin
  inherited Create;
  FLayerPrompts := ALayerPrompts;
  FLayerPrompts.FItems.Add(Self);
  FPrompts := TCustomerLineList.Create;
  FId := AId;
end;

destructor TLayerPrompt.Destroy;
begin
  FPrompts.Free;
  FLayerPrompts.FItems.Remove(Self);
  inherited;
end;

{ TLayerPromptList }

function TLayerPromptList.Add(AId: Integer): TLayerPrompt;
begin
  Result := TLayerPrompt.Create(Self, AId);
end;

procedure TLayerPromptList.Assign(ALayerPrompts: TLayerPromptList);
var
  i: Integer;
begin
  Clear;
  for i := 0 to ALayerPrompts.Count - 1 do
    Add(0).Assign(ALayerPrompts[i]);
end;

procedure TLayerPromptList.Clear;
begin
  while (FItems.Count > 0) do TLayerPrompt(FItems.Last).Free;
end;

constructor TLayerPromptList.Create;
begin
  inherited Create;
  FItems := TList.Create;
end;

procedure TLayerPromptList.Delete(Index: Integer);
begin
  TLayerPrompt(FItems[Index]).Free;
end;

destructor TLayerPromptList.Destroy;
begin
  Clear;
  FreeAndNil(FItems);
  inherited;
end;

function TLayerPromptList.GetCount: Integer;
begin
  Result := FItems.Count;
end;

function TLayerPromptList.GetItem(Index: Integer): TLayerPrompt;
begin
  Result := FItems[Index];
end;

function TLayerPromptList.LayerPromptById(AId: Integer): TLayerPrompt;
var
  i: Integer;
begin
  Result := nil;
  for i := 0 to Count - 1 do
  begin
    if Items[i].Id = AId then
    begin
      Result := Items[i];
      break;
    end;
  end;
end;

{ TScreenKey }

constructor TScreenKey.Create(AKeyList: TKeyList; ALayer: Integer; AId: Integer; AValue: string; ACaption: string; ALanguageCode : string);
begin
  inherited Create(AKeyList, ALayer, AId, AValue, ACaption, ALanguageCode);
  FSubScreenKeys := TKeyList.Create(SSubScreenKey);
end;

destructor TScreenKey.Destroy;
begin
  FreeAndNil(FSubScreenKeys);
  inherited;
end;

{ TScreenKeyList }

function TScreenKeyList.GetItem(Index: Integer): TScreenKey;
begin
  Result := TScreenKey(FItems[Index]);
end;

function TScreenKeyList.Add(ALayer: Integer; AId: Integer; AValue: string; ACaption: string; ALanguageCode : string): TScreenKey;
begin
  result := TScreenKey.Create(Self, ALayer, AId, AValue, ACaption, ALanguageCode);
end;

procedure TScreenKeyList.Delete(Index: Integer);
begin
  if Assigned(TScreenKey(FItems[Index])) then
  begin
    while (TScreenKey(FItems[Index]).SubScreenKeys.Count > 0) do
      if Assigned(TScreenKey(FItems[Index]).SubScreenKeys[0]) then
        TScreenKey(FItems[Index]).SubScreenKeys[0].Free;
    TScreenKey(FItems[Index]).Free;
  end;
end;

procedure TScreenKeyList.Clear;
begin
  while FItems.Count > 0 do
    Delete(0);
end;

function TScreenKeyList.KeyByLayerAndId(ALayer: Integer; AId: Integer): TScreenKey;
var
  i: Integer;
begin
  Result := nil;
  for i := 0 to Count - 1 do
  begin
    if (Items[i].Layer = ALayer) and (Items[i].Id = AId) then
    begin
      Result := TScreenKey(Items[i]);
      break;
    end;
  end;
end;

function TScreenKeyList.KeyByValue(AValue: string): TScreenKey;
var
  i: Integer;
begin
  Result := nil;
  for i := 0 to Count - 1 do
  begin
    if Items[i].Value = AValue then
    begin
      Result := TScreenKey(Items[i]);
      break;
    end;
  end;
end;

{ TCharityDonations }

procedure TCharityDonationOptions.Assign(ACharityDonations: TCharityDonationOptions);
var           // CPCLIENTS-13012
  i: byte;    // CPCLIENTS-13012
begin
  FCustomerDisplay.Assign(ACharityDonations.CustomerDisplay);
  FCashierDisplay.Assign(ACharityDonations.CashierDisplay);
  FAOCustomerDisplay.Assign(ACharityDonations.AOCustomerDisplay);
  FAOCashierDisplay.Assign(ACharityDonations.AOCashierDisplay);

  FOAOCustomerDisplay.Assign(ACharityDonations.OAOCustomerDisplay);
  FOAOCashierDisplay.Assign(ACharityDonations.OAOCashierDisplay);
  FAmountConfirmCustomerDisplay.Assign(ACharityDonations.AmountConfirmCustomerDisplay);
  FAmountConfirmCashierDisplay.Assign(ACharityDonations.AmountConfirmCashierDisplay);

  FRoundUpLabel.Assign(ACharityDonations.RoundUpLabel);
  FOtherAmountLabel.Assign(ACharityDonations.OtherAmountLabel);
  FSkipDonationLabel.Assign(ACharityDonations.SkipDonationLabel);

  for i := Low(DonationAmount) to High(DonationAmount) do       // CPCLIENTS-13012
    DonationAmount[i] := ACharityDonations.DonationAmount[i];   // CPCLIENTS-13012

  RoundToNearestDollarAmount := ACharityDonations.RoundToNearestDollarAmount;
  DelayDonationUntilRoundUpSetByPOS := ACharityDonations.DelayDonationUntilRoundUpSetByPOS; // CPCLIENTS-7473
  AllowOtherAmount := ACharityDonations.AllowOtherAmount;
end;

procedure TCharityDonationOptions.CalculateSKIPandOTHERandROUNDUPbtns(var SKIPbtn, OTHERbtn, ROUNDUPbtn: byte; LanguageID: byte; RoundupAmt: string);

  function RoundupIsSet: boolean;
  begin
    Result := Assigned(RoundUpLabel.CustomerLineById(LanguageID)) and not RoundUpLabel.CustomerLineById(LanguageID).Line1.Trim.IsEmpty
              and RoundToNearestDollarAmount;
  end;

var
  BtnNumber: byte;
  i: byte;
begin
  BtnNumber := 0;
  SkipBtn := 0;
  OtherBtn := 0;
  ROUNDUPBtn := 0;
  for i := 1 to MAX_DONATION_AMTS do
    if IncludeButton(DonationAmount[i]) then
      Inc(BtnNumber) ;
  // if 4 buttons are used for values and we need both Skip, Other and RoundUp buttons then we need to rewrite button 4
  if RoundUpIsSet and AllowOtherAmount and (not SkipDonationLabel.CustomerLineById(LanguageID).Line1.Trim.IsEmpty)
         and (BtnNumber = 4) then
    Dec(BtnNumber);

  if Assigned(OtherAmountLabel.CustomerLineById(LanguageID)) and (AllowOtherAmount) then
  begin
    Inc(BtnNumber);
    Otherbtn := BtnNumber;
  end;
  if RoundupIsSet then
  begin
    Inc(BtnNumber) ;
    ROUNDUPbtn := BtnNumber;
  end;
  if Assigned(SkipDonationLabel.CustomerLineById(LanguageID)) then
  begin
    Inc(BtnNumber) ;
    SKIPbtn := BtnNumber;
  end;
end;

constructor TCharityDonationOptions.Create;
begin
  inherited;
  FCustomerDisplay := TCustomerLineList.Create;
  FCashierDisplay := TLineCollection5.Create;
  FAOCustomerDisplay := TCustomerLineList.Create;
  FAOCashierDisplay := TLineCollection5.Create;
  FOAOCustomerDisplay := TCustomerLineList.Create;
  FOAOCashierDisplay := TLineCollection5.Create;
  FAmountConfirmCustomerDisplay := TCustomerLineList.Create;
  FAmountConfirmCashierDisplay := TLineCollection5.Create;
  FRoundUpLabel := TCustomerLineList.Create;
  FOtherAmountLabel := TCustomerLineList.Create;
  FSkipDonationLabel := TCustomerLineList.Create;
end;

destructor TCharityDonationOptions.Destroy;
begin
  FreeAndNil(FCustomerDisplay);
  FreeAndNil(FCashierDisplay);
  FreeAndNil(FAOCustomerDisplay);
  FreeAndNil(FAOCashierDisplay);
  FreeAndNil(FOAOCustomerDisplay);
  FreeAndNil(FOAOCashierDisplay);
  FreeAndNil(FAmountConfirmCustomerDisplay);
  FreeAndNil(FAmountConfirmCashierDisplay);
  FreeAndNil(FRoundUpLabel);
  FreeAndNil(FOtherAmountLabel);
  FreeAndNil(FSkipDonationLabel);
  inherited Destroy;
end;

function TCharityDonationOptions.IncludeButton(Amt: integer): boolean;
begin
  Result := Amt > 0;
end;

// CPCLIENTS-6548 - DeCA Fee Amount changes - start
{ TFeeAmounts }

constructor TFeeAmounts.Create;
begin
  inherited;
end;

destructor TFeeAmounts.Destroy;
begin
  inherited Destroy;
end;
// CPCLIENTS-6548 - DeCA Fee Amount changes - end

{ TTruRating }

constructor TTruRatingProps.Create; // 6330
begin
  inherited;
  FTruRatingDefaultCashierLines := TLineCollection5.Create;
end;

destructor TTruRatingProps.Destroy; // 6330
begin
  FTruRatingDefaultCashierLines.Free;
  inherited;
end;

initialization
  ExtendedLog('UWinEPSConfiguration initialization');
finalization
  ExtendedLog('UWinEPSConfiguration finalization');

end.

