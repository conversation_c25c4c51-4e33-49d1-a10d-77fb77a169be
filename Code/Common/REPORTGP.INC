
{ Member: REPORTGP.INC                                                 }
{ Screen: Reporting, HSETRPTG.SCR                                      }
{                    SSUMRPTG.SCR                                      }
{                    LSUMRPTG.SCR                                      }
{                    CSUMRPTG.SCR                                      }
{                    CSOFRPTG.SCR                                      }
{$A-}

           DSReportGroupCL     = 2;
           DSReportGroupNL     = 16;
           DSReportGroupPNum   = 3;
           HSReportGroupDSN_   = 'HSRPTGRP.EFT';
           SSReportGroupDSN_   = 'SSRPTGRP.EFT';
           LSReportGroupDSN_   = 'LSRPTGRP.EFT';
           CSReportGroupDSN_   = 'CSRPTGRP.EFT';
           CSOReportGroupDSN_  = 'SORPTGRP.EFT';
Type
           DSReportGroupRec = Record
                                DSReportGroupC_ : String[DSReportGroupCL];
                                DSReportGroupN_ : String[DSReportGroupNL];
                              End;
Var
           DSReportGroupFile : File Of DSReportGroupRec;
           DSReportGroupBuf  : DSReportGroupRec;
Const
