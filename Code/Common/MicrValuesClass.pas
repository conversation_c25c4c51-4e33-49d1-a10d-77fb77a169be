// (c) MTXEPS, Inc. 1988-2008
unit MicrValuesClass;
(******************************************************************************
 *
 *  class for parsing micr data
 *
 *  Copyright (c) 2006 by MTXEPS, Inc. All Rights Reserved.
 *
 ******************************************************************************
  Revision History
  ================
  08-31-07 TSL-01 change SetMICRVars to a boolean function from a procedure
  01-29-07 TSL N/A Create
 ******************************************************************************)

interface

uses
  {$IFDEF MSWINDOWS}
  Windows,
  {$ENDIF}
  SysUtils,
  StrUtils,
  Classes,
  MTX_Lib,
  MTX_Constants,
  MdMsg;

type

  TMICRValues = class
    FChkAcct: string;
    FChkRouting: string;
    FIsMICROK: boolean;
    FMdMsgRec: MdMsgRec;
    FRawMicr: string;
    FIsToad: boolean;
    FIsCheckNumUpdated: boolean;
  private
    function  GetChkNum: string;
    function  GetTAFormat: string;
    function  GetABCFormat: string;
    procedure InitMICRStuff(InRec: MdMsgRec; aIsToad: boolean);
    function  IsABCFormat: boolean;
    function  IsTASFormat: boolean;
    function  IsTAFormat: boolean;
    function  MakeRawMICR(aData : fleetArray) : string;
    function RemoveNonDigits(aStr: string): string;
    procedure SetMICRManualFields;
    function  ValidateFChkAcct: boolean;
    procedure ValidateMICRFormatAndSetMicrFields;

  public
    function SetMICRVars(var InRec: MdMsgRec; const aIsToad: boolean = false): boolean;

    property ChkAcct : string read FChkAcct;
    property ChkRouting : string read FChkRouting;
    property ChkNum: string read GetChkNum;
    property IsMICROK: boolean read FIsMICROK;
    property IsToad: boolean read FIsToad;
    property Toad: string read FRawMicr;
    property RawMicr: string read FRawMicr;
    property ABCFormattedMICR: string read GetABCFormat;
    property TAFormattedMICR: string read GetTAFormat;
    property IsCheckNumUpdated: boolean read FIsCheckNumUpdated;
  end;

implementation

uses
  FinalizationLog,
  StringUtils;

function TMICRValues.GetChkNum: string;
begin
  result := FMdMsgRec.Check_Num;
end;

function TMICRValues.GetTAFormat: string;
begin
  result := '';
  if IsMICROK then
    result := FChkRouting + 'T' + FChkAcct + 'A' + ChkNum;
end;

function TMICRValues.GetABCFormat: string;
begin
  result := '';
  if IsMICROK then
    result := 'A' + FChkRouting + 'B' + FChkAcct + 'C' + ChkNum;
end;

procedure TMICRValues.InitMICRStuff(InRec: MdMsgRec; aIsToad: boolean);
begin
  FMdMsgRec := InRec;
  FIsCheckNumUpdated := false;
  FIsToad := aIsToad;
  FIsMICROK := false;
  FChkRouting := '';
  FChkAcct := '';
  FRawMicr := '';
  FMdMsgRec.Check_Field_3 := PackStr_(FMdMsgRec.Check_Field_3);
  FMdMsgRec.Check_Field_2 := PackStr_(FMdMsgRec.Check_Field_2);
  FRawMicr := MakeRawMICR(FMdMsgRec.FleetData);
end;

function TMICRValues.IsABCFormat: boolean;
var APos, BPos, CPos, BLen: byte;
    TempMicrData : string;
    tmpStr: string;
begin
  result := false;                      { assume it is not ABC format }
  TempMicrData := PackStr_(FMdMsgRec.Check);      { take out the spaces }
  APos := Pos('A', TempMicrData);
  BPos := Pos('B', TempMicrData);
  CPos := Pos('C', TempMicrData);
  if (APos = 1) and (BPos > 0) then     { ABC format }
  begin
    FMdMsgRec.Check := TempMicrData;
    if (CPos > 0) then
    begin
      BLen := CPos - BPos - 1;
      msgDebug('IsABCFormat - FMdMsgRec.Check=' + FMdMsgRec.Check);
      tmpStr := Copy(FMdMsgRec.Check, CPos + 1, length(FMdMsgRec.Check) - CPos);
      FMdMsgRec.Check_Num := RightStr(tmpStr, 8);
      FIsCheckNumUpdated := tmpStr <> FMdMsgRec.Check_Num;
      msgDebug('IsABCFormat - AFTER FMdMsgRec.Check_Num=' + FMdMsgRec.Check_Num + '/ FIsCheckNumUpdated=' + YN(FIsCheckNumUpdated));
    end
    else
      BLen := Length(FMdMsgRec.Check) - BPos;
    FChkRouting := Copy(FMdMsgRec.Check, 2, BPos - 2);
    FChkAcct    := Copy(FMdMsgRec.Check, BPos+1, BLen);
    result := true;
  end;
end;

function TMICRValues.IsTASFormat: boolean;
var APos, TPos, SPos, BLen: byte;
    TempMicrData : string;
begin
  result := false;
  TempMicrData := PackStr_(FMdMsgRec.Check);      { take out the spaces }
  APos := Pos('A', TempMicrData);
  SPos := Pos('S', TempMicrData);
  TPos := Pos('T', TempMicrData);

  if (TPos = 1) and (APos > 1) then       { Check for TxxxxxxAxxxxxSxxxx }
  begin
    result := true;
    FMdMsgRec.Check := TempMicrData;
    Delete(TempMicrData, 1, TPos);        { now check for T..T..A..S }
    TPos := Pos('T', TempMicrData);
    if (TPos > 0) then                    { Was T..T..A..S }
    begin
      APos := Pos('A', TempMicrData);
      FChkRouting := Copy(TempMICRData, 1, TPos-1);
      FChkAcct  := Copy(TempMICRData, TPos+1, APos-TPos-1);
      SPos := Pos('S', TempMICRData);
      if (SPos > 0) then
        FMdMsgRec.Check_Num := RightStr(Copy(TempMICRData, APos+1, SPos-APos-1), 8);
    end
    else { Was T..A..S }
    begin
      if (SPos > 0) then
      begin
        BLen := SPos - APos - 1;
        FMdMsgRec.Check_Num := RightStr(Copy(FMdMsgRec.Check, SPos + 1, length(FMdMsgRec.Check) - SPos),8);
      end
      else
        BLen := length(FMdMsgRec.Check) - APos;
      FChkRouting := Copy(FMdMsgRec.Check, 2, APos - 2);
      FChkAcct    := Copy(FMdMsgRec.Check, APos + 1, BLen);
    end;
  end;
end;

function TMICRValues.IsTAFormat: boolean;
var APos, TPos: byte;
    TempMicrData : string;
begin
  result := false;
  TempMicrData := PackStr_(FMdMsgRec.Check);      { take out the spaces }
  APos := Pos('A', TempMicrData);
  TPos := Pos('T', TempMicrData);

  if (TPos > 1) and (APos > TPos) then     { Check for xxxxxxTxxxxxxA#### }
  begin
    FMdMsgRec.Check := TempMicrData;
    FChkRouting := Copy(FMdMsgRec.Check, 1, TPos - 1);
    FChkAcct    := Copy(FMdMsgRec.Check, TPos + 1, APos - TPos - 1);
    if (APos < length(FMdMsgRec.Check)) then  { there is a check number }
      FMdMsgRec.Check_Num := RightStr(Copy(FMdMsgRec.Check, APos + 1, length(FMdMsgRec.Check) - APos),8);
    result := true;
  end;
end;

function TMICRValues.MakeRawMICR(aData : fleetArray) : string;
var
  i : integer;
begin
  result := '';
  i := 1;
  while (ord(aData[i]) <> 0) do
    begin
    result := result + aData[i];
    inc(i);
    end;
end;   { makeRawMICR }

function TMICRValues.RemoveNonDigits(aStr: string): string;
var i: integer;
begin
  result := aStr;
  for i := length(result) downto 1 do
  begin
    if pos(result[i], '**********') < 1
      then Delete(result, i, 1);
  end;
end;

procedure RemoveNonDigitsShortString(var aStr: string255);
var i: integer;
begin
  for i := length(aStr) downto 1 do
  begin
    if pos(aStr[i], '**********') < 1
      then Delete(aStr, i, 1);
  end;
end;

procedure TMICRValues.SetMICRManualFields;
begin
  // CPCLIENTS-5350 To assign Manual entry mode if it is other than swipe for Telecheck
  if (FRawMicr = '') and (FMdMsgRec.Entry <> 'T') then
    FMdMsgRec.Entry_Check := 'M';
  FMdMsgRec.Check_Field_3 := RemoveNonDigits(FMdMsgRec.Check_Field_3);
  FMdMsgRec.Check_Field_2 := RemoveNonDigits(FMdMsgRec.Check_Field_2);
  FMdMsgRec.Check_Num := RemoveNonDigits(FMdMsgRec.Check_Num);
  if (trim(FMdMsgRec.Check_Field_3) = '')
    then FMdMsgRec.Check_Field_3 := FChkRouting
    else FChkRouting := FMdMsgRec.Check_Field_3;
  if (trim(FMdMsgRec.Check_Field_2) = '')
    then FMdMsgRec.Check_Field_2 := FChkAcct
    else FChkAcct    := FMdMsgRec.Check_Field_2;
end;

function TMICRValues.ValidateFChkAcct: boolean;
begin
  if (Length(FChkRouting) = 9) and
     (Length(FChkAcct) > 1)    and
     (Length(FChkAcct) < 20)   then
  begin
    FMdMsgRec.AcctNo := FChkAcct;
    result := true;
  end
  else
  begin
    sm('****ERROR: MICR failed, length routing# want 9 is ' + intToStr(Length(FChkRouting)) +
    '  length Acct# want 1..20 is  ' + intToStr(Length(FChkAcct)));
    result := false;
  end;
end;

procedure TMICRValues.ValidateMICRFormatAndSetMicrFields;
begin
  msgDebug('ValidateMICRFormatAndSetMicrFields - FMdMsgRec.primaryIDType=' + FMdMsgRec.primaryIDType + '/ FRawMicr=' + FRawMicr + '/ FMdMsgRec.Check_Num=' + FMdMsgRec.Check_Num);
  if (FRawMicr <> '') and (FMdMsgRec.Check = '') then
    sm('RAW Micr only received')
  else
  if FMdMsgRec.primaryIDType = idMICRString then // DOEP-64525
    FMdMsgRec.Check_Num := RemoveNonDigits(FMdMsgRec.Check_Num)
  else
  if not IsABCFormat and not IsTASFormat and not IsTAFormat and not IsToad then
    sm('****ERROR: MICR Format is incorrect >' + FMdMsgRec.Check + '<')
  else
  begin
    FChkRouting := RemoveNonDigits(FChkRouting);
    FChkAcct := RemoveNonDigits(FChkAcct);
    FMdMsgRec.Check_Num := RemoveNonDigits(FMdMsgRec.Check_Num);
    msgDebug('ValidateMICRFormatAndSetMicrFields - FMdMsgRec.Check_Num=' + FMdMsgRec.Check_Num);
  end;
end;

// This is the Public function

function TMICRValues.SetMICRVars(var InRec: MdMsgRec; const aIsToad: boolean = false): boolean;
begin
  result := false;
  try
    InitMICRStuff(InRec, aIsToad);
    if (FMdMsgRec.Check = '') or (FMdMsgRec.Entry_Check = 'M') then
    begin
      if (trim(FMdMsgRec.Check_Field_2) = '') or (trim(FMdMsgRec.Check_Field_3) = '') then
        ValidateMICRFormatAndSetMicrFields;
      SetMICRManualFields;
    end
    else
      ValidateMICRFormatAndSetMicrFields;

    if (RawMicr <> '') or aIsToad or (FMdMsgRec.primaryIDType = idMICRString) // DOEP-64525
      then result := true
      else result := ValidateFChkAcct;
    FIsMICROK := result;
    InRec := FMdMsgRec;
  except
    on e: exception do
      sm('Try..Except: SetMICRVars ' + e.Message);
  end;
end;

initialization
  ExtendedLog('MicrValuesClass Initialization');
finalization
  ExtendedLog('MicrValuesClass Finalization');

end.
