
{ Member:  DEBITBIN.INC                                                }
{ Screen:  POS Debit BIN Table, DEBITBIN.SCR                           }
{$A-}
           DSDBINPANLL       = 2;
           DSDBINPANPrefL    = 13;
           DSDBINProcTypeL   = 16;
           DSDBINPNum        = 5;    { # of parameters on screen }
//           DebitBINDSN_      = 'debitbin.eft';
//           FSABinDSN_        = 'fsabin.eft';                                    { YHJ-453 }
           DebitBINScrName_  = 'DEBITBIN';
Type
           DSDBINRec = Record
                         DSDBINPANPref_ : String[DSDBINPANLL    +
                                                 DSDBINPANPrefL +
                                                 DSDBINPANPrefL];
                         DSDBINProcType_ : String[DSDBINProcTypeL];
                       End;
{$IFNDEF MTXEPSDLL} // [Hint] DEBITBIN.INC(20): Variable 'DSDBINFile' is declared but never used in 'scat2'
Var
           DSDBINFile : File of DSDBINRec;
           DSDBINBuf  : DSDBINRec;
{$ENDIF}           
Const
