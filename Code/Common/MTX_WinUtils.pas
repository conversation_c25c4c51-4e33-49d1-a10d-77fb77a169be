// (c) MTXEPS, Inc. 1988-2008
unit MTX_WinUtils;
(******************************************************************************
 *
 *  MTX_WinUtils
 *
 *  Utility function specific to Microsoft Windows. As such these routines are
 *  not available on Linux.
 *
 ******************************************************************************

  Revision History
  ================
  03-27-06 SRM N/A
    - Created unit.
    - Coeded the TerminateProcessInstances section of routines.
  04-03-06 SRM N/A
    - Fixed bug in return result for KillProcessInstances_ToolHelp
    - Added ExecuteApp function.
  04-05-06 SRM N/A
    - ExecuteApp now hides the main window of the application executed.

 ******************************************************************************)

{$WARN SYMBOL_PLATFORM OFF}

interface

uses
  Windows,
  Classes,
  MTX_Types;

type
   TWin32Version =
    (
      wvUnknown,
      wvWin95,
      wvWin98,
      wvWinNT,
      wvWin2000
    ) ;
    
const
  CRLF = Chr(13)+Chr(10);

function ExecuteApp(const ACmdLine: string; const ATimeout: Cardinal = INFINITE): TMTXBoolean;
function KillProcessInstances(AExeFilename: string): TMTXBoolean;
function GetDLLPath(const DLLName: TMTXString): TMTXString;
function GetAllProcesses: string; // XE: Remove WinEPS - not for OpenEPS but keep
function GetProcessCpuUsage(ProcessNameList, ProcessPidList, ProcessUsageList: TStrings): Double; // XE: Remove WinEPS - not for OpenEPS but keep
function GetCPUUsageStr(aDelimiter: string=CRLF): string; // XE: Remove WinEPS - not for OpenEPS but keep
function GetMemoryUsage: string; // XE: Remove WinEPS - not for OpenEPS but keep
function IsAdmin: boolean; // DEV-8663 // XE: Remove WinEPS - not for OpenEPS but keep
function ProcessExists(exeFileName: string): Boolean; // DEV-11499 // XE: Remove WinEPS - not for OpenEPS but keep

implementation

uses
  FinalizationLog,
  PSAPI,
  SysUtils,
  TLHelp32,
  MTX_Lib;

var
  Total: Double;
  PIDList, PIDListTemp,
  KernelTimeList,KernelTimeListTemp,
  UserTimeList, UserTimeListTemp,
  DTList, DTListTemp: TStrings; 

(******************************************************************************
 * GetDLLPath
 *
 * Returns the path (e.g. c:\..\openeps) of the directory where the loaded copy
 * of the DLL in which is called is located.
 ******************************************************************************)
function GetDLLPath(const DLLName: TMTXString): TMTXString;
var
  LibHandle: THandle;
  Buffer: array[0..255] of Char;
begin
  Result:= '';
  LibHandle:= GetModuleHandle(PChar(DLLName));
  if LibHandle <> INVALID_HANDLE_VALUE then
  begin
    FillChar(Buffer, Sizeof(Buffer), 0);
    GetModuleFileName(LibHandle, Buffer, SizeOf(Buffer));
    Result:= ExtractFileDir(Buffer);
  end;
end;

(******************************************************************************
 * ExecuteApp
 *
 * Executes the application (/w optional params) as specified in the ACmdLine
 * parameter.
 ******************************************************************************)
function ExecuteApp(const ACmdLine: string; const ATimeout: TMTXCardinal): TMTXBoolean;
var
  StartupInfo: _STARTUPINFOW {A};                                                             // 828.5
  ProcessInfo: _PROCESS_INFORMATION;
begin
  FillChar(StartupInfo, Sizeof(StartupInfo), 0);
  StartupInfo.cb:= SizeOf(StartupInfo);
  StartupInfo.dwFlags:= STARTF_USESHOWWINDOW;
  StartupInfo.wShowWindow:=  SW_HIDE;

  Result:= CreateProcess
    (
      nil,
      PChar(ACmdLine),
      nil,
      nil,
      False,
      0,
      nil,
      nil,
      StartupInfo,
      ProcessInfo
    );
  Result:= Result and (WaitForSingleObject(ProcessInfo.hProcess, ATimeout) =  WAIT_OBJECT_0);
end;

(******************************************************************************
 * GetSystemDir
 *
 * Returns the Windows System directory (e.g. c:/Windows/System32)
 ******************************************************************************)
function GetSystemDir: TMTXString;
var
  Buffer: array[0..255] of Char;
begin
  FillChar(Buffer, SizeOf(Buffer), 0);
  GetSystemDirectory(Buffer, SizeOf(Buffer));
  Result:= IncludeTrailingPathDelimiter(Buffer);
end;

(******************************************************************************
 * GetWinVersion
 *
 * Returns a value of the TWin32Version enumeration indicating the version of
 * Windows.
 ******************************************************************************)
function GetWinVersion: TWin32Version;
begin
  Result := wvUnknown;
  if Win32Platform = VER_PLATFORM_WIN32_WINDOWS then
    if (Win32MajorVersion > 4) or
       ((Win32MajorVersion = 4) and
       (Win32MinorVersion > 0)) then
      Result := wvWin98
      else
       Result := wvWin95
   else
     if Win32MajorVersion <= 4 then
       Result := wvWinNT
     else
       if Win32MajorVersion = 5 then
         Result := wvWin2000
end;

(******************************************************************************
 * KillProcessInstances
 *
 * This routine kills a set of processes by their EXE Filename
 *
 * Note: There are two version of this routine. For 95, 98, 2k, XP, 2k3 we use
 *       the Tool Help 32 API. For NT 4.0 we use the PSAPI.
 ******************************************************************************)
procedure KillProcess(const AProcessHandle: THandle);
begin
  TerminateProcess(AProcessHandle, 0);
end;

// Tool Help 32 Library - Work on all Windows versions except NT 4.0
function KillProcessInstances_ToolHelp(AExeFilename: string): TMTXBoolean;
var
  ProcessID: array[0..8192] of THandle;
  ProcessCount: Cardinal;
  I: Cardinal;
  ProcessHandle: THandle;
  ExeName: array[0..255] of Char;
  ProcessFilename: string;
begin
  ProcessCount:= 0;
  EnumProcesses(@ProcessID, 8192, ProcessCount);
  for I:= 0 to ProcessCount - 1 do
  begin
    ProcessHandle:= OpenProcess(PROCESS_QUERY_INFORMATION	or PROCESS_VM_READ or PROCESS_TERMINATE, False,  ProcessID[I]);
    if ProcessHandle <> 0 then
    begin
      try
        FillChar(ExeName, SizeOf(ExeName), 0);
        GetModuleFilenameEx(ProcessHandle, 0, ExeName, SizeOf(ExeName));
        ProcessFilename:= ExtractFileName(ExeName);
        if SameText(ProcessFilename, AExeFilename) then KillProcess(ProcessHandle);
      finally
        CloseHandle(ProcessHandle);
      end;
    end;
  end;
  Result:= True;
end;

// All Win32 Versions if PSAPI.DLL is installed
function KillProcessInstances_PSAPI(const AExeFilename: string): TMTXBoolean;
var
  Snapshot: THandle;
  Entry: PROCESSENTRY32;
  ProcessID: THandle;
  ProcessHandle: THandle;
  More: TMTXBoolean;
  ProcessFilename: TMTXString;
begin
  Result:= False;

  // Initialize Entry Structure
  FillChar(Entry, SizeOf(PROCESSENTRY32), 0);
  Entry.dwSize:= SizeOf(PROCESSENTRY32);

  // Obtain a snapshot of the currently running processes
  Snapshot:= CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
  if Snapshot = INVALID_HANDLE_VALUE then raise Exception.Create('Unable to create process snapshot (CreateToolhelp32Snapshot).');
  try
    // Enumerate all processes
    More:= Process32First(Snapshot, Entry);
    while (More) do
    begin
      ProcessFilename:= Entry.szExeFile;
      if SameText(ProcessFilename, AExeFilename) then
      begin
        ProcessID:= Entry.th32ProcessID;
        ProcessHandle:= OpenProcess(PROCESS_TERMINATE, False, ProcessID);
        if ProcessHandle = INVALID_HANDLE_VALUE then raise Exception.Create('Call to CreateProcess failed.');
        try
          Result:= TerminateProcess(ProcessHandle, 1);
        finally
          CloseHandle(ProcessHandle);
        end;
      end;
      FillChar(Entry, SizeOf(PROCESSENTRY32), 0);
      Entry.dwSize:= SizeOf(PROCESSENTRY32);
      More:= Process32Next(Snapshot, Entry);
    end;
  finally
    CloseHandle(Snapshot);
  end;
end;

function GetAllProcesses: string;
var
  Snapshot: THandle;
  Entry: PROCESSENTRY32;
  More: TMTXBoolean;
  ProcessFilename: TMTXString;
begin
  result := '';

  // Initialize Entry Structure
  FillChar(Entry,SizeOf(PROCESSENTRY32),0);
  Entry.dwSize := SizeOf(PROCESSENTRY32);

  // Obtain a snapshot of the currently running processes
  Snapshot := CreateToolhelp32Snapshot(TH32CS_SNAPALL,0);
  if Snapshot <> INVALID_HANDLE_VALUE then
    try    // Enumerate all processes
      More := Process32First(Snapshot,Entry);
      while More do
        begin
        ProcessFilename := Entry.szExeFile;
        result := result + '|' + ProcessFilename;
        FillChar(Entry, SizeOf(PROCESSENTRY32),0);
        Entry.dwSize := SizeOf(PROCESSENTRY32);
        More := Process32Next(Snapshot,Entry);
        end;
    finally
      CloseHandle(Snapshot);
    end;
end;

// External Utility - Work on all versions
function KillProcessInstances_TaskKill(const AExeFilename: string): TMTXBoolean;
var
  CmdLine: string;
begin
  CmdLine:= Format('kill -f %s', [AExeFilename]);
  Result:= ExecuteApp(CmdLine);
end;

function KillProcessInstances(AExeFilename: string): TMTXBoolean;
begin
  case GetWinVersion of
    wvWinNT:
    begin
      if FileExists(GetSystemDir + 'psapi.dll') then
        Result:= KillProcessInstances_PSAPI(AExeFilename)
      else
        Result:= KillProcessInstances_TaskKill(AExeFilename);
    end;
  else
    Result:= KillProcessInstances_ToolHelp(AExeFilename);
  end;
end;

//------------------------------------------------------------------------------
(*
function GetProcessCpuUsage(ProcessNameList, ProcessPidList, ProcessUsageList: TStrings{; var CPUUsage: Extended}): Double;
var
  ExeName: string;
  _vt,_ct,_et,_kt,_ut: _FILETIME;
  kt, ut: Int64;
  b1: LongBool;
  PRNum: Byte;
  CPUUse: Double;
  Proc: ProcessEntry32;
  hSnap: HWND;
  pid, k1: Cardinal; // k1 is the Application Id Which Windows Uses In Kernel
  Looper: Bool;

  function GetCpuUsageByProc(a: byte): Double;
  var
    i: Shortint;
    UserTime, KernelTime, DelayTime: Double;
  begin
    try
      i := PIDListTemp.IndexOf(PIDList.Strings[a]); //checks if this a new process or not
      if i = -1 then //if this is a new process then it's informations will be saved
      begin
        PIDListTemp.Add(PIDList.Strings[a]);
        KernelTimeListTemp.Add(KernelTimeList.Strings[a]);
        UserTimeListTemp.Add(UserTimeList.Strings[a]);
        DTListTemp.Add(DTList.Strings[a]);
        result := 0;
      end
      else
      begin
        DelayTime := StrToFloat(DTList.Strings[a]) - StrToFloat(DTListTemp.Strings[i]);
        KernelTime := StrToFloat(KernelTimeList.Strings[a]) - StrToFloat(KernelTimeListTemp.Strings[i]);
        UserTime := StrToFloat(UserTimeList.Strings[a]) - StrToFloat(UserTimeListTemp.Strings[i]);
        // CPU Usage Gets From ( ( (NewUserTime-OldUsertime) + (NewKernelTime-OldKernelTime) ) / (Time Between These Two Calculate)
        result := (UserTime+KernelTime)/(DelayTime*100);
        if result < 0 then
          result := 0;
        DTListTemp.Strings[i] := DTList.Strings[a];
        KernelTimeListTemp.Strings[i] := KernelTimeList.Strings[a];
        UserTimeListTemp.Strings[i] := UserTimeList.Strings[a];
      end;
    except
      on e: exception do
        SM('Try..Except MTX_WinUtils.GetCpuUsageByProc: ' + e.message);
    end;
  end;
begin
  try
    Total := 0;
    PRNum := 0; // I Think It's a bug coz In BDS2006 local variables don't automatically change to zero so we should do it
    PIDList.Clear;
    UserTimeList.Clear;
    KernelTimeList.Clear;
    DTList.Clear;
    Proc.dwSize := SizeOf(Proc); //Give Proc.dwSize the Size of bytes of PROCESSENTRY32
    hSnap := CreateToolhelp32Snapshot(TH32CS_SNAPALL,0);
    Looper := Process32First(hSnap,Proc);
    b1 := False;
    PIDList.Clear;
    KernelTimeList.Clear;
    UserTimeList.Clear;
    while Integer(Looper) <> 0 do
    begin
       ExeName := ExtractFileName(Proc.szExeFile);
       k1 := OpenProcess($1F0FFF, b1, Proc.th32ProcessID); //Don't ask me why just do It! this is a Routine I don't know Where of Memory is :$1F0FFF
       GetProcessTimes(k1, _ct, _et, _kt, _ut); // This procedure Returns value of used time of cpu for a certain Application. _et & _ct should be used But We don't need them.
       DTList.Add(inttostr(GetTickCount)); // This list saves time in miliseconds for further use
       kt := _kt.dwHighDateTime; // kt is kernel time
       kt := kt * 4294967296; // Use This multiple BeCause ( shl ) and ( shr ) don't work with 64bit variables
       kt := kt+_kt.dwLowDateTime; // Kt is Total of _kt.dwLowDateTime and _kt.dwHighDateTime
       ut := _ut.dwHighDateTime; // ut Is User Time
       ut := ut * 4294967296;
       ut := ut+_ut.dwLowDateTime;
       ProcessPidList.Add(inttostr(Proc.th32ProcessID));
       PIDList.Add(inttostr(Proc.th32ProcessID)); // pid is Process ID and PIDList stores them
       KernelTimeList.Add(inttostr(kt));
       UserTimeList.Add(inttostr(ut));
       CPUUse := GetCpuUsageByProc(PRNum); // CPUUse is Used Cpu Usage by current Application
       ProcessNameList.Add(ExeName);
       ProcessUsageList.Add(FormatFloat('00.00', CPUUse));

       Total := Total + strtofloat(ProcessUsageList.Strings[PRNum]);// Total is Total of used cpu by all of applications
       Looper := Process32Next(hSnap,Proc); //Looper is oposite Zero until there is a program to process by this function
       PRNum := PRNum+1; //This calculates number of Applications
    end;
    CloseHandle(hSnap);
    if Total > 100 then
      Total := 100;    
    result := Total;
    //CPUUsage := Total;
  except
    on e: exception do
      SM('Try..Except MTX_WinUtils.GetCpuUsage: ' + e.message);
  end;
end;
*)

function GetProcessCpuUsage(ProcessNameList, ProcessPidList, ProcessUsageList: TStrings): Double;
var
  ExeName : String;
  _ct,_et,_kt,_ut:_FILETIME;
  kt,ut:int64;
  b1:LongBool;
  prnum:Byte;
  cpuuse:Double;
  proc: PROCESSENTRY32;
  hSnap: HWND;
  k1: cardinal;// k1 is the Application Id Which Windows Uses In Kernel
  Looper: BOOL;

  function GetCpuUsageByProc(a:byte):Double;
  var
    i:Shortint;
    usertime,kerneltime,delaytime: Double;
  begin
    i := pidlisttemp.IndexOf(pidlist.Strings[a]);//checks if this a new process or not
    if i = -1 then //if this is a new process then it's informations will be saved
    begin
      pidlisttemp.Add(pidlist.Strings[a]);
      kerneltimelisttemp.Add(kerneltimelist.Strings[a]);
      usertimelisttemp.Add(usertimelist.Strings[a]);
      dtlisttemp.Add(dtlist.Strings[a]);
      Result:=0;
    end
    else
    begin
      delaytime := strtofloat(dtlist.Strings[a])-strtofloat(dtlisttemp.Strings[i]);
      kerneltime := strtofloat(kerneltimelist.Strings[a])-strtofloat(kerneltimelisttemp.Strings[i]);
      usertime := strtofloat(usertimelist.Strings[a])-strtofloat(usertimelisttemp.Strings[i]);
      // CPU Usage Gets From ( ( (NewUserTime-OldUsertime) + (NewKernelTime-OldKernelTime) ) / (Time Between These Two Calculate)
      result := (usertime+kerneltime)/(delaytime*100);
      if result < 0 then
        result:=0;
      dtlisttemp.Strings[i] := dtlist.Strings[a];
      kerneltimelisttemp.Strings[i]:= kerneltimelist.Strings[a];
      usertimelisttemp.Strings[i] := usertimelist.Strings[a];
    end;
  end;
begin
  total:=0;
  prnum:=0;// I Think It's a bug coz In BDS2006 local variables don't automatically change to zero so we should do it
  pidlist.Clear;
  usertimelist.Clear;
  kerneltimelist.Clear;
  dtlist.Clear;
  proc.dwSize := SizeOf(Proc);//Give proc.dwSize the Size of bytes of PROCESSENTRY32
  hSnap := CreateToolhelp32Snapshot(TH32CS_SNAPALL,0);
  Looper := Process32First(hSnap,proc);
  b1:=False;
  pidlist.Clear;
  kerneltimelist.Clear;
  usertimelist.Clear;
  while Integer(Looper) <> 0 do
  begin
    ExeName := ExtractFileName(proc.szExeFile);
    k1 := OpenProcess($1F0FFF,b1,proc.th32ProcessID);//Don't ask me why just do It! this is a Routine I don't know Where of Memory is :$1F0FFF
    GetProcessTimes(k1,_ct,_et,_kt,_ut);// This procedure Returns value of used time of cpu for a certain Application. _et & _ct should be used But We don't need them.
    dtlist.Add(inttostr(GetTickCount));// This list saves time in miliseconds for further use
    kt := _kt.dwHighDateTime;// kt is kernel time
    kt := kt * 4294967296;// Use This multiple BeCause ( shl ) and ( shr ) don't work with 64bit variables
    kt := kt+_kt.dwLowDateTime;// Kt is total of _kt.dwLowDateTime and _kt.dwHighDateTime
    ut := _ut.dwHighDateTime;// ut Is User Time
    ut := ut * 4294967296;
    ut := ut+_ut.dwLowDateTime;
    ProcessPidList.Add(inttostr(proc.th32ProcessID));
    pidlist.Add(inttostr(proc.th32ProcessID));// pid is Process ID and pidlist stores them
    kerneltimelist.Add(inttostr(kt));
    usertimelist.Add(inttostr(ut));
    cpuuse := GetCpuUsageByProc(prnum);// cpuuse is Used Cpu Usage by current Application
    ProcessNameList.Add(ExeName);
    ProcessUsageList.Add(FormatFloat('00.00',cpuuse));
    total := total + strtofloat(ProcessUsageList.Strings[prnum]);// total is total of used cpu by all of applications
    Looper := Process32Next(hSnap,proc);//Looper is oposite Zero until there is a program to process by this function
    prnum := prnum+1;//This calculates number of Applications
  end;
  CloseHandle(hSnap);
  result := total;
end;

function GetCPUUsageStr(aDelimiter: string=CRLF): string;  
var
  i: integer;
  tmpStr: string;
  ProcessNameList, ProcessPidList, ProcessUsagelist: TStrings;
  TotalCPUUsage: Double;
begin
  result := '';
  try
    ProcessNameList := TStringList.Create;
    ProcessPidList := TStringList.Create;
    ProcessUsagelist := TStringList.Create;
    try
      TotalCPUUsage := GetProcessCpuUsage(ProcessNameList, ProcessPidList, ProcessUsagelist);
      for i := 0 to ProcessNameList.Count - 1 do
      begin
        tmpStr := Format('%s (PID=%s) %s%% ',
          [ProcessNameList.Strings[i],
          ProcessPidList.Strings[i],
          ProcessUsageList.Strings[i]
          ]);
          result := result + tmpStr + CRLF;
          Sleep(1);
      end;
      result := result + '--------------------------------------------------' + CRLF;
      result := result + 'Processes : ' + inttostr(ProcessNameList.Count) + CRLF;
      result := result + 'CPU Usage : ' + floattostr(Total) + '%' + CRLF;;
      result := result + 'TotalCPU Usage : ' + floattostr(TotalCPUUsage) + '%';      
    finally
      FreeAndNil(ProcessNameList);
      FreeAndNil(ProcessPidList);
      FreeAndNil(ProcessUsagelist);
    end;
  except
    on e: exception do
      SM('Try..Except MTX_WinUtils.GetCpuUsage: ' + e.message);
  end;
end;

function GetMemoryUsage: string; 
var
  MemoryStatus: TMemoryStatus;
  function FormatCapacity(aByte: integer): string;
  begin
    result := FormatFloat('#,##0.00 (MB) ', aByte / 1024);
  end;
begin
  result := '';
  MemoryStatus.dwLength := SizeOf(MemoryStatus);
  GlobalMemoryStatus(MemoryStatus) ;
  result :=
      'Total Physical Memory    : ' + FormatCapacity(MemoryStatus.dwTotalPhys) + CRLF
    + 'Available Physical Memory: ' + FormatCapacity(MemoryStatus.dwAvailPhys) + CRLF
    + 'Memory Usage             : ' + IntToStr(MemoryStatus.dwMemoryLoad) + ' (%)' + CRLF

    + 'Size of ''MemoryStatus'' record: ' + FormatCapacity(MemoryStatus.dwLength) + CRLF
    + 'Total Physical Memory          : ' + FormatCapacity(MemoryStatus.dwMemoryLoad) + CRLF
    + 'Total Bytes of Paging File     : ' + FormatCapacity(MemoryStatus.dwTotalPageFile) + CRLF
    + 'Available bytes in paging file : ' + FormatCapacity(MemoryStatus.dwAvailPageFile) + CRLF
    + 'User Bytes of Address space    : ' + FormatCapacity(MemoryStatus.dwTotalVirtual) + CRLF
    + 'Available User bytes of address space: ' + FormatCapacity(MemoryStatus.dwAvailVirtual);
end;

function IsAdmin: Boolean; // DEV-8663
var
  hAccessToken       : tHandle;
  ptgGroups          : pTokenGroups;
  dwInfoBufferSize   : DWORD;
  psidAdministrators : PSID;
  int                : integer;            // counter
  blnResult          : boolean;            // return flag

const
 SECURITY_NT_AUTHORITY: SID_IDENTIFIER_AUTHORITY =
    (Value: (0,0,0,0,0,5)); // ntifs
 SECURITY_BUILTIN_DOMAIN_RID: DWORD = $00000020;
 DOMAIN_ALIAS_RID_ADMINS: DWORD = $00000220;
 DOMAIN_ALIAS_RID_USERS : DWORD = $00000221;
 DOMAIN_ALIAS_RID_GUESTS: DWORD = $00000222;
 DOMAIN_ALIAS_RID_POWER_: DWORD = $00000223;

begin
  Result := False;
  ptgGroups := nil;
  blnResult := OpenThreadToken( GetCurrentThread, TOKEN_QUERY,
                                True, hAccessToken );
  if ( not blnResult ) then
  begin
    if GetLastError = ERROR_NO_TOKEN then
    blnResult := OpenProcessToken( GetCurrentProcess,
    				   TOKEN_QUERY, hAccessToken );
  end;

  if ( blnResult ) then
  try

    GetMem(ptgGroups, 1024);
    blnResult := GetTokenInformation( hAccessToken, TokenGroups,
                                      ptgGroups, 1024,
                                      dwInfoBufferSize );
    CloseHandle( hAccessToken );

    if ( blnResult ) then
    begin

      AllocateAndInitializeSid( SECURITY_NT_AUTHORITY, 2,
                                SECURITY_BUILTIN_DOMAIN_RID,
                                DOMAIN_ALIAS_RID_ADMINS,
        			0, 0, 0, 0, 0, 0,
        			psidAdministrators );
      {$R-}
      for int := 0 to ptgGroups.GroupCount - 1 do

        if EqualSid( psidAdministrators,
                     ptgGroups.Groups[ int ].Sid ) then
        begin
          Result := True;
          Break;
        end;
      {$R+}

      FreeSid( psidAdministrators );
    end;

  finally
    FreeMem( ptgGroups );
  end;
end;

function ProcessExists(exeFileName: string): Boolean; // DEV-11499
var
  ContinueLoop: BOOL;
  FSnapshotHandle: THandle;
  FProcessEntry32: TProcessEntry32;
begin
  FSnapshotHandle := CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
  FProcessEntry32.dwSize := SizeOf(FProcessEntry32);
  ContinueLoop := Process32First(FSnapshotHandle, FProcessEntry32);
  Result := False;
  while Integer(ContinueLoop) <> 0 do
  begin
    if ((UpperCase(ExtractFileName(FProcessEntry32.szExeFile)) =
      UpperCase(ExeFileName)) or (UpperCase(FProcessEntry32.szExeFile) =
      UpperCase(ExeFileName))) then
    begin
      Result := True;
    end;
    ContinueLoop := Process32Next(FSnapshotHandle, FProcessEntry32);
  end;
  CloseHandle(FSnapshotHandle);
end;

procedure MtxWinUtilsInitialization;
begin
  ExtendedLog('MTX_WinUtils initialization', procedure
    begin
      PIDList := TStringList.Create;
      KernelTimeList := TStringList.Create;
      UserTimeList := TStringList.Create;
      DTList := TStringList.Create;
      PIDListTemp := TStringList.Create;
      KernelTimeListTemp := TStringList.Create;
      UserTimeListTemp := TStringList.Create;
      DTListTemp := TStringList.Create;
    end
    );
end;

initialization
  MtxWinUtilsInitialization;

finalization
  ExtendedLog('MTX_WinUtils finalization', procedure
    begin
      FreeAndNil(PIDList);
      FreeAndNil(KernelTimeList);
      FreeAndNil(UserTimeList);
      FreeAndNil(DTList);
      FreeAndNil(PIDListTemp);
      FreeAndNil(KernelTimeListTemp);
      FreeAndNil(UserTimeListTemp);
      FreeAndNil(DTListTemp);
    end
    );

end.

