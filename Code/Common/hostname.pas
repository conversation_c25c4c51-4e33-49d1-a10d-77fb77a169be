{ (c) NCR Corp. 1988-2013 }
unit hostname;

interface

uses
  SysUtils;

const
{
   NOTE:  DON'T EVER USE MTX AS A HOST EXTENSION - WE USE THIS CODE ELSEWHERE
   AND IT WOULD BE CONFUSING
}
  /// JMR-F : this does not appear in the host arrays
{$IFNDEF GUIJR} // for OpenEPS, Menu
  hnACI = 1;   ACI = 'ACI';  ACI_Name = 'ACI Host';
  hnASC = 2;   ASC = 'ASC';  ASC_Name = 'Ascendent Host';       { TSL-J }
  hnATL = 3;   ATL = 'ATL';  ATL_Name = 'Concord:EPC';
  hnBYL = 4;   BYL = 'BYL';  BYL_Name = 'Concord:H&C';
  hnCON = 5;   CON = 'CON';  CON_Name = 'Concord:Memphis';
  hnDEM = 6;   DEM = 'DEM';  DEM_Name = 'DEMO (MicroTrax)';
  hnFLC = 7;   FLC = 'FLC';  FLC_Name = 'FLC Host';  // DEV-18499
  hnNOV = 8;   NOV = 'NOV';  NOV_Name = 'Schnucks Check Auth';  // DEV-7446 // DEV-14949: was Elavon
  hnTRN = 9;   TRN = 'TRN';  TRN_Name = 'ePicTranz';
  hnISD = 10;  ISD = 'ISD';  ISD_Name = 'ISD Host';
  hnLML = 11;  LML = 'LML';  LML_Name = 'LML Host';
  hnPBL = 12;  PBL = 'PBL';  PBL_Name = 'LX1';
  hnPB2 = 13;  PB2 = 'PB2';  PB2_Name = 'LX2 - Check';
  hnLYN = 14;  LYN = 'LYN';  LYN_Name = 'Lynk Systems Inc';
  hnMER = 15;  MER = 'MER';  MER_Name = 'Mercury Host';
  hnMON = 16;  MON = 'MON';  MON_Name = 'Moneris';
  hnMPS = 17;  MPS = 'MPS';  MPS_Name = 'MPS';
  hnEPS = 18;  EPS = 'EPS';  EPS_Name = 'Server EPS';
  hnSVD = 19;  SVD = 'SVD';  SVD_Name = 'SVDot Host';
  hnRPD = 20;  RPD = 'RPD';  RPD_Name = 'Rapid Connect'; // TFS-9730
  hnJET = 21;  JET = 'JET';  JET_Name = 'JetPay'; // CPCLIENTS-4824 add new host
  hnVNT = 22;  VNT = 'VNT';  VNT_Name = 'Vantiv Host'; // CPCLIENTS-7672 add hosts: VNT, BPL
  hnBPL = 23;  BPL = 'BPL';  BPL_Name = 'BPL Host';
  hnCBS = 24;  CBS = 'CBS';  CBS_Name = 'CyberGateWay Host'; // CPCLIENTS-11513 added new host for BAMS
  hnUNU = 25;  UNU = 'UNU';  UNU_Name = 'NOT DEFINED'; /// always last

  hnERC = 90;  ERC = 'ERC';  ERC_Name = 'ePic ERC';
  hnREC = 91;  REC = 'REC';  REC_Name = 'MTX Receipt Host';
  hnFL2 = 98;  FL2 = 'FL2';  FL2_Name = 'FL2 Host'; // DEV-18499
  hnLM2 = 99;  LM2 = 'LM2';  LM2_Name = 'LM2 Host'; // DEV-11373
  // for compile
  hnCHS = 103; CHS = 'CHS';  CHS_Name = 'Chase Host'; // DEV-15516
  hnTDB = 111; TDB = 'TDB';  TDB_Name = 'TDMS'; // DOEP-32150
  hnINC = 118; INCHOST = 'INC';  INC_Name = 'Incomm';
{$ELSE} // ConMan
{ // XE: Remove WinEPS
  hnACI = 1;   ACI = 'ACI';  ACI_Name = 'ACI Host';
  hnADS = 2;   ADS = 'ADS';  ADS_Name = 'ADS Host'; // DEV-9340
  hnFDN = 3;   FDN = 'FDN';  FDN_Name = 'BAMS Host';
  hnCHS = 4;   CHS = 'CHS';  CHS_Name = 'Chase Host'; // DEV-15516
  hnATL = 5;   ATL = 'ATL';  ATL_Name = 'Concord:EPC'; // DEV-15042
  hnBYL = 6;   BYL = 'BYL';  BYL_Name = 'Concord:H&C';
  hnNOV = 7;   NOV = 'NOV';  NOV_Name = 'Elavon'; // DEV-7446: was Nova
  hnTRN = 8;   TRN = 'TRN';  TRN_Name = 'ePicTranz';
  hnFLC = 9;   FLC = 'FLC';  FLC_Name = 'TeleCheck'; // DEV-18499 // DOEP-54006: only for Check
  hnINC = 10;   INCHOST = 'INC';  INC_Name = 'Incomm'; /// INC will be confused with System.Inc
  hnLYN = 11;  LYN = 'LYN';  LYN_Name = 'Lynk Systems Inc';
  hnMPS = 12;  MPS = 'MPS';  MPS_Name = 'MPS';
  hnTDB = 13;  TDB = 'TDB';  TDB_Name = 'TDMS'; // DOEP-32150
  hnTDM = 14;  TDM = 'TDM';  TDM_Name = 'Tandem';
  hnVNT = 15;  VNT = 'VNT';  VNT_Name = 'Vantiv IBM';
  hnSVD = 16;  SVD = 'SVD';  SVD_Name = 'SVDot Host'; /// just before UNU
  hnRPD = 17;  RPD = 'RPD';  RPD_Name = 'Rapid Connect'; // TFS-9730
  hnUNU = 18;  UNU = 'UNU';  UNU_Name = 'NOT DEFINED'; /// always last
  hnREC = 90;  REC = 'REC';  REC_Name = 'MTX Receipt Host';
  // for compile <
  hnASC = 102;  ASC = 'ASC';  ASC_Name = 'Ascendent Host';
  hnCON = 106;  CON = 'CON';  CON_Name = 'Concord:Memphis';
  hnDEM = 107;  DEM = 'DEM';  DEM_Name = 'DEMO (MicroTrax)';
  hnERC = 190;  ERC = 'ERC';  ERC_Name = 'ePic ERC';
  hnLML = 110;  LML = 'LML';  LML_Name = 'LML Host';
  hnLM2 = 199;  LM2 = 'LM2';  LM2_Name = 'LM2 Host';
  hnPBL = 111;  PBL = 'PBL';  PBL_Name = 'LX1';
  hnPB2 = 112;  PB2 = 'PB2';  PB2_Name = 'LX2 - Check';
  hnMER = 115;  MER = 'MER';  MER_Name = 'Mercury Host';
  hnMON = 116;  MON = 'MON';  MON_Name = 'Moneris';
  hnFL2 = 198;  FL2 = 'FL2';  FL2_Name = 'FL2 Host'; // DEV-18499
  hnEPS = 113;  EPS = 'EPS';  EPS_Name = 'Server EPS';
  hnISD = 114;  ISD = 'ISD';  ISD_Name = 'ISD Host';
}  
{$ENDIF}
  hnMSL = 114;  MSL = 'MSL';  MSL_Name = 'Mainsail Check';

  UNUSED = 'UNUSED';
  DemoName = 'DEMO';

  cnDYL = 1;  _DYL = 'DYL';   DYL_Name = 'Async Dial';
  cnTCP = 2;  _TCP = 'TCP';   TCP_Name = 'TCP/IP';
  cnX25 = 3;  _X25 = 'X25';   X25_Name = 'X.25';
  cnNDF = 4;  _NDF = 'NDF';   NDF_Name = 'Comm Not Defined'; ///  always last
  NumComms = cnNDF;       /// 'comm not defined' must always be last for this to work
  MaxHostsAllowed = 10;   /// currently = the number of payment types
  HostDisplayL = 20;      /// length that host names can be

var
  HostDispName_Array : Array [1..hnUNU] of string[HostDisplayL];
  HostProgName_Array : Array [1..hnUNU] of string[3];
  HostComm_Array     : Array [1..hnUNU,1..NumComms] of string[3];

  CommDisp_Array     : Array [1..NumComms] of string[16] =
                      (DYL_Name, TCP_Name, X25_Name, NDF_Name);
  CommType_Array     : Array [1..NumComms] of string[3] =
                      (_DYL, _TCP, _X25, _NDF);
  HostTempCommType   : Array [1..NumComms] of string[3];
  NumHosts           : integer;
  isPublix           : boolean;

procedure GetHostNames(aIncludeInvisibleHost: boolean = false); /// populates HostDispName_Array, HostProgName_Array

implementation

procedure GetHostNames(aIncludeInvisibleHost: boolean = false);
begin  // GetHostNames 
  Fillchar(HostComm_Array,SizeOf(HostComm_Array), #0); // init 
{$IFNDEF GUIJR}
  if isPublix then
  begin
    NumHosts := 4;

    HostDispName_Array[1]  := DEM_Name;
    HostProgName_Array[1]  := DEM;
    HostComm_Array[1,cnTCP] := _TCP;

    HostDispName_Array[2]  := PBL_Name;
    HostProgName_Array[2]  := PBL;
    HostComm_Array[2,cnTCP] := _TCP;

    HostDispName_Array[3]  := PB2_Name;
    HostProgName_Array[3]  := PB2;
    HostComm_Array[3,cnTCP] := _TCP;

    HostDispName_Array[4]  := UNU_Name;
    HostProgName_Array[4]  := UNU;
    HostComm_Array[4,cnTCP] := _TCP;

    exit;
  end;
{$ENDIF}
  NumHosts := hnUNU; /// unused must always be last for this to work

  HostDispName_Array[hnACI]  := ACI_Name;
  HostProgName_Array[hnACI]  := ACI;
  HostComm_Array[hnACI,cnTCP] := _TCP;

  HostDispName_Array[hnATL]  := ATL_Name;
  HostProgName_Array[hnATL]  := ATL;
  HostComm_Array[hnATL,cnTCP] := _TCP;

{$IFDEF GUIJR} // DEV-9340
{ // XE: Remove WinEPS
  HostDispName_Array[hnADS]  := ADS_Name;
  HostProgName_Array[hnADS]  := ADS;
  HostComm_Array[hnADS,cnTCP] := _TCP;

  HostDispName_Array[hnFDN]  := FDN_Name;
  HostProgName_Array[hnFDN]  := FDN;
  HostComm_Array[hnFDN,cnTCP] := _TCP;
}  
{$ELSE}
  HostDispName_Array[hnASC]  := ASC_Name;            
  HostProgName_Array[hnASC]  := ASC;
  HostComm_Array[hnASC,cnTCP] := _TCP;
{$ENDIF}
  HostDispName_Array[hnBYL]  := BYL_Name;
  HostProgName_Array[hnBYL]  := BYL;
  HostComm_Array[hnBYL,cnTCP] := _TCP;
{$IFDEF GUIJR} // DEV-15516: only for ServerEPS
  HostDispName_Array[hnCHS]  := CHS_Name; 
  HostProgName_Array[hnCHS]  := CHS;
  HostComm_Array[hnCHS,cnTCP] := _TCP;
{$ENDIF}
{$IFNDEF GUIJR}
  HostDispName_Array[hnCON]  := CON_Name;
  HostProgName_Array[hnCON]  := CON;
  HostComm_Array[hnCON,cnTCP] := _TCP;

  HostDispName_Array[hnDEM]  := DEM_Name;
  HostProgName_Array[hnDEM]  := DEM;
  HostComm_Array[hnDEM,cnTCP] := _TCP;

  HostDispName_Array[hnEPS]  := EPS_Name;
  HostProgName_Array[hnEPS]  := EPS;
  HostComm_Array[hnEPS,cnTCP] := _TCP;
{$ENDIF}

  HostDispName_Array[hnLYN]  := LYN_Name;
  HostProgName_Array[hnLYN]  := LYN;
  HostComm_Array[hnLYN,cnTCP] := _TCP;

  HostDispName_Array[hnFLC]  := FLC_Name; // DEV-18499
  HostProgName_Array[hnFLC]  := FLC;
  HostComm_Array[hnFLC,cnTCP] := _TCP;

{$IFNDEF GUIJR}
  HostDispName_Array[hnLML]  := LML_Name;
  HostProgName_Array[hnLML]  := LML;
  HostComm_Array[hnLML,cnTCP] := _TCP;  

  HostDispName_Array[hnISD]  := ISD_Name;
  HostProgName_Array[hnISD]  := ISD;
  HostComm_Array[hnISD,cnTCP] := _TCP;

  HostDispName_Array[hnMER]  := MER_Name;
  HostProgName_Array[hnMER]  := MER;
  HostComm_Array[hnMER,cnTCP] := _TCP;

  HostDispName_Array[hnMON]  := MON_Name;
  HostProgName_Array[hnMON]  := MON;
  HostComm_Array[hnMON,cnTCP] := _TCP;
{$ENDIF}
  HostDispName_Array[hnMPS]  := MPS_Name;
  HostProgName_Array[hnMPS]  := MPS;
  HostComm_Array[hnMPS,cnTCP] := _TCP;

  HostDispName_Array[hnNOV]  := NOV_Name;
  HostProgName_Array[hnNOV]  := NOV;
  HostComm_Array[hnNOV,cnTCP] := _TCP;

{$IFNDEF GUIJR}
  HostDispName_Array[hnPBL]  := PBL_Name;
  HostProgName_Array[hnPBL]  := PBL;
  HostComm_Array[hnPBL,cnTCP] := _TCP;

  HostDispName_Array[hnPB2]  := PB2_Name;
  HostProgName_Array[hnPB2]  := PB2;
  HostComm_Array[hnPB2,cnTCP] := _TCP;
{$ENDIF}
  HostDispName_Array[hnSVD]  := SVD_Name;
  HostProgName_Array[hnSVD]  := SVD;
  HostComm_Array[hnSVD,cnTCP] := _TCP;

  HostDispName_Array[hnTRN]  := TRN_Name;
  HostProgName_Array[hnTRN]  := TRN;
  HostComm_Array[hnTRN,cnTCP] := _TCP;

  HostDispName_Array[hnRPD]  := RPD_Name; // TFS-9730
  HostProgName_Array[hnRPD]  := RPD;
  HostComm_Array[hnRPD,cnTCP] := _TCP;

{$IFDEF GUIJR}
{ // XE: Remove WinEPS
  HostDispName_Array[hnTDB]  := TDB_Name;
  HostProgName_Array[hnTDB]  := TDB;
  HostComm_Array[hnTDB,cnTCP] := _TCP;

  HostDispName_Array[hnTDM]  := TDM_Name;
  HostProgName_Array[hnTDM]  := TDM;
  HostComm_Array[hnTDM,cnTCP] := _TCP;

  HostDispName_Array[hnINC]  := INC_Name;
  HostProgName_Array[hnINC]  := INCHOST;
  HostComm_Array[hnINC,cnTCP] := _TCP;

  HostDispName_Array[hnVNT]  := VNT_Name;
  HostProgName_Array[hnVNT]  := VNT;
  HostComm_Array[hnVNT,cnTCP] := _TCP;
}
{$ENDIF}

  HostDispName_Array[hnUNU]  := UNU_Name;
  HostProgName_Array[hnUNU]  := UNU;
  HostComm_Array[hnUNU,cnTCP] := _TCP;
end; // GetHostNames

end.
