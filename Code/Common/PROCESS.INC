{*
v813.1 07-09-04 TSL-J add Print Sig Line stuff
v812  09-16-03 TSL-I Put DSProcPumpIncrement in procFieldNames
v810  03-25-03 JMR-E Move FormFeed to RptInfo.inc
v810  01-06-03 TSL-H Added var DSProcPumpIncrement (a number to add to the pump #)
v810  11-22-02 TRI-C Added values for journal service
v808  04-10-02 TRI-B Added DSProcJournalDaysPOS, DSProcJournalDaysEPS
rel807: 051801 TRI-A Added DSProcEncryption field
rel807: 051001 TSL-G Add DSFields as Tstring for the updateFiles program
** 10-27-00 JMR-D Add Credit to Debit Conversion settings
** 07-12-00 JMR-C Add Auto Export Transaction Data
** 06-27-00 JMR-B Add Command Message IP Port
** 06-01-00 JMR-A Add form hide & disable alarm options
** 05-04-00 TSL-F Add ReportDone =W if waiting, set to D by rpt.exe when done
** 03-31-00 TSL-E Add var for different v490.dll types, take out ifdefs
** 03-07-00 TSL-D Add Dup Trans check for AcctNo only
** 01-19-00 TSL-C Add issuing Form Feed at end of reports as option
** 11-24-99 TSL-B Change TermName to TermIPport so 6201 for TCP/IP server
                  port isn't hard coded anymore
** 05-21-98 TSL-A Need a new var in NT - use priner init string
************************************************************************** }

{ Member: PROCESS.INC                                                  }
{ Screen: EFT Processing Definitions, PROCESS.SCR                      }
{$A-}

           DSProcAutoL    = 1;
           DSProcAOffL    = 2;
           DSProcStartHHL = 2;
           DSProcStartMML = 2;
           DSProcTrainIDL = 9;
           DSProcStopHHL  = 2;
           DSProcStopMML  = 2;
           DSProcArchDaysL= 2;
           DSProcCutHHL   = 2;
           DSProcCutMML   = 2;
           DSProcTrackCkrL= 1;
           DSProcLPortL   = 1;
           DSProcBaudL    = 3;
           DSProcLBitsL   = 1;
           DSProcLParityL = 1;
           DSProcLStopL   = 1;
           DSProcLIRQL    = 1;
           DSProcTermIPportL  = 8;    { TSL-B }  // this is 8 only because this field used to be TermName
           DSProcPrintLogL    = 1;
           DSProcPrintTrxL    = 1;
           DSProcPrintRptL    = 1;
           DSProcTermTraceL   = 1;
           DSProcFormFeedL    = 1;    { TSL-C } { JMR-E : unused }
           DSProcDupAcctOnlyL = 1;    { TSL-D }
           DSProc490typeL     = 1;    { TSL-E }
           DSProcReportDoneL  = 1;    { TSL-F }
           DSProcHideL        = 1;    { JMR-A }
           DSProcDisableAlarmL = 1;   { JMR-A }
           DSProcCmdMsgIPportL = 5;   { JMR-B }
           DSProcExportTranDataL = 1;   { JMR-C }
           DSProcOnlineVerifyL   = 1;   { JMR-D }   { Verify prefix in Debit BIN file }
           DSProcOnlineConvertL  = 1;   { JMR-D }   { If not in Debit BIN file, convert back to Credit }
           DSProcOfflineChoiceL  = 1;   { JMR-D }   { Offline radio buttons 1-3 }
           DSProcOfflineConvertL = 1;   { JMR-D }   { If not found, convert back to Credit and use Credit offline processing }
           // decrement the following value by x+1 for each entry above, where x is the value added above.
           {DSProcPrinterInitL = 8;} { JMR-D : This is all used up now.}
           DSProcEncryptionL = 1;   { TRI-A }
           DSProcJournalDaysPOSL = 2; { TRI-B }
           DSProcJournalDaysEPSL = 2; { TRI-B }
           DSProcUseJournalServiceL = 1; { TRI-C }
           DSProcJournalPortL = 5; { TRI-C }
           DSProcSuspendJournalingL = 1; { TRI-C }
           // decrement the following value by x+1 for each entry above, where x is the value added above.
           DSProcPumpIncrementL = 2;      { TSL-H }
           DSProcSigLineYNL = 1;          { TSL-J }
           DSProcSigLineAmtL = 3;         { TSL-J }
           DSProcPrinterNormL = 4;
           DSProcPumpAllowedTendersL = 1;                                       { YHJ-328 }
           DSProcPumpDefaultTenderL = 15;                                       { YHJ-328 }

           pCR = #10;
           procFieldNames : PChar =
             'DSProcAuto=1' + pCR +
             'DSProcAOff=2' + pCR +
             'DSProcStartHH=2' + pCR +
             'DSProcStartMM=2' + pCR +
             'DSProcTrainID=9' + pCR +
             'DSProcStopHH=2' + pCR +
             'DSProcStopMM=2' + pCR +
             'DSProcArchDays=2' + pCR +
             'DSProcCutHH=2' + pCR +
             'DSProcCutMM=2' + pCR +
             'DSProcTrackCkr=1' + pCR +
             'DSProcLPort=1' + pCR +
             'DSProcBaud=3' + pCR +
             'DSProcLBits=1' + pCR +
             'DSProcLParity=1' + pCR +
             'DSProcLStop=1' + pCR +
             'DSProcLIRQ=1' + pCR +
             'DSProcTermIPport=8' + pCR +
             'DSProcPrintLog=1' + pCR +
             'DSProcPrintTrx=1' + pCR +
             'DSProcPrintRpt=1' + pCR +
             'DSProcTermTrace=1' + pCR +
             'DSProcFormFeed=1' + pCR +
             'DSProcDupAcctOnly=1' + pCR +
             'DSProc490type=1' + pCR +
             'DSProcReportDone=1' + pCR +
             'DSProcHide=1' + pCR +
             'DSProcDisableAlarm=1' + pCR +
             'DSProcCmdMsgIPport=5' + pCR +
             'DSProcExportTranData=1' + pCR +
             'DSProcOnlineVerify=1' + pCR +
             'DSProcOnlineConvert=1' + pCR +
             'DSProcOfflineChoice=1' + pCR +
             'DSProcOfflineConvert=1' + pCR +
             'DSProcEncryption=1' + pCR + { TRI-A }
             'DSProcJournalDaysPOS=2' + pCR + { TRI-B }
             'DSProcJournalDaysEPS=2' + pCR + { TRI-B }
             'DSProcUseJournalService=1' + pCR + { TRI-C }
             'DSProcJournalPort=5' + pCR +       { TRI-C }
             'DSProcSuspendJournaling=1' + pCR + { TRI-C }
             'DSProcPumpIncrement=2' + pCR +     { TSL-I }
             'DSProcSigLineYN=1' + pCR +         { TSL-J }
             'DSProcSigLineAmt=3' + pCR +        { TSL-J }
             'DSProcPrinterNorm=9' + pCR +
             'DSProcPumpDefaultTender=15' + pCR;                                { YHJ-328 }

           ProcessDSN_    = 'process.eft';
           ProcessScr_    = 'PROCESS';
Type
           DSProcRec = record
                     DSProcAuto        : String[DSProcAutoL];
                     DSProcAoff        : String[DSProcAOffL];
                     DSProcStartHH     : String[DSProcStartHHL];
                     DSProcStartMM     : String[DSProcStartMML];
                     DSProcTrainID     : String[DSProcTrainIDL];
                     DSProcStopHH      : String[DSProcStopHHL];
                     DSProcStopMM      : String[DSProcStopMML];
                     DSProcArchDays    : String[DSProcArchDaysL];
                     DSProcCutHH       : String[DSProcCutHHL];
                     DSProcCutMM       : String[DSProcCutMML];
                     DSProcTrackCkr    : String[DSProcTrackCkrL];
                     DSProcLPort       : String[DSProcLPortL];
                     DSProcBaud        : String[DSProcBaudL];
                     DSProcLBits       : String[DSProcLBitsL];
                     DSProcLParity     : String[DSProcLParityL];
                     DSProcLStop       : String[DSProcLStopL];
                     DSProcLIRQ        : String[DSProcLIRQL];
                     DSProcTermIPport  : String[DSProcTermIPportL];
                     DSProcPrintLog    : String[DSProcPrintLogL];
                     DSProcPrintTrx    : String[DSProcPrintTrxL];
                     DSProcPrintRpt    : String[DSProcPrintRptL];
                     DSProcTermTrace   : String[DSProcTermTraceL];
                     DSProcFormFeedUnused    : String[DSProcFormFeedL];     { TSL-C } { JMR-E : unused }
                     DSProcDupAcctOnly : String[DSProcDupAcctOnlyL];  { TSL-D }
                     DSProc490type     : String[DSProc490typeL];      { TSL-E }
                     DSProcReportDone  : String[DSProcReportDoneL];   { TSL-F }
                     DSProcHide        : String[DSProcHideL];         { JMR-A }
                     DSProcDisableAlarm: String[DSProcDisableAlarmL]; { JMR-A }
                     DSProcCmdMsgIPportUnused: String[DSProcCmdMsgIPportL]; { JMR-B }
                     DSProcExportTranData: String[DSProcExportTranDataL]; { JMR-C }
                     DSProcOnlineVerify  : String[DSProcOnlineVerifyL];   { JMR-D }
                     DSProcOnlineConvert : String[DSProcOnlineConvertL];  { JMR-D }
                     DSProcOfflineChoice : String[DSProcOfflineChoiceL];  { JMR-D }
                     DSProcOfflineConvert: String[DSProcOfflineConvertL]; { JMR-D }
                     {DSProcPrinterInit : String[DSProcPrinterInitL];}    { JMR-D }
                     DSProcEncryption    : String[DSProcEncryptionL];     { TRI-A }
                     DSProcJournalDaysPOSUnused : String[DSProcJournalDaysPOSL]; { TRI-B }
                     DSProcJournalDaysEPSUnused : String[DSProcJournalDaysEPSL]; { TRI-B } { same as DSProcArchDays }
                     DSProcUseJournalService : String [DSProcUseJournalServiceL]; { TRI-C }
                     DSProcJournalPort : String [DSProcJournalPortL]; { TRI-C }
                     DSProcSuspendJournaling : String [DSProcSuspendJournalingL]; { TRI-C }
                     DSProcPumpIncrement : String[DSProcPumpIncrementL];   { TSL-H }
                     DSProcSigLineYN : char;
                     DSProcSigLineAmt: string[DSProcSigLineAmtL];
                     DSProcPrinterNorm : String[DSProcPrinterNormL];
                     DSProcPumpAllowedTenders: string[DSProcPumpAllowedTendersL*12];{ YHJ-328 }
                     DSProcPumpDefaultTender: string[DSProcPumpDefaultTenderL]; { YHJ-328 }
                     DSProcTruncateCardDataAtEOD: string[1];                    { YHJ-517 }
                     //DSProcUseSSLYN: string[1]; // DEV-7810
                     DSProcSSLPort: string[DSProcTermIPportL]; // DEV-7810
                     DSProcFtpCSVFile: string[1]; // YHJ-694 <
                     DSProcCSVFtpServer: string[50];
                     DSProcCSVFtpUserName: string[20];
                     DSProcCSVFtpPassword: string[20]; // YHJ-694 >
                     DSProcCSVFtpHostDir: string[100]; // YHJ-837
                     DSProcOfflineBlackhawkReceiptVerbiage: string[100]; // DEV-11973
                     //DSProcOfflineBlackhawkReceiptVerbiageLang2: string[100];
                     //DSProcOfflineBlackhawkReceiptVerbiageLang3: string[100];
                     DSProcRemoveOfflinesFromActlogAtEOD: string[1];
                     ExcludedTenders: string[200]; // DEV-18499
                     DSProcDeclineBusinessCardPONoReq: string[1];
                     DoNotRetainCustomerName: string[1];
                  End;
Var
           DSProcFile  : File Of DSProcRec;
           DSProcBuf   : DSProcRec;
Const
