// (c) MTXEPS, Inc. 1988-2010
unit GeneralLog;

interface

uses
  Forms, SysUtils, Windows;
  //, SBConstants,SBSockets, SBClient, SBX509;

procedure Log(Msg: string);
function PrintBinary(buffer: PAnsiChar; bufferSize: Integer): AnsiString;
function SetLogFilename(LogFolder,Prefix,Basename,Suffix: string): string;
function DateTimeFromName(Filename: string): TDateTime;
procedure LogConsole(S: string);
function NowString: string;
procedure AcquireLock;
procedure ReleaseLock;

var
  LogFilename: string = '';
  DoWriteJournal: boolean = false;

implementation

uses
  SyncObjs,  //critical section
  DateUtils,
  //MTX_Constants,
  //POSCalls, NOT NEEDED
  Dialogs;

var
  Lock: TCriticalSection;

procedure CreateLock;        // JTG
begin
  if Lock = nil
    then Lock := TCriticalSection.Create;
end;

procedure AcquireLock;        // JTG
begin
  if Lock = nil
    then Lock := TCriticalSection.Create;
  Lock.Acquire;
end;

procedure DestroyLock;        // JTG
begin
  if Lock <> nil
    then FreeAndNil(Lock);
end;

procedure ReleaseLock;         // JTG
begin
  if Lock <> nil
    then Lock.Release;
end;

function PrintBinary(buffer: PAnsiChar; bufferSize: Integer): AnsiString;
var
  i: integer;
begin
  result := '';
  for i := 0 to BufferSize - 1 do
    if Buffer[i] in [#32..#126]
      then result := result + Buffer[i]
      else result := format('%s[%2.2x]',[result,ord(Buffer[i])]);
end;

procedure LogConsole(S: string);
begin
  {$IFDEF CONSOLE}
  writeln(S);
  {$ENDIF}
end;

function NowString: string;
begin
  result := FormatDateTime('yyyymmdd-hhnnss', Now)
end;

function SetLogFilename(LogFolder,Prefix,Basename,Suffix: string): string;
begin
  if LogFolder <> '' then
    ForceDirectories(LogFolder);
  if Basename = ''
    then LogFilename := LogFolder + Prefix + NowString + Suffix
    else LogFilename := LogFolder + Prefix + Basename + ' ' + NowString + Suffix;
  result := LogFilename;
end;

procedure LocalLog(Msg: string);
const
  PrevLine: string = '';
  SameMsgCount: integer = 0;
var
  i: integer;
  log: TextFile;
  PrevMsg,FormattedMsg,FormattedPrevMsg: string;
begin
  if LogFilename = '' then
    SetLogFilename('','TEST ','','.log');
  if Msg = PrevLine then
    inc(SameMsgCount)
  else
    begin
    FormattedMsg := FormatDateTime('yyyy-mm-dd hh:nn:ss.zzz ', Now) + Msg;  // create Msg line ahead of time to minimize time spent with file open
    AssignFile(log,LogFilename);
    {$I-} Append(log); {$I+}
    i := IOResult;
    if i <> 0 then
      if not FileExists(LogFilename) then
        begin
        {$I-} Rewrite(log); {$I+}
        i := IOResult;
        end;
    if i = 0 then
      try
        if SameMsgCount > 0 then
          begin
          FormattedPrevMsg := format('%s %s (repeated %d times)',[FormatDateTime('yyyy-mm-dd hh:nn:ss.zzz', Now),PrevLine,SameMsgCount]);
          writeln(log, FormattedPrevMsg);
          SameMsgCount := 0;
          end;
        writeln(log, FormattedMsg);
      finally
        CloseFile(log);
      end;
    PrevLine := Msg;
    end;
end;

function DateTimeFromName(Filename: string): TDateTime;
var
  YYYY,MM,DD,HH,NN,SS: word;
  i: integer;
  S: string;
begin
  result := Now;
  for i := 1 to length(Filename) do             // scan the filename to get to the 1st numeral
    if Filename[i] in ['0'..'9'] then
      begin                                     // we got a numeral, so get the rest of it for the timestamp
      S := copy(Filename,i,15);
      YYYY := StrToIntDef(copy(S,1,4),0);       // if any StrToInt fails, make sure the EncodeDateTime func fails
      MM   := StrToIntDef(copy(S,5,2),0);
      DD   := StrToIntDef(copy(S,7,2),0);
      HH   := StrToIntDef(copy(S,10,2),99);
      NN   := StrToIntDef(copy(S,12,2),99);
      SS   := StrToIntDef(copy(S,14,2),99);
      Log(format('Using %s from filename %s for the DateTime information (Year[%d] Mon[%d] Day[%d] %d:%d:%d',[S,Filename,YYYY,MM,DD,HH,NN,SS]));
      if not TryEncodeDateTime(YYYY,MM,DD,HH,NN,SS,0,result)
        then result := Now;
      break;      // of course, no need to continue in the for loop, since we found the time
      end;
end;

{
procedure ZipUp(S: string);
var
  ZipCmd,newRsLogName,ArcName,DateStamp: string;
  tmpHandle: THandle;
begin
  DateStamp := FormatDateTime('yyyymmdd-hhnnss',Now);
  arcName := S + DateStamp + '.zip';
  newRsLogName := S + DateStamp + '.txt';
  RenameFile(sExePath+ S + '.txt',sExePath+newRsLogName);
  zipCmd := 'zip.exe -j "' + sExePath + 'Archive\' + arcName + '" "' + sExePath + newRsLogName + '" "' + '"';
  EasyCreateProcessEx(zipCmd, tmpHandle, true, INFINITE);
  SysUtils.DeleteFile(sExePath + newRsLogName);
end;
}

procedure Log(Msg: string);
var
  Tries: integer;

  procedure TryLog;
  begin
    try
      inc(Tries);
      LocalLog(Msg);
    except on e: exception do
      if Tries <= 2 then
        begin
        sleep(50);      //sleep a moment
        TryLog;
        end;  //else do nothing
    end;
  end;

begin
  AcquireLock;                           //if we make this a MUTEX, then it should always work..
  Tries := 0;
  TryLog;
  //if DoWriteJournal then WriteJournal('SATURN >> '+Msg);
  ReleaseLock;
end;

end.

