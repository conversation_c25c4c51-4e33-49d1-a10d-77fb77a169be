// >>>>>>>>>>>>>>>>>    PLEASE READ THIS NOTE  <<<<<<<<<<
//
// This is a unit for General Utility functions that will compile under ANY Delphi compiler,
// for ANY project.
//
// Therefore, no IFDEFs are allowed in here. No usage of special components that we only
// have for one Delphi version. If you want to write a new function that is special like
// then please put it into MTX_Lib or MTX_Utils. THIS unit is to be EASILY unit tested,
// and buildable by Delphi 6 or Delphi 2007 or Delphi XE..
//
// One hint: include var parameters ErrorCode and ErrorStr if necessary to return error conditions
// to calling programs, so that this unit is not tied into logging functions like SM...
// And that would also make unit testing tremendously easier.
//

unit GeneralUtilities;

interface

// be sure to NEVER include MTX_Lib or MTX_Utils or any other unit that is compiler-specific or product-specific..
uses
  FinalizationLog,
  {$IFDEF MSWINDOWS}
  Windows,
  {$ENDIF}
  SysUtils,StrUtils,DateUtils,Types,Classes,
  MTX_Constants,
  MRTypes,
  System.TypInfo,
  System.SyncObjs;

type
  TEnum = record
  public
    class function AsString<T>(aEnum: T; aPrefixLen: integer=DEFAULT_ENUM_PREFIX_LEN): string; static; // remove prefix
    class function AsInteger<T>(aEnum: T): Integer; static;
  end;

  Obj = class
    class procedure using<T: class>(O: T; Proc: TProc<T>); overload; static;
    class function using<T: class>(O: T; Func: TFunc<T>): boolean; overload; static; // TResult
  end;

procedure LocalProcessMessages;
procedure Delay(MS: integer);
procedure MtxSleep(msecs: integer);

function GetVersionString(Filename,VerStr: string): string;
function WriteLastUpdateFile(FileSection: string; var ErrorCode: integer; var ErrorStr: string): boolean; overload; //JTG Dev 26852
//function WriteLastUpdateFile(FileSection: string; var ErrorCode: integer; var ErrorStr: string; IniFolder: string=''): boolean; //JTG Dev 26852
function WriteSignonTimestamp(var ErrorCode: integer; var ErrorStr: string): boolean; //JTG Dev 26852
function GetFileInfoFromLastUpdateFile(Section: string;
                var PreviousVersion,CurrentVersion: string; var UpdateTimestamp: TDateTime; var TimeValueExists: boolean;
                var ErrorCode: integer; var ErrorStr: string): boolean; //JTG Dev 26852
function CashierSignonFromLastUpdateFile(Section: string; var CashierSignon: TDateTime; var TimeValueExists: boolean;
                var ErrorCode: integer; var ErrorStr: string): boolean; //JTG Dev 26852
function EmvParmVerAndEmvCapkVerFromLastUpdateFile(Section: string; var SerialNumber, EmvParmVer, EmvCapkVer: string; var ErrorCode: integer; var ErrorStr:
    string): boolean;
function WriteEmvParmVerAndEmvCapkVerToLastUpdateFile(Section: string; const SerialNumber, EmvParmVer, EmvCapkVer: string; var ErrorCode: integer; var
    ErrorStr: string): boolean;
function WriteLastKeyFile(KeyFileName: string): boolean;
function ReadLastKeyFile: string;
function WriteSerialNumber(s: string): boolean;
function Clean(const S: AnsiString; Token: AnsiString): AnsiString;
// {$IF Defined(D2010_AND_LATER)}
{$IF CompilerVersion > 20}   // Delphi 2010
procedure StrToCharArray(const S: AnsiString; var CharArray: array of AnsiChar; const MaxLen: Integer); overload;
procedure StrToCharArray(const S: string; var CharArray: Array09; const MaxLen: Integer); overload;
function BytesToHex(aSource: Array of byte): string;
function ByteArrayToStr(const aByteArray: array of byte; Len: integer): string;
function BoolArrayToStr(aSource: array of longbool): string;
function CharArrayToStr(const CharArray: array of AnsiChar): AnsiString; overload;
{$IFEND}
function CharArrayToStr(const CharArray: array of Char): string; overload;
procedure StrToByteArray(const S: string; var CharArray: array of byte; const MaxLen: Integer);
function MakeStr(const Args: array of const): string;
function Join(separator : string; const Args: array of const): string;

function IsTrueString(S: AnsiString): boolean;   // allow various synonyms for 'TRUE'
function IsFalseString(S: AnsiString): boolean;  // allow various synonyms for 'FALSE'
function WritePinPadSerialNumber(aSerialNumber: string; var ErrorCode: integer; var ErrorStr: string): boolean;
function PinPadSerialNumberFromLastUpdateFile(var aSerialNumber: string; var ErrorCode: integer; var ErrorStr: string): boolean;
function UpperCaseAnsiChar(C: AnsiChar): AnsiChar;
function SplitStr(aStr,aDelimiter: string; var aList: TStringList; aTrim: boolean=false): boolean; overload;
function SplitStr(aStr,aDelimiter: WideString; var aList: TStringList; aTrim: boolean=false): boolean; overload;
function DelimitedText(list: TStringList): string;
function Lookup(Filename,Keyword: string; var ErrorStr: string): string;
function ReplaceOrAdd(Filename,Keyword,Value: string; var ErrorStr: string): boolean;
function RemoveReadOnlyAttributesForFolder(Folder: string; var ErrMsg: string): boolean;
function IsDigit(const s: string): boolean;
function CopyOneFile(Name1,Name2:string): boolean;

{$IFDEF MSWINDOWS}
function EasyCreateProcess(cmdLine: string; const Wait: Boolean = false; const TimeoutInterval: Cardinal = INFINITE): Boolean;
function EasyCreateProcessEx(cmdLine: string; var aHandle: THandle; const Wait: Boolean = false; const TimeoutInterval: Cardinal = INFINITE): Boolean;
function ExecuteAndWait(const CommandLine: string; SecondsToWait: cardinal): boolean;
function GetFileDateStr(Filename: string): string;  // JTG 23296
function CopyNewFiles(SourceFolder,TargetFolder,Mask: string; FilesCopied: TStringList; var ErrMsg: string; DoCopy: boolean=true): integer;
{$ENDIF}

function SubStrCount(ASubStr: string; AStrToScan: string): Integer; // DEV-48004/48008, useful for other development too, so put here
function IsValidVersion(const AVersion: string; var AErrorMessage: string): Boolean;

function GetIniFolder : string;

procedure ConvertChrNib(AFldChrMap : TsFldChrMap; var AFldBitMap : TFldBitMap);
procedure ConvertNibChr(AFldBitMap : TFldBitMap; var AFldChrMap : TsFldChrMap);

procedure GetFileSize(AFilePath: string; var AFileSize: integer);  //CPCLIENTS-9079/9080

function WriteLastUpdateFile(Section: string; Indent: string; Value: string): boolean; overload;
function ReadLastUpdateFile(Section: string; Indent: string): string;
function BoolStr(In_Bool : Boolean) : String5;
function TrueFalse(B: boolean): string;
function IsValidYYYYMMDD(DateStr: string): boolean;
function CompareFileVersion(Ver1, Ver2: string): integer; // CPCLIENTS-4096: 0: ver1 = ver2, 1: ver1 > ver2, -1: ver1 < ver2
procedure DeleteCTLSCONFIG(aSection: string); //CPCLIENTS-9079/9080
function ExtractStr(const str: AnsiString; startText, endText: AnsiString): AnsiString;

const
  EXCEPTIONCODE = -1;
  LASTUPDATEINI = 'LastUpdate.ini';
  INIFOLDER = 'C:\Program Files\MicroTrax\OpenEPS\';
  sTF: array[boolean] of string[5] = ('FALSE','TRUE');
  EMVPARM_VERSION = 'EMVPARM_VERSION';
  EMVCAPK_VERSION = 'EMVCAPK_VERSION';
  FILE_VERSION_DELIMITER = '.';
  SCTLSConfig = 'CTLSConfig'; //CPCLIENTS-9079/9080 //CPCLIENTS-9385

var
  MTXSleepEvent: TEvent; // THandle;

implementation

uses
  IniFiles, Math, AnsiStrings, System.RegularExpressions, System.IOUtils; //CPCLIENTS-9079/9080 //CPCLIENTS-9385

const
  COMPARE_VERSION_UNKNOWN = -99; // CPCLIENTS-4096

function IsTrueString(S: AnsiString): boolean;  // allow various synonyms for 'TRUE' // XE: Remove WinEPS - not for OpenEPS but keep
const
  _TRUE: AnsiString = 'TRUE';
  _YES: AnsiString = 'YES';
  _T: AnsiString = 'T';
  _1: AnsiString = '1';
begin
  S := AnsiStrings.Trim(S);
  result := SameText(S, _TRUE) or SameText(S, _T) or SameText(S, _YES) or SameText(S, _1);
end;

function IsFalseString(S: AnsiString): boolean;  // allow various synonyms for 'FALSE' // XE: Remove WinEPS - not for OpenEPS but keep
const
  _FALSE: AnsiString = 'FALSE';
  _NO: AnsiString = 'NO';
  _F: AnsiString = 'F';
  _0: AnsiString = '0';
begin
  S := AnsiStrings.Trim(S);
  result := SameText(S, _FALSE) or SameText(S, _F) or SameText(S, _NO) or SameText(S, _0);
end;

procedure StrToByteArray(const S: string; var CharArray: array of byte; const MaxLen: Integer); // XE: Remove WinEPS - not for OpenEPS but keep
var
  i,Len: Integer;
begin
  fillchar(CharArray,MaxLen,0);
  Len := Min(length(S),MaxLen);
  for i := 0 to Len do
    CharArray[i] := ord(S[i+1]);
end;

function MakeStr(const Args: array of const): string;
var
  I: Integer;
begin
  Result := '';
  for I := 0 to High(Args) do
     with Args[I] do
        case VType of
          vtInteger:  Result := Result + IntToStr(VInteger);
          vtBoolean:  Result := Result + BoolToStr(VBoolean);
          vtChar:     Result := Result + VChar;
          vtExtended: Result := Result + FloatToStr(VExtended^);
          vtString:   Result := Result + VString^;
          vtPChar:    Result := Result + VPChar;
          vtObject:   Result := Result + VObject.ClassName;
          vtClass:    Result := Result + VClass.ClassName;
          vtAnsiString:  Result := Result + string(VAnsiString);
          vtUnicodeString:  Result := Result + string(VUnicodeString);
          vtCurrency:    Result := Result + CurrToStr(VCurrency^);
          vtVariant:     Result := Result + string(VVariant^);
          vtInt64:       Result := Result + IntToStr(VInt64^);
  end;
end;

function Join(separator : string; const Args: array of const): string;
var
  joined : string;
  I : integer;
begin
  for I := Low(Args) to High(Args) - 1 do
    joined := joined + MakeStr(Args[i]) + separator;
  joined := joined + MakeStr(Args[High(Args)]);
  Result := joined;
end;

// {$IF Defined(D2010_AND_LATER)}
{$IF CompilerVersion > 20}   // Delphi 2010
procedure StrToCharArray(const S: AnsiString; var CharArray: array of AnsiChar; const MaxLen: Integer); overload; // XE: Remove WinEPS - not for OpenEPS but keep
var
  i,Len: Integer;
begin
  fillchar(CharArray,MaxLen,0);
  if S <> '' then // Prevent error from access to S[i+1] below if blank
  begin
    Len := Min(length(S),MaxLen);
    for i := 0 to Len do
      CharArray[i] := S[i+1];
  end;
end;

procedure StrToCharArray(const S: string; var CharArray: Array09; const MaxLen: Integer); overload;
var
  i,Len: Integer;
begin
  fillchar(CharArray,MaxLen,0);
  if S <> '' then // Prevent error from access to S[i+1] below if blank
  begin
    Len := Min(length(S),MaxLen);
    for i := 0 to Len do
      CharArray[i] := AnsiChar(S[i+1]);
  end;
end;

function BytesToHex(aSource: Array of byte): string;
begin
  SetLength(Result, Length(aSource) * 2);
  if Length(aSource) > 0 then
    BinToHex(aSource[0], PChar(Result), Length(aSource));
end;

function ByteArrayToStr(const aByteArray: array of byte; Len: integer): string;
begin
  SetLength(result,Len);
  move(aByteArray,result[1],Len);
end;

function BoolArrayToStr(aSource: array of longbool): string;
var
  index: integer;
  s: string;
begin
  s := '';
  for index := Low(aSource) to High(aSource) do
    if (aSource[index]) then
      s := s + '1'
    else
      s := s + '0';
  Result := s;
end;

function CharArrayToStr(const CharArray: array of AnsiChar): AnsiString; overload;  // XE: Remove WinEPS - not for OpenEPS but keep
var
  i: integer;
begin
  result := '';
  for i := 0 to length(CharArray)-1 do
    if CharArray[i] = #0
      then break
      else result := result + CharArray[i];
end;
{$IFEND}

function CharArrayToStr(const CharArray: array of Char): String; overload; // XE: Remove WinEPS - not for OpenEPS but keep
var
  i: integer;
begin
  result := '';
  for i := 0 to length(CharArray)-1 do
    if CharArray[i] = #0
      then break
      else result := result + CharArray[i];
end;

//remove any amount of embedded white space around a token or char
function Clean(const S: AnsiString; Token: AnsiString): AnsiString; // XE: Remove WinEPS - not for OpenEPS but keep
var
  i: integer;
begin
  result := S;
  for i := length(result) downto 1 do      //unit test showed that #0 kills the StringReplace function
    if not (result[i] in [' '..'~']) then
      delete(result,i,1);

  repeat
    result := StringReplace(result,' '+Token,Token,[rfReplaceAll,rfIgnoreCase]);
  until pos(' '+Token,result) = 0;
  repeat
    result := StringReplace(result,Token+' ',Token,[rfReplaceAll,rfIgnoreCase]);
  until pos(Token+' ',result) = 0;
end;

function GetVersionString(Filename,VerStr: string): string;
var
  Size,Handle: dword;
  Len: Cardinal;
  Buffer,Value: pchar;
  TransNo: pLongInt;
  SFInfo: string;
begin
  result := '';
  {$IFDEF MSWINDOWS}
  Size := GetFileVersionInfoSize(pChar(FileName),Handle);
  if Size > 0 then
  begin
    Buffer := AllocMem(Size);
    try
      GetFileVersionInfo(pChar(FileName),0,Size,Buffer);
      VerQueryValue(Buffer, PChar('VarFileInfo\Translation'),Pointer(TransNo),Len);
      SFInfo := format('%s%.4x%.4x%s%s%',['StringFileInfo\',LoWord(TransNo^),HiWord(Transno^),'\',VerStr]);
      if VerQueryValue(Buffer,PChar(SFInfo),Pointer(Value),Len)
        then result := Value;
    finally
      if Assigned(Buffer) then
        FreeMem(Buffer,Size);    // always release memory that's hard-allocated
    end;
  end;
  {$ENDIF}
end;

// DOEP-48288, CPCLIENTS-10545
function GetIniFolder : string;
var
  IniFilePath : string;
begin
  Result := INIFOLDER;
  {$IFDEF MTXEPSDLL}
  IniFilePath := 'C:' + DefaultDir;
  if (IniFilePath <> INIFOLDER) and DirectoryExists(IniFilePath) then
    Result := IniFilePath;
  {$ENDIF}
end;

function WriteLastUpdateFile(FileSection: string; var ErrorCode: integer; var ErrorStr: string): boolean; //JTG Dev 26852
var
  Ini: TIniFile;
  IniFilename,Filename: TFilename;
  Path,Section,CurrentVersion,PreviousVersion,LocalDir: string;
begin
  result := false;
  ErrorCode := 0;
  ErrorStr := '';
  try
    // DOEP-48288
    //IniFilename := INIFOLDER + LASTUPDATEINI;
    IniFilename := GetIniFolder + LASTUPDATEINI;
    Section := ExtractFilename(FileSection);     //Section is the unpathed filename
    Path := ExtractFilePath(FileSection);        //if we pass in a fully qualified path, then use it
    LocalDir := IncludeTrailingPathDelimiter(ExtractFilePath(paramstr(0)));

    if Path = '' then                            //otherwise use the local directory
      begin
      Filename := LocalDir + Section;            //Section is also the name of the file
      if not FileExists(Filename) then
        // DOEP-48288
        //Filename := IniFolder + Section;
        Filename := GetIniFolder + Section;
      end
    else
      Filename := FileSection;

    Ini := TIniFile.Create(IniFilename);
    try
      CurrentVersion := GetVersionString(Filename,'FileVersion');
      PreviousVersion := Ini.ReadString(Section,'CurrentVersion','');
      if CurrentVersion <> PreviousVersion then    // if versions are the same, no update took place, so leave it all alone
        begin
        Ini.WriteDateTime(Section,'UpdateTimestamp',Now);
        Ini.WriteString(Section,'PreviousVersion',PreviousVersion);
        Ini.WriteString(Section,'CurrentVersion',CurrentVersion);
        end;
    finally
      Ini.Free;
    end;
    result := true;
  except on e: exception do
    begin
    ErrorCode := EXCEPTIONCODE;
    ErrorStr := 'EXCEPTION: ' + e.Message;
    end;
  end;
end;

function WriteSignonTimestamp(var ErrorCode: integer; var ErrorStr: string): boolean; //JTG Dev 26852
var
  Ini: TIniFile;
  IniFilename: TFilename;
  LocalDir: string;
begin
  result := false;
  ErrorCode := 0;
  ErrorStr := '';
  try
    LocalDir := IncludeTrailingPathDelimiter(ExtractFilePath(paramstr(0)));
    // DOEP-48288
    //IniFilename := INIFOLDER + LASTUPDATEINI;
    IniFilename := GetIniFolder + LASTUPDATEINI;
    Ini := TIniFile.Create(IniFilename);
    try
      Ini.WriteDateTime('Other','CashierSignon',Now);
    finally
      Ini.Free;
    end;
    result := true;
  except on e: exception do
    begin
    ErrorCode := EXCEPTIONCODE;
    ErrorStr := 'EXCEPTION: ' + e.Message;
    end;
  end;
end;

function GetFileInfoFromLastUpdateFile(Section: string;
                var PreviousVersion,CurrentVersion: string; var UpdateTimestamp: TDateTime; var TimeValueExists: boolean;
                var ErrorCode: integer; var ErrorStr: string): boolean; //JTG Dev 26852
var
  Ini: TIniFile;
  IniFilename: TFilename;
begin
  result := false;
  ErrorCode := 0;
  ErrorStr := '';
  try
    // DOEP-48288
    //IniFilename := INIFOLDER + LASTUPDATEINI;
    IniFilename := GetIniFolder + LASTUPDATEINI;
    //IniFilename := IncludeTrailingPathDelimiter(ExtractFilePath(paramstr(0))) + LASTUPDATEINI;  // use explicit path in case 'current dir' is different. INI always in local dir
    Ini := TIniFile.Create(IniFilename);
    try
      PreviousVersion := Ini.ReadString(Section,'PreviousVersion','');
      CurrentVersion := Ini.ReadString(Section,'CurrentVersion','');
      TimeValueExists := Ini.ValueExists(Section,'UpdateTimestamp');
      UpdateTimestamp := Ini.ReadDateTime(Section,'UpdateTimestamp',Now);
    finally
      Ini.Free;
    end;
    result := true;
  except on e: exception do
    begin
    ErrorCode := EXCEPTIONCODE;
    ErrorStr := 'EXCEPTION: ' + e.Message;
    end;
  end;
end;

function EmvParmVerAndEmvCapkVerFromLastUpdateFile(Section: string; var SerialNumber, EmvParmVer, EmvCapkVer: string; var ErrorCode: integer; var ErrorStr:
    string): boolean;
var
  Ini: TIniFile;
  IniFilename: TFilename;
begin
  result := false;
  ErrorCode := 0;
  ErrorStr := '';
  try
    IniFilename := GetIniFolder + LASTUPDATEINI;
    Ini := TIniFile.Create(IniFilename);
    try
      SerialNumber := Ini.ReadString(Section, 'PinPadSerialNumber', '');
      EmvParmVer := Ini.ReadString(Section, EMVPARM_VERSION, '');
      EmvCapkVer := Ini.ReadString(Section, EMVCAPK_VERSION, '');
    finally
      Ini.Free;
    end;

    result := true;
  except on e: exception do
    begin
      ErrorCode := EXCEPTIONCODE;
      ErrorStr := 'EXCEPTION: ' + e.Message;
    end;
  end;
end;

function WriteEmvParmVerAndEmvCapkVerToLastUpdateFile(Section: string; const SerialNumber, EmvParmVer, EmvCapkVer: string; var ErrorCode: integer; var
    ErrorStr: string): boolean;
var
  Ini: TIniFile;
  IniFilename: TFilename;
begin
  result := false;
  ErrorCode := 0;
  ErrorStr := '';
  try
    IniFilename := GetIniFolder + LASTUPDATEINI;
    Ini := TIniFile.Create(IniFilename);
    try
      Ini.WriteString(Section, 'PinPadSerialNumber', SerialNumber);
      Ini.WriteString(Section, EMVPARM_VERSION, EmvParmVer);
      Ini.WriteString(Section, EMVCAPK_VERSION, EmvCapkVer);
    finally
      Ini.Free;
    end;

    result := true;
  except on e: exception do
    begin
      ErrorCode := EXCEPTIONCODE;
      ErrorStr := 'EXCEPTION: ' + e.Message;
    end;
  end;
end;

function WriteLastKeyFile(KeyFileName: string): boolean;
var
  Ini: TIniFile;
  IniFilename: TFilename;
begin
  result := false;
  try
    IniFilename := GetIniFolder + LASTUPDATEINI;
    Ini := TIniFile.Create(IniFilename);
    try
      Ini.WriteString('OTHER', 'KeyFileName', KeyFileName);
    finally
      Ini.Free;
    end;

    result := true;
  except on e: exception do
    begin
    end;
  end;
end;

function WriteSerialNumber(s: string): boolean;
var
  Ini: TIniFile;
  IniFilename: TFilename;
begin
  result := false;
  try
    IniFilename := GetIniFolder + LASTUPDATEINI;
    Ini := TIniFile.Create(IniFilename);
    try
      Ini.WriteString('OTHER', 'PinPadSerialNumber', s);
    finally
      Ini.Free;
    end;

    result := true;
  except on e: exception do
    begin
    end;
  end;
end;

function ReadLastKeyFile: string;
var
  Ini: TIniFile;
  IniFilename: TFilename;
begin
  result := '';
  try
    IniFilename := GetIniFolder + LASTUPDATEINI;
    Ini := TIniFile.Create(IniFilename);
    try
      Result := Ini.ReadString('OTHER','KeyFileName', '');
    finally
      Ini.Free;
    end;
  except on e: exception do
    begin
    end;
  end;
end;

function CashierSignonFromLastUpdateFile(Section: string; var CashierSignon: TDateTime; var TimeValueExists: boolean;
                var ErrorCode: integer; var ErrorStr: string): boolean; //JTG Dev 26852
var
  Ini: TIniFile;
  IniFilename: TFilename;
begin
  result := false;
  ErrorCode := 0;
  ErrorStr := '';
  try
    // DOEP-48288
    //IniFilename := INIFOLDER + LASTUPDATEINI;
    IniFilename := GetIniFolder + LASTUPDATEINI;
    //IniFilename := IncludeTrailingPathDelimiter(ExtractFilePath(paramstr(0))) + LASTUPDATEINI;  // use explicit path in case 'current dir' is different. INI always in local dir
    Ini := TIniFile.Create(IniFilename);
    try
      TimeValueExists := Ini.ValueExists(Section,'CashierSignon');
      CashierSignOn := Ini.ReadDateTime(Section,'CashierSignon',Now);
    finally
      Ini.Free;
    end;
    result := true;
  except on e: exception do
    begin
    ErrorCode := EXCEPTIONCODE;
    ErrorStr := 'EXCEPTION: ' + e.Message;
    end;
  end;
end;

function PinPadSerialNumberFromLastUpdateFile(var aSerialNumber: string; var ErrorCode: integer; var ErrorStr: string): boolean;
const
  SECTION = 'Other';
  DEFAULT = '';
var
  Ini: TIniFile;
  IniFilename: TFilename;
begin
  result := false;
  ErrorCode := 0;
  ErrorStr := '';
  try
    // DOEP-48288
    //IniFilename := INIFOLDER + LASTUPDATEINI;
    IniFilename := GetIniFolder + LASTUPDATEINI;
    //IniFilename := IncludeTrailingPathDelimiter(ExtractFilePath(paramstr(0))) + LASTUPDATEINI;  // use explicit path in case 'current dir' is different. INI always in local dir
    Ini := TIniFile.Create(IniFilename);
    try
      aSerialNumber := Ini.ReadString(SECTION,'PinPadSerialNumber',DEFAULT);
    finally
      Ini.Free;
    end;
    result := true;
  except on e: exception do
    begin
    ErrorCode := EXCEPTIONCODE;
    ErrorStr := 'EXCEPTION: ' + e.Message;
    end;
  end;
end;

function WritePinPadSerialNumber(aSerialNumber: string; var ErrorCode: integer; var ErrorStr: string): boolean;
const
  SECTION = 'Other';
var
  Ini: TIniFile;
  IniFilename: TFilename;
  LocalDir: string;
begin
  result := false;
  ErrorCode := 0;
  ErrorStr := '';
  try
    LocalDir := IncludeTrailingPathDelimiter(ExtractFilePath(paramstr(0)));
    // DOEP-48288
    //IniFilename := INIFOLDER + LASTUPDATEINI;
    IniFilename := GetIniFolder + LASTUPDATEINI;
    Ini := TIniFile.Create(IniFilename);
    try
      Ini.WriteString(SECTION,'PinPadSerialNumber',aSerialNumber);
    finally
      Ini.Free;
    end;
    result := true;
  except on e: exception do
    begin
    ErrorCode := EXCEPTIONCODE;
    ErrorStr := 'EXCEPTION: ' + e.Message;
    end;
  end;
end;

{$IFDEF MSWINDOWS}
function EasyCreateProcessEx(cmdLine: string; var aHandle: THandle; const Wait: Boolean = false; const TimeoutInterval: Cardinal = INFINITE): Boolean;
// This proc takes care of all the tedious parameters of the CreateProcess API call.
var
  dw             : integer ;
  lb             : longbool;
  lp             : pointer ;
  ts             : TStartupInfo;
  tp             : TProcessinformation;
begin
  result := false;
  try
    dw:=0;
    lb:=false;
    lp:=nil;
    fillchar(ts, sizeof(ts), 0);
    fillchar(tp, sizeof(tp), 0);
    ts.dwflags     := STARTF_USESHOWWINDOW;
    ts.wShowWindow := SW_HIDE;
    Result := CreateProcess(nil,pChar(cmdLine),nil,nil,lb,dw,lp,nil,ts,tp);
    if Wait and Result then
      WaitForSingleObject(tp.hProcess, TimeoutInterval);
    aHandle := tp.hProcess;
    CloseHandle(tp.hProcess);
    CloseHandle(tp.hThread);
  except
    on E: Exception do
      ;//SM('Exception in MTX_Lib.EasyCreateProcessEx: ' + E.Message);
  end;
end;
{$ENDIF}

{$IFDEF MSWINDOWS}
function GetFileDateStr(Filename: string): string;  // JTG 23296
var
  //Age: integer;
  sFilename: string;

  // This overloaded version of FileAge is deprecated
  (*procedure ReturnFileDate(aFilename: string);
  begin
    Age := FileAge(aFileName);
    if Age >= 0
      then result := FormatDateTime('yyyy-mm-dd hh:nn:ss',FileDateToDateTime(Age))
      else result := 'FILE DATE UNAVAILABLE';
  end;*)

  function ReturnFileDate(aFilename: string): string;
  var
    timeDate : TDateTime;
  begin
    if FileAge(aFileName, timeDate) then
      result := FormatDateTime('yyyy-mm-dd hh:nn:ss',timeDate)
    else
      result := 'FILE DATE UNAVAILABLE';
  end;

begin
  sFilename := Filename;
  if not FileExists(sFilename) then
    begin
    sFilename := IncludeTrailingPathDelimiter(ExtractFilePath(ParamStr(0))) + Filename;  // if can't find it as given, then try filename in local folder
    if not FileExists(sFilename) then
      sFilename := ExtractFilename(Filename);           // finally just try the filename by itself
    end;
  if FileExists(sFilename)
    then result := ReturnFileDate(sFilename)
    else result := 'CANNOT LOCATE FILE: '+Filename;
end;
{$ENDIF}

{$IFDEF MSWINDOWS}
function EasyCreateProcess(cmdLine: string; const Wait: Boolean = false; const TimeoutInterval: Cardinal = INFINITE): Boolean;
var
  aHandle: THandle;
begin
  result := false;
  try
    result := EasyCreateProcessEx(cmdLine, aHandle, Wait, TimeoutInterval);
  except
    on E: Exception do
      ;//SM('Exception in MTX_Lib.EasyCreateProcess: ' + E.Message);
  end;
end;
{$ENDIF}

{$IFDEF MSWINDOWS}
function ExecuteAndWait(const CommandLine: string; SecondsToWait: cardinal): boolean; // XE: Remove WinEPS - not for OpenEPS but keep
const
  ErrUINT = high(cardinal);
var
  tSI: TStartupInfo;
  tPI: TProcessInformation;
  dwI: DWORD;
  Handle: Cardinal;
begin
  Handle := ErrUINT;
  fillchar(tSI,sizeof(TStartupInfo),0);
  tSI.cb := sizeof(TStartupInfo);
  tSI.dwflags := STARTF_USESHOWWINDOW;
  tSI.wShowWindow := SW_HIDE;
  if CreateProcess(nil,pchar(CommandLine),nil,nil,false,0,nil,nil,tSI,tPI) then
    begin
    dwI := WaitForSingleObject(tPI.hProcess,SecondsToWait*1000);
    if dwI = WAIT_OBJECT_0 then
      if GetExitCodeProcess(tPI.hProcess,dwI) then
        Handle := dwI;
    CloseHandle(tPI.hProcess);
    CloseHandle(tPI.hThread);
    end;
  result := Handle <> ErrUINT;
end;
{$ENDIF}

function UpperCaseAnsiChar(C: AnsiChar): AnsiChar; // XE: Remove WinEPS - not for OpenEPS but keep
begin
  if C in ['a'..'z']
    then result := AnsiChar(ord(C) - 32)
    else result := C;
end;

//JTG: original - mismatched aStr and aDelimiter types..
function SplitStr(aStr,aDelimiter: string; var aList: TStringList; aTrim: boolean=false): boolean; overload;
var
  idx: Integer;
begin
  result := false;
  try
    if NOT Assigned(aList) then Exit;
    aList.Clear;
    if aTrim then
      aStr := Trim(aStr);
    while Pos(aDelimiter, aStr) > 0 do
    begin
      idx := Pos(aDelimiter, aStr);
      aList.Add(Copy(aStr, 1, idx-1));
      aStr := Copy(aStr, idx + Length(aDelimiter), Length(aStr) - (idx + Length(aDelimiter)-1));
    end;
    if Length(aStr) > 0 then
      aList.Add(aStr);
    result := True;
  except
    ;
  end;
end;

function SplitStr(aStr,aDelimiter: WideString; var aList: TStringList; aTrim: boolean=false): boolean; overload; // XE: Remove WinEPS - not for OpenEPS but keep
var
  idx: Integer;
begin
  result := false;
  try
    if NOT Assigned(aList) then Exit;
    aList.Clear;
    if aTrim then
      aStr := Trim(aStr);
    while Pos(aDelimiter, aStr) > 0 do
    begin
      idx := Pos(aDelimiter, aStr);
      aList.Add(Copy(aStr, 1, idx-1));
      aStr := Copy(aStr, idx + Length(aDelimiter), Length(aStr) - (idx + Length(aDelimiter)-1));
    end;
    if Length(aStr) > 0 then
      aList.Add(aStr);
    result := True;
  except
    ;
  end;
end;

function DelimitedText(list: TStringList): string;
var idx: integer;
begin
  result := '';
  if NOT Assigned(list) then
    Exit;
  for idx := 0 to list.Count -1 do
    if idx = 0
      then result := list[idx]
      else result := result + list.Delimiter + list[idx];
end;

function Lookup(Filename,Keyword: string; var ErrorStr: string): string; // XE: Remove WinEPS - not for OpenEPS but keep
var
  Reg: TStringList;
  Dir: string;
begin
  try
    result := '';
    ErrorStr := '';
    if FileExists(Filename) then
      begin
      Reg := TStringList.Create;
      try
        Reg.Duplicates := dupAccept;
        Reg.CaseSensitive := false;
        Reg.Sorted := false;
        Reg.LoadFromFile(Filename);
        result := trim(Reg.Values[Keyword]);
      finally
        Reg.Free;
      end;
      end
    else
      begin
      GetDir(0,Dir);
      ErrorStr := format('Unable to find file %s in %s, so result for %s is blank',[Filename,Dir,Keyword]);
      end;
  except on e: exception do
    ErrorStr := format('GeneralUtilities.Lookup ERROR: %s',[e.Message]);
  end;
end;

function ReplaceOrAdd(Filename,Keyword,Value: string; var ErrorStr: string): boolean; //creates new file if not exist // XE: Remove WinEPS - not for OpenEPS but keep
var
  Reg: TStringList;
  OldValue: string;
begin
  result := false;
  try
    ErrorStr := '';
    Reg := TStringList.Create;
    try
      Reg.Duplicates := dupAccept;
      Reg.CaseSensitive := false;
      Reg.Sorted := false;
      if FileExists(Filename) then
        Reg.LoadFromFile(Filename);
      OldValue := trim(Reg.Values[Keyword]);
      if OldValue = ''
        then Reg.Add(format('%s=%s',[Keyword,Value]))
        else Reg.Values[Keyword] := Value;
      Reg.SaveToFile(Filename);
    finally
      Reg.Free;
    end;
    result := true;
  except on e: exception do
    ErrorStr := format('GeneralUtilities.Lookup ERROR: %s',[e.Message]);
  end;
end;

{$WARN SYMBOL_PLATFORM OFF}
function RemoveReadOnlyAttributesForFolder(Folder: string; var ErrMsg: string): boolean; // XE: Remove WinEPS - not for OpenEPS but keep
var
  SR: TSearchRec;
  Attributes: word;
  ErrorCode: integer;
  CurrentDir: string;
begin
  result := true;   // default, in case there are no files to work on...
  ErrMsg := '';
  {$IFDEF MSWINDOWS} // FileGetAttr is only for windows
  if (Folder <> '') and DirectoryExists(Folder) then
    try
      GetDir(0,CurrentDir);    // change back to original dir
      ChDir(Folder);    // JTG: would changing the currently directory cause other problems?
      try
        if FindFirst('*.*', faReadOnly, SR) = 0 then    //JTG refactored & fixed
        try
          repeat
            Attributes := SysUtils.FileGetAttr(SR.name);
            if (Attributes and faReadOnly) = faReadOnly then
              try
                //ErrMsg := format('WARNING: [%s] is read-only. Making it writeable.',[SR.name]);
                ErrorCode := SysUtils.FileSetAttr(SR.name,Attributes and not faReadOnly);
                result := ErrorCode = 0;
                if not result then
                  ErrMsg := ErrMsg + format('%s FAILED TO MAKE [%s] WRITEABLE in folder [%s] ErrorCode=%d'#13#10,
                            [ERRMSG,SR.Name,Folder, ErrorCode]); // was SysErrorMessage(ErrorCode)
              except on e: EInOutError do
                begin
                result := false;
                ErrMsg := ErrMsg + format('WARNING: Unable to make [%s] writeable because it is in use: %s'#13#10,[SR.name,e.Message]);
                end;
              end;
          until FindNext(SR) <> 0;
        finally
          SysUtils.FindClose(SR);
        end;
      finally
        ChDir(CurrentDir);   // change back to original dir
      end;
    except on e: exception do
      ErrMsg := ErrMsg + format('RemoveReadOnlyAttributesForFolder: Folder= [%s] (%s)',[Folder,e.message]);
    end;
  {$ENDIF}
end;
{$WARN SYMBOL_PLATFORM ON}


function IsDigit(const s: string): boolean;
var
  //x: integer;
  ch : Char;
begin
  Result := TRUE;
  //for x:=1 to length(s) do
  for ch in s do
  begin
    //if NOT (s[x] in ['0'..'9']) then
    if not CharInSet(ch, ['0'..'9']) then
    begin
      Result := FALSE;
      break;
    end;
  end;
end;

function CopyOneFile(Name1,Name2:string): boolean; // XE: Remove WinEPS - not for OpenEPS but keep
var
  fsSource, fsTarget, MemoryStream: TStream; // Source, Target and Memory
begin
  result := false;
  try
    MemoryStream := TMemoryStream.Create;                // Create Memory stream
    try
      fsSource := TFileStream.Create(Name1,fmOpenRead or fmShareDenyNone);   // Open Source File
      try
        MemoryStream.CopyFrom(fsSource,fsSource.Size);       // Load Source file into Memory
        // note that both the Source file stream and the memory stream are now at their ends,
      finally
        fsSource.Free;        // don't need Source stream anymore
      end;

      fsTarget := TFileStream.Create(Name2,fmCreate or fmShareExclusive);    // Create Target File
      try
        MemoryStream.Seek(0,soFromBeginning);        // but, I have to reset the memory to it's start
        fsTarget.CopyFrom(MemoryStream,MemoryStream.Size);            // Now I can pump it to the target file:
      finally
        fsTarget.Free;                            // And voila! Now my stuff is copied
      end;
    finally
      MemoryStream.Free;
    end;
    result := true;
  except
  end;
end;


{$IFDEF MSWINDOWS}
function CopyNewFiles(SourceFolder,TargetFolder,Mask: string; FilesCopied: TStringList; var ErrMsg: string; DoCopy: boolean=true): integer; // XE: Remove WinEPS - not for OpenEPS but keep
const
  OVERWRITE_OK = false;
var
  SR: TSearchRec;
  //iFileAge: integer;
  CurrentDir: string;
  SourceFileDate,TargetFileDate: TDateTime;
  OK: boolean;
begin
  result := 0;   // default, in case there are no files to work on...
  ErrMsg := '';
  if (SourceFolder = '') or (TargetFolder = '') then
    ErrMsg := 'Invalid or blank folder specified'
  else if not DirectoryExists(SourceFolder) then
    ErrMsg := SourceFolder + ' folder does not exist!'
  else if not DirectoryExists(TargetFolder) then
    ErrMsg := TargetFolder + ' folder does not exist!'  // we can create the target folder, but an error here is more likely a bad input
  else
    try
      try
        GetDir(0,CurrentDir);    // change back to original dir
        ChDir(SourceFolder);    // JTG: would changing the currently directory cause other problems?
        SourceFolder := IncludeTrailingPathDelimiter(SourceFolder);
        TargetFolder := IncludeTrailingPathDelimiter(TargetFolder);
        if FindFirst(SourceFolder+Mask,faReadOnly,SR) = 0 then
          try
            repeat
              OK := FileAge(SourceFolder+SR.name,SourceFileDate);
              if OK then
                if FileExists(TargetFolder+SR.name)
                  then OK := FileAge(TargetFolder+SR.name,TargetFileDate)
                  else TargetFileDate := 0;      //and OK is already true
              if OK and (SourceFileDate > IncSecond(TargetFileDate,10)) then    //give then at least 10 seconds difference
                begin
                if DoCopy
                  // {$IFDEF D2010_AND_LATER}
                  {$IF CompilerVersion > 20}   // Delphi 2010
                  then CopyFile(pWideChar(SourceFolder+SR.Name),pWideChar(TargetFolder+SR.Name),OVERWRITE_OK);
                  {$ELSE}
                  then CopyFile(pChar(SourceFolder+SR.Name),pChar(TargetFolder+SR.Name),OVERWRITE_OK);
                  {$ENDIF}
                if Assigned(FilesCopied) then
                  FilesCopied.Add(SR.Name);
                inc(result);
                end;
            until FindNext(SR) <> 0;
          finally
            SysUtils.FindClose(SR);
          end;
      finally
        ChDir(CurrentDir);   // change back to original dir
      end;
    except on e: exception do
      ErrMsg := format('CopyNewFiles: SourceFolder[%s] TargetFolder[%s] (%s)',[SourceFolder,TargetFolder,e.message]);
    end;
end;
{$ENDIF}

function SubStrCount(ASubStr: string; AStrToScan: string): Integer;
begin
  // DEV-48004/48008, a function to count number of occurences of given substring (or char) in the string to scan
  if (Length(ASubStr) = 0) or (Length(AStrToScan) = 0) or (Pos(ASubStr, AStrToScan) = 0) then
    Result := 0
  else
    Result := (Length(AStrToScan) - Length(StringReplace(AStrToScan, ASubStr, '', [rfReplaceAll]))) div Length(ASubStr);
end;

function IsValidVersion(const AVersion: string; var AErrorMessage: string): boolean;
const
  ValidVersionPattern = '^\d+(\.\d+){3}$';
begin
  result := True;
  AErrorMessage := '';

  // The version contains 'file' when there's a failure on Linux
  if pos('file', AVersion) > 0 then
  begin
    AErrorMessage := 'Version invalid due to file corruption';
    Exit(False);
  end;
  if Trim(AVersion) = '' then
  begin
    AErrorMessage := 'Version blank';
    Exit(False);
  end;
  if Not TRegEx.Match(AVersion, ValidVersionPattern).Success then
  begin
    AErrorMessage := format('Version number %s is invalid', [AVersion]);
    Exit(False);
  end;
end;

procedure LocalProcessMessages;
{$IFDEF MSWINDOWS}
var Msg: TMsg;
{$ENDIF}
begin
{$IFDEF MSWINDOWS}
  while PeekMessage(Msg, 0, 0, 0, PM_REMOVE) do
  begin
    TranslateMessage(Msg);
    DispatchMessage(Msg);
  end;
{$ENDIF}
end;

procedure Delay(MS: integer);
var TZero: TDateTime;
begin
  try
    TZero := Now;
    repeat
      Sleep(50);
      {$IFDEF MSWINDOWS}
          LocalProcessMessages;
      {$ENDIF MSWINDOWS}
    until MillisecondSpan(TZero, Now) > MS;
  except
    on e: exception do
      //SM('Exception in GeneralUtilities.Delay - ' + e.message);
  end;
end;

{______________________________________________________________________________}
{ Convert Character Hex to Nibble Hex                                          }
{______________________________________________________________________________}
procedure ConvertChrNib(AFldChrMap : TsFldChrMap; var AFldBitMap : TFldBitMap);
var
  i,K,N,M,X: longword;
begin
  i := 1;
  K := 0;
  M := 7;
  fillchar(AFldBitMap, sizeof(TFldBitMap), 0);
  repeat
    if AFldChrMap[i] in ['0'..'9']
      then N := ord(AFldChrMap[i]) - 48  { c - ord('0') }
      else N := ord(AFldChrMap[i]) - 55; { c - (ord('A') - 10) [65 - 10] }
    X := (N shl (M * 4));
    AFldBitMap[K] := AFldBitMap[K] + X;
    if (i mod 8) = 0 then
      begin
      inc(K);
      M := 7;
      end
    else
      dec(M);
    inc(i);
  until i > lenChrMap;
end;

{______________________________________________________________________________}
{ Convert Nibble Hex to Character Hex                                          }
{______________________________________________________________________________}
procedure ConvertNibChr(AFldBitMap : TFldBitMap; var AFldChrMap : TsFldChrMap);
var
  I : longword;
  K : longword;
  N : longword;
  M : longword;
begin
  I := 1;
  K := 0;
  M := 0;
  AFldChrMap := '';
  repeat
    N := ((AFldBitMap[K]) shl (M * 4)) and $F0000000;
    N := N div $10000000;
    if N < 10 then
      AFldChrMap := AFldChrMap + chr(N + 48)  { ord('0') }
    else
      AFldChrMap := AFldChrMap + chr(N + 55); { ord('A') - 10 }
    inc(M);
    if (I mod 8) = 0 then
      begin
      inc(K);
      M := 0;
      end;
    inc(I);
  until I > lenChrMap;
end;

//CPCLIENTS-9079/9080 Get file size of given file.
procedure GetFileSize(AFilePath: string; var AFileSize: integer);
var
  Bytes: TBytes;
begin
  if AFilePath > '' then
  begin
    try
      Bytes := TFile.ReadAllBytes(AFilePath);
      aFileSize := Length(Bytes);
    except
      on e:exception do
      begin
//        SM('Try..Except GetFileSize - ' + e.message); //CPCLIENTS-9385
      end;
    end;
  end;
end;

function WriteLastUpdateFile(Section: string; Indent: string; Value: string): boolean;
var
  IniFile: TIniFile;
  Filename: TFilename;
begin
  result := false;
  try
    Filename := GetIniFolder + LASTUPDATEINI;
    IniFile := TIniFile.Create(Filename);
    try
      IniFile.WriteString(Section, Indent, Value);
    finally
      IniFile.Free;
    end;
    result := true;
  except
    on e: exception do
      ;//SM('Try..Except WriteLastUpdateFile - ' + e.message);
  end;
end;

function ReadLastUpdateFile(Section: string; Indent: string): string;
var
  IniFile: TIniFile;
  Filename: TFilename;
begin
  result := '';
  try
    Filename := GetIniFolder + LASTUPDATEINI;
    IniFile := TIniFile.Create(Filename);
    try
      result := IniFile.ReadString(Section, Indent, '');
    finally
      IniFile.Free;
    end;
  except
    on e: exception do
      ;//SM('Try..Except ReadLastUpdateFile - ' + e.message);
  end;
end;

function BoolStr(In_Bool: boolean): string5; // unit test in Test_MTX_Lib  // jtg refactored to one line
const
  STR_TF: array[boolean] of string5 = (('False'),('True'));
begin
  result := STR_TF[In_Bool];
end;

function TrueFalse(B: boolean): string;
const
  tmpArray: array[boolean] of string = (('False'),('True'));
begin
  result := tmpArray[B];
end;
procedure MtxSleep(msecs: integer);
begin
  {$IFDEF LINUX}
  Sleep(msecs);
  {$ELSE}
  // WaitForSingleObject(MTXSleepEvent, msecs);
  MTXSleepEvent.WaitFor(msecs);
  {$ENDIF}
end;

function IsValidYYYYMMDD(DateStr: string): boolean;
var
  y, m, d: Integer;
const
  DAYS_OF_MONTH: array[1..12] of integer = (31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31);
begin
  result := false;
  if length(DateStr) <> 8 then Exit;
  if not TryStrToInt(Copy(DateStr, 1, 4), y) then Exit;
  if y <= 0 then Exit; // year zero not exists
  if not TryStrToInt(Copy(DateStr, 5, 2), m) then Exit;
  if not InRange(m, 1, 12) then Exit;
  if not TryStrToInt(Copy(DateStr, 7, 2), d) then Exit;
  if not InRange(d, 1, DAYS_OF_MONTH[m]) then Exit;
  if (not IsLeapYear(y)) and (m = 2) and (d = 29) then Exit;
  result := true;
end;

// CompareFileVersion('',            '12.11.38') = -99 // no version to compare
// CompareFileVersion('13',          '12.11.38') = 1   // bigger 1st octet only
// CompareFileVersion('13.11',       '12.11.38') = 1   // bigger 1st octet with 2nd octet only
// CompareFileVersion('13.11.38',    '12.11.38') = 1   // bigger 1st octet
// CompareFileVersion('12.13',       '12.11.38') = 1   // bigger 2nd octet only
// CompareFileVersion('12.12.38',    '12.11.38') = 1   // bigger 2rd octet
// CompareFileVersion('12.11.42',    '12.11.38') = 1   // bigger 3rd octet
// CompareFileVersion('12.11.38.01', '12.11.38') = 1   // bigger 4th octet
// CompareFileVersion('12.11.38',    '12.11.38') = 0   // same ver
// CompareFileVersion('12',          '12.11.38') = -1  // 1st octec only
// CompareFileVersion('12.11',       '12.11.38') = -1  // no 3rd octet
// CompareFileVersion('11',          '12.11.38') = -1  // smaller 1st octet only
// CompareFileVersion('11.11',       '12.11.38') = -1  // smaller 1st octet with 2nd octet only
// CompareFileVersion('11.11.38',    '12.11.38') = -1  // smaller 1st octet
// CompareFileVersion('12.2',        '12.11.38') = -1  // smaller 2nd octet only
// CompareFileVersion('12.2.38',     '12.11.38') = -1  // smaller 2nd octet
// CompareFileVersion('12.11.7',     '12.11.38') = -1  // smaller 3rd octet
// CompareFileVersion('12.11.7.01',  '12.11.38') = -1  // smaller 3rd octet with 4th octet
// CompareFileVersion('',            '12.11.38') = -1  // blank ver
function CompareFileVersion(Ver1, Ver2: string): integer; // CPCLIENTS-4096
var
  I, MaxOctet: integer;
  list1, list2: TStringList;
  tmpVer1, tmpVer2: integer;
begin
  result := COMPARE_VERSION_UNKNOWN;
  list1 := TStringList.Create;
  list2 := TstringList.Create;
  try
    list1.Delimiter := FILE_VERSION_DELIMITER;
    list2.Delimiter := FILE_VERSION_DELIMITER;
    list1.DelimitedText := Ver1;
    list2.DelimitedText := Ver2;
    MaxOctet := Max(list1.Count, list2.Count);
    for I := 1 to MaxOctet do
    begin
      tmpVer1 := 0;
      if I <= list1.Count then
        tmpVer1 := StrToIntDef(list1[I-1], 0);
      tmpVer2 := 0;
      if I <= list2.Count then
        tmpVer2 := StrToIntDef(list2[I-1], 0);
      result := CompareValue(tmpVer1, tmpVer2); // 0: ver1 = ver2, 1: ver1 > ver2, -1: ver1 < ver2
      if result <> 0 then
        break;
    end;
  finally
    list1.Free;
    list2.Free;
  end;
end;

//CPCLIENTS-9079/9080 Delete CTLSConfig section from LastUpdate.Ini
procedure DeleteCTLSCONFIG(aSection: string);
var
  IniFile: TIniFile;
  Filename: TFilename;
  Keys: TStringList;
  Key: string;
begin
  Filename := GetIniFolder + LASTUPDATEINI;
  Keys := TStringList.Create;
  try
    IniFile := TIniFile.Create(Filename);
    IniFile.ReadSection(aSection, Keys);
    try
      for Key in Keys do
      begin
        if Key.ToUpper.Contains(SCTLSCONFIG.ToUpper) then
          IniFile.DeleteKey(aSection, Key);
      end;
    finally
      IniFile.Free;
    end;
  finally
    Keys.Free;
  end;
end;

function ExtractStr(const str: AnsiString; startText, endText: AnsiString): AnsiString;
var I, tmpPos, startPos, endPos: integer;
begin
  result := '';
  tmpPos := Pos(startText, str);
  if tmpPos > 0 then
  begin
    startPos := tmpPos + Length(startText);
    I := startPos;
    while I <= Length(str) do
    begin

      if (Copy(str, I, Length(endText)) = endText) then
      begin
        result := Copy(str, startPos, I-startPos);
        Exit;
      end;
      Inc(I);
    end;
  end;
end;

class function TEnum.AsString<T>(aEnum: T; aPrefixLen: integer=DEFAULT_ENUM_PREFIX_LEN): string;
begin
  result := GetEnumName(TypeInfo(T), AsInteger(aEnum));
  // TEnum.AsInteger<TNick>(Blah)
  result := result.Substring(aPrefixLen, result.Length - aPrefixLen);
end;

class function TEnum.AsInteger<T>(aEnum: T): Integer;
begin
  case Sizeof(T) of
    1: Result := pByte(@aEnum)^;
    2: Result := pWord(@aEnum)^;
    4: Result := pCardinal(@aEnum)^;
  end;
end;

class function Obj.Using<T>(O: T; Func: TFunc<T>): boolean;
begin
  try
    //result := Func(O);
  finally
    FreeAndNil(O);
  end;
end;

class procedure Obj.using<T>(O: T; Proc: TProc<T>);
begin
  try
    Proc(O);
  finally
    FreeAndNil(O);
  end;
end;

initialization
  ExtendedLog('GeneralUtilities Initialization');
  {$IFDEF MSWINDOWS}
  MTXSleepEvent := TEvent.Create{CreateEvent}(Nil, False, False, 'Sleep'{Nil});
  {$ENDIF}
  ExtendedLog('XPIMX860Com initialization END');

finalization
  ExtendedLog('GeneralUtilities Finalization');
  {$IFDEF MSWINDOWS}
  // CloseHandle(MTXSleepEvent);
  MTXSleepEvent.Free;
  {$ENDIF}

end.
