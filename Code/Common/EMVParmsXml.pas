
{**********************************************}
{                                              }
{           Delphi XML Data Binding            }
{                                              }
{         Generated on: 6/29/2012 6:56:51 AM   }
{       Generated from: C:\temp\EMVParms.xml   }
{   Settings stored in: C:\temp\EMVParms.xdb   }
{                                              }
{**********************************************}
unit EMVParmsXml;

interface

uses
  FinalizationLog,
  xmldom, XMLDoc, XMLIntf, variants;

type

{ Forward Decls }

  IXMLEMVParmsType = interface;
  IXMLAppType = interface;

{ IXMLEMVParmsType }

  IXMLEMVParmsType = interface(IXMLNodeCollection)
    ['{D4E8E387-5A89-49AF-89FF-202D5C4195D3}']
    { Property Accessors }
    function Get_Version: WideString;
    function Get_App(Index: Integer): IXMLAppType;
    procedure Set_Version(Value: WideString);
    { Methods & Properties }
    function Add: IXMLAppType;
    function Insert(const Index: Integer): IXMLAppType;
    property Version: WideString read Get_Version write Set_Version;
    property App[Index: Integer]: IXMLAppType read Get_App; default;
  end;

{ IXMLAppType }

  IXMLAppType = interface(IXMLNode)
    ['{52DF9FF4-13B4-4E43-8262-422F9764B0FF}']
    { Property Accessors }
    function Get_RID: WideString;
    function Get_PIX: WideString;
    function Get_PartialNameFlag: Integer;
    function Get_RecommendedAIDName: WideString;
    function Get_Version: WideString;
    function Get_SchmRef: Smallint;
    function Get_IssuerRef: Smallint;
    function Get_TRMDataPresent: Smallint;
    function Get_TargetRSPercent: Smallint;
    function Get_FloorLimit: Integer;
    function Get_RSThreshold: Integer;
    function Get_MaxTargetRSPercent: Smallint;
    function Get_MerchForceOnlineFlg: Smallint;
    function Get_BlackListCardSupportFlg: Smallint;
    function Get_FallBackAllowedFlg: Smallint;
    function Get_TACDefault: WideString;
    function Get_TACDenial: WideString;
    function Get_TACOnline: WideString;
    function Get_DefaultTDOL: WideString;
    function Get_DefaultDDOL: WideString;
    function Get_NextRecord: Smallint;
    function Get_AutoSelectAppl: Smallint;
    function Get_EMVCounter: Cardinal;
    function Get_CountryCode: WideString;
    function Get_CurrencyCode: WideString;
    function Get_TermCapacity: WideString;
    function Get_AdditionalCapacity: WideString;
    function Get_TermType: WideString;
    function Get_MerchCatCode: WideString;
    function Get_TerminalCatCode: WideString;
    function Get_TermID: WideString;
    function Get_MerchID: WideString;
    function Get_AcquireID: WideString;
    function Get_CAPKIndex: WideString;
    function Get_PINBypassFlg: WideString;
    function Get_PINTimout: WideString;
    function Get_PINFormat: WideString;
    function Get_PINScriptNumber: WideString;
    function Get_PINMacroNumber: WideString;
    function Get_PINDevriKeyFlg: WideString;
    function Get_PINDevriMacroNum: WideString;
    function Get_CardStatDisplayFlg: WideString;
    function Get_TermCurExp: Smallint;
    function Get_IssAcqflag: Smallint;
    function Get_NoDisplaySupportFlag: Smallint;
    function Get_ModifyCandListFlag: Smallint;
    procedure Set_RID(Value: WideString);
    procedure Set_PIX(Value: WideString);
    procedure Set_PartialNameFlag(Value: Integer);
    procedure Set_RecommendedAIDName(Value: WideString);
    procedure Set_Version(Value: WideString);
    procedure Set_SchmRef(Value: Smallint);
    procedure Set_IssuerRef(Value: Smallint);
    procedure Set_TRMDataPresent(Value: Smallint);
    procedure Set_TargetRSPercent(Value: Smallint);
    procedure Set_FloorLimit(Value: Integer);
    procedure Set_RSThreshold(Value: Integer);
    procedure Set_MaxTargetRSPercent(Value: Smallint);
    procedure Set_MerchForceOnlineFlg(Value: Smallint);
    procedure Set_BlackListCardSupportFlg(Value: Smallint);
    procedure Set_FallBackAllowedFlg(Value: Smallint);
    procedure Set_TACDefault(Value: WideString);
    procedure Set_TACDenial(Value: WideString);
    procedure Set_TACOnline(Value: WideString);
    procedure Set_DefaultTDOL(Value: WideString);
    procedure Set_DefaultDDOL(Value: WideString);
    procedure Set_NextRecord(Value: Smallint);
    procedure Set_AutoSelectAppl(Value: Smallint);
    procedure Set_EMVCounter(Value: Cardinal);
    procedure Set_CountryCode(Value: WideString);
    procedure Set_CurrencyCode(Value: WideString);
    procedure Set_TermCapacity(Value: WideString);
    procedure Set_AdditionalCapacity(Value: WideString);
    procedure Set_TermType(Value: WideString);
    procedure Set_MerchCatCode(Value: WideString);
    procedure Set_TerminalCatCode(Value: WideString);
    procedure Set_TermID(Value: WideString);
    procedure Set_MerchID(Value: WideString);
    procedure Set_AcquireID(Value: WideString);
    procedure Set_CAPKIndex(Value: WideString);
    procedure Set_PINBypassFlg(Value: WideString);
    procedure Set_PINTimout(Value: WideString);
    procedure Set_PINFormat(Value: WideString);
    procedure Set_PINScriptNumber(Value: WideString);
    procedure Set_PINMacroNumber(Value: WideString);
    procedure Set_PINDevriKeyFlg(Value: WideString);
    procedure Set_PINDevriMacroNum(Value: WideString);
    procedure Set_CardStatDisplayFlg(Value: WideString);
    procedure Set_TermCurExp(Value: Smallint);
    procedure Set_IssAcqflag(Value: Smallint);
    procedure Set_NoDisplaySupportFlag(Value: Smallint);
    procedure Set_ModifyCandListFlag(Value: Smallint);
    { Methods & Properties }
    property RID: WideString read Get_RID write Set_RID;
    property PIX: WideString read Get_PIX write Set_PIX;
    property PartialNameFlag: Integer read Get_PartialNameFlag write Set_PartialNameFlag;
    property RecommendedAIDName: WideString read Get_RecommendedAIDName write Set_RecommendedAIDName;
    property Version: WideString read Get_Version write Set_Version;
    property SchmRef: Smallint read Get_SchmRef write Set_SchmRef;
    property IssuerRef: Smallint read Get_IssuerRef write Set_IssuerRef;
    property TRMDataPresent: Smallint read Get_TRMDataPresent write Set_TRMDataPresent;
    property TargetRSPercent: Smallint read Get_TargetRSPercent write Set_TargetRSPercent;
    property FloorLimit: Integer read Get_FloorLimit write Set_FloorLimit;
    property RSThreshold: Integer read Get_RSThreshold write Set_RSThreshold;
    property MaxTargetRSPercent: Smallint read Get_MaxTargetRSPercent write Set_MaxTargetRSPercent;
    property MerchForceOnlineFlg: Smallint read Get_MerchForceOnlineFlg write Set_MerchForceOnlineFlg;
    property BlackListCardSupportFlg: Smallint read Get_BlackListCardSupportFlg write Set_BlackListCardSupportFlg;
    property FallBackAllowedFlg: Smallint read Get_FallBackAllowedFlg write Set_FallBackAllowedFlg;
    property TACDefault: WideString read Get_TACDefault write Set_TACDefault;
    property TACDenial: WideString read Get_TACDenial write Set_TACDenial;
    property TACOnline: WideString read Get_TACOnline write Set_TACOnline;
    property DefaultTDOL: WideString read Get_DefaultTDOL write Set_DefaultTDOL;
    property DefaultDDOL: WideString read Get_DefaultDDOL write Set_DefaultDDOL;
    property NextRecord: Smallint read Get_NextRecord write Set_NextRecord;
    property AutoSelectAppl: Smallint read Get_AutoSelectAppl write Set_AutoSelectAppl;
    property EMVCounter: Cardinal read Get_EMVCounter write Set_EMVCounter;
    property CountryCode: WideString read Get_CountryCode write Set_CountryCode;
    property CurrencyCode: WideString read Get_CurrencyCode write Set_CurrencyCode;
    property TermCapacity: WideString read Get_TermCapacity write Set_TermCapacity;
    property AdditionalCapacity: WideString read Get_AdditionalCapacity write Set_AdditionalCapacity;
    property TermType: WideString read Get_TermType write Set_TermType;
    property MerchCatCode: WideString read Get_MerchCatCode write Set_MerchCatCode;
    property TerminalCatCode: WideString read Get_TerminalCatCode write Set_TerminalCatCode;
    property TermID: WideString read Get_TermID write Set_TermID;
    property MerchID: WideString read Get_MerchID write Set_MerchID;
    property AcquireID: WideString read Get_AcquireID write Set_AcquireID;
    property CAPKIndex: WideString read Get_CAPKIndex write Set_CAPKIndex;
    property PINBypassFlg: WideString read Get_PINBypassFlg write Set_PINBypassFlg;
    property PINTimout: WideString read Get_PINTimout write Set_PINTimout;
    property PINFormat: WideString read Get_PINFormat write Set_PINFormat;
    property PINScriptNumber: WideString read Get_PINScriptNumber write Set_PINScriptNumber;
    property PINMacroNumber: WideString read Get_PINMacroNumber write Set_PINMacroNumber;
    property PINDevriKeyFlg: WideString read Get_PINDevriKeyFlg write Set_PINDevriKeyFlg;
    property PINDevriMacroNum: WideString read Get_PINDevriMacroNum write Set_PINDevriMacroNum;
    property CardStatDisplayFlg: WideString read Get_CardStatDisplayFlg write Set_CardStatDisplayFlg;
    property TermCurExp: Smallint read Get_TermCurExp write Set_TermCurExp;
    property IssAcqflag: Smallint read Get_IssAcqflag write Set_IssAcqflag;
    property NoDisplaySupportFlag: Smallint read Get_NoDisplaySupportFlag write Set_NoDisplaySupportFlag;
    property ModifyCandListFlag: Smallint read Get_ModifyCandListFlag write Set_ModifyCandListFlag;
  end;

{ Forward Decls }

  TXMLEMVParmsType = class;
  TXMLAppType = class;

{ TXMLEMVParmsType }

  TXMLEMVParmsType = class(TXMLNodeCollection, IXMLEMVParmsType)
  protected
    { IXMLEMVParmsType }
    function Get_Version: WideString;
    function Get_App(Index: Integer): IXMLAppType;
    procedure Set_Version(Value: WideString);
    function Add: IXMLAppType;
    function Insert(const Index: Integer): IXMLAppType;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLAppType }

  TXMLAppType = class(TXMLNode, IXMLAppType)
  protected
    { IXMLAppType }
    function Get_RID: WideString;
    function Get_PIX: WideString;
    function Get_PartialNameFlag: Integer;
    function Get_RecommendedAIDName: WideString;
    function Get_Version: WideString;
    function Get_SchmRef: Smallint;
    function Get_IssuerRef: Smallint;
    function Get_TRMDataPresent: Smallint;
    function Get_TargetRSPercent: Smallint;
    function Get_FloorLimit: Integer;
    function Get_RSThreshold: Integer;
    function Get_MaxTargetRSPercent: Smallint;
    function Get_MerchForceOnlineFlg: Smallint;
    function Get_BlackListCardSupportFlg: Smallint;
    function Get_FallBackAllowedFlg: Smallint;
    function Get_TACDefault: WideString;
    function Get_TACDenial: WideString;
    function Get_TACOnline: WideString;
    function Get_DefaultTDOL: WideString;
    function Get_DefaultDDOL: WideString;
    function Get_NextRecord: Smallint;
    function Get_AutoSelectAppl: Smallint;
    function Get_EMVCounter: Cardinal;
    function Get_CountryCode: WideString;
    function Get_CurrencyCode: WideString;
    function Get_TermCapacity: WideString;
    function Get_AdditionalCapacity: WideString;
    function Get_TermType: WideString;
    function Get_MerchCatCode: WideString;
    function Get_TerminalCatCode: WideString;
    function Get_TermID: WideString;
    function Get_MerchID: WideString;
    function Get_AcquireID: WideString;
    function Get_CAPKIndex: WideString;
    function Get_PINBypassFlg: WideString;
    function Get_PINTimout: WideString;
    function Get_PINFormat: WideString;
    function Get_PINScriptNumber: WideString;
    function Get_PINMacroNumber: WideString;
    function Get_PINDevriKeyFlg: WideString;
    function Get_PINDevriMacroNum: WideString;
    function Get_CardStatDisplayFlg: WideString;
    function Get_TermCurExp: Smallint;
    function Get_IssAcqflag: Smallint;
    function Get_NoDisplaySupportFlag: Smallint;
    function Get_ModifyCandListFlag: Smallint;
    procedure Set_RID(Value: WideString);
    procedure Set_PIX(Value: WideString);
    procedure Set_PartialNameFlag(Value: Integer);
    procedure Set_RecommendedAIDName(Value: WideString);
    procedure Set_Version(Value: WideString);
    procedure Set_SchmRef(Value: Smallint);
    procedure Set_IssuerRef(Value: Smallint);
    procedure Set_TRMDataPresent(Value: Smallint);
    procedure Set_TargetRSPercent(Value: Smallint);
    procedure Set_FloorLimit(Value: Integer);
    procedure Set_RSThreshold(Value: Integer);
    procedure Set_MaxTargetRSPercent(Value: Smallint);
    procedure Set_MerchForceOnlineFlg(Value: Smallint);
    procedure Set_BlackListCardSupportFlg(Value: Smallint);
    procedure Set_FallBackAllowedFlg(Value: Smallint);
    procedure Set_TACDefault(Value: WideString);
    procedure Set_TACDenial(Value: WideString);
    procedure Set_TACOnline(Value: WideString);
    procedure Set_DefaultTDOL(Value: WideString);
    procedure Set_DefaultDDOL(Value: WideString);
    procedure Set_NextRecord(Value: Smallint);
    procedure Set_AutoSelectAppl(Value: Smallint);
    procedure Set_EMVCounter(Value: Cardinal);
    procedure Set_CountryCode(Value: WideString);
    procedure Set_CurrencyCode(Value: WideString);
    procedure Set_TermCapacity(Value: WideString);
    procedure Set_AdditionalCapacity(Value: WideString);
    procedure Set_TermType(Value: WideString);
    procedure Set_MerchCatCode(Value: WideString);
    procedure Set_TerminalCatCode(Value: WideString);
    procedure Set_TermID(Value: WideString);
    procedure Set_MerchID(Value: WideString);
    procedure Set_AcquireID(Value: WideString);
    procedure Set_CAPKIndex(Value: WideString);
    procedure Set_PINBypassFlg(Value: WideString);
    procedure Set_PINTimout(Value: WideString);
    procedure Set_PINFormat(Value: WideString);
    procedure Set_PINScriptNumber(Value: WideString);
    procedure Set_PINMacroNumber(Value: WideString);
    procedure Set_PINDevriKeyFlg(Value: WideString);
    procedure Set_PINDevriMacroNum(Value: WideString);
    procedure Set_CardStatDisplayFlg(Value: WideString);
    procedure Set_TermCurExp(Value: Smallint);
    procedure Set_IssAcqflag(Value: Smallint);
    procedure Set_NoDisplaySupportFlag(Value: Smallint);
    procedure Set_ModifyCandListFlag(Value: Smallint);
  end;

{ Global Functions }

function GetEMVParms(Doc: IXMLDocument): IXMLEMVParmsType;
function LoadEMVParms(const FileName: WideString): IXMLEMVParmsType;
function NewEMVParms: IXMLEMVParmsType;

implementation

{ Global Functions }

function GetEMVParms(Doc: IXMLDocument): IXMLEMVParmsType;
begin
  Result := Doc.GetDocBinding('EMVParms', TXMLEMVParmsType) as IXMLEMVParmsType;
end;
function LoadEMVParms(const FileName: WideString): IXMLEMVParmsType;
begin
  Result := LoadXMLDocument(FileName).GetDocBinding('EMVParms', TXMLEMVParmsType) as IXMLEMVParmsType;
end;

function NewEMVParms: IXMLEMVParmsType;
begin
  Result := NewXMLDocument.GetDocBinding('EMVParms', TXMLEMVParmsType) as IXMLEMVParmsType;
end;

{ TXMLEMVParmsType }

procedure TXMLEMVParmsType.AfterConstruction;
begin
  RegisterChildNode('App', TXMLAppType);
  ItemTag := 'App';
  ItemInterface := IXMLAppType;
  inherited;
end;

function TXMLEMVParmsType.Get_Version: WideString;
begin
  Result := AttributeNodes['Version'].Text;
end;

procedure TXMLEMVParmsType.Set_Version(Value: WideString);
begin
  SetAttribute('Version', Value);
end;

function TXMLEMVParmsType.Get_App(Index: Integer): IXMLAppType;
begin
  Result := List[Index] as IXMLAppType;
end;

function TXMLEMVParmsType.Add: IXMLAppType;
begin
  Result := AddItem(-1) as IXMLAppType;
end;

function TXMLEMVParmsType.Insert(const Index: Integer): IXMLAppType;
begin
  Result := AddItem(Index) as IXMLAppType;
end;


{ TXMLAppType }

function TXMLAppType.Get_RID: WideString;
begin
  Result := AttributeNodes['RID'].Text;
end;

procedure TXMLAppType.Set_RID(Value: WideString);
begin
  SetAttribute('RID', Value);
end;

function TXMLAppType.Get_PIX: WideString;
begin
  Result := AttributeNodes['PIX'].Text;
end;

procedure TXMLAppType.Set_PIX(Value: WideString);
begin
  SetAttribute('PIX', Value);
end;

function TXMLAppType.Get_PartialNameFlag: Integer;
begin
  if VarIsNull(AttributeNodes['PartialNameFlag'].NodeValue)
    then result := 0
    else Result := AttributeNodes['PartialNameFlag'].NodeValue;
end;

procedure TXMLAppType.Set_PartialNameFlag(Value: Integer);
begin
  SetAttribute('PartialNameFlag', Value);
end;

function TXMLAppType.Get_RecommendedAIDName: WideString;
begin
  Result := AttributeNodes['RecommendedAIDName'].Text;
end;

procedure TXMLAppType.Set_RecommendedAIDName(Value: WideString);
begin
  SetAttribute('RecommendedAIDName', Value);
end;

function TXMLAppType.Get_Version: WideString;
begin
  Result := AttributeNodes['Version'].Text;
end;

procedure TXMLAppType.Set_Version(Value: WideString);
begin
  SetAttribute('Version', Value);
end;

function TXMLAppType.Get_SchmRef: Smallint;
begin
  if VarIsNull(ChildNodes['SchmRef'].NodeValue)
    then Result := 0
    else Result := ChildNodes['SchmRef'].NodeValue;
end;

procedure TXMLAppType.Set_SchmRef(Value: Smallint);
begin
  ChildNodes['SchmRef'].NodeValue := Value;
end;

function TXMLAppType.Get_IssuerRef: Smallint;
begin
  if VarIsNull(ChildNodes['IssuerRef'].NodeValue)
    then Result := 0
    else Result := ChildNodes['IssuerRef'].NodeValue;
end;

procedure TXMLAppType.Set_IssuerRef(Value: Smallint);
begin
  ChildNodes['IssuerRef'].NodeValue := Value;
end;

function TXMLAppType.Get_TRMDataPresent: Smallint;
begin
  if VarIsNull(ChildNodes['TRMDataPresent'].NodeValue)
    then Result := 0
    else Result := ChildNodes['TRMDataPresent'].NodeValue;
end;

procedure TXMLAppType.Set_TRMDataPresent(Value: Smallint);
begin
  ChildNodes['TRMDataPresent'].NodeValue := Value;
end;

function TXMLAppType.Get_TargetRSPercent: Smallint;
begin
  if VarIsNull(ChildNodes['TargetRSPercent'].NodeValue)
    then Result := 0
    else Result := ChildNodes['TargetRSPercent'].NodeValue;
end;

procedure TXMLAppType.Set_TargetRSPercent(Value: Smallint);
begin
  ChildNodes['TargetRSPercent'].NodeValue := Value;
end;

function TXMLAppType.Get_FloorLimit: Integer;
begin
  if VarIsNull(ChildNodes['FloorLimit'].NodeValue)
    then result := 0
    else Result := ChildNodes['FloorLimit'].NodeValue;
end;

procedure TXMLAppType.Set_FloorLimit(Value: Integer);
begin
  ChildNodes['FloorLimit'].NodeValue := Value;
end;

function TXMLAppType.Get_RSThreshold: Integer;
begin
  if VarIsNull(ChildNodes['RSThreshold'].NodeValue)
    then result := 0
    else Result := ChildNodes['RSThreshold'].NodeValue;
end;

procedure TXMLAppType.Set_RSThreshold(Value: Integer);
begin
  ChildNodes['RSThreshold'].NodeValue := Value;
end;

function TXMLAppType.Get_MaxTargetRSPercent: Smallint;
begin
  if VarIsNull(ChildNodes['MaxTargetRSPercent'].NodeValue)
    then result := 0
    else Result := ChildNodes['MaxTargetRSPercent'].NodeValue;
end;

procedure TXMLAppType.Set_MaxTargetRSPercent(Value: Smallint);
begin
  ChildNodes['MaxTargetRSPercent'].NodeValue := Value;
end;

function TXMLAppType.Get_MerchForceOnlineFlg: Smallint;
begin
  if VarIsNull(ChildNodes['MerchForceOnlineFlg'].NodeValue)
    then result := 0
    else Result := ChildNodes['MerchForceOnlineFlg'].NodeValue;
end;

procedure TXMLAppType.Set_MerchForceOnlineFlg(Value: Smallint);
begin
  ChildNodes['MerchForceOnlineFlg'].NodeValue := Value;
end;

function TXMLAppType.Get_BlackListCardSupportFlg: Smallint;
begin
  if VarIsNull(ChildNodes['BlackListCardSupportFlg'].NodeValue)
    then result := 0
    else Result := ChildNodes['BlackListCardSupportFlg'].NodeValue;
end;

procedure TXMLAppType.Set_BlackListCardSupportFlg(Value: Smallint);
begin
  ChildNodes['BlackListCardSupportFlg'].NodeValue := Value;
end;

function TXMLAppType.Get_FallBackAllowedFlg: Smallint;
begin
  if VarIsNull(ChildNodes['FallBackAllowedFlg'].NodeValue)
    then result := 0
    else Result := ChildNodes['FallBackAllowedFlg'].NodeValue;
end;

procedure TXMLAppType.Set_FallBackAllowedFlg(Value: Smallint);
begin
  ChildNodes['FallBackAllowedFlg'].NodeValue := Value;
end;

function TXMLAppType.Get_TACDefault: WideString;
begin
  Result := ChildNodes['TACDefault'].Text;
end;

procedure TXMLAppType.Set_TACDefault(Value: WideString);
begin
  ChildNodes['TACDefault'].NodeValue := Value;
end;

function TXMLAppType.Get_TACDenial: WideString;
begin
  Result := ChildNodes['TACDenial'].Text;
end;

procedure TXMLAppType.Set_TACDenial(Value: WideString);
begin
  ChildNodes['TACDenial'].NodeValue := Value;
end;

function TXMLAppType.Get_TACOnline: WideString;
begin
  Result := ChildNodes['TACOnline'].Text;
end;

procedure TXMLAppType.Set_TACOnline(Value: WideString);
begin
  ChildNodes['TACOnline'].NodeValue := Value;
end;

function TXMLAppType.Get_DefaultTDOL: WideString;
begin
  Result := ChildNodes['DefaultTDOL'].Text;
end;

procedure TXMLAppType.Set_DefaultTDOL(Value: WideString);
begin
  ChildNodes['DefaultTDOL'].NodeValue := Value;
end;

function TXMLAppType.Get_DefaultDDOL: WideString;
begin
  Result := ChildNodes['DefaultDDOL'].Text;
end;

procedure TXMLAppType.Set_DefaultDDOL(Value: WideString);
begin
  ChildNodes['DefaultDDOL'].NodeValue := Value;
end;

function TXMLAppType.Get_NextRecord: Smallint;
begin
  if VarIsNull(ChildNodes['NextRecord'].NodeValue)
    then result := 0
    else Result := ChildNodes['NextRecord'].NodeValue;
end;

procedure TXMLAppType.Set_NextRecord(Value: Smallint);
begin
  ChildNodes['NextRecord'].NodeValue := Value;
end;

function TXMLAppType.Get_AutoSelectAppl: Smallint;
begin
  if VarIsNull(ChildNodes['AutoSelectAppl'].NodeValue)
    then result := 0
    else Result := ChildNodes['AutoSelectAppl'].NodeValue;
end;

procedure TXMLAppType.Set_AutoSelectAppl(Value: Smallint);
begin
  ChildNodes['AutoSelectAppl'].NodeValue := Value;
end;

function TXMLAppType.Get_EMVCounter: Cardinal;
begin
  if VarIsNull(ChildNodes['EMVCounter'].NodeValue)
    then result := 0
    else Result := ChildNodes['EMVCounter'].NodeValue;
end;

procedure TXMLAppType.Set_EMVCounter(Value: Cardinal);
begin
  ChildNodes['EMVCounter'].NodeValue := Value;
end;

function TXMLAppType.Get_CountryCode: WideString;
begin
  Result := ChildNodes['CountryCode'].Text;
end;

procedure TXMLAppType.Set_CountryCode(Value: WideString);
begin
  ChildNodes['CountryCode'].NodeValue := Value;
end;

function TXMLAppType.Get_CurrencyCode: WideString;
begin
  Result := ChildNodes['CurrencyCode'].Text;
end;

procedure TXMLAppType.Set_CurrencyCode(Value: WideString);
begin
  ChildNodes['CurrencyCode'].NodeValue := Value;
end;

function TXMLAppType.Get_TermCapacity: WideString;
begin
  Result := ChildNodes['TermCapacity'].Text;
end;

procedure TXMLAppType.Set_TermCapacity(Value: WideString);
begin
  ChildNodes['TermCapacity'].NodeValue := Value;
end;

function TXMLAppType.Get_AdditionalCapacity: WideString;
begin
  Result := ChildNodes['AdditionalCapacity'].Text;
end;

procedure TXMLAppType.Set_AdditionalCapacity(Value: WideString);
begin
  ChildNodes['AdditionalCapacity'].NodeValue := Value;
end;

function TXMLAppType.Get_TermType: WideString;
begin
  Result := ChildNodes['TermType'].Text;
end;

procedure TXMLAppType.Set_TermType(Value: WideString);
begin
  ChildNodes['TermType'].NodeValue := Value;
end;

function TXMLAppType.Get_MerchCatCode: WideString;
begin
  Result := ChildNodes['MerchCatCode'].Text;
end;

procedure TXMLAppType.Set_MerchCatCode(Value: WideString);
begin
  ChildNodes['MerchCatCode'].NodeValue := Value;
end;

function TXMLAppType.Get_TerminalCatCode: WideString;
begin
  Result := ChildNodes['TerminalCatCode'].Text;
end;

procedure TXMLAppType.Set_TerminalCatCode(Value: WideString);
begin
  ChildNodes['TerminalCatCode'].NodeValue := Value;
end;

function TXMLAppType.Get_TermID: WideString;
begin
  Result := ChildNodes['TermID'].Text;
end;

procedure TXMLAppType.Set_TermID(Value: WideString);
begin
  ChildNodes['TermID'].NodeValue := Value;
end;

function TXMLAppType.Get_MerchID: WideString;
begin
  Result := ChildNodes['MerchID'].Text;
end;

procedure TXMLAppType.Set_MerchID(Value: WideString);
begin
  ChildNodes['MerchID'].NodeValue := Value;
end;

function TXMLAppType.Get_AcquireID: WideString;
begin
  Result := ChildNodes['AcquireID'].Text;
end;

procedure TXMLAppType.Set_AcquireID(Value: WideString);
begin
  ChildNodes['AcquireID'].NodeValue := Value;
end;

function TXMLAppType.Get_CAPKIndex: WideString;
begin
  Result := ChildNodes['CAPKIndex'].Text;
end;

procedure TXMLAppType.Set_CAPKIndex(Value: WideString);
begin
  ChildNodes['CAPKIndex'].NodeValue := Value;
end;

function TXMLAppType.Get_PINBypassFlg: WideString;
begin
  Result := ChildNodes['PINBypassFlg'].Text;
end;

procedure TXMLAppType.Set_PINBypassFlg(Value: WideString);
begin
  ChildNodes['PINBypassFlg'].NodeValue := Value;
end;

function TXMLAppType.Get_PINTimout: WideString;
begin
  Result := ChildNodes['PINTimout'].Text;
end;

procedure TXMLAppType.Set_PINTimout(Value: WideString);
begin
  ChildNodes['PINTimout'].NodeValue := Value;
end;

function TXMLAppType.Get_PINFormat: WideString;
begin
  Result := ChildNodes['PINFormat'].Text;
end;

procedure TXMLAppType.Set_PINFormat(Value: WideString);
begin
  ChildNodes['PINFormat'].NodeValue := Value;
end;

function TXMLAppType.Get_PINScriptNumber: WideString;
begin
  Result := ChildNodes['PINScriptNumber'].Text;
end;

procedure TXMLAppType.Set_PINScriptNumber(Value: WideString);
begin
  ChildNodes['PINScriptNumber'].NodeValue := Value;
end;

function TXMLAppType.Get_PINMacroNumber: WideString;
begin
  Result := ChildNodes['PINMacroNumber'].Text;
end;

procedure TXMLAppType.Set_PINMacroNumber(Value: WideString);
begin
  ChildNodes['PINMacroNumber'].NodeValue := Value;
end;

function TXMLAppType.Get_PINDevriKeyFlg: WideString;
begin
  Result := ChildNodes['PINDevriKeyFlg'].Text;
end;

procedure TXMLAppType.Set_PINDevriKeyFlg(Value: WideString);
begin
  ChildNodes['PINDevriKeyFlg'].NodeValue := Value;
end;

function TXMLAppType.Get_PINDevriMacroNum: WideString;
begin
  Result := ChildNodes['PINDevriMacroNum'].Text;
end;

procedure TXMLAppType.Set_PINDevriMacroNum(Value: WideString);
begin
  ChildNodes['PINDevriMacroNum'].NodeValue := Value;
end;

function TXMLAppType.Get_CardStatDisplayFlg: WideString;
begin
  Result := ChildNodes['CardStatDisplayFlg'].Text;
end;

procedure TXMLAppType.Set_CardStatDisplayFlg(Value: WideString);
begin
  ChildNodes['CardStatDisplayFlg'].NodeValue := Value;
end;

function TXMLAppType.Get_TermCurExp: Smallint;
begin
  if VarIsNull(ChildNodes['TermCurExp'].NodeValue)
    then result := 0
    else Result := ChildNodes['TermCurExp'].NodeValue;
end;

procedure TXMLAppType.Set_TermCurExp(Value: Smallint);
begin
  ChildNodes['TermCurExp'].NodeValue := Value;
end;

function TXMLAppType.Get_IssAcqflag: Smallint;
begin
  if VarIsNull(ChildNodes['IssAcqflag'].NodeValue)
    then result := 0
    else Result := ChildNodes['IssAcqflag'].NodeValue;
end;

procedure TXMLAppType.Set_IssAcqflag(Value: Smallint);
begin
  ChildNodes['IssAcqflag'].NodeValue := Value;
end;

function TXMLAppType.Get_NoDisplaySupportFlag: Smallint;
begin
  if VarIsNull(ChildNodes['NoDisplaySupportFlag'].NodeValue)
    then result := 0
    else Result := ChildNodes['NoDisplaySupportFlag'].NodeValue;
end;

procedure TXMLAppType.Set_NoDisplaySupportFlag(Value: Smallint);
begin
  ChildNodes['NoDisplaySupportFlag'].NodeValue := Value;
end;

function TXMLAppType.Get_ModifyCandListFlag: Smallint;
begin
  if VarIsNull(ChildNodes['ModifyCandListFlag'].NodeValue)
    then result := 0
    else Result := ChildNodes['ModifyCandListFlag'].NodeValue;
end;

procedure TXMLAppType.Set_ModifyCandListFlag(Value: Smallint);
begin
  ChildNodes['ModifyCandListFlag'].NodeValue := Value;
end;

initialization
  ExtendedLog('EMVParmsXml initialization');
finalization
  ExtendedLog('EMVParmsXml Finalization');

end.
