// (c) MTXEPS, Inc. 1988-2008
unit FormatVelocityMsgs;

interface

uses
  FinalizationLog,
  SysUtils,
  {$IFDEF MSWINDOWS}
  Windows,
  {$ENDIF MSWINDOWS}
  MTX_Constants,
  Classes;

type
  TFormatVelocityMsgs = class
    FHostSuffix: string;
    FCheckAuthType: string;

  private
    function MakeBYLVelocityMsgs(aData: string): TStringList;
    function MakeACIVelocityMsgs(aData: string): TStringList;

  public
    constructor Create(aHostSuffix, aCheckAuthType: string);
    function MakeVelocityMsgs(aData: string): TStringList;

  end;

implementation

function TFormatVelocityMsgs.MakeBYLVelocityMsgs(aData: string): TStringList;
begin
  result := TStringList.create;
  result.add('Customer limit ID :' + copy(aData, 1, 6));    { Byte 1 - 6: Limit ID of customer}
  result.add('Daily check count :' + copy(aData, 7, 2));    { Byte 7 - 8: Daily Check Count}
  result.add('Daily check amount:' + copy(aData, 9, 8));    { Byte 9 - 16: Daily Check Amount}
  result.add('Daily cshbck Count:' + copy(aData,17, 2));    { Byte 17 - 18: Daily Cashback Count}
  result.add('Daily cshbck amnt :' + copy(aData,19, 8));    { Byte 19 - 26: Daily Cashback Amount}
  result.add('Period chk count  :' + copy(aData,27, 2));    { Byte 27 - 28: Period Check Count}
  result.add('Period chk amount :' + copy(aData,29, 8));    { Byte 29 - 36: Period Check Amount}
  result.add('Per cshbck count  :' + copy(aData,37, 2));    { Byte 37 - 38: Period Cashback Count}
  result.add('Per cshbck amnt   :' + copy(aData,39, 8));    { Byte 39 - 46: Period Cashback Amount}
  result.add('LifeTime chk count:' + copy(aData,47, 4));    { Byte 47 - 50: Life-time Check Count}
  result.add('LifeTime chk amnt :' + copy(aData,51, 12));   { Byte 51 - 62: Life-time Check Amount}
  result.add('Current badchk cnt:' + copy(aData,63, 2));    { Byte 63 - 64: Current Bad Check Count}
  result.add('Current badchk amt:' + copy(aData,65, 8));    { Byte 65 - 72: Current Bad Check Amount}
end;

function TFormatVelocityMsgs.MakeACIVelocityMsgs(aData: string): TStringList;
var i, len: integer;
begin
  result := nil;
  i := 1;
  try
    result := TStringList.create;
    while (i <= length(aData)) do                          // parse through msg data }
    begin                                                  // format is xxxxYYY where xxxx = length, YYY=1A1 or 1D3
      len := strToIntDef(copy(aData,i,4),0);
      if (len > 3) then
        result.add(copy(aData,i + 7,len - 3))              // 40 is the length of the velocity line string
      else                                                 // if no length then it is a display message
      begin
        result.add(copy(aData, i, 40));                    // if a display msg, just take 40 chars at a time
        len := 36;                                         // set len to 36 so inc(i, len + 4) = 40
      end;
      inc(i,len + 4);                                      // add len bytes which are not included in len
    end;
  except
    on e: exception do
      ;
  end;
end;

function TFormatVelocityMsgs.MakeVelocityMsgs(aData: string): TStringList;
begin
  result := nil;
  try
    result := TStringList.Create;
    if (FHostSuffix = 'BYL') and (FCheckAuthType = CHECK_AUTH_TYPE_BUYCHECK) then
      result := MakeBYLVelocityMsgs(aData)
    else
    if (FHostSuffix = 'ACI') then
      result := MakeACIVelocityMsgs(aData)
    else
      result.Clear;
  except
    on e: exception do
      ;
  end;
end;

constructor TFormatVelocityMsgs.Create(aHostSuffix, aCheckAuthType: string);
begin
  FHostSuffix := aHostSuffix;
  FCheckAuthType := aCheckAuthType;
end;

initialization
  ExtendedLog('FormatVelocityMsgs initialization');
finalization
  ExtendedLog('FormatVelocityMsgs finalization');

end.
