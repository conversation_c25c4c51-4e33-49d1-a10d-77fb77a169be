// (c) MTXEPS, Inc. 1988-2008
{*
v823.0 (patch) 03-12-08 JMR-B Load global XMLResponseCodes only once instead of with every trx.
v814.0 05-18-04 TSL-C Add DefaultDir from OpenEPS to XML file load if needed
v814.0 05-05-04 TRI-A  Use XML
v809 07-25-02 TSL-B Change 'NO' to 'ND'
v808 07-01-02 JMR   Cleanup
v808 03-20-03 TSL-? Change SendMessage(AnyToLst to SM( for one EXE
 ** 07-14-00 TSL-A  Bup MaxIrspCodes to 50
 ** 02-19-98 JGS Get rid of DOS in uses
*************************************************************************** }
unit Map_IRsp;

{ ************************************************************************* }
interface
{ ************************************************************************* }

Uses
     FinalizationLog,
     MTX_Lib,
     SysUtils,
     UResponseCodes,
     UXMLCommon,
     MTX_Constants,
     MdMsg;     //JMR

procedure IMapRsp(var InRec: MDMsgRec);
procedure IMapRspHostMsg(var InRec: MdMsgRec; hostMsg: string);
function IMapRspCashLines5x40(InRec : MDMsgRec; var LineCollection: TLineCollection5; const hostMsg: string = ''): Boolean;
function MapRsp(var aMdMsgRec: MdMsgRec; aLines: TLineCollection5; aCardLanguageCode: string): string; // moved from XPIBaseTerm to share with Terminal class
procedure MapMdMsgRec(var aMdMsg: MdMsgRec; aCardLanguageCode: string);
{$IFDEF MTXEPSDLL}
procedure GetExtCustomerDisplay(var aExtPrimeCustDisp, aExtAltCustDisp: String40); // DOEP-71599
{$ENDIF}

{ ************************************************************************* }
implementation
{ ************************************************************************* }
{$IFDEF MTXEPSDLL}
var
  ExtPrimeCustDisp, ExtAltCustDisp: String40; // DOEP-71599
{$ENDIF}

procedure IMapRspHostMsg(var InRec: MdMsgRec; hostMsg: string);
var i: integer;
begin
//  SM('===DEBUG-JMR: Map_IRsp.IMapRspHostMsg = >' + hostMsg + '<');
  IMapRsp(InRec);
  for i := 1 to length(hostMsg) do
    if (ord(hostMsg[i]) < 32) then
      hostMsg[i] := ' ';

  if (trim(InRec.CashPadDisp) = USEHOSTMSG) or
     ((InRec.ReqCodeN in EBT_Set) and (InRec.TermRspCode[1] <> 'A') and (trim(hostMsg) <> '')) then  // have to use host msg for EBT
    InRec.CashPadDisp := rpad(hostMsg, sizeof(InRec.CashPadDisp) - 1);

  if (trim(InRec.PrimeCustDisp) = USEHOSTMSG) then
    InRec.PrimeCustDisp := rpad(hostMsg, sizeof(InRec.PrimeCustDisp) - 1);
  if (trim(InRec.AltCustDisp) = USEHOSTMSG) then
    InRec.AltCustDisp := rpad(hostMsg, sizeof(InRec.AltCustDisp) - 1);
end;

procedure IMapRsp(var InRec : MDMsgRec);
var
  rc: TResponseCodes;
  fileLocation: string;
//  RespCodeFileName: string;
//  XMLOK: Boolean;
  rcode: TResponseCode;

  procedure MakeND;
  begin
    with InRec do
    begin
      TermRspCode   := 'ND';
      CashPadDisp   := 'DECLINED        ';
      PrimeCustDisp := 'DECLINED            ';
      AltCustDisp   := 'CONTACT BANK        ';
    end;
  end;

  procedure UseRespCode(RespCode: TResponseCode);
  begin
    with InRec do
    begin
      TermRspCode := RespCode.TerminalAction;
      CashPadDisp := replaceField(InRec, RespCode.CashierLines1x16.Line1,sizeOf(CashPadDisp) - 1, tCashpad1); { TSL-A }
      if RespCode.CustomerLines.Count > 0 then
      begin
        if (languageID > 0) then   // languageID is 1 based, but if not set could be zero, customer lines are zero based
          dec(languageID);
        PrimeCustDisp := replaceField(InRec, RespCode.CustomerLines[languageID].Line1, sizeOf(PrimeCustDisp) - 1, tCust1); { TSL-A }
        AltCustDisp := replaceField(InRec, RespCode.CustomerLines[languageID].Line2, sizeOf(AltCustDisp) - 1, tCust2); { TSL-A }
        {$IFDEF MTXEPSDLL}
        ExtPrimeCustDisp := replaceField(InRec, RespCode.CustomerLines[languageID].Line1, sizeOf(ExtPrimeCustDisp) - 1, tCust1); // DOEP-71599
        ExtAltCustDisp := replaceField(InRec, RespCode.CustomerLines[languageID].Line2, sizeOf(ExtAltCustDisp) - 1, tCust2); // DOEP-71599
        {$ENDIF}
      end;
    end;
  end;

begin
  try
    {$IFDEF MTXEPSDLL}
      fileLocation := DefaultDir;
    {$ELSE}
      fileLocation := WinEPSDir + CONFIG_DIR + RESPONSECODES_DIR;
    {$ENDIF}
    rc := nil;
    try //JMR-B
      if not Assigned(XMLResponseCodesList) then
        XMLResponseCodesList := TResponseCodesList.Create(fileLocation);
      rc := XMLResponseCodesList.FindResponseCodes(INTERNAL_SUFFIX);
    except
      SM('Exception in IMapRsp creating/loading XMLResponseCodesList from ' + fileLocation);
    end;

    if Assigned(rc) then
    begin
      rcode := rc.LocateByResponseCodeAndCardCode(InRec.MTXRspCode, Trim(InRec.CardProcID));
      if rcode <> nil then
        UseRespCode(rcode)
      else
      begin
        rcode := rc.LocateByResponseCodeAndCardCode(InRec.MTXRspCode, DEFAULT_CARDCODE);
        if rcode <> nil then
        begin
          UseRespCode(rcode);
        end
        else
        begin
          rcode := rc.LocateByResponseCodeAndCardCode(DEFAULT_RESPCODE, Trim(InRec.CardProcID));
          if rcode <> nil then
            UseRespCode(rcode)
          else
          begin
            rcode := rc.LocateByResponseCodeAndCardCode(DEFAULT_RESPCODE, DEFAULT_CARDCODE);
            if rcode <> nil then
              UseRespCode(rcode)
            else
              begin
                SM('****ERROR: Unable to locate Response Code: ' + DEFAULT_RESPCODE + ' and Card Code: ' + DEFAULT_CARDCODE);
                MakeND;
              end;
          end;
        end;
      end;
    end
    else
      begin
        SM('****ERROR: Problem with XML file.');
        MakeND;
      end;
  except on e:exception do
    SM('Exception in IMapRsp. ' + e.Message);
  end;
end;   { MapRsp }

procedure MapMdMsgRec(var aMdMsg: MdMsgRec; aCardLanguageCode: string);
begin
  aMdMsg.languageID := StrToIntDef(aCardLanguageCode, 1);
  IMapRsp(aMdMsg);
end;

function MapRsp(var aMdMsgRec: MdMsgRec; aLines: TLineCollection5; aCardLanguageCode: string): string;
begin
  MsgDebug('CardProcID=' + aMdMsgRec.CardProcID + ' MTXRspCode=' +
    aMdMsgRec.MTXRspCode);
  MapMdMsgRec(aMdMsgRec, aCardLanguageCode);

  IMapRspCashLines5x40(aMdMsgRec, aLines);
  result := aMdMsgRec.TermRspCode;
end;

function IMapRspCashLines5x40(InRec: MDMsgRec; var LineCollection: TLineCollection5; const hostMsg: string = ''): Boolean;
var
  rc: TResponseCodes;
  fileLocation: string;
  rcode: TResponseCode;
  FoundExactMatch: Boolean;

  function repUseHostMsg(inStr: string): string;
  begin
    if (trim(inStr) = USEHOSTMSG) and (Trim(hostMsg) <> '')
      then result := hostMsg
      else result := inStr;
  end;   { repUseHostMsg }

begin
  rcode := nil;
  try
//    SM('===DEBUG-JMR: Map_IRsp.IMapRspCashLines5x40 = >' + hostMsg + '<');
    FoundExactMatch := False;
    {$IFDEF MTXEPSDLL}
      fileLocation := DefaultDir;
    {$ELSE}
      fileLocation := WinEPSDir + CONFIG_DIR + RESPONSECODES_DIR;
    {$ENDIF}

    rc := nil;
    try //JMR-B
      if not Assigned(XMLResponseCodesList) then
        XMLResponseCodesList := TResponseCodesList.Create(fileLocation);
      rc := XMLResponseCodesList.FindResponseCodes(INTERNAL_SUFFIX);
    except
      SM('Exception in IMapRspCashLines5x40 creating/loading XMLResponseCodesList from ' + fileLocation);
    end;

    if Assigned(rc) then
    begin
      rcode := rc.LocateByResponseCodeAndCardCode(InRec.MTXRspCode, Trim(InRec.CardProcID));
      if rcode <> nil then
      begin
        LineCollection.Assign(rcode.CashierLines5x40);
        FoundExactMatch := True;
        Result := True;
      end
      else
      begin
        rcode := rc.LocateByResponseCodeAndCardCode(InRec.MTXRspCode, DEFAULT_CARDCODE);
        if rcode <> nil then
        begin
          LineCollection.Assign(rcode.CashierLines5x40);
          FoundExactMatch := True;
          Result := True;
        end
        else
        begin
          rcode := rc.LocateByResponseCodeAndCardCode(DEFAULT_RESPCODE, Trim(InRec.CardProcID));
          if rcode <> nil then
          begin
            LineCollection.Assign(rcode.CashierLines5x40);
            Result := True;
          end
          else
          begin
            rcode := rc.LocateByResponseCodeAndCardCODE(DEFAULT_RESPCODE, DEFAULT_CARDCODE);
            if rcode <> nil then
            begin
              LineCollection.Assign(rcode.CashierLines5x40);
              Result := True;
            end
            else
              Result := False;
          end;
        end;
      end;
    end
    else
      Result := False;

    if not FoundExactMatch and (InRec.MTXRspCode = TrxDecNoAuthNo) then
    begin
      LineCollection.Clear;
      LineCollection.Line1 := 'Declined';
      LineCollection.Line2 := 'No Auth Number';
      Result := True;
    end;

    if not result and (trim(InRec.CashPadDisp) <> '') then
      LineCollection.Line1 := InRec.CashPadDisp;

    if Result then
    begin
      if (InRec.MTXRspCode = TrxDecHostNetworkDown) and (rcode.TerminalAction[1] = 'A') then
        LineCollection.Line1 := InRec.CashPadDisp
      else
      begin
        LineCollection.Line1 := replaceField(InRec, LineCollection.Line1, SizeOf(LineCollection.Line1) - 1, tCashpad1);
        LineCollection.Line1 := repUseHostMsg(LineCollection.Line1);
      end;
    end;

  except
    on e:exception do
    begin
      result := false;
      SM('Exception in IMapRspCashLines5x40. ' + e.Message);
    end;
  end;
end;

{$IFDEF MTXEPSDLL}
procedure GetExtCustomerDisplay(var aExtPrimeCustDisp, aExtAltCustDisp: String40); // DOEP-71599
begin
  aExtPrimeCustDisp := ExtPrimeCustDisp;
  aExtAltCustDisp := ExtAltCustDisp;
end;
{$ENDIF}
initialization
  ExtendedLog('Map_IRsp Initialization');
finalization
  ExtendedLog('Map_IRsp Finalization');

end.
