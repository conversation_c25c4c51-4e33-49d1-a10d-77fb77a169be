// (c) MTXEPS, Inc. 1988-2008
unit SCATConstants;
(******************************************************************************
 *
 *  SCATConstants
 *
 *  This unit defines constants used by the SCAT code.
 *
 ******************************************************************************
  VER   Revision History
  ======================
  v825.0 12-20-07 TSL 02 Add tm4150 and tmMX860
  820.0 07-18-06 TSL 01 add SCATIdleScreen, SCATStopScreen
  03-28-06 SRM N/A
    - Created header block
 ******************************************************************************)

interface

uses
  SysUtils,
  FinalizationLog,
  MTX_Constants;
  
type
  /// SHOULD match sequence with TermTypeDescrip, MaxNumberOfKeys, ButtonNames (was in MTX_Constants) !!!!
  /// SHOULD match GetTermType (was in OpenEPSCalls) and MaxTermKeys (was in scat2) !!!!
  TTerminalType = ( // was in UWinEPSConfiguration, Menu and OpenEPS use
        termNone = 0,
        termEverest,
        termOmni7000,
        termICE5500,    // DEV-18207 // supported by ServerEPS only
        termHYP4100,
        termHYP4150,
        termHYP4150PCI,
        termHYP5200,    // DOEP-28237
        termHYP5300,    // DOEP-25332
        termHYP5300PCIv3,
        termENT1000,
        termENT2100,
        termL4250,
        termFPE_L5200,
        termFPE_L5300,
        termFPE_L5300_PCIv3,
        termING6550,
        termING6780,
        termING_iSC250, // DOEP-25060
        termING_iSC350,
        termING_iSC480,
        termING_UPPLane, //CPCLIENTS-5966 UPP-Lane
        termRBA_iSC250, // TFS-9643
        //termRBA_iSC350,
        termRBA_iSC480,
        termRBA_iPP350,
        termRBA_ICMP,
        termNCR,
        termOmni3750,
        termMAGP90,     // DEV-12874
        termMX760,
        termMX830,
        termMX850,
        termMX860,
        termMX870,
        termMX880,      // DEV-12717
        termMX915,      // DEV-45022
        termMX925,     
        termVx570,
        termVX670,      // DAE 4855
        termSCAT6780,
        termIDTech,
        termXPI830,
        termXPI850,     // TFS-15534
        termXPI860,     // supported by ServerEPS only
        termXPI870,
        termXPI880,     // supported by ServerEPS only
        termXPI915,
        termXPI925,
        termXPIe335,
        termXPI_Engage, //CPCLIENTS-5966 XPI-Engage //CPCLIENTS-5784 use proper naming convention
        termIDTechNCF,
        termVX820,
        termE355,

        { !!! NOT IN USE. Put to the end  !!! }
        termOmni490,    // DEV-14223
        termICE6000,    // DEV-14223
        termC2000,      // DEV-14223
        termNCR5993     // DEV-14223
        { !!! NOT IN USE. Put to the end  !!! }
      );

const

  { TerminalType - don't renumber, comes in from SCAT this way: see SCAT spec }

  tmNone      = '0';  {0}
  tmOmni490   = '1';  {1} // DEV-14223
  tmEverest   = '2';  {2}
  tmC2000     = '3';  {3} // DEV-14223
  tmENT2100   = '4';  {4}
  tmNCR5993   = '5';  {5} // DEV-14223
  tmICE5000   = '6';  {6}
  tmICE6000   = '7';  {7} // DEV-14223
  tmOmni7000  = '8';  {8}
  tmICE4100   = '9';  {9}
  tmENT1000   = ':';  {10}     { next char after 9 so we can take ord }
  tmL4250     = 'B';  {11}
  tmING6550   = '=';  {12}
  tmOmni3750  = '>';  {13}
  tmMX830     = '?';  {14}
  tmMX850     = '@';  {15}
  tmMX860     = '%';  {16}
  tmMX870     = '<';  {17}
  tmMX880     = '*';  {18} // DEV-12717
  tmVX670     = '#';  {19}
  tmING6780   = '$';  {20}
  tm4150      = 'F';  {21} // TSL-02
  tmMAGP90    = '&';  {22} // DEV-12874
  tmSCAT6780  = '!';  {23} // Ingenico version of our SCAT code
  tm4150pci   = 'G';  {24}
  tmVX570     = ';';
  tmMX760     = 'A';  // DEV-14813
  tmHYP5300   = 'H';
  tmHYP5300PCIv3 = 'K';
  tmIDTech    = 'C';
  tmXPIMX860  = 'D';
  tmXPIMX880  = 'E';
  tmHYP5200   = 'L';
  tmMX915     = 'I';
  tmMX925     = 'O';
  tmING_iSC250 = 'J'; // DOEP-25060
  tmING_iSC350 = 'M';
  tmIDTechNFC  = 'N'; // DOEP-48429
  tmING_iSC480 = 'P';
  tmXPIe335    = 'Q';
  tmNCR        = 'R';
  tmXPIMX915   = 'S';
  tmXPIMX925   = 'T';
  tmRBA_iSC250 = 'U'; // TFS-9643
  tmRBA_iSC350 = 'V';
  tmRBA_iSC480 = 'W';
  tmRBA_iPP350 = ')';
  tmRBA_ICMP   = 'X';
  tmRBA_iUP250 = ']';
  tmFPE_L5200  = 'Y';
  tmFPE_L5300  = 'Z';
  tmXPIMX830   = '+';  // TFS-15534
  tmXPIMX850   = '/';  // TFS-15534
  tmXPIMX870   = '|';  // TFS-15534
  tmFPE_L5300_PCIv3  = '(';
  tmFPE_LUXE   = '}'; // 6167
  tmVX820      = '^';
  tmVX690      = '[';
  tmE355       = '~';  // TFS-112056
  tmPOSP2P     = '-';   // CPCLIENTS-2348 : consider this a pseudo term type. P2P data comes from OpenIP (Sonic). No pin pad directly attached to OpenEPS.
  tmXPI_Engage  = '\'; //CPCLIENTS-5966 for Verifone Engage Series like P400/M400 //CPCLIENT-5784 use proper naming convention
  tmING_UPPLane = '{'; //CPCLIENTS-5966 for ING-Lane series like Lane8000 //CPCLIENT-5784 use proper naming convention

  MX800TermSet        = [tmMX870, tmMX830, tmMX850, tmMX860, tmMX880, tmMX760, tmMX915, tmMX925];
  XPISet              = [tmXPIMX830,tmXPIMX850,tmXPIMX860,tmXPIMX870,tmXPIMX880,tmXPIe335,tmXPIMX915,tmXPIMX925];
  VFI800OnlyTermSet   = MX800TermSet - [tmMX915, tmMX925] + XPISet - [tmXPIe335,tmXPIMX915,tmXPIMX925];   // TFS-29412 terminals limited to 500 byte messages
  VFIDateEntryTermSet = [tmMX860,tmMX880,tmMX870,tmMX915,tmMX925,tmXPIMX860,tmXPIMX870,tmXPIMX880,tmXPIMX915,tmXPIMX925];  // TFS-29069
  RBASet              = [tmRBA_iSC250, tmRBA_iSC350,tmRBA_iSC480,tmRBA_iPP350, tmRBA_ICMP, tmRBA_iUP250, tmING_UPPLane]; //CPCLIENTS-5966 Added Lane series
  FPESet              = [tmFPE_L5200, tmFPE_L5300, tmFPE_L5300_PCIv3, tmFPE_LUXE]; // 6167
  XPIModSet           = [tmVX820, tmVX690, tmE355, tmXPI_Engage]; //CPCLIENTS-5966 Added XPI Engage PinPads like P400/M400 PinPad
  HYC4100Set          = [tmICE4100,tm4150,tm4150pci,tmL4250,tmHYP5300,tmHYP5200,tmHYP5300PCIv3];
  ING250sc350Set      = [tmING_iSC250, tmING_iSC350,tmING_iSC480];
  TermDownloadableSet = [tmOmni490,tmEverest,tmC2000,tmICE5000,tmICE6000, tmVX670, tmNCR,
                         tmOmni7000,tmNCR5993] + MX800TermSet + HYC4100Set + ING250sc350Set + FPESet;
  ICETermSet          = [tmICE5000, tmICE6000] + HYC4100Set;
  verifoneTermSet     = [tmOmni490, tmEverest, tmOmni7000, tmVX670] + MX800TermSet;
  TermSigCapSet       = [tmNCR5993, tmICE5000, tmICE6000, tmOmni7000, tmENT1000, tmING6550, tmING6780, tmSCAT6780]
                         + ING250sc350Set + MX800TermSet + HYC4100Set + XPISet + RBASet + FPESet + [tmE355, tmXPI_Engage]; // TFS-112056 //CPCLIENTS-7177 Enable Signature Capture for Engage
  TermOPOSSet         = [tmENT2100, tmENT1000, tmING6550, tmING6780];
  TermClassSet        = TermOPOSSet + [tmNCR5993] + MX800TermSet + XPISet + RBASet + FPESet + XPIModSet;
  TermK_Menu_TwoSet   = ICETermSet + TermClassSet + [tmOmni7000, tmVX670] + ING250sc350Set;
  TermNoHardKeySet    = [tmICE4100,tm4150,tm4150pci,tmENT1000] + MX800TermSet + XPISet;   // these terminals have no hard keys/buttons
  TermIdleMsgScreen   = [tmENT1000, tmING6550,tmING6780,tmSCAT6780] + MX800TermSet + HYC4100Set + XPISet;
  TermGetTrackSource  = [tmOMNI7000,tmSCAT6780] + HYC4100Set + ING250sc350Set;  // i.e. RFID
  ScreenFileNeededSet = [tmICE5000, tmICE4100, tm4150, tmENT1000, tmENT2100, tmL4250,
                         tmMX760, tmMX830, tmMX850, tmMX860, tmMX870, tmMX880, tmMX915, tmMX925,
                         tmING6550, tmING6780, tmHYP5200, tmHYP5300, tm4150pci, tmHYP5300PCIv3,
                         tmING_iSC480
                        ]; // moved from UIsoio.GetScreenFileNeeded // not support 'SCAT-ICE6000', 'SCAT-NCR5993' any more
  TermSmartWICSet     = [tmXPI_Engage]; //CPCLIENTS-11047/11048

  TerminalsWithSoftKeys: set of byte = [integer(termICE5500),integer(termICE6000), //JTG
    integer(termHYP4100),integer(termHYP4150),integer(termENT1000),integer(termNCR5993),
    integer(termL4250),integer(termMX870),integer(termING6550), integer(termING6780),
    integer(termMX830), integer(termMX850), integer(termMX860), integer(termXPI860), integer(termXPIe335), integer(termNCR),
    integer(termMX880), integer(termXPI880), integer(termXPI915), integer(termXPI925),// DEV-12717: add MX880
    integer(termXPI830), integer(termXPI850), integer(termXPI860), integer(termXPI870), // TFS-15533
    integer(termMX915), // DEV-45022
    integer(termMX925),
    integer(termMX760), // DEV-14813
    integer(termHYP4150PCI),// DEV-15094
    integer(termHYP5200), // DOEP-28237
    integer(termHYP5300), // DOEP-25332
    integer(termHYP5300PCIv3),
    integer(termING_iSC250), // DOEP-25060
    integer(termING_iSC350),
    integer(termING_iSC480),
    integer(termRBA_iSC250),
    //integer(termRBA_iSC350),
    integer(termRBA_iSC480),
    //integer(termRBA_iPP350),
    integer(termRBA_ICMP),
    integer(termSCAT6780),
    integer(termFPE_L5200),
    integer(termFPE_L5300),
    integer(termFPE_L5300_PCIv3)
    ];

  TerminalsWithExtraCaptions: set of byte = [integer(termOmni7000),integer(termENT2100),
    integer(termOmni3750), integer(termVx670), integer(termVx570)];                                 {DAE 4855}

  MaxOpenEPSTermType = 56; // must equal the last element # in the array below // TFS-15534 //CPCLIENTS-5966 Added ING UPP and XPI Engage family

  TermTypeChars: array[0..MaxOpenEPSTermType] of char =
                (
                    tmNone,             // NONE,
                    tmEverest,          // SCAT-EVEREST
                    tmOmni7000,         // SCAT-OMNI7000
                    tmICE5000,          // SCAT-ICE5500
                    tmICE4100,          // SCAT-HYP4100
                    tm4150,             // SCAT-HYP4150
                    tm4150pci,          // SCAT-HYP4150 PCIv2
                    tmHYP5200,          // SCAT-HYP5200
                    tmHYP5300,          // SCAT-HYP5300
                    tmHYP5300PCIv3,     // SCAT-HYP5300 PCIv3
                    tmENT1000,          // SCAT-ENT1000
                    tmENT2100,          // SCAT-ENT2100
                    tmL4250,            // SCAT-L4250
                    tmFPE_L5200,        // FPE-L5200
                    tmFPE_L5300,        // FPE-L5300
                    tmFPE_L5300_PCIv3,  // FPE-L5300 PCIv3
                    tmFPE_LUXE,         // FPE-LUXE
                    tmING6550,          // SCAT-ING6550
                    tmING6780,          // SCAT-ING6780
                    tmING_iSC250,       // SCAT-ING iSC250
                    tmING_iSC350,       // SCAT-ING iSC350
                    tmING_iSC480,       // SCAT-ING iSC480
                    tmING_UPPLane,       //CPCLIENTS-5966 UPP-Lane //CPCLIENTS-5784 use proper naming convention
                    tmRBA_iSC250,       // RBA-iSC250
                    //tmRBA_iSC350,       // RBA-iSC350
                    tmRBA_iSC480,       // RBA-iSC480
                    tmRBA_iPP350,       // RBA_iPP350
                    tmRBA_ICMP,         // RBA-ICMP
                    tmRBA_iUP250,       // RBA-iUP/iUR 250
                    tmNCR,
                    tmOmni3750,         // SB-OMNI3750
                    tmMAGP90,           // SCAT-MAGP90
                    tmMX760,            // SCAT-MX760
                    tmMX830,            // SCAT-MX830
                    tmMX850,            // SCAT-MX850
                    tmMX860,            // SCAT-MX860
                    tmMX870,            // SCAT-MX870
                    tmMX880,            // SCAT-MX880
                    tmMX915,            // SCAT-MX915
                    tmMX925,            // SCAT-MX925
                    tmVX570,            // SB-Vx570
                    tmVX670,            // SCAT-VX670
                    tmSCAT6780,         // SCAT-6780
                    tmIDTech,           // SCAT-IDTechP2P
                    tmXPIMX830,         // XPI-MX830
                    tmXPIMX850,         // XPI-MX850
                    tmXPIMX860,         // XPI-MX860
                    tmXPIMX870,         // XPI-MX870
                    tmXPIMX880,         // XPI-MX880
                    tmXPIMX915,         // XPI-MX915
                    tmXPIMX925,         // XPI-MX925
                    tmXPIe335,          // XPI-e335
                    tmXPI_Engage,        //CPCLIENTS-5966 XPI-Engage //CPCLIENTS-5784 use proper naming convention
                    tmVX820,            //XPI-VX820
                    tmVX690,            //XPI-VX690
                    tmIDTechNFC,        // SCAT-IDTechP2PNFC
                    tmE355,             // XPI-e355
                    tmPOSP2P            // CPCLIENTS-2348 : consider this a pseudo term type. P2P data comes from OpenIP (Sonic). No pin pad directly attached to OpenEPS.
                );
 
  tmCodeChars : array[-1..MaxOpenEPSTermType] of string = // used in scat2 // TODO-YHJ: can we just use GetTermTypeDesc and remove this?
                   (     '<INVALID>',
                         '<NONE>',
                         '<Everest>',
                         '<OMNI7000>',
                         '<ICE5000>',
                         '<HYP4100>',
                         '<HYP4150>',
                         '<HYP4150PCI>',
                         '<HYP5200>',
                         '<HYP5300>',
                         '<HYP5300PCIv3>',
                         '<Entouch1000>',
                         '<Entouch2100>',
                         '<HYP4250>',
                         '<FPE-L5200>',
                         '<FPE-L5300>',
                         '<FPE-L5300 PCIv3>',
                         '<FPE-LUXE>',
                         '<ING6550>',
                         '<ING6780>',
                         '<ING_iSC250>',
                         '<ING_iSC350>',
                         '<ING_iSC480>',
                         '<UPP-Lane>', //CPCLIENTS-5966 UPP-Lane
                         '<RBA_iSC250>',
                         //'<RBA_iSC350>',
                         '<RBA_iSC480>',
                         '<RBA_iPP350>',
                         '<RBA_ICMP>',
                         '<RBA-iUP/iUR 250>',
                         '<NCR>',
                         '<OMNI3750>',
                         '<MAGP90>',
                         '<MX760>',
                         '<MX830>',
                         '<MX850>',
                         '<MX860>',
                         '<MX870>',
                         '<MX880>',
                         '<MX915>',
                         '<MX925>',                         
                         '<VX570>',
                         '<VX670>',
                         '<SCAT-6780>',
                         '<IDTech>',
                         '<XPIMX830>',
                         '<XPIMX850>',
                         '<XPIMX860>',
                         '<XPIMX870>',
                         '<XPIMX880>',
                         '<XPIMX915>',
                         '<XPIMX925>',
                         '<XPIe335>',
                         '<XPI-Engage>', //CPCLIENTS-5966 XPI-Engage
                         '<XPIVX820>',
                         '<XPIVX690>',
                         '<IDTechNFC>',
                         '<XPIe355>',
                         '<POSP2P>'
                   );

  TermTypeDescrip  : array[0..MaxOpenEPSTermType] of string =
                   (     'NONE',
                         'SCAT-EVEREST',
                         'SCAT-OMNI7000',
                         'SCAT-ICE5500',        // DEV-18207
                         'SCAT-HYP4100',
                         'SCAT-HYP4150',        // DAE 6686
                         'SCAT-HYP4150 PCIv2',
                         'SCAT-HYP5200',        // DOEP-28237
                         'SCAT-HYP5300',        // DOEP-25332
                         'SCAT-HYP5300 PCIv3',
                         'SCAT-ENT1000',
                         'SCAT-ENT2100',
                         'SCAT-L4250',
                         'FPE-L5200',
                         'FPE-L5300',
                         'FPE-L5300 PCIv3',
                         'FPE-LUXE',
                         'SCAT-ING6550',
                         'SCAT-ING6780',        
                         'SCAT-ING iSC250',     // DOEP-25060
                         'SCAT-ING iSC350',
                         'SCAT-ING iSC480',
                         'UPP-Lane', //CPCLIENTS-5966 UPP-Lane
                         'RBA-iSC250',
                         //'RBA-iSC350',
                         'RBA-iSC480',
                         'RBA-iPP350',
                         'RBA-ICMP',
                         'RBA-iUP/iUR 250',
                         'SCAT-NCR',
                         'SB-OMNI3750',
                         'SCAT-MAGP90',         // DEV-12874
                         'SCAT-MX760',          // DEV-14813
                         'SCAT-MX830',
                         'SCAT-MX850',
                         'SCAT-MX860',          // DAE 7416
                         'SCAT-MX870',
                         'SCAT-MX880',          // DEV-12717
                         'SCAT-MX915',          // DEV-45022
                         'SCAT-MX925',
                         'SB-Vx570',            // DAE 4855
                         'SCAT-VX670',          
                         'SCAT-6780',
                         // DOEP-47651, needed to include the IDTech P2P for *BOTH* ConMan GUI *AND* mtx P2P DLL
                       {$IF Defined(GUIJR) OR Defined(P2P)} // For ConMan GUI compile as well as mtx.dpr (P2P) DLL compile
                         'SCAT-IDTechP2P'
                       {$ELSE}
                         'SCAT-IDTech'
                       {$IFEND},
                                                // DEV-32600, the P2P IDTech is for ServerEPS only, the non-P2P IDTech is for WinEPS and Publix only
                         'XPI-MX830',           // TFS-15534
                         'XPI-MX850',           // TFS-15534
                         'XPI-MX860',           // DEV-44699: WinEPS GUI doesn't support XPI terminal
                         'XPI-MX870',           // TFS-15534
                         'XPI-MX880',
                         'XPI-MX915',
                         'XPI-MX925',
                         'XPI-e335',
                         'XPI-Engage', //CPCLIENTS-5966 XPI-Engage
                         'XPI-VX820',
                         'XPI-VX690',
                         // !!! SHOULD BE LAST and removed from GUI
                         'SCAT-IDTechP2PNFC',    // DOEP-48429
                         'XPI-e355',  // TFS-112056
                         'POSP2P'
                   );

   functionKey = 0;
   screenKey   = 1;
   MaxNumberOfKeys : array[0..MaxOpenEPSTermType, functionKey..screenKey] of integer =
                   ((0,0),      // NONE
                    (3,3),      // EVEREST
                    (4,4),      // OMNI7000
                    (0,6),      // ICE5500              // DEV-18207
                    (0,6),      // HYP4100
                    (0,6),      // HYP4150              // DAE 6686
                    (0,6),      // HYP4150pci
                    (0,6),      // HYP5200              // DOEP-28237
                    (0,6),      // HYP5300              // DOEP-25332
                    (0,6),      // HYP5300 PCIv3
                    (0,6),      // ENT1000
                    (0,4),      // ENT2100
                    (0,6),      // L4250
                    (0,6),      // FPE-L5200
                    (0,6),      // FPE-L5300
                    (0,6),      // FPE-L5300 PCIv3
                    (0,6),      // FPE-LUXE
                    (0,6),      // ING6550
                    (0,6),      // ING6780              
                    (0,6),      // SCAT-ING iSC250
                    (0,6),      // SCAT-ING iSC350
                    (0,6),      // SCAT-ING iSC480
                    (0,6),      //CPCLIENTS-5966 UPP-Lane
                    (0,6),      // RBA-iSC250
                    //(0,6),      // RBA-iSC350
                    (0,6),      // RBA-iSC480
                    (0,4),      // RBA-iPP350
                    (0,6),      // RBA-ICMP
                    (0,2),      // RBA-iUP/iUR 250
                    (0,6),      // NCR
                    (0,4),      // OMNI3750             
                    (0,0),      // MAGP90               // DEV-12874
                    (0,8),      // MX670                // DEV-14813
                    (0,4),      // MX830
                    (0,4),      // MX850
                    (0,6),      // MX860                // DAE 7416
                    (0,6),      // MX870
                    (0,6),      // MX880                // DEV-12717
                    (0,6),      // MX915                // DEV-45022
                    (0,6),      // MX925
                    (0,4),      // Vx570                // DAE 4855
                    (0,6),      // VX670
                    (0,6),      // SCAT-6780
                    (0,0),      // SCAT-IDTechP2P for ServerEPS
                    (0,6),      // XPI830: ServerEPS
                    (0,6),      // XPI850: ServerEPS
                    (0,6),      // XPI860: ServerEPS
                    (0,6),      // XPI870: ServerEPS
                    (0,6),      // XPI880: ServerEPS
                    (0,6),      // XPI915
                    (0,6),      // XPI925
                    (0,6),      // XPIe335
                    (0,6),      //CPCLIENTS-5966 XPI-Engage //CPCLIENTS-14456
                    (0,4),      // VX820
                    (0,4),      // VX690
                    (0,0),      // SCAT-IDTechP2PNFC
                    (0,6),      // E355
                    (0,0)      // CPCLIENTS-2348 : P2P data comes from OpenIP (Sonic). No pin pad directly attached to OpenEPS.
                   );
               
  // used in IntegratedU and UBioEditor GUI
  ButtonNames : array[0..MaxOpenEPSTermType] of string =
                   ('not_used',
                    'evebtn',           // SCAT-EVEREST
                    'omni_btn',         // SCAT-OMNI7000
                    'not_used',         // SCAT-ICE5500         // DEV-18207
                    'not_used',         // SCAT-HYP4100         // DAE 6686
                    'not_used',         // SCAT-HYP4150
                    'not_used',         // SCAT-HYP4150 PCIv2
                    'not_used',         // SCAT-HYP5200         // DOEP-28237
                    'not_used',         // SCAT-HYP5300         // DOEP-25332
                    'not_used',         // SCAT-HYP5300 PCIv3
                    'not_used',         // SCAT-ENT1000
                    'ent2100_btn',      // SCAT-ENT2100
                    'not_used',         // SCAT-L4250
                    'not_used',         // FPE-L5200
                    'not_used',         // FPE-L5300
                    'not_used',         // FPE-L5300 PCIv3
                    'not_used',         // FPE-LUXE
                    'not_used',         // SCAT-ING6550
                    'not_used',         // SCAT-ING6780         
                    'not_used',         // SCAT-ING iSC250
                    'not_used',         // SCAT-ING iSC350
                    'not_used',         // SCAT-ING iSC480
                    'not_used',         //CPCLIENTS-5966 UPP-Lane
                    'not_used',         // RBA-iSC250
                    //'not_used',         // RBA-iSC350
                    'not_used',         // RBA-iSC480
                    'RBAiPP350btn',     // RBA-iPP350
                    'not_used',         // RBA-ICMP
                    'not_used',         // RBA-iUP/iUR 250
                    'not_used',         // SCAT-NCR                    
                    'omni3750_btn',     // SB-OMNI3750          
                    'not_used',         // SCAT-MAGP90          // DEV-12874
                    'mx670_btn',        // SCAT-MX760           // DEV-14813
                    'mx830_btn',        // SCAT-MX830
                    'mx850_btn',        // SCAT-MX850
                    'not_used',         // SCAT-MX860           // DAE 7416
                    'not_used',         // SCAT-MX870           
                    'not_used',         // SCAT-MX880           // DEV-12717
                    'not_used',         // SCAT-MX915           // DEV-45022
                    'not_used',         // SCAT-MX925                     
                    'Vx570_btn',        // SB-Vx570             // DAE 4855
                    'vx670_btn',        // SCAT-VX670
                    'not_used',         // SCAT-6780
                    'not_used',         // SCAT-IDTechP2P for ServerEPS, SCAT-IDTech for WinEPS
                    'not_used',         // XPI-MX830 for ServerEPS
                    'not_used',         // XPI-MX850 for ServerEPS  // TFS-15534
                    'not_used',         // XPI-MX860 for ServerEPS
                    'not_used',         // XPI-MX870 for ServerEPS
                    'not_used',         // XPI-MX880 for ServerEPS
                    'not_used',         // XPI-MX915
                    'not_used',         // XPI-MX925
                    'not_used',         // XPI-e355
                    'not_used',         //CPCLIENTS-5966 XPI-Engage
                    'not_used',         // XPI-VX820
                    'not_used',         // XPI-VX690
                    'not_used',         // SCAT-IDTechNFC
                    'not_used',         // XPI-e355
                    'not_used'         // CPCLIENTS-2348 : P2P data comes from OpenIP (Sonic). No pin pad directly attached to OpenEPS.
                   );

  { SCAT Message type codes }
  Display_Prompt      = '0';
  Tender_Type         = '1';
  Track_Data          = '2';
  FreqShop_Data       = '3';
  PIN                 = '4';
  MICR                = '5';
  Smart_Card_Data     = '6';
  Keyboard            = '7';
  SCAT_Wait           = '8';
  Return_To_Idle      = '9';
  Signoff             = 'B';
  Signature_Capture   = 'C';
  Download_Parameters = 'D';
  New_Working_Key     = 'E';
  Version_Info        = 'F';
  Terminal_Type       = 'G';
  SplitMessage        = 'H';
  Init_Code_Download  = 'I';
  Cashback_Amt        = 'J';
  SCAT_Status         = 'K';
  Gas_Rewards_Voucher = 'L';
  Debug               = 'M';
  Cust_Prompt         = 'N';
  File_Request        = 'O';
  File_Load           = 'P';
  SCAT_State          = 'Q';
  SoftKeys            = 'S';
  Cancel_Error        = 'X';
  Packing_List_Ver    = '#';
  PrinterMsg          = '%';
  SW_Start            = '[';
  SW_End              = '\';
  SW_GetRx            = '{';
  SW_UpdateRx         = '}';
  SW_LockCard         = ']';
  SW_RemoveCard       = '~';
  SW_GetPin           = '<';
  SW_CardRemoved      = '?';
  SW_CustStartWIC     = '/';
  SW_BenefitReversal  = 'y';  // TFS-10734
  HippaScreen         = '@';
  Clear_Cust_Prompt   = 'n';
  PINWithCredit       = '$';
  Language            = '(';
  DisplayWithESC      = 'm';
  DownLoadDone        = '=';
  SCATPowerOn         = 'r';
  SCATIdleScreen      = 'i';               { TSL-01 }
  SCATStopScreen      = 'j';               { TSL-01 }
  Sig_CapExtended     = 'c';
  HYCEncHandShake     = 'h';
  MoreCardData        = 'b';
  SW_CustOkWithList   = 'd';  // JTG 36840 (?)
  EnhancedManualEntry = 'x';  //9724 10430
  IntervalMsg         = 'o';  // TFS-11710
  CancelMsg           = 'p';  // TFS-12250
  ReReqEMVTrackData   = 'q';  // TFS-14059
  EMVFinishCrDbSelect = 'z';  // TFS-24615
  DCCScreen           = 'r';  // TFS 11758
  File_Request_FPE    = 'R';  // TFS-16335
  Tip                 = 't';  // TFS ?????
  PromptEmployeeLogin = 'e';
  ComStatusCode       = 'f';
  ExtendedEntry       = 'g';  //CPCLIENTS-9047

  FPE_POS_PORTAL_CMD  = 'v';  // Returned to SCAT by the FPE Terminal class in a rare error case

  FPE_FORM_COMMAND    = 'V';

  FPE_SW_BenefitReversal = '>';
  FPE_SW_BenefitReversal_Chars = '<Benefit_Reversal>';

  alwaysSendSCATCmd : set of AnsiChar = [track_data, SoftKeys]; // TFS-166588: Display_Prompt, DisplayWithESC // TFS-195079: Signoff
             //          1         2         3         4         5         6         7
             // 1234567890123456789012345678901234567890123456789012345678901234567890
  MsgCodeChs = '0123456789BCDEFGIJKLMNOPQSX#[\{}]~><?n$(m=rij@chbdx/opqzry';   {JAM-BR}         // TFS-12041  TFS-11710  TFS-12250 TFS-14059  TFS-24615
  MsgCodeChars : Array[0..58] of String = ('<Unknown>','<Display_Prompt>', '<Tender_Type>',           // TFS-14059  // TFS-24615  // TFS-10734
               '<Track_Data>','<FreqShop_Data>','<PIN>','<MICR>',
               '<Smart_Card_Data>', '<Keyboard>', '<SCAT_Wait>', '<Return_To_Idle>',
               '<Signoff>', '<Signature_Capture>','<Download_Parameters>',
               '<New_Working_Key>', '<Version_Info>', '<Terminal_Type>',
               '<Init_Code_Download>','<Cashback_Amt>','<SCAT_Status>',
               '<Gas_Rewards_Voucher>', '<Debug>', '<Display_Cust_Prompt>',
               '<File_Request>', '<File_Load>', '<SCAT_State>',
               '<Soft_Key>', '<Cancel>', '<Packing_List_Version>',
               '<SW_Start>', '<SW_End>', '<SW_GetRx>', '<SW_UpdateRx>', '<SW_LockCard>',
               '<SW_RemoveCard>', '<Benefit_Reversal>', '<SW_GetWicPin>', '<SW_WicRemoved>',
               '<Clear_Display_Cust_Prompt>','<PINWithCredit>','<Get_Language>',
               '<DisplayWithESC>','<DownloadDone>','<SCATPowerOn>','<SCATIdleScreen>',
               '<SCATStopScreen>','<HippaScreen>','<Sig_CapExtended>','<HYCEncHandShake>','<MoreCardData>',
               '<SW_CustOkWithList>','<EnhanceManualEntry>','<SW_CustStartWIC>','<Interval_Display>','<Cancel_Message>', //JTG 36840 // TFS-11710 // TFS-12250
               '<ReReq_EMV_TrackData>','<EMVFinishCrDbSelect>','<DCC_Screen', 'SW_BenefitReversal');    // TFS-14059   //11758 for 'r' // TFS-24615 // TFS-10734

  SmartWicMsgSet = ['[', '<','{','}','\',']','~','>','?'];  {JAM-BN}

  { SWIC msgs for Remove card }
  MAX_WicMsg = 5;
  MSG_ID_NO_CURRENT_WIC = 4;
  MSG_ID_NO_MSG_DEFINED = 1;
  REMOVE_CARD = 'Remove Card';
  WICMsg : Array[1..MAX_WicMsg] of string = ('','Thank You','No Allowable WIC Items',         // SAK
                                    'No Current WIC','Not Enough WIC Quantity');

  { SCAT Tender type codes }
  T_No_Tender         = 'N';
  T_Credit            = '1';
  T_Debit             = '2';
  T_EBT_FS            = '3';
  T_EBT_CASH          = '4';
  T_Check             = '5';
  T_Priv_Credit       = '6';
  T_Priv_Debit        = '7';
  T_User1             = '8';
  T_User2             = '9';
  T_Pin_Change        = '0';
  T_No_Tender_CS      = 'S';
  T_FrequentShopper   = 'F';    { for parm download only }
  T_EBT               = 'E';    { for parm download only }
  T_EBT_FS_Balance    = 'G';
  T_EBT_CASH_Balance  = 'H';

  { SCAT Cancel Error code }
  sec_Cancelled          = 'X';
  sec_FunctionKey        = 'A';
  sec_NoDataAvailable    = 'Z';
  sec_DeviceError        = 'W';
  sec_trackFail          = 'T';
  sec_FreqShopFail       = 'R';
  sec_MICRFail           = 'M';
  sec_sigCapFail         = 'N';
  sec_SmartCardFail      = 'O';
  sec_SmartCardWriteFail = 'V';
  sec_SmartCardChanged   = 'D';
  sec_SmartCardNoCard    = 'C';
  sec_MasterKeyFail      = 'P';
  sec_WorkingKeyFail     = 'K';
  sec_FileSysError       = 'F';
  sec_AcceptBypass       = 'b';
  sec_CreditWanted       = 'c';
  sec_CashierLogOff      = 'd';
  sec_NoKeyAtTrack2      = 'n';
  sec_NeedSessionKey     = 'e';
  sec_UserInputMismatch  = 'f';
  sec_CardRemoved        = 'E';
  sec_PINError           = 'I'; // 9470

  CancelCodeChs       = ESC +'XAZWTRMNOVDCPKFbcdnefE';
  CancelCodeChars : Array[1..23] of String = ('<ESC>','<Cancelled>',
                  '<Function_Key>','<No_Data>','<Device_Error>',
                  '<Track_Read_Failure>','<Freq_Read_Failure>', '<MICR_Read_Failure>',
                  '<Signature_Capture_Failure>','<Smartcard_Failure>','<Smartcard_Write_Failure>',
                  '<SmartCard_Card_Changed>', '<SmartCard_No_Card_Inserted>',
                  '<MasterKey_Failure>', '<Working_Key_Failure>','<File_System_Error>',
                  '<Accept_Bypass>', '<CreditButtonPressed>',
                  '<Cashier_Log_Off>','<"No" Key Pressed at SlideCard>','<No Encryption Key Injected>','<User Input Mismatch>','<Card Removed>');
  { SCAT_Status }
  SCAT_Busy                 = '0';
  SCAT_Ready                = '1';

  { bioSocket status }
  bsNotConnected   = 0;
  bsConnected      = 1;
  bsJustConnected  = 2;
  bsConnectedSet : set of byte = [bsConnected, bsJustConnected];

  { Track Data }
  Track_One          = '1';
  Track_Two          = '2';
  Track_One_and_Two  = '4';

  { Parameter download }
  Global_Parms1     = 'G';
  Global_Parms2     = 'H';
  Global_Parms3     = 'I';
  Global_Parms4     = 'L';       { TSL-24 }

  { Keyboard input type  }
  K_Init               = ' ';
  K_Alpha_Numeric      = 'A';
  K_DateWithSoftKey    = 'B';
  K_Amount_With_Cents  = 'C';
  K_Amount             = 'D';
  K_DisplayWithSoftKey = 'E';
  K_Fleet_VehicleID    = 'F';
  K_Fleet_DriverID     = 'G';
  K_Fleet_Odometer     = 'H';
  K_Num_Entry_PAN      = 'K';
  K_Num_Entry_ExpDate  = 'L';
  K_Num_Entry_Cvv2     = 'M';
  K_Numeric            = 'N';
  K_One_Key            = 'O';
  K_PhoneNumber        = 'P';   { TSL-25 }
  K_CardSlide          = 'S';
  K_NumericWithSoftKey = 'T';
  K_Numeric_Masked     = 'U';
  K_Yes_No             = 'Y';
  K_ZipCode            = 'Z';
  K_EditAmount         = 'a';

  { Key Codes }
  K_Menu_One                 = 'K';
  K_Menu_Two                 = 'L';
  K_Menu_Three               = 'M';
  K_Menu_Four                = 'N';
  K_Menu_Five                = 'O';
  K_Menu_Six                 = 'P';
  K_Menu_Seven               = 'Q'; // 6207 <
  K_Menu_Eight               = 'R';
  K_Menu_Nine                = 'S';
  K_Menu_Zero                = 'T';
  K_MENU_Cancel              = 'U'; // 6207 >

  K_Enter                    = #$0D;
  K_Clear                    = #$1B;
  K_BackSpace                = #$08;
  K_Alpha                    = #$20;
  K_Asterisk                 = #$2A;
  K_Pound                    = #$23;
  K_ND                       = #$00;           { Not defined }
  K_None                     = #$21;
  K_0                        = '0';

  // key codes for Hippa Screen stuff
  K_Previous                 = 'A';
  K_Next                     = 'B';
  K_Accept                   = 'C';   
  // for DigitalID button press
  K_DigitalID                = 'W';
  CancelKey                  = 'CANCEL';  //CPCLIENTS-17930
  SCATfunctionKeys           = 'ABCDEFGHIJ'; { comes from SCAT }
  SCATscreenKeys             = 'KLMNOP';     {   "    }
  keyChs = 'ABCDEFGHIJKLMNOP'+K_Enter+K_Clear+K_BackSpace+K_Alpha+K_Asterisk+
           K_Pound+K_ND+K_None+K_DigitalID;
  keyChars : Array[1..25] of String = ('<K_Func1>','<K_Func2>','<K_Func3>','<K_Func4>',
             '<K_Func5>','<K_Func6>','<K_Func7>','<K_Func8>','<K_Func9>','<K_Func10>',
             '<K_Menu1>','<K_Menu2>','<K_Menu3>','<K_Menu4>','<K_Menu5>','<K_Menu6>',
             '<K_Enter>','<K_Clear>','<K_BackSpace>','<K_Alpha>','<K_Asterisk>',
             '<K_Pound>','<K_ND>','<K_None>','<K_DigitalID>');

  { softKey type prompts for SCAT }
  sktNone                               = 0;
  sktShaws                              = 1;
  sktEBTType                            = 2;
  sktPurchBalInq                        = 3;
  sktYesNo                              = 4;
  sktCashBackAmount                     = 5;
  sktTenderType                         = 6;
  sktBioMetricsChar                     = 7;
  sktLanguage                           = 8;
  sktDisplayWithSoftKey                 = 9;
  sktTipAmount                          = 10;
  sktCharityNames                       = 11; // CPCLIENTS-5868  // Charity Donations
  sktDonationAmount                     = 12; // CPCLIENTS-5869  // Charity Donations
  sktDisplayWithHardKeys                = 13; // 6207

  defaultBioTimer = 30;
  V490File   = 'scat490.out' ;
  VX670File  = 'S670';
  C2KFile    = 'scatc2k.hex';
  EVERFile   = 'scatever.out';
  EVERIBM    = 'sevr';                                                          
  O7000File  = 'scat7k.out';     { TSL-05 }
  O7000USB   = 's7k';
  fICE5KFile = 'scat5k0';
  fICE6KFile = 'scat6k0';
  fICE4KFile = 'scat4100';
  fTUG4KFile = 'scat4100_tug';
  f4150File  = 'scat4150';
  f4150FilePCI = 'scat4150pci';
  f4250File  = 'scat4250';
  f5300File  = 'scat5300';
  Ver        = 'SCATVER';
  Ver2       = 'REVTACS';
  Ver3       = '!SCAT';
  DEFAULTCOMBUFFERSIZE = 512;
  DEFAULTCOMBUFFERSIZEO7K = 768;
  COMBUFFERSIZE4150 = 950;
  COMBUFFERSIZE65000 = 65000;
  DEFAULTIBMCOMBUFFERSIZE = 226;
  SWIPEAGAINMSG1 = 'Swipe Card Again';
  // encryption types for SCAT
  etNone = '0';
  etP2PCAPABLE = '1';
  etP2PENCRYPTION = '2';
  etP2PENCRYPTION_MANUALENTRY = '3';    //9724

  // psuedo TACs that aren't in the GUI config screens
  Chr_MoreCardData           = '-';

  Bit128_Index_EnhancedManualEntry = 1;   //12882 12887
  Bit128_Index_IsTokenDataFromPOS = 8;   //12882  12887
  SIGN_OFF = 'SIGNOFF';

// TODO-YHJ: I moved term type related functions to here for easy maintenance
// but they should be moved to TermClass in the future.
function GetTermCharIndex (C: Char): Integer; 
function GetTermIndexByName(aTermName: string): integer;
function GetTermTypeDesc(aTermChar: char; RemovePrefix: boolean = False): string; // moved from StoreMonitoring.TermStrings
function GetTermType(aTermName: string): char; // moved from OpenEPSCalls
function MaxTermKeys(aTermType: Char): integer; // moved from

implementation

function GetTermCharIndex (C: char): integer; // TODO: move to TermClass
var
  i: integer;
begin
  result := -1;
  for i := 0 to MaxOpenEPSTermType do
    if TermTypeChars[i] = C then
    begin
      result := i;
      break;
    end;
end;

function GetTermIndexByName(aTermName: string): integer;
var i: integer;
begin
  result := -1;
  for i := 0 to MaxOpenEPSTermType do
    if SameText(TermTypeDescrip[i], aTermName) then
    begin
      result := i;
      Exit;
    end;
end;

// YHJ - moved from storemonitoring.TermStrings and renamed to GetTermTypeDesc
// UnitTest: TestRefactoring
// TODO: move to TermClass
function GetTermTypeDesc(aTermChar: char; RemovePrefix: boolean = False): string;
var i, Idx: integer;
begin
  result := 'UNKNOWN('+aTermChar+')';
  for i := 0 to MaxOpenEPSTermType do
    if aTermChar = TermTypeChars[i] then
    begin
      result := TermTypeDescrip[i];
      if RemovePrefix then
      begin
      Idx := Pos('-', result);
      if Idx > 0 then
        result := Copy(result, Idx+1, Length(result)-Idx);
      end;
      Exit;
    end;
    // updated results
    //TermTypeDesc not match (G) - Old=HYP4150PCI, New=HYP4150 PCIv2
    //TermTypeDesc not match (:) - Old=Entouch1000, New=ENT1000
    //TermTypeDesc not match (4) - Old=Entouch2100, New=ENT2100
    //TermTypeDesc not match (B) - Old=HYP4250, New=L4250
    //TermTypeDesc not match (;) - Old=UNKNOWN(;), New=Vx570
    //TermTypeDesc not match (!) - Old=SCAT6780, New=6780
end;

// moved from scat2, TODO: move to TermClass
function MaxTermKeys(aTermType: Char): integer;
var i: integer;
begin
  // TODO: KHD, TermType-related code. Move to terminal class or another unit? TermType (from DLLTypes.pas) is constant in SCATConstants.pas, and integer() typecast is enumerated type in UWinEPSConfiguration.pas
  result := 0;
  for i := 0 to MaxOpenEPSTermType do
    if aTermType = TermTypeChars[i] then
     begin
       result := MaxNumberOfKeys[i, screenKey];
       Exit;
     end;
end;

// moved from OpenEPSCalls
// UnitTest: TestRefactoring
// TODO: move to terminal class
function GetTermType(aTermName: string): char;
var i: integer;
begin
  result := tmNone;
  for i := 0 to MaxOpenEPSTermType do
    if SameText(TermTypeDescrip[i], aTermName) then
    begin
      if TermTypeChars[i] <> '' then
        result := TermTypeChars[i];
      Exit;
    end;
end;

initialization
  ExtendedLog('SCATConstants Initialization');
finalization
  ExtendedLog('SCATConstants Finalization');

end.
