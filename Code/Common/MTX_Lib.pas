// (c) Retalix, Inc. 1988-2014
{$I OpenEPS_Def.inc}
{$WARN SYMBOL_PLATFORM OFF} // Surpress platform specific Warnings - SRM @ 2005-12-13
unit MTX_Lib;

{$V-}
interface

uses
  {$IFDEF LINUX}
  IdGlobal,
  {$ELSE}
  Windows, //Forms, //CPCLIENTS-9385
  {$IFDEF GUIJR}
  gpTimezone,
  {$ENDIF}
  PsAPI,
  ShellAPI,
  {$ENDIF LINUX}
  Sysutils, StrUtils, Types,
  Classes, IniFiles,                                              // jmr-gui
  DBClient,
  DateUtils,
  TrxLog,
  //hostname,
  MTX_Constants,
  MdMsg,
  StoreConfigurationsXML,
  //ReportGroupsXML,
{$IFNDEF WOLF}
  LbClass, LbCipher, LbUtils,
{$ENDIF}
  TypInfo,
  MRTypes;                         // CPCLIENTS-14502

type
  TReplaceType = (rtHex, rtDec, rtSpace, rtBlank);
  TProgramVersion = record
    sProgram: string20; // JMR: if these are long strings, AQTime reports a memory leak
    sVersion: string20;
  end;
  THostConfigType = (hctStoreNumber, hctMerchantID, hctCurrencyCode, hctMerchantNum, hctTerminalId, hctTerminalNumber); // DOEP-34251, 47031   // TFS-14360

const
  ENGINE_DEAD_LATENCY = 180;  //seconds
  FILE_ERROR = -1; // JTG result returned by FileOpen if an error occurs
  HexChrSet: string[16] = '0123456789ABCDEF';
  NUM_VERSION_ELEMENTS = 20;         //jtg; had better be >= RequiredFileCnt

  NUMERICS = ['0'..'9'];
  NUMERICS_PLUS = ['0'..'9','-'];
  SHORTZIP = 5;
  LONGZIP = 10;
  usingServerEPSHost = TRUE;      //69739

var
  EngineDeadTime: TDateTime = 0;
  endOfDayEvent: boolean = false;
  ServiceStopEventOccurred : boolean = false;
  SettlementReqPosPending: boolean = false;
  GlobalBINFileName:       string = '';
  FSABinFileName: string = ''; // moved from scat2
  LastP_Record        : PrintRec; // moved from v490unit to here
  MDMsg_ID            : String[8] = 'STARTUP ';
  WinEPSVersion       : string;                              // TSL-W
  SpoolFileDay1       : Integer; {File;}                     // TRI-C
  shutdownCompleted   : boolean = false;
  shuttingDownEps     : boolean = false;                     // JMR-Q
  lanesUp             : boolean = false;                     // JMR-K
  LanesStatus         : string;
  DUMMY               : ARRAY[1..2] OF LONGINT;
  SessionOK           : Array[1..8] of Boolean;
  flagArray : Array[1..FLAG_ARRAY_SIZE] of boolean;           { JMR-F }
  GuiIsUp             : boolean = False;                         // jmr-gui
  host1Up             : boolean = False;                       { JMR-M }
  host2Up             : boolean = False;                       { JMR-O }
  crToDbFlag          : Boolean = False;  { JMR-A }
  Trace_Data          : Boolean = False;  { JGS-B }
  HostSuffixArray     : Array[1..WinEPS_MaxTenders] of string[3];  { suffix of active hosts }
  HostSuffixArrayi    : Array[1..WinEPS_MaxTenders] of integer;    { host num from hostName.pas }
  HostSuffixForTender : Array[1..WinEPS_MaxTenders] of string[3];  { suffix by tender type }
  HostSuffixForTenderi: Array[1..WinEPS_MaxTenders] of integer;    { host num from hostName.pas }
  HostProgNameArray   : Array[1..WinEPS_MaxTenders] of string[8];  { host program name from host.eft }
  HostERCUseArray     : Array[1..WinEPS_MaxTenders] of string[1];
  HostCallActiveHosts : integer;                    { TSL-07 }
  OffApp_Suffix       : String[3] = 'eft';
  Status_Running      : Boolean = false;
  falseValue          : boolean = false;             { JMR-J }
  emptyString255      : string255 = '';              { JMR-J }
  fValue              : boolean = false;             { TSL-02 }
  eString255          : string255 = '';              { TSL-02 }
  TermConfigDir       : string;                      { TRI-G }
  printerException    : boolean = false;
  userHasUnMaskRights : boolean = false; { does user have rights to see full account numbers? }
  userCanEnterCSVEncryptionKey : boolean = false;
  Global_OperatorNo   : Integer;                                      
  Global_Operator     : string = 'GUI';             // until this is resolved
  ProgramVersion: array [1..NUM_VERSION_ELEMENTS] of TProgramVersion;
  DraconFlag:           boolean = false;
  MTXRegistrySettingsRecord : TStringList;
  PublixDebugMsg: string; // Publix host debug message
  ApprovedTrxCount: integer; // DEV-11499
  DeclinedTrxCount: integer; // DEV-11499
  TimeToLogVisitedLanes: boolean = false; // DEV-23206
  HostAddress: string = '';         // JTG SOAP Dynamic Host Address

//function IsFuelAndWorldPay(HostSuffix: string): boolean;     //65869 // XE: Remove WinEPS - fuel
procedure GlobalVarInit;
function IsPublix(aSuffix: string): boolean;
function ConvertToDecimalSeparator(const S: string): string; //JTG 33046 - in case the English decimal point snuck in, but we are on a different decimal separator
function DLLPath(TargetFile: string): string;    // JTG Doep-25063 .. look for MTXPOSDLL in multiple places
function CreateTrxLogObject(aFileName: string; PersistentFile: boolean = false): TTrxLog;
function GetOneTagOfXMLDataFromStr(aXML: string; aParam: string): string;  //unit test in Test_MTX_Lib // XE: Remove WinEPS - not in use but keep 
//function IsKnownHost(hostSuffix: string): boolean;
function IsValidCardProcID(inRec: MdMsgRec): boolean;
function SetCardProcIDInteg(tReqCode : byte): string2;
function IsValidDateString(str: String; var aDate: TDateTime): boolean;   // JTG upgrade

function LRC(S : AnsiString) : byte;
Function LRCCorrect(inStr : AnsiString) : Boolean;

function GetLocalIP: string;
function CenterText(S: string; FinalWidth: integer): string;
function FileToString(const FileName: string): string;
function FileToAnsiString(const FileName: string): AnsiString; //CPCLIENTS-9079/9080

{$IFDEF MSWINDOWS}
function NetServerGetInfo (serverName : PWideChar; level : Integer;
        var bufptr : Pointer) : Cardinal; stdcall; external 'NETAPI32.DLL';
function NetApiBufferFree (buffer : Pointer) : Cardinal; stdcall; external 'NETAPI32.DLL';
procedure LogMemory(S: string = ''); // XE: Remove WinEPS - not in use but keep
function LogMemoryStr: string; // XE: Remove WinEPS - not in use but keep
function CurrentMemoryUsage: Cardinal; // XE: Remove WinEPS - not in use but keep

type
  SERVER_INFO_503 = record  
    sv503_sessopens : Integer;
    sv503_sessvcs : Integer;
    sv503_opensearch : Integer;
    sv503_sizreqbuf : Integer;
    sv503_initworkitems : Integer;
    sv503_maxworkitems : Integer;
    sv503_rawworkitems : Integer;
    sv503_irpstacksize : Integer;
    sv503_maxrawbuflen : Integer;
    sv503_sessusers : Integer;
    sv503_sessconns : Integer;
    sv503_maxpagedmemoryusage : Integer;
    sv503_maxnonpagedmemoryusage : Integer;
    sv503_enablesoftcompat :BOOL;
    sv503_enableforcedlogoff :BOOL;
    sv503_timesource :BOOL;
    sv503_acceptdownlevelapis :BOOL;
    sv503_lmannounce :BOOL;
    sv503_domain : PWideChar;
    sv503_maxcopyreadlen : Integer;
    sv503_maxcopywritelen : Integer;
    sv503_minkeepsearch : Integer;
    sv503_maxkeepsearch : Integer;
    sv503_minkeepcomplsearch : Integer;
    sv503_maxkeepcomplsearch : Integer;
    sv503_threadcountadd : Integer;
    sv503_numblockthreads : Integer;
    sv503_scavtimeout : Integer;
    sv503_minrcvqueue : Integer;
    sv503_minfreeworkitems : Integer;
    sv503_xactmemsize : Integer;
    sv503_threadpriority : Integer;
    sv503_maxmpxct : Integer;
    sv503_oplockbreakwait : Integer;
    sv503_oplockbreakresponsewait : Integer;
    sv503_enableoplocks : BOOL;
    sv503_enableoplockforceclose : BOOL;
    sv503_enablefcbopens : BOOL;
    sv503_enableraw : BOOL;
    sv503_enablesharednetdrives : BOOL;
    sv503_minfreeconnections : Integer;
    sv503_maxfreeconnections : Integer;
  end;
  PSERVER_INFO_503 = ^SERVER_INFO_503;
{$ENDIF}

function RandomHexString(NumDigits: integer): AnsiString;

{$IFNDEF RPT}
{$IFNDEF MTXEPSDLL}
//function  GetLaneState: string5;           { JMR-R }
function SetTenderName (var aMsgBuf: mdMsgRec): string;   //unit test in Test_MTX_Lib
{$ENDIF MTXEPSDLL}
{$ENDIF RPT}

Procedure DList(In_Str : AnsiString);
Procedure DListA(Hdr : AnsiString; Var In_Buf; In_BufL : Integer);              
Procedure InitMDMsg(var InRec : MDMsgRec);
function  BLFill(In_Str : String255; Len : Integer) : String255;
function  BoolStr(In_Bool : Boolean) : String5;
function  BRFill(In_Str : String255; Len : Integer) : String255;
function  Fnhhmmss(UseSeparators : boolean) : string8;
function  Fnmmddyy(UseSeparators : boolean) : string8;
function  Fnyymmdd: string6;
function FnYYYYMMDD: string;
function  Get_Extended_Time(QPlusSecs : LongInt) : LongInt;
function  aJulianDate(aDate: string6 {yymmdd}) : integer;
function  LFill(s : string255; c : char; l : word) : string255;
function  RFill(s : string255; c : char; l : word) : string255;
function MyVal(S: String255): longint;
function PackStr_(S: string255): string255;
function Reg_Lookup(FileName: string255; const In_Key : String; Spool_It : Boolean; IsTrim: boolean = true) : String255;
function  Reg_Save(In_Key : String255; In_Text : string255; FileName: string=''; logIt: boolean=true): Boolean; // TSL-B // XE: Remove WinEPS - not in use but keep
function  Rpad(s : string255; l : byte) : string255;
function  Lpad(S: string; L: integer; PadChar: char = ' '): string;  // JTG
function  Rtrim(s : string255) : string255;
function  Ltrim(s : string255) : string255; // XE: Remove WinEPS - not in use but keep
function  Str_(I : LongInt) : String255;
function  String_(I : Integer; S_ : Char) : String255;
function  TwoDigits(D: Integer) : String2;
function  ZDel(S : String255; L : Integer) : String255;
function  ZFill(S : String255; L : Integer) : String255;
procedure SM(OutMessage: AnsiString; IsInTrans: TIsInTrans = iitTrue; truncate: boolean = true);    // CPCLIENTS-14502
Procedure SMUserActivity(msg: string; aUsername: string = '');
function tenderNum(aReqCodeN: byte): integer;
function  tenderIndex(inRec: mdMsgRec): integer;  { TSL-06 }
{$IFDEF MSWINDOWS}
  procedure WriteToEventLog(str1: string; eventType: word); { JMR-X }
{$ENDIF}
procedure TraceDebug(msg: string); overload;
procedure MsgDebug(Msg: string); overload;
procedure MsgDebug(Msg: string; const Args: array of const); overload;
procedure MsgLog(Msg: string; IsInTrans: TIsInTrans = iitTrue); overload;
procedure MsgLog(Msg: string; const Args: array of const; IsInTrans: TIsInTrans = iitTrue); overload;
procedure MsgNotice(Msg: string; IsInTrans: TIsInTrans = iitTrue); overload;
procedure MsgNotice(Msg: string; const Args: array of const; IsInTrans: TIsInTrans = iitTrue); overload;
procedure MsgWarn(Msg: string; IsInTrans: TIsInTrans = iitTrue); overload;
procedure MsgWarn(Msg: string; const Args: array of const; IsInTrans: TIsInTrans = iitTrue); overload;
procedure MsgError(Msg: string; IsInTrans: TIsInTrans = iitTrue); overload;
procedure MsgError(Msg: string; const Args: array of const; IsInTrans: TIsInTrans = iitTrue); overload;

function GetTickCountExt: Cardinal;
//function GetApplicationPath: string; // XE: Remove WinEPS - not in use but keep //CPCLIENTS-9385
function TheOSversion: string;         { TSL-A }
{$IFDEF MSWINDOWS}
function GetCurrentUserAndDomain: string; // XE: Remove WinEPS - not in use but keep
function IsLocalSystemAccount: boolean; // DEV-28948 // XE: Remove WinEPS - not in use but keep
function RemoveReadOnlyAttribute(Filename: string): boolean; // XE: Remove WinEPS - not in use but keep
function XPFirewallAddAllowedProgram(exeName: string; programName: string): Boolean; { TRI-H } // XE: XE: Remove WinEPS - not in use but keep
function XPFirewallAddPort(Name: string; Port: Integer): Boolean; { TRI-I } // XE: XE: Remove WinEPS - not in use but keep
//function GetValueFromIniFile(sect, ident, default, filename: string) : string; overload; // XE: Remove WinEPS - not in use but keep //CPCLIENTS-9385
//function GetIntValueFromIniFile(sect,ident: string; default: integer;filename: string): integer; overload; // XE: Remove WinEPS - not in use but keep //CPCLIENTS-9385
function ExecuteAndWait(const CommandLine : string; secondsToWait: cardinal) : cardinal;
{$ENDIF}

function iif(Condition: Boolean; TrueResult, FalseResult: Variant): Variant; overload;
function PrintableStr(SourceStr: AnsiString; ReplaceType: TReplaceType = rtHex): AnsiString;
function PrintHex(aMsg: AnsiString): string;
function PrintHexOnly(const aMsg: AnsiString): String;  // TFS-36143
function getSCATVersionFromFileName(aSearch: string; aType: char): string;
{$IFDEF MSWINDOWS}
function GetSystemFolder: string; // XE: Remove WinEPS - not in use but keep  
{$ENDIF}

{$IFDEF TEST}   // to expose the functions to testing
//Function QFlagAvailable(flag: Byte) : boolean;
//Procedure QGetFlag(flag: Byte);
//Procedure QReleaseFlag(flag: Byte);
function RemoveReadOnlyAttributesThisFolder(sDir: string): boolean; // XE: Remove WinEPS - not in use but keep
{$ENDIF}
function MakeTOADFormat(aMICRData: string): string;
function GetArrayText(Source: string; Index, TextSize: Integer): string;
function YN(B: boolean): Char;
function TF(B: boolean): Char;
function TrueFalse(B: boolean): string; 
function FmtXmlToWrite(aXML: string): string;
function FmtXmlToRead(aXML: string): string;
function GetValidXMLVersion(XMLFile: TMTX_XMLFile): Double;

function GetFileDateStr(Filename: string): string;   // JTG 23296
function GetVersionInfo(Filename : String; SearchKey : String) : string;
function GetVersionString(Filename,VerStr: string): string;
function GetLatestVersionFile(filePattern: string; verPos, verLen: integer): string;
procedure replaceSpecialXMLChars(var inStr: string);
function DeleteFiles(aPattern: string; aLog: boolean=false): boolean;
function IsNumeric(aValue: string): boolean; overload; // DEV-7810
procedure RemoveDirAndFiles(const aDir: String); //CPCLIENTS-9077/9078
// SSL
function ByteStrToHexStr(S: AnsiString): AnsiString;
function StrToHexStr(S: AnsiString; GroupSize: integer): AnsiString;
function HexStrToByteString(aHex: AnsiString): AnsiString;  //converts every 2 Hex char to 1 integer byte str
function StrXOR(S1,S2: AnsiString; ComparisonBytes: integer): AnsiString; // XE: Remove WinEPS - not in use but keep
function HexToB64(aHex: AnsiString): AnsiString;
function B64ToHex(B64: AnsiString): AnsiString;
function IsValidMMDD(aMMDD: string): boolean; // DEV-12505
function BuildHexString(St : AnsiString) : AnsiString; // DEV-11196 // XE: Remove WinEPS - not in use but keep
function FileSizeInMB(Filename: string): double;  // pass in Filename, get back size in MB
function FileSizeInBytes(Filename: string): integer;  // pass in Filename, get back size in bytes   Dev 25063
{$IFNDEF WOLF}
function GetMD5Hash(const s: AnsiString): string;
function GetMD5HashFromFile(const fileName: string): AnsiString; // XE: Remove WinEPS - not in use but keep
{$ENDIF}
function IsHexDigit(const s: AnsiString): boolean;
function EngineRecentlyDead: boolean;
function FilePieceToString(const FileName: string; var aFilesize: integer; const Piece: integer; const PieceSize: integer; var LogMsg: string): string;
function FilePieceToStringAnsi(const FileName: string; var aFilesize: integer; const Piece: integer; const PieceSize: integer; var LogMsg: string): AnsiString;
procedure LogLIM(ModuleName: string); // DEV-31929: Location In Memory // XE: Remove WinEPS - not in use but keep
function DoubleStrCorrectedForDecimalSeparator(S: string): string; // JTG 32650
function GetHostConfigFromXML(const HostName : string; const HostConfigType : THostConfigType) : string; // DOEP-34251
function IsExpDateOK(aDate: string): boolean;   // YYMM is expected here
function WinEPSTenderToOpenEPS(aWinEPSTender: integer): integer;
function GetMaxLanesFromSetupTxt(aDir: string=''): integer;
function CleanMD5(aMD5: string): string;
function IsHostOneOf(aHost, aHostList: string): boolean; overload;   // TFS-16036                        // CPCLIENTS-10869
function IsHostOneOf(const aHost: string; const aHostListToCheck: array of string): boolean; overload;   // CPCLIENTS-10869
function GetLaneType(LaneNumber: string): string;
procedure CheckIniFiles;                                             // CPCLIENTS-11670
function IniFileKeyIsBad(const AKey: string): boolean;                      // CPCLIENTS-11670
function FixAnyErrorsInIniFile(const AFileName: string): boolean;    // CPCLIENTS-11670
procedure ThrottleIfNecessary(ADelayMS: integer; ALastMsgSentAt, ALastMsgRcvdAt: TDateTime);      // CPCLIENTS-15794

implementation

{$IFDEF MSWINDOWS}
  {$r fisEVT.res} { needed for Event Logging } { JMR-X }
{$ENDIF}

uses  {$IFDEF MTXEPSDLL}
      epsTrace, { need this to call procedure ShowTrace }
      {$ENDIF MTXEPSDLL}
      WinSock,
      MTX_XMLClasses,
      MTX_Utils,
      MTXEncryptionUtils,
      StringUtils,
      Base64,
      FinalizationLog,
      SyncObjs,
      System.IOUtils,
      DLLTypes,                 // CPCLIENTS-11670
      LanesXML,                 // CPCLIENTS-13825
      GeneralUtilities;         // CPCLIENTS-15794

const
  YesAltZ                              = 18;
  NoAltZ                               = 17;
  DontSlice                            = 13;
  DoSlice                              = 14;
  SendCmd                              = 15;
  GiveUpSlice                          = 0;
  OutputMessage                        = 4;
  CheckforMessage                      = 5;
  ReceiveMessage                       = 6;
  GetTheFlag                           = 1;
  ReleaseTheFlag                       = 2;
  CheckTheFlag                         = 16;

var
(* // XE: Remove WinEPS
{$IFNDEF RPT}
  {$IFNDEF MTXEPSDLL}                                     { JMR-4 }
      Err32Lines                           : TStrings;    { TSL-X }
      LastLinePrinted                      : string255;
      SaveMessage_                         : String255;
  {$ENDIF MTXEPSDLL}
{$ENDIF RPT}
*)

  specialXMLChars: array[1..5] of string = ('&','<','>','"',#39);
  replaceXMLChars: array[1..5] of string = ('&amp;','&lt;','&gt;','&quot;','&apos;');
  SMLock: TCriticalSection;
  RegLock: TCriticalSection;

procedure SMAcquireLock;        // JTG
begin
  if SMLock = nil
    then SMLock := TCriticalSection.Create;
  SMLock.Acquire;
end;

procedure SMDestroyLock;        // JTG
begin
  if SMLock <> nil
    then FreeAndNil(SMLock);
end;

procedure SMReleaseLock;         // JTG
begin
  if SMLock <> nil
    then SMLock.Release;
end;

procedure RegAcquireLock;        // JTG
begin
  if RegLock = nil
    then RegLock := TCriticalSection.Create;
  RegLock.Acquire;
end;

procedure RegDestroyLock;        // JTG
begin
  if RegLock <> nil
    then FreeAndNil(RegLock);
end;

procedure RegReleaseLock;         // JTG
begin
  if RegLock <> nil
    then RegLock.Release;
end;

function IsPublix(aSuffix: string): boolean;
begin
  result := SameText(aSuffix,'PBL') or SameText(aSuffix,'PB2') or SameText(aSuffix,'PB3');
end;

function EngineRecentlyDead: boolean;
begin
  result := SecondsBetween(EngineDeadTime,Now) < ENGINE_DEAD_LATENCY;
end;

//JTG 32650 takes an existing string of a double that might have be made under a different language setting, and
//converts that string to use whatever the current system DecimalSeparator is
function DoubleStrCorrectedForDecimalSeparator(S: string): string;
const
  KNOWNDECIMALSEPARATORS = [',','.'];
var
  i: integer;                       //we are assuming the input string is in format for doubles (3.0, 23,0 etc)
  FmtSettings: TFormatSettings;
begin                               //and also, assuming there is NO trinary separator.
  GetLocaleFormatSettings(LOCALE_SYSTEM_DEFAULT, FmtSettings);
  for i := length(S) downto 1 do    //go backward to get decimal point, then later, if there is a need, continue on for any trinary seps
    if S[i] in KNOWNDECIMALSEPARATORS then
      begin
      // S[i] := DecimalSeparator;      //32650                                 // 828.5
      S[i] := FmtSettings.DecimalSeparator;
      break;
      end;
  result := S;
end;

function FileSizeInMB(Filename: string): double;  // pass in Filename, get back size in MB
const
  MB = 1024.0*1024.0;
var
  f: file of byte;
  OldFileMode: integer;
begin
  result := 0.0;
  if FileExists(Filename) then
    try
      assignfile(f,Filename);
      OldFileMode := FileMode;
      FileMode := fmOpenRead + fmShareDenyNone;
      reset(f);
      try
        result := FileSize(f)/MB;
      finally
        closefile(f);
        FileMode := OldFileMode;
      end;
    except on e: exception do
      SM(format('MTX_Lib.FileSizeInMB EXCEPTION(%s) attempting to get file size for [%s]',[e.message,Filename]));
    end
  else
    SM(format('MTX_Lib.FileSizeInMB UNABLE to find file (%s) ',[Filename]));
end;

function FileSizeInBytes(Filename: string): integer;  // pass in Filename, get back size in bytes   Dev 25063
var
  f: file of byte;
  OldFileMode: integer;
begin
  result := -1;      // will show an error; 0 means no file.
  if FileExists(Filename) then
    try
      assignfile(f,Filename);
      OldFileMode := FileMode;
      FileMode := fmOpenRead + fmShareDenyNone;
      reset(f);
      try
        result := FileSize(f);
      finally
        closefile(f);
        FileMode := OldFileMode;
      end;
    except on e: exception do
      SM(format('MTX_Lib.FileSizeInBytes EXCEPTION(%s) attempting to get file size for [%s]',[e.message,Filename]));
    end
  else
    begin
    result := 0;
    SM(format('MTX_Lib.FileSizeInBytes UNABLE to find file (%s) ',[Filename]));
    end;
end;

function FileToByteArray1(const FileName: string): TByteDynArray;
const
  BLOCK_SIZE = 1024;
var
  BytesRead,BytesToWrite,Count: integer;
  f: file of byte;
  pTemp: pointer;
begin
  AssignFile(f,FileName);
  reset(f);
  try
    Count := FileSize(f);
    SetLength(result,Count);
    pTemp := @result[0];
    BytesRead := BLOCK_SIZE;
    while BytesRead = BLOCK_SIZE do
      begin
      BytesToWrite := min(Count,BLOCK_SIZE);
      BlockRead(f,pTemp^,BytesToWrite,BytesRead);
      pTemp := Pointer(LongInt(pTemp) + BLOCK_SIZE);
      dec(Count,BytesRead);
      end;
  finally
    CloseFile(f);
  end;
end;

function FileToByteArray(const FileName: string): TByteDynArray;
var
  myfile: TFileStream;
  Count,aFileSize: Int64;
  f: file of byte;
  OldFileMode: byte;
begin
  OldFileMode := FileMode;    // save original filemode
  aFileSize := 0;
  SetLength(result,aFileSize);
  FileMode := fmOpenRead +fmShareDenyNone;
  AssignFile(f,FileName);

  reset(f);
  try
    Count := FileSize(f);  // Count is Int64, but we only need a regular integer for the parameter...
    aFileSize := Count;
  finally
    CloseFile(f);
  end;

  SetLength(result,aFileSize);
  if Count > 0 then
    begin
    myFile := TFileStream.Create(FileName,fmShareDenyNone);
    try
      myFile.Seek(0,soFromBeginning);
      myFile.ReadBuffer(result[0],Count);
      aFileSize := length(result);
    finally
      myFile.Free;
      if aFileSize = 0 then
        result := FileToByteArray1(Filename);
    end;
    end;
  FileMode := OldFileMode;    // revert to original
end;

function ByteDynArrayToStr(const aByteArray: TByteDynArray): string;
var
  i: integer;
begin
  SetLength(result,length(aByteArray));
  for i := 0 to length(aByteArray)-1 do
    result[i+1] := chr(aByteArray[i]);
end;

//CPCLIENTS-9079/9080
function ByteDynArrayToAnsiStr(const aByteArray: TByteDynArray): AnsiString;
var
  i: integer;
begin
  SetLength(result,length(aByteArray));
  for i := 0 to length(aByteArray)-1 do
    result[i+1] := AnsiChar(aByteArray[i]);
end;

function FileToString(const FileName: string): string;
var
  FileBytes: TByteDynArray;
begin
  result := '';   // have a default in case one of the following two funcs fail
  FileBytes := nil;
  try
    FileBytes := FileToByteArray(FileName);
    result := ByteDynArrayToStr(FileBytes);
  except on e: exception do
    SM(format('FileToString exception (%s) for file %s',[e.Message,Filename]));
  end;
end;

//CPCLIENT-9079/9080
function FileToAnsiString(const FileName: string): AnsiString;
var
  FileBytes: TByteDynArray;
begin
  result := '';   // have a default in case one of the following two funcs fail
  FileBytes := nil;
  try
    FileBytes := FileToByteArray(FileName);
    result := ByteDynArrayToAnsiStr(FileBytes);
  except on e: exception do
    SM(format('FileToAnsiString exception (%s) for file %s',[e.Message,Filename]));
  end;
end;

function CenterText(S: string; FinalWidth: integer): string;
var
  i: integer;
begin
  result := trim(S);
  i := (FinalWidth - length(result)) div 2;
  result := rpad(lpad(result,FinalWidth - i,' '),FinalWidth);
end;

function CreateTrxLogObject(aFileName: string; PersistentFile: boolean = false): TTrxLog;
begin
  result := nil;
end;

function GetOneTagOfXMLDataFromStr(aXML: string; aParam: string): string; // XE: Remove WinEPS - not in use but keep
var
  i: integer;
begin
  aParam := '<'+aParam+'>';   // look for ONLY a tag, otherwise POS func might hit data as well
  i := pos(aParam,aXML);
  if i > 0 then
    try
      delete(aXML,1,i+length(aParam)-1);
      i := pos('<',aXML);           { get position at end of value }
      result := LeftStr(aXML,i-1);  { copy in the value }
    except
      result := '';
    end
  else
    sm('****WARNING: No XML Tag ' + aParam + ' line found in data string >' + aXML + '<');
end;  { GetOneTagOfXMLDataFromStr }

// return true if this is a host that we support (in hostname.pas)
//function IsKnownHost(hostSuffix: string): boolean;    //unit test in Test_MTX_Lib
//var i: integer;
//begin
//  result := false;
//  try
//    hostname.GetHostNames;
//    for i := 1 to hostname.NumHosts do
//    begin
//      if SameText(HostProgName_Array[i], hostSuffix) then
//      begin
//        result := true;
//        break;
//      end;
//    end;
//    if result = false then
//      if SameText(hostSuffix, LM2) then
//        result := true;
//    {$IFDEF FUEL}
//    { // XE: Remove WinEP - not for OpenEPS but JIC
//    if (result = false) and SameText(hostSuffix, 'EPS') then
//      result := true;
//    }
//    {$ENDIF FUEL}
//  except
//    on e : Exception do
//      SM('MTX_LIB.IsKnownHost exception. >' + hostSuffix + '<. ' + e.message);
//  end;
//end;

{$IFNDEF MTXEPSDLL}
function SetTenderName(var aMsgBuf: mdMsgRec): string;   //unit test in Test_MTX_Lib
begin
  case aMsgBuf.ReqCodeN of
    DbPurchN..DbBalInqN:                      result := tnDebit;
    CrPurchN..CrReturnWithValidN:             result := tnCredit; // DOEP-33267, 33268, 19227 - As MtPurch = CrPurch
    PropDbPurchN..PropDbBalInqN,
    PropDbPreAuthN, PropDbPreAuthCompN:       result := tnPrivateDebit; // CPCLIENTS-12016
    PropCrPurchN..PropCrBalInqN:              result := tnPrivateCredit;
    CheckPurchN..CheckBalInqN:                result := tnCheck;
    EBT_FS_PurchN..EBT_FS_BalInqN:            result := tnEBT_FS;
    EBT_Cash_PurchN..EBT_Cash_BalInqN:        result := tnEBT_CA;
    User_1_PurchN..User_1_BalInqN:            result := tnGiftCard;
    FleetPurchN..FleetPreAuthCompN:           result := tnFleet;
    User_2_PurchN,User_2_OverrideN,
    User_2_VoiceN,User_2_BalInqN:             result := tnPhoneCard;
    User_2_ReturnN:                           result := tnEBT_FS;
    wireLessActivationN,
    wireLessDeactivateN,
    wireLessPreAuthN:                         result := tnWirelessPhone;     // CPCLIENTS-11904
    achPurchN..achReturnN:                    result := tnACH;
    ConnectPayPurchN, ConnectPayReturnN:      result := tnConnectPay;
    eWicBalInqN..eWicPreAuthCompN:            result := tnEWic;
    else result := tnCredit;
  end;
end;   { SetTenderName }
{$ENDIF}

function IsValidCardProcID(inRec: MdMsgRec): boolean; //unit test in Test_MTX_Lib
begin
  result := StrToIntDef(inrec.CardProcID,0) in [WinEPS_Credit..WinEPS_MultiTender]; //19132 //  CPCLIENTS-9586 Extended value for new Tender Type
end;

Function SetCardProcIDInteg(tReqCode : byte): string2;
begin
  result := CrProcID;    // just set something valid
  Case tReqCode of
    DbPurchN..DbBalInqN, WorkingKeyReqN : result := DbProcID;
    CrPurchN..CrReturnWithValidN, MtPurchN : result := CrProcID; //DOEP-33267,33268,19227
    PropDbPurchN..PropDbBalInqN,
    PropDbPreAuthN, PropDbPreAuthCompN : result := PropDbProcID;  // CPCLIENTS-12016
    PropCrPurchN..PropCrBalInqN : result := PropCrProcID;
    CheckPurchN..CheckBalInqN   : result := CkProcID;

    EBT_FS_PurchN..
    EBT_FS_BalInqN,
    User_2_ReturnN              : result := EBT_FS_ProcID;

    EBT_Cash_PurchN..EBT_Cash_BalInqN : result := EBT_Cash_ProcID;
    User_1_PurchN..User_1_BalInqN, User_1_CashOutN     : result := User1ProcID;   //CPCLIENTS-19419: add support for GC cashout

    FleetPurchN,                      { actually, fleet not used in stand beside }
    FleetReturnN,
    FleetPreAuthN,
    FleetPreAuthCompN                 : result := FleetProcID;

    User_2_PurchN,
    User_2_OverrideN,
    User_2_VoiceN,
    User_2_BalInqN                    : result := User2ProcID;

    WirelessActivationN,
    WirelessDeactivateN,
    WirelessPreAuthN                  : result := WireLessProcID;     // CPCLIENTS-11904

    AchPurchN..AchReturnN             : result := AchProcID;
    ConnectPayPurchN, ConnectPayReturnN : result := ConnectPayProcID;
    eWicBalInqN..eWicPreAuthCompN     : result := eWicProcID;
    BenefitPurchaseN,
    BenefitBalanceInquiryN,
    BenefitReturnN,
    BenefitItemQualificationN  : result := BenefitsProgramProcID; // CPCLIENTS-9586 Assigning Card Processing Id that supports Transaction Type
    else
      sm('****WARNING:  SetCardProcID - ReqCodeN = ' + intToStr(tReqCode) + ' not a valid Request Code, credit assumed');
  end; { of Case tReqCode }
end;

{$IFDEF MSWINDOWS}

{--------------------------------------------------------------------------
Name:         ExecAndWait
Description:  Executes an external executable and waits for its completion
             before continuing, returning the exit code of the process

             Return :  If CreateProcess() or the function fails for any
                       other reason, the return value is ErrUINT
                       otherwise,
                       When the spawned process is signaled as expected,
                       returns the exit code of the spawned process
}

function ExecuteAndWait(const CommandLine: string; SecondsToWait: cardinal): cardinal;
const
  ErrUINT = high(cardinal);
var
  tSI: TStartupInfo;
  tPI: TProcessInformation;
  dwI: DWORD;
begin
  result := ErrUINT;
  try
    fillchar(tSI,sizeof(TStartupInfo),0);
    tSI.cb := sizeof(TStartupInfo);
    tSI.dwflags := STARTF_USESHOWWINDOW;                                { jmr }
    tSI.wShowWindow := SW_HIDE;                                             { jmr }
    if CreateProcess(nil,pchar(CommandLine),nil,nil,false,0,nil,nil,tSI,tPI) then
      begin
      dwI := WaitForSingleObject(tPI.hProcess,SecondsToWait*1000);  { jmr }
      if dwI = WAIT_OBJECT_0 then
        if GetExitCodeProcess(tPI.hProcess,dwI) then
          result := dwI;
      CloseHandle(tPI.hProcess);
      CloseHandle(tPI.hThread);
      end;
    SM(Format('ExecuteAndWait: %s (ExitCode = %d)', [iif(result=0, 'Success', 'Failure'), result]));
  except on e: exception do
    SM('ExecuteAndWait EXCEPTION: '+e.Message);
  end;
end;

//CPCLIENTS-9385
// XE: Remove WinEPS - not in use but keep
//function GetValueFromIniFile(sect,ident,default,filename: string): string; overload;  //unit test in Test_MTX_Lib
//var
//  iniFile : TIniFile;
//begin
//  iniFile := TIniFile.Create(IncludeTrailingPathDelimiter(SysUtils.ExtractFilePath(Application.EXEName)) + filename);
//  try
//    result := iniFile.ReadString(sect,ident,default);
//  finally
//    iniFile.Free;
//  end;
//end;

//CPCLIENTS-9385
// XE: Remove WinEPS - not in use but keep
//function GetIntValueFromIniFile(sect,ident: string; default: integer; filename: string): integer; overload;  //unit test in Test_MTX_Lib
//var
//  iniFile : TIniFile;
//begin
//  iniFile := TIniFile.Create(IncludeTrailingPathDelimiter(SysUtils.ExtractFilePath(Application.EXEName)) + filename);
//  try
//    result := iniFile.ReadInteger(sect,ident,default);
//  finally
//    iniFile.Free;
//  end;
//end;
{$ENDIF}  // actually, the end of the "IFNDEF LINUX"

function GetLocalIP: string;    //unit test in Test_MTX_Lib
{$IFDEF MSWINDOWS}
var
  wsaData: TWSAData;
  addr: TSockAddrIn;
  Phe: PHostEnt;
  szHostName: array[0..128] of AnsiChar;
{$ENDIF}  
begin
  Result := '';
  {$IFDEF MSWINDOWS}
  if WSAStartup($101, WSAData) <> 0 then
    Exit;
  try
    if GetHostName(szHostName, 128) <> SOCKET_ERROR then
    begin
      Phe := GetHostByName(szHostName);
      if Assigned(Phe) then
      begin
        addr.sin_addr.S_addr := longint(plongint(Phe^.h_addr_list^)^);
        Result := inet_ntoa(addr.sin_addr);
      end;
    end;
  finally
    WSACleanup;
  end;
  {$ENDIF}
end;

{$IFDEF MSWINDOWS}
procedure WriteToEventLog(str1: string; eventType: word);  { JMR-X }
var
  Strings: Array[0..0] Of PChar;
  hEventLog : integer;   // handle returned by RegisterEventSource
const
  LOGMSG_INFO = $40020001;
begin
  try
    Strings[0]:= PChar(str1);
    hEventLog := RegisterEventSource(NIL, pchar('WinEPS'));
    if
    ReportEvent(
      hEventLog,	                 // handle returned by RegisterEventSource
      eventType,	                 // event type to log
      0,	                         // event category
      LOGMSG_INFO,               	 // event identifier
      nil,	                 // user security identifier (optional)
      1,                           // number of strings to merge with message
      0,	                         // size of binary data, in bytes
      @Strings,	                 // array of strings to merge with message
      nil                     	 // address of binary data
    ) = false then
      sm('****WARNING:  COULD NOT WRITE THIS MSG TO EVENT LOG: ' + str1);
    DeregisterEventSource(hEventLog);   // close log
  except
    on e : Exception do
      SM('MTX_LIB.WriteToEventLog exception writing >' + str1 + '<.  ' + e.message);
  end;
end;
{$ENDIF}

//CPCLIENTS-9385
//function GetApplicationPath: string; // XE: Remove WinEPS - not in use but keep
//begin
//  {$IFDEF LINUX}
//    result := HARDPATH;
//  {$ELSE}
//    result := ExtractFileName(Application.ExeName);
//  {$ENDIF}
//end;

procedure DList(In_Str: AnsiString);
var
  i: integer;
  S: AnsiString;                                                          
const
  MAX_LINE_LENGTH = 82; // Bill no like line wraps
begin
  // JTG: moved defines to encompass whole proc, since does nothing otherwise anyway
  {$IFNDEF RPT}                                                 { JMR-D }
  if Trace_Data then
    begin
    S := '';
    for i := 1 to length(In_Str) do      { TSL-P }
      if In_Str[i] in [' '..#$7E]
        then S := S + In_Str[i]
        else S := format('%s[%.2x]',[S,ord(In_Str[i])]);
    while length(S) > 0 do
      begin
      SM(copy(S,1,MAX_LINE_LENGTH));
      delete(S,1,MAX_LINE_LENGTH);
      end;
    end;
  {$ENDIF RPT}                                                  { JMR-D }
end;

Procedure DListA(Hdr : AnsiString; Var In_Buf; In_BufL : Integer);              
Type
  In_Array = Array[1..65535] of Byte;
Var
  I,
  Where,
  Count    : Integer;
  DebugStr : AnsiString;
Begin
  If (Trace_Data = False) Then
    Exit;
  Where := 1;
  While (In_BufL > 0) Do                         { While chrs to print }
    Begin
      DebugStr := Hdr;
      If (In_BufL >= 240-Length(DebugStr)) Then    { If'n more'n 3 lines }
        Begin
          Count := 240-Length(DebugStr);
          For I := 0 to Count-1 Do
            DebugStr := DebugStr + Chr(In_Array(In_Buf)[Where+I]);
          DList(DebugStr);
          Inc(Where, Count);
	  Dec(In_BufL, Count);
          Hdr := '';
        End
      Else
        Begin
          For I := 0 to In_BufL-1 Do
            DebugStr := DebugStr + Chr(In_Array(In_Buf)[Where+I]);
          DList(DebugStr);
          In_BufL := 0;
        End;
    End;
End;

Procedure InitMDMsg(var InRec : MDMsgRec);
begin
  FillChar(InRec,SizeOf(InRec),#0);
end;

//jtg refactor to use intrinsic function; make 8 lines into 1
function  String_(i: integer; S_: char): string255;   //unit test in Test_MTX_Lib
begin
  result := StringOfChar(S_,i);
end;

function Str_(i: longint): string255;   // jtg refactored 6 lines into 1   //unit test in Test_MTX_Lib
begin
  str(i,result);
end;

function MyVal(S: String255): longint; // jtg refactored 8 lines into 1   //unit test in Test_MTX_Lib
begin
  result := StrToIntDef(PackStr_(S),0);
end;

function PackStr_(S: string255): string255;  //jtg refactor
var
  i: integer;
begin
  result := S;
  i := 1;
  while (length(result) > 0) and (i <= length(result)) do
    if result[i] = ' '
      then delete(result,i,1)
      else inc(i);
end;

// jtg refactor 8 lines into 1
function TwoDigits(D: integer): string2;  { Returns string = XX or 0X }  //unit test in Test_MTX_Lib
begin
  result := format('%2.2d',[D mod 100]);  // returns only last 2 digits if > 2 digits
end;

function Get_Extended_Time(QPlusSecs: longInt): longInt;  //jtg refactored to 1 line   //unit test in Test_MTX_Lib
begin
  result := SecondsBetween(Now,EncodeDateDay(BASE_YEAR_2000,1)) + QPlusSecs;
end;

function fnyymmdd: string6;  // jtg refactored 13 lines into 1      //unit test in Test_MTX_Lib
begin
  result := formatdatetime('yymmdd',Today);
end;

function FnYYYYMMDD: string;
begin
  result := formatdatetime('yyyymmdd',Today);
end;

function FnMMDDYY(UseSeparators: boolean): string8; // jtg refactored 24 lines into 3   //unit test in Test_MTX_Lib
begin
  if UseSeparators
    then result := formatdatetime('mm/dd/yy',Today)
    else result := formatdatetime('mmddyy',Today);
end;

function FnHHMMSS(UseSeparators: boolean): string8; // jtg refactored 24 lines into 3  //unit test in Test_MTX_Lib
begin
  if UseSeparators
    then result := formatdatetime('hh:mm:ss',Time)
    else result := formatdatetime('hhmmss',Time);
end;

function Rtrim(s: string255): string255;   //unit test in Test_MTX_Lib
begin
  result := TrimRight(S);  //JTG: this intrinsic func also trims control chars & handles widestrings
end;

function Ltrim(s: string255): string255;   //unit test in Test_MTX_Lib // XE: Remove WinEPS - not in use but keep
begin
  result := TrimLeft(S);  //JTG: this intrinsic func also trims control chars & handles widestrings
end;

function Rpad(s: string255; l: byte): string255;
begin
  result := RFill(s,' ',l);
end;

// JTG
function Lpad(S: string; L: integer; PadChar: char = ' '): string;
begin
  result := s;
  while length(result) < L do
    result := PadChar + result;
end;

function BLFill(In_Str: string255; Len: integer): string255;  //unit test in Test_MTX_Lib
begin
  result := LFill(In_Str,' ',Len);
end;

function BRFill(In_Str: String255; Len: Integer): String255;
begin
  result := RFill(In_Str,' ',Len);
end;

function LFill(s: string255; c: char; l: word): string255;
begin
  if (length(s) < l)
    then result := StringOfChar(c, l-length(s)) + s
    else result := s;
end;

function RFill(s: string255; c: char; l: word): string255;
begin
  if (length(s) < l)
    then result := s + StringOfChar(c, l-length(s))
    else result := s;
end;

function ZFill(S: String255; L: Integer): String255;
begin
  result := LFill(S, '0', L);
end;

function ZDel(S: String255; L: Integer): String255;
begin
  result := S;
  while length(result) > L do
    if result[1] = '0'
      then delete(result,1,1)
      else exit;
end;

function aJulianDate(aDate: string6 {yymmdd}) : integer; // unit test in Test_MTX_Lib
/// <summary>
/// returns an integer for the number of days since Jan 1, 2000, where 01/01/2000 = 0
/// </summary>
/// <input>
/// 6 character string in the format YYMMDD
/// </input>
/// <output>
/// an integer, -1 to 36524 [for input = '991231']
/// return value of -1 indicates conversion failure, due to improper input, ostensibly
/// </output>
/// <failure_modes>
/// this function is known to be used in SmartWIC, where it is converted to four digit strings
///  thus resulting in failure on 05/19/2027 where the result will be 10,000; while
///  this is not a failure of this function, it may cause a failure elsewhere.
/// </failure_modes>
const
  ERROR = -1;
var
  aYear,aMonth,aDay,CurrentCentury: integer;
  T1,T2: TDateTime;
begin
  try
    T1 := EncodeDate(2000,1,1);   // The Julian's origin is Jan 1 2000, not Cesaerian

    aYear := strToIntDef(copy(aDate,1,2),-1);
    aMonth := strToIntDef(copy(aDate,3,2),-1);
    aDay := strToIntDef(copy(aDate,5,2),-1);

    if (aYear >= 0) and (aMonth > 0) and (aDay > 0) then
      begin
      CurrentCentury := (CurrentYear div 100) * 100; // So Century = 2000, 2100, 2200, etc...
      aYear := CurrentCentury + aYear;

      if TryEncodeDate(aYear,aMonth,aDay,T2)
        then result := DaysBetween(T2,T1)
        else result := ERROR;
      end
    else
      result := ERROR;
  except on e:exception do
    result := ERROR;
  end;
end;

function BoolStr(In_Bool: boolean): string5; // unit test in Test_MTX_Lib  // jtg refactored to one line
const
  STR_TF: array[boolean] of string5 = (('False'),('True'));
begin
  result := STR_TF[In_Bool];
end;

// JTG refactor Reg_Lookup to use TStringList's intrinsic method 'Values'
function Reg_Lookup(FileName: string255; const In_Key: string; Spool_It: boolean; IsTrim: boolean = true): String255;  // unit test in Test_MTX_Lib
var
  Reg: TStringList;
begin
  try
    CheckIniFIles;    // CPCLIENTS-11670   This code seems to be the first code hit even before sign on, so check here first
    result := '';
    RegAcquireLock;
    try
      {$IFDEF MTXPOSDLL} // JMR: why is this here?
        if SameText(FileName, REGISTRYMTX) then
          exit;
      {$ENDIF}
    
      Reg := TStringList.Create;
      try
        if (extractFileName(FileName) = FileName) then
          FileName := DefaultDir + FileName;
        Reg.Duplicates := dupAccept;
        Reg.CaseSensitive := false;
        Reg.Sorted := false;
        if FileExists(Filename) then
        begin
          Reg.LoadFromFile(Filename);
          Reg.Sorted := true;    // optimum way.. load first THEN sort
          result := (Reg.Values[In_Key]);
          if IsTrim then
             result := Trim(result);

          {$IFNDEF RPT}
          if Spool_It then
            if length(result) > 0
              then SM(format('%s Value %s=%s is being used',[FileName,In_Key,result]))
              else SM(format('NOTICE: %s Value for %s not found',[FileName,In_Key]));
          {$ENDIF RPT}
        end
        else
        begin
          //SM(format('NOTICE: File %s does not exist; so value for %s not found',[FileName,In_Key]));
          //GetDir(0,dir);
          //SM('Reg_Lookup in >' + dir + '<');
        end;
      finally
        Reg.Free;
      end;
    finally
      RegReleaseLock;
    end;
  except on e: exception do
    SM('Exception: MTX_Lib.Reg_Lookup: ' + e.Message);
  end;
end;

// XE: Remove WinEPS - not in use but keep
function Reg_Save(In_Key: String255; In_Text: string255; FileName: string=''; logIt: boolean=true): boolean; // unit test in Test_MTX_Lib  { TSL-B }
var
  T,T1: Text;
  In_Buf: String255;
  SavedOK,Found: boolean;
  tmpFileName: string;
begin
  if FileName = '' then // DEV-18500 <
  begin
    FileName := REGISTRYMTX;
    tmpFileName := 'REGISTRY.TMP';
  end
  else
    tmpFileName := FileName + '.tmp'; // DEV-18500 >
  {$IFNDEF GUIJR}
  Found   := false;
  SavedOK := false;
  assign(T, FileName);
  {$I-}
  reset(T);
  {$I+}
  if IOResult = 0 then
    begin
    assign(T1, tmpFileName);
    {$I-}
    rewrite(T1);
    {$I+}
    If (IoResult = 0) Then
      begin
      repeat
        ReadLn(T, In_Buf);
        if Copy(In_Buf,1,Pos('=',In_Buf)-1) = In_Key then
          begin
          Found := True;                         { it is already there so }
          Writeln(T1, In_Key + '=' + In_Text);   { write new value instead}
          end
        else
          Writeln(T1, In_Buf);
      until eof(T);
      if not Found then                      { it is not there so }
        Writeln(T1, In_Key + '=' + In_Text);       { put it in (at file end)}
      close(T);
      close(T1);
      //deletefile(REGISTRYMTX);
      deletefile(FileName);
      //SavedOK := renamefile('REGISTRY.TMP',REGISTRYMTX);
      SavedOK := renamefile(tmpFileName, FileName);
      end;
    end
  else       { no REGISTRYMTX file exists so create it }
    begin
    {$I-}
    Rewrite(T);
    {$I+}
    if IoResult = 0 then
      begin
      writeln(T,In_Key + '=' + In_Text);
      close(T);
      SavedOK := true;
      End
    else
      SavedOK := false;
    end;

  {$IFNDEF RPT}
  if logIt then
  begin 
    if SavedOK
      then SM('Reg_Save Value '+ In_Key + '=' + In_Text + ' is being used')
      else SM('**** WARNING: ' + FileName + ' file Error - Registry Value ' + In_Key +
              '=' + In_Text + ' NOT being used');
  end;
  {$ENDIF RPT}
  {$ENDIF GUIJR}
  result := SavedOK;
end;

function IsNumeric(aValue: string): boolean; // DEV-7810
var i: integer;
begin
  result := false;
  aValue := Trim(aValue);
  if aValue = '' then
    Exit;
  for i := 1 to Length(aValue) do
  begin
    if (aValue[i] in ['0'..'9']) then
      result := true
    else
    begin
      result := false;
      Exit;
    end;
  end;
end;

//CPCLIENT-9077/9078 Remove secified directory/sub-directory/files
procedure RemoveDirAndFiles(const aDir: String);
var
  sDir: String;
  Rec: TSearchRec;
begin
  sDir := IncludeTrailingPathDelimiter(aDir);
  if FindFirst(sDir + '*.*', faAnyFile, Rec) = 0 then
  try
    repeat
      if (Rec.Attr and faDirectory) = faDirectory then
      begin
        if (Rec.Name <> '.') and (Rec.Name <> '..') then
          RemoveDirAndFiles(sDir + Rec.Name);
      end else
      begin
        DeleteFile(sDir + Rec.Name);
      end;
    until FindNext(Rec) <> 0;
  finally
    FindClose(Rec);
  end;
  RemoveDir(sDir);
end;

// if the string is a valid date, it is also returned in aDate as a valid TDateTime
function IsValidDateString(str: string; var aDate: TDateTime): boolean;   // JTG upgrade  // unit test in Test_MTX_Lib
var
  aMonth,aDay,aYear,CurrentCentury: word;
  DS: Char;
begin
  result := false;    // the default condition should be false
  aDate := 0;
  if length(str) <> 8 then exit; // we depend on a specific format here
  //if str[3] <> '/' then exit;    // probably not important, but old code checked
  //if str[6] <> '/' then exit;    // for these slashes
  DS := SysUtils.GetLocaleChar(SysLocale.DefaultLCID, {$IFDEF MSWINDOWS} LOCALE_SDATE {$ELSE} $0000001D {$ENDIF}, '/'); // DOEP-59693
  if (str[3] <> DS) or (str[6] <> DS) then exit;    // use date seperator from system locale
  aMonth := StrToIntDef(copy(Str,1,2),0);
  aDay := StrToIntDef(copy(Str,4,2),0);
  aYear := StrToIntDef(copy(Str,7,2),0);
  CurrentCentury := (CurrentYear div 100) * 100; // So Century = 2000, 2100, 2200, etc...
  aYear := CurrentCentury + aYear;
  result := isValidDate(aYear,aMonth,aDay);
  if result
    then aDate := EncodeDate(aYear,aMonth,aDay);
end;

// XE: Remove WinEPS - not in use but keep
procedure TraceDebug(msg: string);     { JMR-Z }
var
  ok: boolean;
  F: TextFile;
const
  MAX_DEBUG_DAYS    = 30;
  DEBUG_FILE        = 'debug.txt';
  DEBUG_FILE_OLD    = 'debug.old';

  { If the current debugfile is older than MAX_DEBUG_DAYS then copy it to .old
    A new debugfile will be created with the next call to TraceDebug(). }
  procedure CheckFile;
  var
    f: TextFile;
    Line, DateS: string;
    FoundDate: Boolean;
    JDate,aDate: TDateTime;
  begin
    assignfile(f,{DefaultDir + }DEBUG_FILE);
    {$I-}
    reset(f);
    {$I+}
    if IOResult = 0 then
      begin
      FoundDate := false;
      repeat
        {$I-}
        readln(f,Line);
        {$I+}
        if IOResult = 0 then
          begin
          setlength(DateS,8);
          DateS := copy(Line,1,8);
          FoundDate := IsValidDateString(DateS,aDate);
          end;
      until FoundDate or eof(f);
      closefile(f);

      if FoundDate then
        begin
        JDate := aDate;
        if (Date() - MAX_DEBUG_DAYS) > JDate then
          begin
          {$IFDEF LINUX}
           CopyFileTo(pchar({DefaultDir + }DEBUG_FILE),pchar({DefaultDir + }DEBUG_FILE_OLD));
          {$ELSE}
           CopyFile(pchar({DefaultDir + }DEBUG_FILE),pchar({DefaultDir + }DEBUG_FILE_OLD), false);
          {$ENDIF}
           DeleteFile(pchar({DefaultDir + }DEBUG_FILE));
          end;
        end;
      end;
  end;

begin
  {$IFNDEF DEBUG}
   exit;
  {$ENDIF DEBUG}
  CheckFile;
  FileMode := 66;
  AssignFile(F,DEBUG_FILE);
  if FileExists(DEBUG_FILE)
    then {$I-} Append(F) {$I+}
    else {$I-} Rewrite(F); {$I+}
  OK := IOResult = 0;
  if OK then
    begin
    WriteLn(F,FormatDateTime('mm/dd/yy hh:nn:ss',Now) + ': '+ msg);
    {$I-}
    CloseFile(F);
    if IOResult <> 0 then;  //have to access the IOResult in order to 'clear' it
    {$I+}
    end;
end; { TraceDebug }

procedure MsgDebug(Msg: string);
begin
  {$IFDEF DEBUG}
  SM('****DEBUG: ' + Msg, IS_NOT_IN_TRANS);
  {$ENDIF}
end;

procedure MsgDebug(Msg: string; const Args: array of const);
begin
  {$IFDEF DEBUG}
  try
    SM(Format('****DEBUG: ' + Msg, Args), IS_NOT_IN_TRANS);
  except on e: exception do
    MsgLog('****ERROR: >>> The FORMAT string (' + Msg + ') is INCORRECT for the given arguments');
  end;
  {$ENDIF}
end;

procedure MsgLog(Msg: string; IsInTrans: TIsInTrans = iitTrue);  overload;     // CPCLIENTS-14502
begin
  SM(Msg, IsInTrans);
end;

procedure MsgLog(Msg: string; const Args: array of const; IsInTrans: TIsInTrans = iitTrue);  overload;    // CPCLIENTS-14502
begin
  try
    SM(Format(Msg, Args), IsInTrans);
  except on e: exception do
    SM('****ERROR: >>> The FORMAT string (' + Msg + ') is INCORRECT for the given arguments');
  end;
end;

procedure MsgNotice(Msg: string; IsInTrans: TIsInTrans = iitTrue);  overload;    // CPCLIENTS-14502
begin
  SM('****NOTICE: ' + Msg, IsInTrans);
end;

procedure MsgNotice(Msg: string; const Args: array of const; IsInTrans: TIsInTrans = iitTrue);   overload;    // CPCLIENTS-14502
begin
  try
    SM(Format('****NOTICE: ' + Msg, Args), IsInTrans);
  except on e: exception do
    SM('****ERROR: >>> The FORMAT string (' + Msg + ') is INCORRECT for the given arguments', IsInTrans);    // CPCLIENTS-14502
  end;
end;

procedure MsgWarn(Msg: string; IsInTrans: TIsInTrans = iitTrue);    overload;    // CPCLIENTS-14502
begin
  SM('****WARNING: ' + Msg, IsInTrans);
end;

procedure MsgWarn(Msg: string; const Args: array of const; IsInTrans: TIsInTrans = iitTrue);   overload;    // CPCLIENTS-14502
begin
  try
    SM(Format('****WARNING: ' + Msg, Args), IsInTrans);
  except on e: exception do
    SM('****ERROR: >>> The FORMAT string (' + Msg + ') is INCORRECT for the given arguments');
  end;
end;

procedure MsgError(Msg: string; IsInTrans: TIsInTrans = iitTrue);  overload;    // CPCLIENTS-14502
begin
  SM('****ERROR: ' + Msg, IsInTrans);
end;

procedure MsgError(Msg: string; const Args: array of const; IsInTrans: TIsInTrans = iitTrue);  overload;    // CPCLIENTS-14502
begin
  SM(Format('****ERROR: ' + Msg, Args), IsinTrans);
end;

{ ********************************************************************
  Get Lst line, put in day 1 spool, then print or spool for later
  ******************************************************************** }
procedure SM(OutMessage: AnsiString; IsInTrans: TIsInTrans = iitTrue; truncate: boolean = true);    // CPCLIENTS-14502
const
  OFFSET_ZERO = 0;
  ORIGIN_EOF = 2;
  HDR = '******* MTX_LIB.SM  ';  //JTG: force consistency across all the messages
  CP = 'CheckPoint = ';

{$IFNDEF RPT}
{$IFNDEF MTXEPSDLL}
  RepeatCounter: integer = 0;

var
  i: integer;
  Temp_Line,checkpoint: string;
  OpenOK: boolean;
{$ENDIF MTXEPSDLL}
{$ENDIF RPT}
begin
  {$IFDEF HOSTSIM}        exit; {$ENDIF}
  {$IFDEF VT}             exit; {$ENDIF}  // JTG to stop VT writing to spool file in OpenEPS folder when opening XMLs
  {$IFDEF GUIJR}          exit; {$ENDIF}
  {$IFDEF UpdateXMLFiles} exit; {$ENDIF}
  {$IFDEF UPDATEFILES}    exit; {$ENDIF}
  {$IFDEF OPENIP}         exit; {$ENDIF}
  {$IFDEF RPT}            exit; {$ENDIF}
  {$IFDEF RSSRV}          exit; {$ENDIF}  // JTG to stop RS Service from writing to spool file, especially on the backup RS
  {$IFDEF VOUCHERCLEAR}   exit; {$ENDIF}
  {$IFDEF MTXPOSDLL}      exit; {$ENDIF}
  {$IFDEF OENDLL}  exit; {$ENDIF}
  SMAcquireLock;   //JTG creates the lock if not already assigned, no need to test for assignment twice in a row
  try
    try
      {$IFDEF RSSRV}
      Log(OutMessage);  // JTG
      {$ELSE RSSRV}
        {$IFNDEF RPT}                                                 { JMR-D }
          {$IFNDEF MTXEPSDLL}
          {$ELSE}
           ShowTrace({DLLTypes.idTCP=}2, OutMessage, IsInTrans);      // CPCLIENTS-14502
          {$ENDIF MTXEPSDLL}
        {$ENDIF RPT}                                                  { JMR-D }
      {$ENDIF RSSRV}
    except on e: Exception do
      begin
        {nothing};
      end;
    end;
  finally
    SMReleaseLock;
  end;
end;

//current memory size of the current process in bytes
function CurrentMemoryUsage: Cardinal; // XE: Remove WinEPS - not in use but keep
{$IFDEF MSWINDOWS}
var
  pmc: TProcessMemoryCounters;
{$ENDIF}
begin
  {$IFDEF MSWINDOWS}
  pmc.cb := SizeOf(pmc);
  if GetProcessMemoryInfo(GetCurrentProcess,@pmc,SizeOf(pmc))
    then result := pmc.WorkingSetSize
    else result := 0;
  {$ENDIF}
end;

function LogMemoryStr: string; // XE: Remove WinEPS - not in use but keep
begin
  result := format('MEMORY %dK ::: ',[CurrentMemoryUsage div 1024]);
end;

procedure LogMemory(S: string = ''); // XE: Remove WinEPS - not in use but keep
begin
  {$IFDEF MSWINDOWS}
  SM(format('MEMORY %dK ::: %s',[CurrentMemoryUsage div 1024,S]))
  {$ENDIF}
end;

// AFR, 3/26/2007, DEV-4842 - added output to event log
Procedure SMUserActivity(msg: string; aUsername: string = '');
var
  UAStr: string;
begin
  msg := StringReplace(msg, 'UserBool1', 'Administrator', [rfReplaceAll, rfIgnoreCase]);
  msg := StringReplace(msg, 'UserBool2', 'TruncateCardDataAtEOD', [rfReplaceAll, rfIgnoreCase]);
  msg := StringReplace(msg, 'UserBool3', 'WindowsGroup', [rfReplaceAll, rfIgnoreCase]);
  msg := StringReplace(msg, 'UserBool4', 'VoucherClear', [rfReplaceAll, rfIgnoreCase]);
  
  UAStr := 'UAL: User ' + {$IFDEF ENGINE} aUsername {$ELSE} Global_Operator {$ENDIF} + ' ' + msg;
  SM(UAStr);
{$IFDEF MSWINDOWS}
  //if (Reg_Lookup(REGISTRYMTX,'WRITEUSERACTIVITYTOEVENTLOG', False) = 'YES') then
    WriteToEventLog(UAStr, EVENTLOG_INFORMATION_TYPE);
{$ENDIF}
end;

function tenderNum(aReqCodeN: byte): integer;
begin
  case aReqCodeN of
    DbPurchN..DbBalInqN, DbReturnWithValidN,
    WorkingKeyReqN                                : result := idebit; // DOEP-33268, 33273
    CrPurchN..CrReturnWithValidN,CrAdjustmentN    : result := icredit; // DOEP-33267, 33268
    PropDbPurchN..PropDbBalInqN,
    PropDbReturnWithValidN,                                             // DOEP-33268
    PropDbPreAuthN, PropDbPreAuthCompN            : result := ipldebit; // CPCLIENTS-12016
    PropCrPurchN..PropCrBalInqN,
    PropCrReturnWithValidN                        : result := iplcredit; //DOEP-33268
    CheckPurchN..CheckBalInqN                     : result := icheck;
    EBT_FS_PurchN..EBT_FS_BalInqN, User_2_ReturnN : result := iebt_fs;
    EBT_Cash_PurchN..EBT_Cash_BalInqN             : result := iebt_ca;
    User_1_PurchN..User_1_BalInqN,
    User_1_RefundActivationN..User_1_CashOutN,
    User_1_PreRechargeN..User_1_FinalTenderN      : result := iuser_1; //DOEP-33269, 33271, 33272
    FleetPurchN..FleetPreAuthCompN                : result := ifleet;
    User_2_PurchN,User_2_OverrideN,User_2_VoiceN,
                                   User_2_BalInqN : result := iuser_2;
    wireLessActivationN, wireLessDeactivateN,
    wireLessPreAuthN                              : result := iwireless;    // CPCLIENTS-11904
    achPurchN..achReturnN                         : result := iach;
    ConnectPayPurchN, ConnectPayReturnN           : result := iConnectPay;
    eWicBalInqN..eWicPreAuthCompN                 : result := ieWic;
    BenefitPurchaseN,
    BenefitBalanceInquiryN,
    BenefitReturnN,
    BenefitItemQualificationN : Result := iBenefitsProgram;  // CPCLIENTS-9586 For new Tender Type
    MtPurchN : Result := iMultiTender;                       //17849
    else                                            result := MaxTerms;
  end;
end;

function TenderIndex(inRec: mdmsgRec): integer;  // unit test in Test_MTX_Lib  { TSL-06 }
begin  { used to index vars TenderOffline and Host_TruncateAcctNo which only goto 10 tenders }
  with InRec Do
    case ReqCodeN of
    DbPurchN..DbBalInqN,WorkingKeyReqN:       result := idebit;
    CrPurchN..CrReturnWithValidN:             result := icredit; // DOEP-33267, 33268
    PropDbPurchN..PropDbBalInqN,
    PropDbPreAuthN, PropDbPreAuthCompN:       result := ipldebit; // CPCLIENTS-12016
    PropCrPurchN..PropCrBalInqN:              result := iplcredit;
    CheckPurchN..CheckBalInqN:                result := icheck;
    EBT_FS_PurchN..EBT_FS_BalInqN:            result := iebt_fs;
    EBT_Cash_PurchN..EBT_Cash_BalInqN:        result := iebt_ca;
    User_1_PurchN..User_1_BalInqN:            result := iuser_1;
    FleetPurchN..FleetPreAuthCompN:           result := ifleet;
    User_2_PurchN,User_2_OverrideN:           result := iuser_2;
    User_2_ReturnN:                           result := iebt_fs;  //jtg
    User_2_VoiceN,User_2_BalInqN:             result := iuser_2;
    wireLessActivationN, wireLessDeactivateN,
    wireLessPreAuthN:                         result := iuser_2{iwireless};    // CPCLIENTS-11904
    achPurchN..achReturnN:                    result := iplCredit{iach};
    ConnectPayPurchN, ConnectPayReturnN:      result := idebit;
    eWicBalInqN..eWicPreAuthCompN:            result := iebt_fs;
    else                                      result := MaxTerms;
    end;
end;

function TheOSversion: string;         { TSL-A }
var
  S: string;
  {$IFDEF MSWINDOWS}
  OSVersionInfo : TOsVersionInfo;
  {$ENDIF MSWINDOWS}
begin
  {$IFDEF MSWINDOWS}
  OsVersionInfo.dwOSVersionInfoSize := sizeof(OsVersionInfo);
  GetVersionEx(OsVersionInfo);
  case OsVersionInfo.dwPlatformId of
    VER_PLATFORM_WIN32s        : S := 'Windows 3.1';
    VER_PLATFORM_WIN32_WINDOWS : S := 'Win95';
    VER_PLATFORM_WIN32_NT      : S := 'WinNT';
    else  S := intToStr(OsVersionInfo.dwPlatformId);
  end;
  with OsVersionInfo do
    result := format('OS Version %s %d.%d %s',[S,dwMajorVersion,dwMinorVersion,szCSDVersion]);
  {$ELSE}
  result := 'Linux';
  {$ENDIF LINUX}
end;

{$IFDEF MSWINDOWS}
function GetCurrentUserAndDomain: string; // XE: Remove WinEPS - not in use but keep
var
  hToken   : THandle;
  ptiUser  : PSIDAndAttributes;
  cbti     : DWORD;
  snu      : SID_NAME_USE;
  szUser   : array [0..255] of Char;
  szDomain : array [0..255] of Char;
  pcchUser   : DWORD;
  pcchDomain : DWORD;
begin
  result := '';
  pcchUser := 255;
  pcchDomain := 255;
  ptiUser := nil;
  try
    if (not OpenThreadToken(GetCurrentThread(), TOKEN_QUERY, TRUE, hToken)) then
    begin
      if (GetLastError() <> ERROR_NO_TOKEN) then exit;
      if (not OpenProcessToken(GetCurrentProcess(), TOKEN_QUERY, hToken)) then exit;
    end;
    if (GetTokenInformation(hToken, TokenUser, nil, 0, cbti)) then
      exit
    else if (GetLastError() <> ERROR_INSUFFICIENT_BUFFER) then
      exit;
    ptiUser :=  HeapAlloc(GetProcessHeap(), 0, cbti);
    if (ptiUser= nil) then exit;

    if (not GetTokenInformation(hToken, TokenUser, ptiUser, cbti, cbti)) then
      exit;
    if (not LookupAccountSid(nil, ptiUser.Sid, szUser, pcchUser, szDomain, pcchDomain, snu)) then
      exit;
    result := format('%s/%s',[szDomain,szUser]);
  finally
    if (hToken > 0) then
      CloseHandle(hToken);
    if (ptiUser <> nil) then
      HeapFree(GetProcessHeap(), 0, ptiUser);
  end;
end;
{$ENDIF MSWINDOWS}

{$IFDEF MSWINDOWS} 
function IsLocalSystemAccount: boolean; // DEV-28948 // XE: Remove WinEPS - not in use but keep
const
  cnMaxNameLen = 254;
var
  sName: string;
  dwNameLen: DWORD;
begin
  dwNameLen := cnMaxNameLen - 1;
  SetLength(sName, cnMaxNameLen);
  GetUserName(PChar(sName), dwNameLen);
  SetLength(sName, dwNameLen);
  result := SameText(Trim(sName), 'SYSTEM');
end;
{$ENDIF}

{$IFDEF MSWINDOWS}
function XPFirewallAddAllowedProgram(exeName: string; programName: string): Boolean; { TRI-H } // XE: Remove WinEPS - not in use but keep
begin
  result := false;
  try
    Result := EasyCreateProcess('netsh firewall add allowedprogram "' + exeName + '" "' + programName + '" ENABLE', true, 10000);
  except
    on E: Exception do
      SM('Exception in EngineU.XPFirewallAddAllowedProgram: ' + E.Message);
  end;
end;
{$ENDIF}

{$IFDEF MSWINDOWS}
function XPFirewallAddPort(Name: string; Port: Integer): Boolean; { TRI-I } // XE: Remove WinEPS - not in use but keep
begin
  result := false;
  try
    Result := EasyCreateProcess('netsh firewall add portopening TCP ' + IntToStr(Port) + ' "' + Name + '"', true, 10000);
  except
    on E: Exception do
      SM('Exception in EngineU.XPFirewallAddPort: ' + E.Message);
  end;
end;
{$ENDIF}

{$IFDEF MSWINDOWS}
function RemoveReadOnlyAttribute(Filename: string): boolean; // XE: Remove WinEPS - not in use but keep
const
  ERRMSG = '*** WARNING ***';
var
  Attributes: word;
  ErrorCode: integer;
begin
  result := true;     // assume OK on this, so as not to break existing code if we use this function
  if FileExists(Filename) then
    begin
    Attributes := FileGetAttr(Filename);
    if (Attributes and faReadOnly) = faReadOnly then
      try
        SM(format('WARNING: [%s] is read-only. Making it writeable.',[Filename]));
        ErrorCode := FileSetAttr(Filename,Attributes and not faReadOnly);
        result := ErrorCode = 0;
        if not result then
          SM(format('%s FAILED TO MAKE [%s] WRITEABLE; ErrorCode=%d',[ERRMSG,Filename, ErrorCode])); // was SysErrorMessage(ErrorCode)
      except on e: EInOutError do
        begin
        result := false;
        SM(format('WARNING: Unable to make [%s] writeable because it is in use: %s',[Filename,e.Message]));
        end;
      end;
    end;
end;
{$ENDIF}

{$IFDEF MSWINDOWS}
// XE: Remove WinEPS - not in use but keep
function RemoveReadOnlyAttributesThisFolder(sDir: string): boolean; // unit test in Test_MTX_Lib
const
  ERRMSG = '*** WARNING ***';    //Dev 25692
var
  SR: TSearchRec;
  Attributes: word;
  ErrorCode: integer;
begin
  result := true;   // default, in case there are no files to work on...
  try
    if NOT DirectoryExists(sDir) then Exit;
    if sDir <> '' then
    begin
      ChDir(sDir);    // JTG: would changing the currently directory cause other problems?
      SM(Format('[CurrentDir] RemoveReadOnlyAttributesThisFolder - CHDIR to %s', [sDir]));
    end;
    if FindFirst('*.*',faReadOnly,SR) = 0 then    //JTG refactored & fixed
    try
      repeat
        Attributes := FileGetAttr(SR.name);
        if (Attributes and faReadOnly) = faReadOnly then
          try
            SM(format('WARNING: [%s] is read-only. Making it writeable.',[SR.name]));
            ErrorCode := FileSetAttr(SR.name,Attributes and not faReadOnly);
            result := ErrorCode = 0;
            if not result then
              SM(format('%s FAILED TO MAKE [%s] WRITEABLE in folder [%s] ErrorCode=%d',
                        [ERRMSG,SR.Name,sDir, ErrorCode])); // was SysErrorMessage(ErrorCode)
          except on e: EInOutError do
            begin
            result := false;
            SM('WARNING: Unable to make ['+SR.name+'] writeable because it is in use: '+e.Message);
            end;
          end;
      until FindNext(SR) <> 0;
    finally
      SysUtils.FindClose(SR);
    end;
  except on e: exception do
    SM('Try..Except: RemoveReadOnlyAttributesThisFolder.  sDir=>'+sDir+'< ' + e.message);
  end;
end;
{$ENDIF}

function GetVersionString(Filename,VerStr: string): string;
{$IFDEF MSWINDOWS}
var
  Size,Handle: dword;
  Len: uint;
  Buffer,Value: pchar;
  TransNo: pLongInt;
  SFInfo: string;
{$ENDIF}
begin
  result := '';
  {$IFDEF MSWINDOWS}
  Size := GetFileVersionInfoSize(pChar(FileName),Handle);
  if Size > 0 then
  begin
    Buffer := AllocMem(Size);
    try
      GetFileVersionInfo(pChar(FileName),0,Size,Buffer);
      VerQueryValue(Buffer, PChar('VarFileInfo\Translation'),Pointer(TransNo),Len);
      SFInfo := format('%s%.4x%.4x%s%s%',['StringFileInfo\',LoWord(TransNo^),HiWord(Transno^),'\',VerStr]);
      if VerQueryValue(Buffer,PChar(SFInfo),Pointer(Value),Len)
        then result := Value;
    finally
      if Assigned(Buffer) then
        FreeMem(Buffer,Size);    // always release memory that's hard-allocated
    end;
  end;
  {$ENDIF}
end;

function GetFileDateStr(Filename: string): string;  // JTG 23296 // XE: Remove WinEPS - not in use but keep
var
  Age: integer;
  sFilename: string;

  procedure ReturnFileDate(aFilename: string);
  begin
    Age := FileAge(aFileName);
    if Age >= 0
      then result := FormatDateTime('yyyy-mm-dd hh:nn:ss',FileDateToDateTime(Age))
      else result := 'FILE DATE UNAVAILABLE';
  end;

begin
  sFilename := Filename;
  if not FileExists(sFilename) then
    begin
    sFilename := IncludeTrailingPathDelimiter(ExtractFilePath(ParamStr(0))) + Filename;  // if can't find it as given, then try filename in local folder
    if not FileExists(sFilename) then
      sFilename := ExtractFilename(Filename);           // finally just try the filename by itself
    end;
  if FileExists(sFilename)
    then ReturnFileDate(sFilename)
    else result := 'CANNOT LOCATE FILE: '+Filename;
end;

function GetVersionInfo(Filename,SearchKey: string): string; // unit test in Test_MTX_Lib
begin
  result := GetVersionString(Filename,SearchKey);
end;

// use this since Clock function (called from GetTickCount) does not work in Linux
function GetTickCountExt: Cardinal;
{$IFDEF LINUX}
var
  hour,minute,second,hundredth: Word;
{$ENDIF LINUX}
begin
{$IFDEF LINUX}
  DecodeTime(Time,Hour,Minute,Second,Hundredth);
  Result := (Hour * 3600 + Minute * 60 + second) * 1000;
{$ELSE}
  Result := GetTickCount;
{$ENDIF LINUX}
end;

function iif(Condition: Boolean; TrueResult, FalseResult: Variant): Variant; 
begin
  if Condition then
    Result := TrueResult
  else
    Result := FalseResult;
end;

{$IFDEF MSWINDOWS}
// XE: Remove WinEPS - not in use but keep
function GetSystemFolder: string; // unit test in Test_MTX_Lib
var
  Buf: array[0..MAX_PATH] of char;
begin
  GetSystemDirectory(Buf,MAX_PATH);  //JTG slight refactor
  result := Buf;
end;
{$ENDIF}

function LRC(s: AnsiString): byte;  // unit test in Test_MTX_Lib
var
  TempSum: byte;
  i: Integer;
begin
  TempSum := Byte(s[1]);
  for i := 2 to length(s) do
    TempSum := TempSum xor Byte(s[i]);
  Result := TempSum xor Byte(ETX);
end;

Function LRCCorrect(inStr : AnsiString) : Boolean;   // unit test in Test_MTX_Lib
Var
  MsgStr : AnsiString;
  CheckByte : AnsiChar;
begin
  Result := False;
  If Length(inStr) > 3 then
  begin
    MsgStr := Copy(inStr, 2, Length(inStr) - 3);
    CheckByte := InStr[Length(InStr)];
    If Byte(CheckByte) = LRC(MsgStr) then
      Result := True
    else
      sm('****ERROR: LRC received x' + format('%.2x',[Byte(CheckByte)]) +
        ' not equal to calculated LRC x' + format('%.2x',[LRC(MsgStr)]));
  end;
end;

function PrintableStr(SourceStr: AnsiString; ReplaceType: TReplaceType = rtHex): AnsiString;
var
  i: integer;
begin
  try
  result := '';
  for i := 1 to length(SourceStr) do
    if SourceStr[i] in [' '..'~'] then    //JTG refactor to use set operations
      result := result + SourceStr[i]
    else
      case ReplaceType of
        rtHex: result := result + Format('[%x]',[Ord(SourceStr[i])]);
        rtDec: result := result + Format('[%d]',[Ord(SourceStr[i])]);
        rtSpace: result := result + ' ';
      end;
  except on e: exception do
    SM(format('PrintableStr EXCEPTION: [%s]',[e.Message]));
  end;
end;

function PrintHex(aMsg: AnsiString): string;
var i: integer;
begin
  result := '';
  for i := 1 to length(aMsg) do
    if (aMsg[i] in [' '..#127])
      then result := result + aMsg[i]
      else result := format('%s[%.2x]', [result, ord(aMsg[i])]);
end;

function PrintHexOnly(const aMsg: AnsiString): String;   // TFS-36143
var
  i: integer;
begin
  Result := '';
  for i := 1 to Length(aMsg) do
    Result := format('%s[%.2x]', [Result, ord(aMsg[i])]);
end;

function getSCATVersionFromFileName(aSearch: string; aType: char): string; // unit test in Test_MTX_Lib
var                        { if aType = 'E' then we get version from extension }
  SRec       : TSearchRec; { else version is 4 digits following name prefix }
  Found      : integer;
  nVersion   : integer;
  maxVersion : Integer;
  srchName   : string;
begin
  result := NOVERSION;
  {$IFDEF MTXEPSDLL}
  srchName := DefaultDir + aSearch + '*.*';
  {$ELSE MTXEPSDLL}
  srchName := WinEPSDir + DOWNLOADDIRNAME + '\' + aSearch + '*.*';
  {$ENDIF MTXEPSDLL}
  maxVersion := 0;
  Found := FindFirst(srchName , faAnyFile, SRec);
  if Found = 0 then
    try
      while (Found = 0) do   { loop thru files to get latest version }
      begin
        if (aType = 'E') then   { get version from extension }
        begin
          nVersion := StrToIntDef(copy(ExtractFileExt(SRec.Name),2,3),0); { ext is ver }
          if (nVersion > maxVersion) then   { if this is newest version, set it }
            maxVersion := nVersion;
        end
        else                   { get version from file name - just after prefix }
        begin
          nVersion := StrToIntDef(copy(SRec.name, length(aSearch) + 1, 4),0);
          if (nVersion > maxVersion) then   { if this is newest version, set it }
            maxVersion := nVersion;
        end;
        Found := FindNext(SRec);
      end;
      result := format('%.4u',[maxVersion]);  { version has to be 4 digits }
    finally
      FindClose(SRec);
    end
  else
    SM('****ERROR:  No terminal code files found for ' + srchName + ' - terminal may not operate.');
end;

function MakeTOADFormat(aMICRData: string): string;
var i : integer;
    startPoint : integer;
begin
  if (aMICRData[1] = 'C') and (aMICRData[2] = 'C') then
    startPoint := 3
  else
    startPoint := 1;

  result := '';
  for i := startPoint to Length(aMICRData) do
    case aMICRData[i] of
      ')': result := result + 't';
      '(': result := result + 'o';
      '&': result := result + 'a';
      '''',
      '-': result := result + 'd';
      else result := result + aMICRData[i];
    end;
end;

function GetArrayText(Source: string; Index, TextSize: Integer): string;        
begin
  try
    result := '';
    if (TextSize <= 0) or
       (Index <= 0) or
       (Length(Source) div TextSize <= 0) or
       (Length(Source) mod TextSize <> 0) then
      exit;
    result := Copy(Source, (Index-1)*TextSize+1, TextSize);
  except on e: exception do
    SM('Exception: MTX_Lib.GetArrayText:  Source=>'+Source+'<  Index=>'+inttostr(Index)+'<  TextSize=>'+inttostr(TextSize)+'<  ' + e.Message);
  end;
end;

function YN(B: boolean): Char;                                                  
const
  tmpArray: array[boolean] of Char = (('N'),('Y'));
begin
  result := tmpArray[B];
end;

function TF(B: boolean): Char;                                                  
const
  tmpArray: array[boolean] of Char = (('F'),('T'));
begin
  result := tmpArray[B];
end;

function TrueFalse(B: boolean): string;                                         
const
  tmpArray: array[boolean] of string = (('False'),('True'));
begin
  result := tmpArray[B];
end;

function GetLatestVersionFile({var sList: TStringList; }                          // moved from OEVersionU.CheckVersionFile
    filePattern: string; verPos, verLen: integer): string;
var
  sr: TSearchRec;
  latestVer, tmpVer: Integer;
begin
  try
    latestVer := 0;
    result := '';
    if FindFirst(filePattern, faAnyFile, sr) = 0 then
    try
      repeat
        tmpVer := StrToIntDef(Copy(sr.Name, verPos, verLen),0);
        if (tmpVer > latestVer) then
        begin
          latestVer := tmpVer;
          //latestFile := sr.Name;
          result := sr.name;
        end;
      until FindNext(sr) <> 0;
    finally
      FindClose(sr);
    end;
  except
    ;
  end;
end;

function FmtXmlToWrite(aXML: string): string;
var
  i: integer;
  srcStr, tmpStr: string;
begin
  try
    if Length(Trim(aXML)) = 0 then
      exit;
    result := ReplaceString(aXML, '"', '&quot;', False);
    result := ReplaceString(result, '''', '&apos;', False);
    result := ReplaceString(result, '<', '&lt;', False);
    result := ReplaceString(result, '>', '&gt;', False); 
    i := 1;
    repeat
      if result[i] = '&' then
      begin
        srcStr := Copy(result, i+1, Length(result)-i);
        tmpStr := LowerCase(srcStr);
        if (Pos('amp;', tmpStr) <> 1) and
           (Pos('quot;', tmpStr) <> 1) and
           (Pos('apos;', tmpStr) <> 1) and
           (Pos('lt;', tmpStr) <> 1) and
           (Pos('gt;', tmpStr) <> 1) then
        result := Copy(result, 1, i) + 'amp;' + srcStr;
        i := i + 4;
      end
      else
        Inc(i);
    until i > Length(result);
  except
    on e : Exception do
      SM('MTX_Lib.FmtXmlToWrite exception '+e.message);      //test for Ian
      //SM(format('MTX_Lib.FmtXmlToWrite exception trying to format [%s] to [%s] = (%s)',[aXML,result,e.message]));
  end;
end;

function FmtXmlToRead(aXML: string): string;
begin
  try
    result := ReplaceString(aXML, '&quot;', '"');
    result := ReplaceString(result, '&apos;', '''');
    result := ReplaceString(result, '&lt;', '<');
    result := ReplaceString(result, '&gt;', '>');
    result := ReplaceString(result, '&amp;', '&');
  except
    on e : Exception do
      SM('MTX_Lib.FmtXmlToRead exception '+e.message);      //test for Ian
  end;
end;

function GetValidXMLVersion(XMLFile: TMTX_XMLFile): Double;                     
var i: integer;
begin
  result := 0;
  for i := 0 to MTX_XMLFILE_COUNT-1 do
    if MTX_Valid_FileVersion[i].XMLFile = XMLFile then
    begin
      result := MTX_Valid_FileVersion[i].Version;
      exit;
    end;
end;

procedure replaceSpecialXMLChars(var inStr: string);
var i : integer;
begin
  for i := 1 to high(specialXMLChars) do
  begin
    if (Pos(specialXMLChars[i], inStr) > 0) then
      inStr := stringReplace(inStr, specialXMLChars[i], replaceXMLChars[i],[rfReplaceAll]);
  end;
end;

function DeleteFiles(aPattern: string; aLog: boolean=false): boolean; 
var
  SR: TSearchRec;
  Path: string;
  OK: boolean;
begin
  result := true;
  try
    Path := IncludeTrailingPathDelimiter(SysUtils.ExtractFilePath(aPattern));
    if SysUtils.FindFirst(aPattern, faArchive + faAnyFile, SR) = 0 then
    try
      SysUtils.DeleteFile(Path + SR.Name);
      while FindNext(SR) = 0 do
      begin
        OK := SysUtils.DeleteFile(Path + SR.Name);
        if aLog then
          SM(Format('DeleteFiles: %s - %s', [iif(OK, 'deleted', 'failed to delete'), Path + SR.Name]));
      end;
    finally
      SysUtils.FindClose(SR);
    end;
  except
    on e: exception do
    begin
      result := false;
      SM('MTX_LIB.DeleteFiles exception - ' + e.message);
    end;
  end;
end;

function RandomHexString(NumDigits: integer): AnsiString;  // unit tested in Test_MTX_Lib
const
  HEXSTR = '0123456789ABCDEF';
var
  i: integer;
begin
  result := '';
  for i := 1 to NumDigits do
    result := result + HEXSTR[random(length(HEXSTR))+1];
end;

function ByteStrToHexStr(S: AnsiString): AnsiString;
var
  i: integer;
begin
  result := '';
  for i := 1 to length(S) do
    result := result + IntToHex(ord(S[i]),2);
  result := UpperCase(result);
end;

function StrToHexStr(S: AnsiString; GroupSize: integer): AnsiString;
var
  i: integer;
begin
  result := '';
  for i := 1 to length(S) do
    begin
    result := result + format('%x',[ord(S[i])]);
    if i mod GroupSize = 0
      then result := result + ' ';
    end;
  result := uppercase(result);
end;

function HexStrToByteString(aHex: AnsiString): AnsiString;  //converts every 2 Hex char to 1 integer byte str
var
  j: integer;
begin
  j := 1;
  result := '';
  try
    while j <= length(aHex) do
      begin
      result := result + AnsiChar(StrToInt('$'+copy(aHex,j,2)));
      inc(j,2);
      end;
  except on e: EConvertError do
    result := '';
  end;
end;

function HexToB64(aHex: AnsiString): AnsiString;
begin
  result := B64Encode(HexStrToByteString(aHex));
end;

function B64ToHex(B64: AnsiString): AnsiString;
begin
  result := ByteStrToHexStr(B64Decode(B64));
end;

function StrXOR(S1,S2: AnsiString; ComparisonBytes: integer): AnsiString; // XE: Remove WinEPS - not in use but keep
var
  i: integer;
begin
  result := '';
  if length(S1) < ComparisonBytes then exit;
  if length(S2) < ComparisonBytes then exit;
  SetLength(result,ComparisonBytes);
  for i := 1 to ComparisonBytes do
    result[i] := AnsiChar(ord(S1[i]) xor ord(S2[i]));
end;

function IsValidMMDD(aMMDD: string): boolean; // DEV-12505
var MM, DD: integer;
begin
  MM := StrToIntDef(Copy(aMMDD, 1, 2), -1);
  DD := StrToIntDef(Copy(aMMDD, 3, 2), -1);
  result := (MM >= 0) and (MM <=24) and (DD >= 0) and (DD < 60);
end;

function BuildHexString(St : AnsiString) : AnsiString; // DEV-11196 // XE: Remove WinEPS - not in use but keep
var i: integer;
begin
  Result := '';
  for I := 1 to Length(St) do
    Result := Result + IntToHex(Ord(St[i]), 2) + ' ';
  Result := Trim(Result);
end;

{$IFNDEF WOLF}
function GetMD5Hash(const s: AnsiString): string;
var
  LbMD51: TLbMD5;
  MD5Digest : TMD5Digest;
begin
  result := '';
  LbMD51 := TLbMD5.Create(nil);
  try
    LbMD51.HashString(s);
    LbMD51.GetDigest(MD5Digest);
    result := BufferToHex(MD5Digest, SizeOf(MD5Digest));
  finally
    LbMD51.Free;
  end;
end;

function GetMD5HashFromFile(const fileName: string): AnsiString; // XE: Remove WinEPS - not in use but keep
var
  FileStream : TFileStream;
  MemoryStream : TMemoryStream;
  tmpStr: AnsiString;
begin
  result := '';
  try
    FileStream := TFileStream.Create(fileName, fmOpenRead);
    MemoryStream := TMemoryStream.Create();
    try
      MemoryStream.LoadFromStream(FileStream);
      SetString(tmpStr, PAnsiChar(MemoryStream.Memory),MemoryStream.Size);
      result := GetMD5Hash(tmpStr);
    finally
      FileStream.Free;
      MemoryStream.Free;
    end;
  except
    on e: exception do
      SM('GetMD5HashFromFile Exception: ' + e.message);
  end;
end;
{$ENDIF}

function IsHexDigit(const s: AnsiString): boolean;
var
  x: integer;
begin
  Result := TRUE;
  for x:=1 to length(s) do
  begin
    if NOT (s[x] in ['0'..'9', 'A'..'F']) then
    begin
      Result := FALSE;
      break;
    end;
  end;
end;

function DLLPath(TargetFile: string): string;    // JTG Doep-25063 .. look for MTXPOSDLL in multiple places
var
  CurrentDir: string;
begin
  GetDir(0,CurrentDir);
  result := '';
  try
    if FileExists(TargetFile) then
      result := ''
    else if FileExists(IncludeTrailingPathDelimiter(DefaultDir)+TargetFile) then
      result := IncludeTrailingPathDelimiter(DefaultDir)
    else if FileExists(IncludeTrailingPathDelimiter(CurrentDir)+TargetFile) then
      result := IncludeTrailingPathDelimiter(CurrentDir)
    else       //37159 try one more thing if still can't find the TargetFile
      begin
      {$IFDEF MSWINDOWS}
      result := GetModuleInfo(TargetFile);
      {$ENDIF}
      if result <> '' then
        result := ExtractFilePath(result);
      end;
  except on e: exception do
    result := '';
  end;
end;

function FilePieceToString(const FileName: string; var aFilesize: integer; const Piece: integer; const PieceSize: integer; var LogMsg: string): string;
var
  BytesRead,BytesToWrite,SaveFileMode: integer;
  f: file of byte;
  pTemp: pointer;
begin
  try
    SaveFileMode := FileMode;
    FileMode := fmOpenRead or fmShareDenyNone;
    AssignFile(f,FileName);
    reset(f);
    try
      aFilesize := FileSize(f);
      BytesToWrite := min(PieceSize,aFilesize);
      setlength(result,BytesToWrite);
      Seek(F,pred(Piece)*PieceSize);
      pTemp := @result[1];
      BlockRead(f,pTemp^,BytesToWrite,BytesRead);
      setlength(result,BytesRead);
      LogMsg := format('MTX_Lib.FilePieceToString read %d bytes (of %d total) for Piece %d of %s',[BytesRead,aFileSize,Piece,Filename]); // this way calling routine can log it..
    finally
      CloseFile(F);
      FileMode := SaveFileMode;
    end;
  except on e: exception do
    LogMsg := 'MTX_Lib.FilePieceToString EXCEPTION in file '+Filename+ ' >> '+e.Message;
  end;
end;

function FilePieceToStringAnsi(const FileName: string; var aFilesize: integer; const Piece: integer; const PieceSize: integer; var LogMsg: string): AnsiString;
var
  BytesRead,BytesToWrite,SaveFileMode: integer;
  f: file of byte;
  pTemp: pointer;
begin
  try
    SaveFileMode := FileMode;
    FileMode := fmOpenRead or fmShareDenyNone;
    AssignFile(f,FileName);
    reset(f);
    try
      aFilesize := FileSize(f);
      BytesToWrite := min(PieceSize,aFilesize);
      setlength(result,BytesToWrite);
      Seek(F,pred(Piece)*PieceSize);
      pTemp := @result[1];
      BlockRead(f,pTemp^,BytesToWrite,BytesRead);
      setlength(result,BytesRead);
      LogMsg := format('MTX_Lib.FilePieceToString read %d bytes (of %d total) for Piece %d of %s',[BytesRead,aFileSize,Piece,Filename]); // this way calling routine can log it..
    finally
      CloseFile(F);
      FileMode := SaveFileMode;
    end;
  except on e: exception do
    LogMsg := 'MTX_Lib.FilePieceToString EXCEPTION in file '+Filename+ ' >> '+e.Message;
  end;
end;

procedure LogLIM(ModuleName: string); // DEV-31929 // XE: Remove WinEPS - not in use but keep
begin
  SM(Format('%s - LIM: %x', [ModuleName, GetModuleHandle(nil)]));
end;

function ConvertToDecimalSeparator(const S: string): string; //JTG 33046 33130 - in case the English decimal point snuck in, but we are on a different decimal
//separator
var
  FmtSettings: TFormatSettings;
begin
  GetLocaleFormatSettings(LOCALE_SYSTEM_DEFAULT, FmtSettings);
  if FmtSettings.DecimalSeparator = '.'   //JTG to save time, let's do nothing if it's already a decimal point
    then result := S
    else result := StringReplace(S,'.',FmtSettings.DecimalSeparator,[rfReplaceAll]);  //33046 33130
end;

// DOEP-31990 - Get host configuration
function GetHostConfigFromXML(const HostName : string; const HostConfigType : THostConfigType) : string;
var
  aHost : TXMLHostType;
begin
  Result := '';
  try
    aHost := XMLStoreConfigurations.FindHost(HostName, soSuffix);
    if Assigned(aHost) then
      case HostConfigType of
        hctStoreNumber: Result := aHost.StoreNumber;
        hctMerchantID: Result := aHost.MerchantNumber;
        hctCurrencyCode: Result := aHost.CurrencyCode; // DOEP-47031
        hctMerchantNum: Result := aHost.MerchantNum;                     // TFS-14360
        hctTerminalId: Result := aHost.TerminalID;                       // TFS-14360
        hctTerminalNumber: Result := aHost.TerminalNumber;               // TFS-14360
      end;
  except
    on e: exception do
      SM(Format('Try..Except: MTX_Lib.GetHostConfigFromXML HostName >%s< HostConfigType >%d< - %s', [HostName, Ord(HostConfigType), e.message]));
  end;
end;

//JTG: moved from FCT. this generic function doesn't belong there, and there is already a better duplicate function in OpenEPS\FormatUtils that maybe we'll use here instead
function IsExpDateOK(aDate: string): boolean;   // YYMM is expected here
var
  DYY, DMM, DDD : Word;                         { JMR-E }
  ExpMM         : integer;
  ExpYY         : integer;
  T_ExpDate     : string6;
  Today         : string6;
begin
  DecodeDate(Date, DYY, DMM, DDD);
  Today := Str_(DYY) + TwoDigits(DMM);             { YYYYMM }
  ExpMM := MyVal(Copy(aDate,3, 2));    { TSL-A }
  ExpYY := MyVal(copy(aDate,1, 2));
  T_ExpDate := aDate;
  if (T_ExpDate <> '') then
    if (copy(T_ExpDate,1,1) = '9')
      then T_ExpDate := '19' + T_ExpDate             { YYYYMM }
      else T_ExpDate := '20' + T_ExpDate;
  if ((Today > T_Expdate) or (ExpMM > 13) or (ExpMM < 1) or (ExpYY > 49)) or (trim(aDate) = '') or (length(aDate) < 4)
    then result := false      { TSL-O }
    else result := true;
end;

function WinEPSTenderToOpenEPS(aWinEPSTender: integer): integer;
begin
  result := ttNone;
  case aWinEPSTender of
    WinEPS_Credit   : result := ttCredit;
    WinEPS_Debit    : result := ttDebit;
    WinEPS_EBT_FS   : result := ttEBT_FS;
    WinEPS_EBT_CA   : result := ttEBT_CA;
    WinEPS_PrivDb   : result := ttPrivateDebit;
    WinEPS_PrivCr   : result := ttPrivateCredit;
    WinEPS_User1    : result := ttGiftCard;
    WinEPS_User2    : result := ttPhoneCard;
    WinEPS_Check    : result := ttCheckAuthorization;
    WinEPS_Fleet    : result := ttFleet;
    WinEPS_WireLess : result := ttWirelessPhone;
    WinEPS_ACH      : result := ttACH;
    WinEPS_ConnectPay : result := ttConnectPay;
    WinEPS_eWIC     : result := tteWIC;
    WinEPS_BenefitsProgram : result := ttBenefitsProgram;    //9635
    WinEPS_MultiTender : result := ttMultiTender;            //17658
  end;
end;   { WinEPSTenderToOpenEPS }

function GetMaxLanesFromSetupTxt(aDir: string=''): integer;
begin
  if aDir = '' then
    aDir := DefaultDir;
  aDir := IncludeTrailingPathDelimiter(aDir);
  result := StrToIntDef(MTX_Lib.Reg_Lookup(aDir+SETUPTXT, SETUP_TXT_MAX_LANE_DIGITS, false), DEFAULT_LANE_NUMBER_DIGITS);
  if (result < DEFAULT_LANE_NUMBER_DIGITS) then
  begin
    SM(Format('GetMaxLanesFromSetupTxt - Lane number digits should be at least %d. (%d in setup.txt)', [DEFAULT_LANE_NUMBER_DIGITS, result]));
    result := DEFAULT_LANE_NUMBER_DIGITS;
  end
  else if (result > MAX_LANE_NUMBER_DIGITS) then
  begin
    SM(Format('GetMaxLanesFromSetupTxt - Lane number digits cannot exceed %d. (%d in setup.txt)', [MAX_LANE_NUMBER_DIGITS, result]));
    result := DEFAULT_LANE_NUMBER_DIGITS;
  end;
end;

function CleanMD5(aMD5: string): string;    //68127
const
  HEX_CHARS: set of char = ['A'..'F','0'..'9'];
var
  i: integer;
begin
  result := '';
  for i := 1 to length(aMD5) do
    if aMD5[i] in HEX_CHARS
      then result := result + aMD5[i]
      else break;                           //stop once we get to ETX or any non_hex char
end;

function IsHostOneOf(aHost, aHostList: string): boolean;          // TFS-16036
var
  HostList: TStringList;
  Host: string;
begin
  Result := False;
  HostList := TStringList.Create;
  try
    HostList.Delimiter := '/';
    HostList.StrictDelimiter := True;
    HostList.DelimitedText := aHostList;
    for Host in HostList do
    begin
      result := SameText(aHost, Host);
      if result then
        Break;
    end;
  finally
    HostList.Free;
  end;
end;

function IsHostOneOf(const aHost: string; const aHostListToCheck: array of string): boolean;    // CPCLIENTS-10869
var
  i: integer;
begin
  Result := False;
  for i := Low(aHostListToCheck) to High(aHostListToCheck) do
  begin
    if aHostListToCheck[i].ToUpper.Equals(aHost.ToUpper) then
      Exit(True);
  end;
end;

function GetLaneType(LaneNumber: string): string;
var
  tmpLane : LanesXML.TXMLLaneType;//CPCLIENTS-13825
begin
  result := '';
  tmpLane := nil;   //CPCLIENTS-13825 - Fix AV, when requested Lane is not configured.
  // TFS-39234 - Fix AV when forwarding offlines during sign on and XMLLanes object is nil
  if not Assigned(XMLLanes) then
    LoadLanesXML(LanesXML_);

  if Assigned(XMLLanes) then
  begin
    //CPCLIENTS-13825 - Fix AV, when requested Lane is not configured.
    tmpLane := XMLLanes.FindLane(LaneNumber ,soLaneNumber);

    if Assigned(tmpLane) then //CPCLIENTS-13825
      result := XMLLanes.FindLane(LaneNumber ,soLaneNumber).LaneType;
  end;
end;

procedure CheckIniFiles;                                                // CPCLIENTS-11670
const
  FileNames: array[0..1] of string = ('Setup.txt', 'OpenEPS.ini');
var
  FileName: string;
begin
    if not DllTypes.IniFilesProcessed then
    begin
      for Filename in FileNames do
      begin
        if FixAnyErrorsInIniFile(FileName) then
          MsgNotice('Errors in ' + FileName + ' were fixed.');
      end;
      DllTypes.IniFilesProcessed := True;
    end;
end;

function IniFileKeyIsBad(const AKey: string): boolean;    // CPCLIENTS-11670
begin
  Result := (AKey > '') and not AKey.Trim.StartsWith('//') and AKey.Contains(' ');    // if this is commented out then ignore it
end;

function FixAnyErrorsInIniFile(const AFileName: string): boolean;    // CPCLIENTS-11670
var
  FullPath: string;
  FileStuff: TStringList;
  AName: string;
  i: integer;
begin
  Result := False;
  FullPath := DefaultDir + AFileName;
  if FileExists(FullPath) then
  begin
    FileStuff := TStringList.Create;
    try
      FileStuff.Duplicates := dupIgnore;
      FileStuff.LoadFromFile(FullPath);
      for i := 0 to FileStuff.Count - 1 do
      begin
        AName := FileStuff.Names[i];
        if IniFileKeyIsBad(AName) then
        begin
          Result := True;
          FileStuff[i] := AName.Trim + FileStuff.NameValueSeparator + FileStuff.ValueFromIndex[i];
        end;
      end;
      if Result then
        FileStuff.SaveToFile(FullPath);
    finally
      FileStuff.Free;
    end;
  end;
end;

procedure ThrottleIfNecessary(ADelayMS: integer; ALastMsgSentAt, ALastMsgRcvdAt: TDateTime);      // CPCLIENTS-15794
var
  timeDiffSend: Int64;
  timeDiffRevc: Int64;
  waitTime: Int64;
begin
  timeDiffRevc := MilliSecondsBetween( Now, ALastMsgRcvdAt );
  timeDiffSend := MilliSecondsBetween( Now, ALastMsgSentAt );
  if timeDiffRevc = 0 then
    timeDiffRevc := timeDiffSend;
  waitTime := min(timeDiffSend, timeDiffRevc);

  MsgDebug( 'waitTime: %d', [ waitTime ] );
  MsgDebug( 'Last time sent: %s', [FormatDateTime( 'hh:mm:ss:zzz', ALastMsgSentAt )] );
  MsgDebug( 'Last time received: %s', [FormatDateTime( 'hh:mm:ss:zzz', ALastMsgRcvdAt )] );

  if( waitTime < ADelayMS )then
  begin
    MsgWarn( 'Force throttling:' + intToStr( ADelayMS - waitTime ));
    MtxSleep(ADelayMS - waitTime);
  end;
end;


procedure GlobalVarInit;
begin
  ExtendedLog('GlobalVarInit', procedure
  begin
    {$IFDEF MTXEPSDLL}
    epsTrace.CheckTrace;
    {$ENDIF}
  end
  );
end;

(* // XE: Remove WinEPS - fuel
function IsFuelAndWorldPay(HostSuffix: string): boolean;     //65869
const
  WORLDPAY_SUFFIX = 'LYN';       //65869
begin
  {$IFDEF FUEL}
  result := SameText(HostSuffix,WORLDPAY_SUFFIX);
  //SM(format('MTX_Lib.IsFuelAndWorldPay = %s (%s)',[sTF[result],HostSuffix]));
  {$ELSE}
  result := false;
  {$ENDIF}
end;
*)

/// This is extracted due to compiler error on closure in initialization.
procedure MtxLibInitialization;
begin
  ExtendedLog('MTX_Lib Initialization', procedure
  begin
    randomize;        // to initialize seed for RandomHexString

    SetLength(TrueBoolStrs,2);
    SetLength(FalseBoolStrs,3);
    TrueBoolStrs[0]:='Y';
    TrueBoolStrs[1]:='YES';
    FalseBoolStrs[0]:='N';
    FalseBoolStrs[1]:='NO';
    FalseBoolStrs[2]:='';
    acctFileName  := WinEPSDir + AcctLog_Pfx + '01' + AcctLog_Sfx;
    ApprovedTrxCount := 0; // DEV-11499
    DeclinedTrxCount := 0;
  end
  );
end;

(*
procedure MsgNotice(Msg: string; SkipPostTransNum: boolean = False); overload;
begin
  // TODO -cMM: MsgNotice default body inserted
end;

procedure MsgNotice(Msg: string; const Args: array of const; SkipPostTransNum: boolean = False); overload;
begin
  // TODO -cMM: MsgNotice default body inserted
end;

procedure MsgError(Msg: string; SkipPostTransNum: boolean = False); overload;
begin
  // TODO -cMM: MsgError default body inserted
end;

procedure MsgError(Msg: string; const Args: array of const; SkipPostTransNum: boolean = False); overload;
begin
  // TODO -cMM: MsgError default body inserted
end;
*)

initialization

  MtxLibInitialization;

finalization
  {$IFDEF TEST} Exit; {$ENDIF}
  ExtendedLog('MTX_Lib Finalization', procedure
    begin
  {$IFNDEF RPT}
  {$IFNDEF MTXEPSDLL}                                     { JMR-4 }
  (* // XE: Remove WinEPS: keep JIC
    if Assigned(Err32Lines) then         { JMR-4 }
      Err32Lines.Free;
  *)
  {$ENDIF MTXEPSDLL}
  {$ENDIF RPT}

    SMDestroyLock;
    RegDestroyLock;
    end
  );
end.


