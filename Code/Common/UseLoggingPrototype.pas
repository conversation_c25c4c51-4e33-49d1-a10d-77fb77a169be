unit UseLoggingPrototype;
//prototype

{$I OpenEPS_DEF.inc}

{ To allow SensitiveLogging, the define ALLOW_SENSITIVE_LOG must be turned on }

interface

uses LoggingTypes, Logging, Classes {$IFDEF USE_CODESITE}, CodeSiteLogging {$ENDIF} {$IFNDEF UNIT_TEST}, DLLTypes {$ENDIF};

var
  BasicLog: TBaseLog = nil;
  DebugLog: TDebugLog = nil;
  SSLDebugLog: TDebugLog = nil;
  SensitiveLog: TDebugLog = nil;

procedure BeforeFinal;
procedure Log(Msg: String; LogType: TLogType = ltBasic; Internal: boolean = False); overload;
procedure Log(Fmt: String; Args: array of const; LogType:TLogType = ltBasic); overload;
procedure Log(Msg: AnsiString; LogType:TLogType = ltBasic); overload;
procedure Log(Fmt: AnsiString; Args: array of const; LogType:TLogType = ltBasic); overload;
procedure LoggingTrackDuplicateLines(DoTrack: boolean);
function ChangeLogFileName(const FileName: string; FileChange: TFileChange = fcRenameCurrentFile; Internal: boolean =
  False): boolean;
function RenameLogFile(const FileName: string; Internal: boolean = False): boolean;
procedure LoggingSetActive(IsActive: boolean; LogType:TLogType = ltDebug);
function RenameFileIfDateChanged(Internal: boolean = False): boolean;

{$IFDEF UNIT_TEST}
procedure ClearLog(LogType: TLogType = ltBasic; Internal: boolean = False);
function ReadLog: TStringList;
{$ENDIF}

implementation
{ -------------------------------------------------------------------------------------------------------------------- }
{ -------------------------------------------------------------------------------------------------------------------- }
uses System.SysUtils, System.SyncObjs, System.Generics.Collections{$IFNDEF UNIT_TEST}, MTX_Lib, MTX_Constants{$ENDIF};

var
  LogFileLock: TCriticalSection;
  BeforeFinalCalled: boolean;
  LaneJournalStarted: boolean;

function CreateBasicLog: boolean; forward;

{$IFNDEF UNIT_TEST}
function GetAltJrnlPath: string;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'GetAltJrnlPath' );{$ENDIF}
  result := '';
  result := MTX_Lib.Reg_Lookup(DefaultDir + OpenEPSIni, cALTJRNLPATH, false);
  if result = '' then
    result := DefaultDir;
  if result <> '' then
  begin
    result := IncludeTrailingPathDelimiter(result);
    if NOT DirectoryExists(result) then
      ForceDirectories(result);
  end;
end;
{$ENDIF}

procedure CheckForCS;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'CheckForCS' );{$ENDIF}
  if not Assigned(LogFileLock) then
    LogFileLock := TCriticalSection.Create;
end;

function CreateBasicLog: boolean;
var
  FullLogPath: string;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'CreateBasicLog' );{$ENDIF}
  Result := False;
  if not Assigned(Logging.FileContents) then
    Exit;
  CheckForCS;
  LogFileLock.Acquire;
  try
    {$IFDEF UNIT_TEST}
    FullLogPath := IncludeTrailingPathDelimiter(ExtractFilePath(ParamStr(0))) + DEFAULT_LOG_FILE_NAME;
    {$ELSE}
    FullLogPath := IncludeTrailingPathDelimiter(GetAltJrnlPath) + DEFAULT_LOG_FILE_NAME;
    {$ENDIF}
    BasicLog := TBaseLog.Create(FullLogPath);
    if Not BasicLog.LoggerThreadStarted then
    begin
      // BasicLog.StartThread;
      Sleep(1000);
    end;
    Result := True;
  except
    Result := False;
  end;
  LogFileLock.Release;
end;

function CreateDebugLog: boolean;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'CreateDebugLog' );{$ENDIF}
  if not Assigned(BasicLog) then
  begin
    Result := CreateBasicLog;
    if not Result then
      Exit;
  end;
  CheckForCS;
  LogFileLock.Acquire;
  try
    DebugLog := TDebugLog.Create(BasicLog, ltDebug);
    Result := True;
  except
    Result := False;
  end;
  LogFileLock.Release;
end;

function CreateSSLDebugLog: boolean;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'CreateSSLDebugLog' );{$ENDIF}
  if not Assigned(BasicLog) then
  begin
    Result := CreateBasicLog;
    if not Result then
      Exit;
  end;
  CheckForCS;
  LogFileLock.Acquire;
  try
    SSLDebugLog := TDebugLog.Create(BasicLog, ltSSLDebug);
    Result := True;
  except
    Result := False;
  end;
  LogFileLock.Release;
end;

function CreateSensitiveLog: boolean;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'CreateSensitiveLog' );{$ENDIF}
  Result := False;
{$IFDEF ALLOW_SENSITIVE_LOG}
  if not Assigned(BasicLog) then
  begin
    Result := CreateBasicLog;
    if not Result then
      Exit;
  end;
  CheckForCS;
  LogFileLock.Acquire;
  try
    SensitiveLog := TDebugLog.Create(BasicLog, ltSensitive);
    Result := True;
  except
    Result := False;
  end;
  LogFileLock.Release;
{$ENDIF}
end;

{$IFDEF UNIT_TEST}
procedure ClearLog(LogType: TLogType = ltBasic; Internal: boolean = False);
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'ClearLog' );{$ENDIF}
  if BeforeFinalCalled or not LaneJournalStarted then
    Exit;
  case LogType of
    ltBasic:       if Assigned(BasicLog) or CreateBasicLog then
                     BasicLog.ClearLog;
    ltDebug:       if Assigned(DebugLog) or CreateDebugLog then
                     DebugLog.ClearLog;
    ltSSLDebug:    if Assigned(SSLDebugLog) or CreateSSLDebugLog then
                     SSLDebugLog.ClearLog;
    ltSensitive:   {$IFDEF ALLOW_SENSITIVE_LOG}
                   if Assigned(SensitiveLog) or CreateSensitiveLog then
                     SensitiveLog.ClearLog;
                   {$ELSE}
                   begin end;
                   {$ENDIF}
  end;
end;
{$ENDIF}

procedure Log(Msg: String; LogType: TLogType = ltBasic; Internal: boolean = False); overload;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'Log' );{$ENDIF}
  if BeforeFinalCalled or not LaneJournalStarted then
    Exit;
  case LogType of
    ltBasic:       if Assigned(BasicLog) or CreateBasicLog then
                     BasicLog.Add(Msg);
    ltDebug:       if Assigned(DebugLog) or CreateDebugLog then
                     DebugLog.Add(DEBUG + Msg);
    ltSSLDebug:    if Assigned(SSLDebugLog) or CreateSSLDebugLog then
                     SSLDebugLog.Add(SSL_DEBUG + Msg);
    ltSensitive:   {$IFDEF ALLOW_SENSITIVE_LOG}
                   if Assigned(SensitiveLog) or CreateSensitiveLog then
                     SensitiveLog.Add(SENSITIVE + Msg);
                   {$ENDIF}
  end;
end;

procedure Log(Fmt: String; Args: array of const; LogType:TLogType = ltBasic); overload;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'Log' );{$ENDIF}
  if BeforeFinalCalled or not LaneJournalStarted then
    Exit;
  case LogType of
    ltBasic:       if Assigned(BasicLog) or CreateBasicLog then
                     BasicLog.Add(Fmt, Args);
    ltDebug:       if Assigned(DebugLog) or CreateDebugLog then
                     DebugLog.Add(DEBUG + Fmt, Args);
    ltSSLDebug:    if Assigned(SSLDebugLog) or CreateSSLDebugLog then
                     SSLDebugLog.Add(SSL_DEBUG + Fmt, Args);
    ltSensitive:   {$IFDEF ALLOW_SENSITIVE_LOG}
                   if Assigned(SensitiveLog) or CreateSensitiveLog then
                     SensitiveLog.Add(SENSITIVE + Fmt, Args);
                   {$ENDIF}
  end;
end;

procedure Log(Msg: AnsiString; LogType:TLogType = ltBasic); overload;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'Log' );{$ENDIF}
  if BeforeFinalCalled or not LaneJournalStarted then
    Exit;
  case LogType of
    ltBasic:       if Assigned(BasicLog) or CreateBasicLog then
                     BasicLog.Add(Msg);
    ltDebug:       if Assigned(DebugLog) or CreateDebugLog then
                     DebugLog.Add(DEBUG + Msg);
    ltSSLDebug:    if Assigned(SSLDebugLog) or CreateSSLDebugLog then
                     SSLDebugLog.Add(SSL_DEBUG + Msg);
    ltSensitive:   {$IFDEF ALLOW_SENSITIVE_LOG}
                   if Assigned(SensitiveLog) or CreateSensitiveLog then
                     SensitiveLog.Add(SENSITIVE + Msg);
                   {$ENDIF}
  end;
end;

procedure Log(Fmt: AnsiString; Args: array of const; LogType:TLogType = ltBasic); overload;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'Log' );{$ENDIF}
  if BeforeFinalCalled or not LaneJournalStarted then
    Exit;
  case LogType of
    ltBasic:       if Assigned(BasicLog) or CreateBasicLog then
                     BasicLog.Add(Fmt, Args);
    ltDebug:       if Assigned(DebugLog) or CreateDebugLog then
                     DebugLog.Add(DEBUG + Fmt, Args);
    ltSSLDebug:    if Assigned(SSLDebugLog) or CreateSSLDebugLog then
                     SSLDebugLog.Add(SSL_DEBUG + Fmt, Args);
    ltSensitive:   {$IFDEF ALLOW_SENSITIVE_LOG}
                   if Assigned(SensitiveLog) or CreateSensitiveLog then
                     SensitiveLog.Add(SENSITIVE + Fmt, Args);
                   {$ENDIF}
  end;
end;

procedure LoggingTrackDuplicateLines(DoTrack: boolean);
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'LoggingTrackDuplicateLines' );{$ENDIF}
  if BeforeFinalCalled or not LaneJournalStarted then
    Exit;
  if Assigned(BasicLog) or CreateBasicLog then
    BasicLog.TrackDuplicateLines := DoTrack;
end;

function ChangeLogFileName(const FileName: string; FileChange: TFileChange = fcRenameCurrentFile; Internal: boolean = False): boolean;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'ChangeLogFileName' );{$ENDIF}
  {$IFNDEF UNIT_TEST}
  if Pos(UpperCase(JournalFilePfx), UpperCase(ExtractFileName(FileName))) > 0 then
    LaneJournalStarted := True
  else
    LaneJournalStarted := False;
  {$ENDIF}
  if BeforeFinalCalled or not LaneJournalStarted then
    Exit;
  if Assigned(BasicLog) or CreateBasicLog then
    Result := BasicLog.ChangeFile(FileName, FileChange)
  else
    Result := False;
end;

function RenameLogFile(const FileName: string; Internal: boolean = False): boolean;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'RenameLogFile' );{$ENDIF}
  {$IFNDEF UNIT_TEST}
  if Pos(UpperCase(JournalFilePfx), UpperCase(ExtractFileName(FileName))) > 0 then
    LaneJournalStarted := True
  else
    LaneJournalStarted := False;
  {$ENDIF}
  if BeforeFinalCalled or not LaneJournalStarted then
    Exit;
  Result := ChangeLogFileName(FileName);
end;

procedure LoggingSetActive(IsActive: boolean; LogType:TLogType = ltDebug);
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'LoggingSetActive' );{$ENDIF}
  if BeforeFinalCalled or not LaneJournalStarted then
    Exit;
  case LogType of
    ltDebug:       if Assigned(DebugLog) or CreateDebugLog then
                     DebugLog.Active := IsActive;
    ltSSLDebug:    if Assigned(SSLDebugLog) or CreateSSLDebugLog then
                     SSLDebugLog.Active := IsActive;
    {$IFDEF ALLOW_SENSITIVE_LOG}
    ltSensitive:   if Assigned(SensitiveLog) or CreateSensitiveLog then
                     SensitiveLog.Active := IsActive;
    {$ENDIF}
  end;
end;

function RenameFileIfDateChanged(Internal: boolean = False): boolean;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'RenameFileIfDateChanged' );{$ENDIF}
  if BeforeFinalCalled or not LaneJournalStarted then
    Exit;
  Result := BasicLog.RenameIfDateChanged;
end;

{$IFDEF UNIT_TEST}
function ReadLog: TStringList;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'ReadLog' );{$ENDIF}
  if BeforeFinalCalled or not LaneJournalStarted then
    Exit;
  if Assigned(BasicLog) then
    Result := TStringList(BasicLog.Read)
  else
    Result := TStringList.Create;
end;
{$ENDIF}

procedure BeforeFinal;
begin
  BeforeFinalCalled := True;
  if Assigned(SensitiveLog) then
    FreeAndNil(SensitiveLog);
  if Assigned(SSLDebugLog) then
    FreeAndNil(SSLDebugLog);
  if Assigned(DebugLog) then
    FreeAndNil(DebugLog);
  if Assigned(BasicLog)then
    FreeAndNil(BasicLog);
  if Assigned(LogFileLock) then
    FreeAndNil(LogFileLock);
end;

initialization
  BeforeFinalCalled := False;
  {$IFDEF UNIT_TEST}
  LaneJournalStarted := True;
  {$ELSE}
  LaneJournalStarted := False;
  {$ENDIF}

finalization


end.

