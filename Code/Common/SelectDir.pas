// (c) MTXEPS, Inc. 1988-2008
unit SelectDir;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, LMDCustomControl, LMDCustomPanel, LMDCustomBevelPanel,
  LMDCustomParentPanel, LMDCustomPanelFill, LMDPanelFill, StdCtrls,
  Buttons, ComCtrls, ShellCtrls;

type
  TFSelectDir = class(TForm)
    TV: TShellTreeView;
    BitBtn1: TBitBtn;
    BitBtn2: TBitBtn;
    pnTitle: TLMDPanelFill;
  private
    { Private declarations }
  public
    Path: string;
    function Execute(initPath: string): boolean;
  end;

var
  FSelectDir: TFSelectDir;

implementation

uses MTX_Lib;

{$R *.dfm}

function TFSelectDir.Execute(initPath: string): boolean;
begin
  result := false;
  Path := '';
  if DirectoryExists(initPath) then
  begin
    TV.Path := initPath;
    //TV.SelectedFolder := initPath;
  end;
  if showModal = mrOK then
  begin
    Path := TrailingBackSlash(TV.Path);
    result := true;
  end;
end;

end.
