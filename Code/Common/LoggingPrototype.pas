unit LoggingPrototype;
// <PERSON><PERSON>'s logging class prototype

{$I OpenEPS_DEF.inc}

interface

Uses LoggingTypes, System.Classes, System.SysUtils, System.Variants, System.Generics.Defaults, System.Generics.Collections,
     System.SyncObjs, System.IOUtils;

const
  MAX_REPEAT_MSGS = 5;
  WAIT_TIME = 1000;   // 1 second

{$M+}
type

TRepeatMessages= class
private
  FHashValue: Integer;
  FMsg: string;
  FRepeatCount: Integer;
  FWhenAdded: TDateTime;
  constructor Create(Msg: string; AddedAt: TDateTime); overload;
  constructor Create(Msg: string; HashValue: integer; AddedAt: TDateTime); overload;
  procedure SetMsg(const Value: string);
  procedure SetRepeatCount(const Value: Integer);
published
  property HashValue: Integer read FHashValue;
  property Msg: string read FMsg write SetMsg;
  property RepeatCount: Integer read FRepeatCount write SetRepeatCount;
end;

TLogMsgType = (lmtAdd, lmtRenameCurrentFile, lmtStartNewFile, lmtRenameKeepingCurrentFile, lmtClear, lmtRead,
               lmtDupsOn, lmtDupsOff, lmtRenameIfDateChanged);

TLogInternalMsg = class
private
  FLogMsg: string;
  FLogMsgDate: TDateTime;
  FLogMsgType: TLogMsgType;
  procedure SetLogMsg(const Value: string);
  procedure SetLogMsgType(const Value: TLogMsgType);
  property LogMsgDate: TDateTime read FLogMsgDate write FLogMsgDate;
public
  constructor Create; overload;
  constructor Create(LogMsgType: TLogMsgType; const LogMsg: string); overload;
  destructor Destroy; override;
published
  property LogMsgType: TLogMsgType read FLogMsgType write SetLogMsgType;
  property LogMsg: string read FLogMsg write SetLogMsg;
end;

TThreadLog = class sealed(TObject)
private type TMatchResult = (mrNotFound, mrFound, mrFoundOutOfOrder);
strict private
  class var _ThreadLogInstance: TThreadLog;
private
  FBaseLogPath: string;
  ThreadLogLock: TCriticalSection;
  FileAccessLock: TCriticalSection;
  FRepeatMessages: TObjectList<TRepeatMessages>;   /// MCD
  FFileName: string;
  FTrackDuplicateLines: Boolean;
  FMatchedMsgNo: Integer;
  procedure AddMsgToQueue(Msg: string; HashValue: integer; ATimestamp: TDateTime);
  procedure CleanUpQueue(Msg: string; HashValue: integer; ATimestamp: TDateTime);
  function NextMsgToMatch: integer;
  function MessageFoundInQueue(Msg: string; var HashValue: integer): TMatchResult;
  procedure OutputToFile(Msg: string; DateTime: TDateTime = 0);
  procedure PrintAndCleanRepeatedMsgs;
  procedure PrintQueue(PrintLogClosed: boolean);
  procedure PrintOneMsg(MsgNo: integer);
  procedure PrintRepeatedMsgLine(MsgCount: integer; NumberOfLines: integer = 1);
  procedure PrintRepeatedMsgs;
  procedure SetBaseLogPath(const Value: string);
  procedure SetFileName(const Value: string);
  procedure SetTrackDuplicateLines(const Value: Boolean);
  property BaseLogPath: string read FBaseLogPath write SetBaseLogPath;
public
  class function Create(AFileName, AFilePath: string): TThreadLog;
  destructor Destroy; override;
  ///	<summary>
  ///	  Add a new string message to the log. If the message already exists in
  ///	  the log and it is in the correct order, a counter for the message
  ///	  will be incremented.
  ///	</summary>
  ///	<param name="Msg">
  ///	  The string message to be added
  ///	</param>
  /// <param name="ATimestamp">
  ///   The TDateTime that the message was added
  /// </param>
  procedure Add(const Msg: string; ATimestamp: TDateTime = 0); overload;

  ///	<summary>
  ///	  Add a new message using the standard Format function.
  ///	</summary>
  ///	<param name="FmtStr">
  ///	  The string to be used a the format for the output.
  ///	</param>
  ///	<param name="Args">
  ///	  An array of values to be inserted into the FmtStr.
  ///	</param>
  procedure Add(const FmtStr: string; const Args: array of const); overload;

  ///	<summary>
  ///	  Changes the name of the current log file.
  ///	</summary>
  ///	<param name="FileName">
  ///	  This will become the name of the current log file.
  ///	</param>
  ///	<param name="FileChange">
  ///	  Of type TFileChange (fcRenameCurrentFile, fcStartNewFile,
  ///	  fcRenameKeepingCurrentFile)
  ///	</param>
  ///	<returns>
  ///	  True if successfully changed, otherwise false.
  ///	</returns>
  ///	<remarks>
  ///	  <para>
  ///	    If a file with FileName exists, it will be deleted.
  ///	  </para>
  ///	  <para>
  ///	    If the FileName parameter is the same as the current log file name,
  ///	    nothing is done.
  ///	  </para>
  ///	  <para>
  ///	    fcRenameCurrentFile will just do a simple rename of the log file,
  ///	    keeping all information in the current log file. fcStartNewFile
  ///	    will create a new empty log file under the new FileName but keep
  ///	    the old log file. fcRenameKeepingCurrentFile will rename the
  ///	    current log file with the FileName parameter and continue to log to
  ///	    a new file with the existing file name.
  ///	  </para>
  ///	</remarks>
  function ChangeFile(FileName: string; FileChange: TFileChange = fcRenameCurrentFile): boolean;
  {$IFDEF UNIT_TEST}
  procedure ClearLog;
  function Read: TStrings;
  procedure ReadAndReport;
  {$ENDIF}
  function RenameIfDateChanged: Boolean;
published
  property FileName: string read FFileName write SetFileName;
  property TrackDuplicateLines: Boolean read FTrackDuplicateLines write SetTrackDuplicateLines;
end;

TLoggerThread = class(TThread)
private
  FThreadLog: TThreadLog;
  FFileName: string;
  FQueueLock: TCriticalSection;
  FQueueAdded: TEvent;
  FKilled: boolean;
  FLogThreadStarted: Boolean;
  FLogPath: string;
  FLogInternalQueue: TSimpleThreadedQueue<TLogInternalMsg>;
  procedure SetLogPath(const Value: string);
protected
  procedure Execute; override;
  function QueueCount: integer;
  function GetNextQueueMsg: TLogInternalMsg;
  procedure ProcessQueueMsg(LogInternalMsg: TLogInternalMsg);
public
  property FileName: string read FFileName write FFileName;
  property LogThreadStarted: Boolean read FLogThreadStarted write
      FLogThreadStarted;
  constructor Create(CreateSuspended: boolean; AFileName, AFilePath: string); overload;
  destructor Destroy; override;
  procedure QueueMsg(LogMsgType: TLogMsgType; LogMsg: string);
  procedure Kill;
published
  property LogPath: string read FLogPath write SetLogPath;
end;

{ ============================== Logging Classes ========================================== }
{ This is a singleton class - only one object can be created }

TBaseLog = class sealed (TObject)
strict private
  class var _BaseLogInstance: TBaseLog;
private
  BaseLogLock: TCriticalSection;
  FFileName: string;
  FTrackDuplicateLines: Boolean;
  FLoggerThread: TLoggerThread;
  FLoggerThreadStarted: Boolean;
  FLogPath: string;
  FStarted: boolean;
  FTmpQueue: TSimpleThreadedQueue <String>;
  {$IFDEF USE_CODESITE}
  procedure JustForCodeSite(s: string);
  {$ENDIF}
  function AddToQueue(LogType: TLogType; MsgType, Msg, Fmt: string; Args: Array of Const): boolean;
  function CharToFileChangeType(Ch: Char): TFileChange;
  function CharToLogType(Ch: Char): TLogType;
  function FileChangeTypeToChar(FileChange: TFileChange): Char;
  function GetLoggerThreadStarted: Boolean;
  procedure SetFileName(const Value: string);
  procedure SetTrackDuplicateLines(const Value: Boolean);
  procedure LogThreadComplete(Sender: TObject);
  function LogTypeToChar(LogType: TLogType): Char;
  procedure QueueIt(s: string);
  procedure SetLogPath(const Value: string);
  procedure StartLog;
  property Started: boolean read FStarted write FStarted;
public
  property LoggerThreadStarted: Boolean read GetLoggerThreadStarted write FLoggerThreadStarted;
  class function Create(AFileName: string): TBaseLog;
  destructor Destroy; override;
  procedure StartThread;

  ///	<summary>
  ///	  Add a new string message to the log. If the message already exists in
  ///	  the log and it is in the correct order, a counter for the message
  ///	  will be incremented.
  ///	</summary>
  ///	<param name="Msg">
  ///	  The string message to be added
  ///	</param>
  procedure Add(const Msg: string; Internal: boolean = False); overload;

  ///	<summary>
  ///	  Add a new AnsiString message to the log. If the message already
  ///	  exists in the log and it is in the correct order, a counter for the
  ///	  message will be incremented.
  ///	</summary>
  ///	<param name="Msg">
  ///	  ANSI string message to log.
  ///	</param>
  procedure Add(const Msg: AnsiString; Internal: boolean = False); overload;

  ///	<summary>
  ///	  Add a new message using the standard Format function.
  ///	</summary>
  ///	<param name="FmtStr">
  ///	  The string to be used a the format for the output.
  ///	</param>
  ///	<param name="Args">
  ///	  An array of values to be inserted into the FmtStr.
  ///	</param>
  procedure Add(const FmtStr: string; const Args: array of const); overload;

  ///	<summary>
  ///	  Add a new AnsiString message using the standard Format function.
  ///	</summary>
  ///	<param name="FmtStr">
  ///	  The AnsiString to be used a the format for the output.
  ///	</param>
  ///	<param name="Args">
  ///	  An array of values to be inserted into the FmtStr.
  ///	</param>
  procedure Add(const FmtStr: AnsiString; const Args: array of const); overload;

  ///	<summary>
  ///	  Changes the name of the current log file.
  ///	</summary>
  ///	<param name="FileName">
  ///	  This will become the name of the current log file.
  ///	</param>
  ///	<param name="FileChange">
  ///	  Of type TFileChange (fcRenameCurrentFile, fcStartNewFile,
  ///	  fcRenameKeepingCurrentFile)
  ///	</param>
  ///	<returns>
  ///	  True if successfully changed, otherwise false.
  ///	</returns>
  ///	<remarks>
  ///	  <para>
  ///	    If a file with FileName exists, it will be deleted.
  ///	  </para>
  ///	  <para>
  ///	    If the FileName parameter is the same as the current log file name,
  ///	    nothing is done.
  ///	  </para>
  ///	  <para>
  ///	    fcRenameCurrentFile will just do a simple rename of the log file,
  ///	    keeping all information in the current log file. fcStartNewFile
  ///	    will create a new empty log file under the new FileName but keep
  ///	    the old log file. fcRenameKeepingCurrentFile will rename the
  ///	    current log file with the FileName parameter and continue to log to
  ///	    a new file with the existing file name.
  ///	  </para>
  ///	</remarks>
  function ChangeFile(FileName: string; FileChange: TFileChange =fcRenameCurrentFile; Internal: boolean = False):
    boolean;
  function RenameIfDateChanged( Internal: boolean = False): Boolean;
  {$IFDEF UNIT_TEST}
  procedure ClearLog(Internal: boolean = False);
  function Read: TStrings;
  {$ENDIF}
published
  property FileName: string read FFileName write SetFileName;
  property LogPath: string read FLogPath write SetLogPath;
  property TrackDuplicateLines: Boolean read FTrackDuplicateLines write SetTrackDuplicateLines;
end;

TDebugLog = class(TObject)
private
  FActive: boolean;
  FLog: TBaseLog;
  FLogType: TLogType;
  procedure SetActive(const Value: boolean);
  procedure SetLog(const Value: TBaseLog);
public
  constructor Create; overload;
  constructor Create(ALog: TBaseLog; ALogType: TLogType); overload;
  destructor Destroy; override;
  procedure Add(const Msg: string); overload;
  procedure Add(const Msg: AnsiString); overload;
  procedure Add(const FmtStr: string; const Args: array of const); overload;
  procedure Add(const FmtStr: AnsiString; const Args: array of const); overload;
  {$IFDEF UNIT_TEST}
  procedure ClearLog;
  function Read: TStrings;
  {$ENDIF}
published
  property Active: boolean read FActive write SetActive;
  property Log: TBaseLog write SetLog;
end;

var
  ReadSignalEvent:       TEvent;
  FileChangeSignalEvent: TEvent;
  FreeLogSignalEvent:    TEvent;
  FileContents:          TStrings;

implementation

{$IF DEFINED(UNIT_TEST) OR DEFINED(USE_CODESITE)}
uses
{$IFEND}
{$IFDEF UNIT_TEST}VCL.Forms{$ENDIF}
{$IFDEF USE_CODESITE}{$IFDEF UNIT_TEST},{$ENDIF}CodeSiteLogging{$ENDIF}
{$IF DEFINED(UNIT_TEST) OR DEFINED(USE_CODESITE)}
;
{$IFEND}

const
  CLEAR_LOG =    'C';
  ADD_MSG =      'A';
  CHANGE_NAME =  'N';
  RENAME =       'R';
  DATE_CHANGE =  'D';
{ -------------------------------------------------------------------------------------------------------------------- }
class function TBaseLog.Create(AFileName: string): TBaseLog;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'TBaseLog.Create: ' + AFileName );{$ENDIF}
  if _BaseLogInstance = nil then
  begin
    _BaseLogInstance := inherited Create;
    _BaseLogInstance.FLogPath := ExtractFilePath(AFileName);
    _BaseLogInstance.FFileName := ExtractFileName(AFileName);
    _BaseLogInstance.FTrackDuplicateLines := True;
    _BaseLogInstance.BaseLogLock := TCriticalSection.Create;
    _BaseLogInstance.FLoggerThread := TLoggerThread.Create(True, _BaseLogInstance.FFileName, _BaseLogInstance.FLogPath);
    _BaseLogInstance.FLoggerThreadStarted := False;
    _BaseLogInstance.FStarted := False;
    // _BaseLogInstance.FLoggerThread.FreeOnTerminate := True;
    _BaseLogInstance.FLoggerThread.OnTerminate := _BaseLogInstance.LogThreadComplete;
    _BaseLogInstance.FTmpQueue := TSimpleThreadedQueue<String>.Create;

    _BaseLogInstance.StartThread;
    if _BaseLogInstance.FLoggerThread.LogThreadStarted then
      _BaseLogInstance.StartLog;

  end;
  Result := _BaseLogInstance;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
destructor TBaseLog.Destroy;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'TBaseLog.Destroy' );{$ENDIF}
  FLoggerThread.Kill;
  FLoggerThread.Terminate;
  FLoggerThread.FQueueAdded.SetEvent;
  FLoggerThread.WaitFor;
  FreeAndNil(FLoggerThread);
  FreeAndNil(FTmpQueue);
  FreeAndNil(BaseLogLock);
  _BaseLogInstance := nil;
  inherited Destroy;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TBaseLog.Add(const Msg: string; Internal: boolean = False);
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'TBaseLog.Add' );{$ENDIF}

  if not FStarted and not Internal then
  begin
    AddToQueue(ltBasic, ADD_MSG, Msg, '', []);
    Exit;
  end;

  BaseLogLock.Enter;
  try
    FLoggerThread.QueueMsg(lmtAdd, Msg);
  finally
    BaseLogLock.Leave;
  end;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TBaseLog.Add(const Msg: AnsiString; Internal: boolean = False);
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'TBaseLog.Add' );{$ENDIF}
  Add(String(Msg));
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TBaseLog.Add(const FmtStr: string; const Args: array of const);
var
  Msg: string;

  function MakeArgsString: string;
  var
    i: integer;
    s: string;
  begin
    {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'MakeArgsString' );{$ENDIF}
    Result := '[';
    for i := Low(Args) to High(Args) do
    begin
      case Args[i].vtype of
        vtinteger: s := IntToStr(Args[i].VInteger);
        vtboolean: s := BoolToStr(Args[i].vboolean, True);
        vtchar: s := String(Args[i].vchar);
        vtextended: s := FloatToStr(Args[i].VExtended^);
        vtString: s := String(Args[i].VString^);
        vtPointer: s := IntToStr(Longint(Args[i].VPointer));
        vtPChar: s := String(Args[i].VPChar);
        vtAnsiString: s := String(AnsiString(Args[i].VAnsiString));
        else s := '<Unknown>'
      end;
      if Length(Result) > 1 then
        Result := Result + ', ' + s
      else
        Result := Result + s;
    end;
    Result := Result + ']';
  end;

begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'TBaseLog.Add' );{$ENDIF}
  BaseLogLock.Enter;
  try
    Msg := Format(FmtStr, Args);
  except
    Msg := 'LOG ERROR - BAD FORMAT adding: ''' + FmtStr + ''' [Args]=' + MakeArgsString;
  end;

  if not FStarted then
  begin
    AddToQueue(ltBasic, ADD_MSG, Msg, '', []);
    Exit;
  end;

  try
    FLoggerThread.QueueMsg(lmtAdd, Msg);
  finally
    BaseLogLock.Leave;
  end;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TBaseLog.Add(const FmtStr: AnsiString; const Args: array of const);
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'TBaseLog.Add' );{$ENDIF}
  Add(String(FmtStr), Args);
end;
{ -------------------------------------------------------------------------------------------------------------------- }
function TBaseLog.AddToQueue(LogType: TLogType; MsgType, Msg, Fmt: string;
    Args: Array of Const): boolean;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod(Self, 'AddToQueue');{$ENDIF}
  Result := False;
  {$IFNDEF ALLOW_SENSITIVE_LOG}
  if LogType = ltSensitive then
  begin
    if Length(Msg) > 0 then
      FillChar(Msg, Length(Msg) * SizeOf(Msg[1]), #0);
    if Length(Fmt) > 0 then
      FillChar(Fmt, Length(Fmt) * SizeOf(Fmt[1]), #0);
    Exit;
  end;
  {$ENDIF N ALLOW_SENSITIVE_LOG}
  if Length(Msg) > 0 then
    QueueIt(MsgType + LogTypeToChar(LogType) + Msg)
  else if Length(Fmt) > 0 then
    QueueIt(MsgType + LogTypeToChar(LogType) + Format(Fmt, Args))
  else
    QueueIt(MsgType + LogTypeToChar(LogType));
end;
{ -------------------------------------------------------------------------------------------------------------------- }
function TBaseLog.ChangeFile(FileName: string; FileChange: TFileChange =
    fcRenameCurrentFile; Internal: boolean = False): boolean;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'TBaseLog.ChangeFile' );{$ENDIF}

  if not FStarted and not Internal then
  begin
    AddToQueue(ltBasic, CHANGE_NAME, FileChangeTypeToChar(FileChange) + FileName, '', []);
    {$IFDEF USE_CODESITE}CodeSite.Send('FileChange added to queue');{$ENDIF}
    Exit;
  end;

  BaseLogLock.Enter;
  try
    FileName := ExtractFileName(FileName);
    case FileChange of
      fcRenameCurrentFile: FLoggerThread.QueueMsg(lmtRenameCurrentFile, FileName);
      fcStartNewFile: FLoggerThread.QueueMsg(lmtStartNewFile, FileName);
      fcRenameKeepingCurrentFile: FLoggerThread.QueueMsg(lmtRenameKeepingCurrentFile, FileName);
    end;
    if FileChange <> fcRenameKeepingCurrentFile then
        FFileName := FileName;
  finally
    BaseLogLock.Leave;
  end;
  {$IFDEF USE_CODESITE}CodeSite.Send('FileChange processed - waiting');{$ENDIF}

  { Wait up to 25 seconds for a response }
  if FileChangeSignalEvent.WaitFor(25*WAIT_TIME) = wrSignaled then
  begin
    FileChangeSignalEvent.ResetEvent;
    Result := True;
  end
  else
    Result := False;
end;

function TBaseLog.CharToFileChangeType(Ch: Char): TFileChange;
begin
  if Ch = '0' then
    Result := fcRenameCurrentFile
  else if Ch = '1' then
    Result := fcStartNewFile
  else if Ch = '2' then
    Result := fcRenameKeepingCurrentFile
  else
    Result := fcRenameCurrentFile;
end;

{ -------------------------------------------------------------------------------------------------------------------- }
function TBaseLog.CharToLogType(Ch: Char): TLogType;
begin
  if Ch = '0' then
    Result := ltBasic
  else if Ch = '1' then
    Result := ltDebug
  else if Ch = '2' then
    Result := ltSSLDebug
  else if Ch = '3' then
    Result := ltSensitive
  else
    Result := ltBasic;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
{$IFDEF UNIT_TEST}
procedure TBaseLog.ClearLog(Internal: boolean = False);
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'TBaseLog.ClearLog' );{$ENDIF}
  if not LoggerThreadStarted and not Internal then
  begin
    AddToQueue(ltBasic, CLEAR_LOG, '', '', []);
    Exit;
  end;

  BaseLogLock.Enter;
  try
    FLoggerThread.QueueMsg(lmtClear, '');
  finally
    BaseLogLock.Leave;
  end;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
{$ENDIF}
function TBaseLog.FileChangeTypeToChar(FileChange: TFileChange): Char;
begin
  case FileChange of
    fcRenameCurrentFile: Result := '0';
    fcStartNewFile: Result := '1';
    fcRenameKeepingCurrentFile: Result := '2';
  end;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
function TBaseLog.GetLoggerThreadStarted: Boolean;
begin
  FLoggerThreadStarted := FLoggerThread.LogThreadStarted;
  Result := FLoggerThreadStarted;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
{$IFDEF USE_CODESITE}
procedure TBaseLog.JustForCodeSite(s: string);
var
  i: integer;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( Self, 'JustForCodeSite' );{$ENDIF}
  CodeSite.Send(s);
end;
{ -------------------------------------------------------------------------------------------------------------------- }
{$ENDIF}
procedure TBaseLog.LogThreadComplete(Sender: TObject);
var
  Thread: TLoggerThread;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( Self, 'LogThreadComplete' );{$ENDIF}
  Thread := Sender as TLoggerThread;
  if Thread.FatalException <> nil then
  begin
    {$IFDEF USE_CODESITE}
    CodeSite.Send('************************************************* TLoggerThread.FatalException');
    {$ENDIF}
  end;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
function TBaseLog.LogTypeToChar(LogType: TLogType): Char;
begin
  case LogType of
    ltBasic: Result :=     '0';
    ltDebug: Result :=     '1';
    ltSSLDebug: Result :=  '2';
    ltSensitive: Result := '3';
    else Result :=         '0';
  end;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TBaseLog.QueueIt(s: string);
begin
  try
    FTmpQueue.Enqueue(s);
  finally
  end;

  if LoggerThreadStarted then
    StartLog;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
{$IFDEF UNIT_TEST}
function TBaseLog.Read: TStrings;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'TBaseLog.Read' );{$ENDIF}
  BaseLogLock.Enter;
  try
    FLoggerThread.QueueMsg(lmtRead, '');
  finally
    BaseLogLock.Leave;
  end;
  Result := TStringList.Create;
  { Wait up to 25 seconds for a response }
  if ReadSignalEvent.WaitFor(25*WAIT_TIME) = wrSignaled then
  begin
    ReadSignalEvent.ResetEvent;
    Result.Assign(FileContents);
  end;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
{$ENDIF}
function TBaseLog.RenameIfDateChanged( Internal: boolean = False): Boolean;
begin
  if not FStarted and not Internal then
  begin
    AddToQueue(ltBasic, DATE_CHANGE, '', '', []);
    Exit;
  end;

  FLoggerThread.QueueMsg(lmtRenameIfDateChanged, '');
  { Wait up to 25 seconds for a response }
  if FileChangeSignalEvent.WaitFor(25*WAIT_TIME) = wrSignaled then
  begin
    FileChangeSignalEvent.ResetEvent;
    Result := True;
  end
  else
    Result := False;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TBaseLog.SetFileName(const Value: string);
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'TBaseLog.SetFileName' );{$ENDIF}
  BaseLogLock.Enter;
  FFileName := Value;
  BaseLogLock.Free;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TBaseLog.SetLogPath(const Value: string);
begin
  FLogPath := Value;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TBaseLog.SetTrackDuplicateLines(const Value: Boolean);
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'TBaseLog.SetTrackDuplicateLines' );{$ENDIF}
  BaseLogLock.Enter;
  try
    FTrackDuplicateLines := Value;
    if Value then
      FLoggerThread.QueueMsg(lmtDupsOn, '')
    else
      FLoggerThread.QueueMsg(lmtDupsOff, '')
  finally
    BaseLogLock.Leave;
  end;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TBaseLog.StartLog;
var
  Code:     char;
  Msg:      string;
  TypeCode: char;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'StartLog' );{$ENDIF}
  try
    while FTmpQueue.QueueSize > 0 do
    begin
      FTmpQueue.Dequeue(Msg);
      Code := Msg[1];
      TypeCode := Msg[2];
      Msg := Copy(Msg, 3, 999);
      if Code = ADD_MSG then
        Add(Msg, True)
      {$IFDEF UNIT_TEST}
      else if Code = CLEAR_LOG then
        ClearLog(True)
      {$ENDIF}
      else if Code = RENAME then
        ChangeFile(Msg, fcRenameCurrentFile, True)
      else if Code = CHANGE_NAME then
        ChangeFile(Msg, CharToFileChangeType(TypeCode), True)
      else if Code = DATE_CHANGE then
        RenameIfDateChanged(True);
    end;
    FStarted := True;
  finally

  end;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TBaseLog.StartThread;
begin
  FLoggerThread.Start;
  FLoggerThreadStarted := True;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
constructor TDebugLog.Create;
begin
  inherited;
  FActive := True;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
{$IFDEF UNIT_TEST}
procedure TDebugLog.ClearLog;
begin
  FLog.ClearLog;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
{$ENDIF}
constructor TDebugLog.Create(ALog: TBaseLog; ALogType: TLogType);
begin
  Create;
  FLog := ALog;
  FLogType := ALogType;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
destructor TDebugLog.Destroy;
begin
  inherited;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
{$IFDEF UNIT_TEST}
function TDebugLog.Read: TStrings;
begin
  Result := FLog.Read;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
{$ENDIF}
procedure TDebugLog.Add(const Msg: string);
begin
  if FActive and Assigned(FLog) then
    FLog.Add(Msg);
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TDebugLog.Add(const Msg: AnsiString);
begin
  if FActive  and Assigned(FLog) then
    FLog.Add(Msg);
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TDebugLog.Add(const FmtStr: string; const Args: array of const);
begin
  if FActive  and Assigned(FLog) then
    FLog.Add(FmtStr, Args);
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TDebugLog.Add(const FmtStr: AnsiString; const Args: array of const);
begin
  if FActive  and Assigned(FLog) then
    FLog.Add(FmtStr, Args);
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TDebugLog.SetActive(const Value: boolean);
begin
  FActive := Value;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TDebugLog.SetLog(const Value: TBaseLog);
begin
  FLog := Value;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
constructor TRepeatMessages.Create(Msg: string; AddedAt: TDateTime);
begin
  Create(Msg, BobJenkinsHash(Msg[1], ByteLength(Msg), 0), AddedAt);
end;
{ -------------------------------------------------------------------------------------------------------------------- }
constructor TRepeatMessages.Create(Msg: string; HashValue: integer; AddedAt: TDateTime);
begin
  inherited Create;
  FHashValue := HashValue;
  FMsg := Msg;
  FRepeatCount := 0;
  FWhenAdded := AddedAt;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TRepeatMessages.SetMsg(const Value: string);
begin
  FMsg := Value;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TRepeatMessages.SetRepeatCount(const Value: Integer);
begin
  FRepeatCount := Value;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
constructor TLogInternalMsg.Create(LogMsgType: TLogMsgType; const LogMsg: string);
begin
  inherited Create;
  FLogMsgType := LogMsgType;
  FLogMsg := LogMsg;
  FLogMsgDate := Now;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
constructor TLogInternalMsg.Create;
begin
  inherited;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
destructor TLogInternalMsg.Destroy;
begin
  inherited;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TLogInternalMsg.SetLogMsg(const Value: string);
begin
  FLogMsg := Value;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TLogInternalMsg.SetLogMsgType(const Value: TLogMsgType);
begin
  FLogMsgType := Value;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
{ TThreadLog }
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TThreadLog.Add(const FmtStr: string; const Args: array of const);

  function MakeArgsString: string;
  var
    i: integer;
    s: string;
  begin
    {$IFDEF USE_CODESITE}CodeSite.TraceMethod( Self, 'Add/MakeArgsString' );{$ENDIF}
    Result := '[';
    for i := Low(Args) to High(Args) do
    begin
      case Args[i].vtype of
        vtinteger: s := IntToStr(Args[i].VInteger);
        vtboolean: s := BoolToStr(Args[i].vboolean, True);
        vtchar: s := String(Args[i].vchar);
        vtextended: s := FloatToStr(Args[i].VExtended^);
        vtString: s := String(Args[i].VString^);
        vtPointer: s := IntToStr(Longint(Args[i].VPointer));
        vtPChar: s := String(Args[i].VPChar);
        vtAnsiString: s := String(AnsiString(Args[i].VAnsiString));
        else s := '<Unknown>'
      end;
      if Length(Result) > 1 then
        Result := Result + ', ' + s
      else
        Result := Result + s;
    end;
    Result := Result + ']';
  end;

begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( Self, 'Add' );{$ENDIF}
  try
    Add(Format(FmtStr, Args));
  except
    Add('LOG ERROR - BAD FORMAT adding: ''' + FmtStr + ''' [Args]=' + MakeArgsString)
  end;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TThreadLog.Add(const Msg: string; ATimestamp: TDateTime = 0);
var
  HashValue: integer;
  MatchResult: TMatchResult;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( Self, 'Add' );{$ENDIF}
  ThreadLogLock.Enter;
  try
    if FTrackDuplicateLines then
    begin
      MatchResult := MessageFoundInQueue(Msg, HashValue);
      if MatchResult = mrNotFound then
      begin
        //////////////////////////////// MCD
        // OutputToFile(IntToStr(HashValue) + ':' + msg, ATimeStamp);
        //////////////////////////////// MCD
        PrintRepeatedMsgs;
        AddMsgToQueue(Msg, HashValue, ATimestamp);
        OutputToFile(Msg, ATimestamp);
      end
      else if MatchResult = mrFoundOutOfOrder then
      begin
        CleanUpQueue(Msg, HashValue, ATimestamp);
      end;
    end
    else
      OutputToFile(Msg, ATimestamp);
  finally
    ThreadLogLock.Leave;
  end;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TThreadLog.AddMsgToQueue(Msg: string; HashValue: integer; ATimestamp: TDateTime);
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( Self, 'AddMsgToQueue' );{$ENDIF}
  FRepeatMessages.Add(TRepeatMessages.Create(Msg, HashValue, ATimestamp));
end;
{ -------------------------------------------------------------------------------------------------------------------- }
function TThreadLog.ChangeFile(FileName: string; FileChange: TFileChange): boolean;
var
  InFile, OutFile: TextFile;
  InStr:           String;

  procedure DoPrematureExit;
  begin
    ThreadLogLock.Leave;
  end;

  procedure CopyAndCreateNewFile;
  begin
    AssignFile(InFile, FFileName);   // Current file
    AssignFile(OutFile, FileName);    // New file
    FileMode := fmOpenRead;
    {$I-}Reset(InFile);{$I-}
    if IOResult > 0 then
    begin
      DoPrematureExit;
      Exit;
    end;
    FileMode := fmOpenWrite;
    {$I-}Append(OutFile);{$I+}
    if IOResult > 0 then
    begin
      DoPrematureExit;
      Exit;
    end;
    ReadLn(InFile, InStr);
    while not Eof(InFile) do
    begin
      WriteLn(OutFile, InStr);
      ReadLn(InFile, InStr);
    end;
    CloseFile(InFile);
    CloseFile(OutFile);
    DeleteFile(PChar(FFileName));
  end;

begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( Self, 'ChangeFile' );{$ENDIF}
  Result := False;
  if FFileName <> '' then
    FileName := ExtractFilePath(FFileName) + ExtractFileName(FileName)
  else
    FileName := FBaseLogPath + ExtractFileName(FileName);
  { if the new filename matches the current file name, do nothing }
  if not SameText(FileName, FFileName) then
  begin
    ThreadLogLock.Enter;
    try
      if FileExists(FileName) then
      begin
        { if just renaming file and new filename already exists, copy contents of this current file to existing file
          and delete current file. }
        if FileChange = fcRenameCurrentFile then
          CopyAndCreateNewFile
        else
          TFile.Delete(FileName);
      end;

      if FileChange in [fcRenameCurrentFile, fcRenameKeepingCurrentFile] then
      begin
        if FileChange = fcRenameKeepingCurrentFile then
          PrintQueue(True);
        if FileExists(FFileName) then
          RenameFile(FFileName, FileName);
        if FileChange = fcRenameKeepingCurrentFile then
          if TFile.Exists(FFileName) then
            TFile.Delete(FFileName);
      end
      else
      begin
        PrintQueue(False);
        FRepeatMessages.Clear;
      end;
      if FileChange <> fcRenameKeepingCurrentFile then
        FFileName := FileName;
      Result := True;
    finally
      ThreadLogLock.Leave;
    end;
  end
  else
    Result := True;
  FileChangeSignalEvent.SetEvent;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TThreadLog.CleanUpQueue(Msg: string; HashValue: integer; ATimestamp: TDateTime);
var
  RepeatCount: integer;
  i: integer;
  NextMatch: integer;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( Self, 'CleanUpQueue' );{$ENDIF}
  {$IFDEF USE_CODESITE}
  // JustForCodeSite('Queue at start');
  {$ENDIF}
  { Find the message that matches }
  NextMatch := NextMsgToMatch;
  if NextMatch = 0 then
  { next match is the start of queue so all messages in the queue have the same count }
  begin
    { if there is no RepeatCount, there is nothing to print }
    if FRepeatMessages[0].FRepeatCount > 0 then
    begin
      RepeatCount := FRepeatMessages[0].FRepeatCount;
      PrintRepeatedMsgLine(RepeatCount, FRepeatMessages.Count);
      { Find the matched message and increment its count }
      Inc(FRepeatMessages[FMatchedMsgNo].FRepeatCount);
      FRepeatMessages[FMatchedMsgNo].FWhenAdded := Now;
      i := 0;
      while i <= FRepeatMessages.Count - 1 do
      begin
        if FRepeatMessages[i].FRepeatCount > 0 then
        begin
          Dec(FRepeatMessages[i].FRepeatCount, RepeatCount);
          if FRepeatMessages[i].FRepeatCount <= 0 then
            FRepeatMessages.Delete(i)
          else
          begin
            PrintOneMsg(i);
            FRepeatMessages[i].FRepeatCount := 0;
            Inc(i);
          end;
        end
        else
          FRepeatMessages.Delete(i);
      end;
    end
    else
    begin
      while (FRepeatMessages[0].FHashValue <> HashValue) and (FRepeatMessages[0].FMsg <> Msg) do
        FRepeatMessages.Delete(0);
      { if there is anything left in the queue, a match was found }
      if FRepeatMessages.Count > 0 then
      begin
        Inc(FRepeatMessages[0].FRepeatCount);
        FRepeatMessages[0].FWhenAdded := Now;
      end
    end;
    {$IFDEF USE_CODESITE}
    // JustForCodeSite('Queue at end');
    {$ENDIF}
    Exit;
  end;

  { if we get here, the messages up to MextMatch should have a RepeatCount > 0 }
  RepeatCount := FRepeatMessages[NextMatch].RepeatCount;
  if RepeatCount > 0 then
  begin
    PrintRepeatedMsgLine(RepeatCount, NextMatch + 1);
    for i := 0 to NextMatch - 1 do
      FRepeatMessages[i].FRepeatCount := FRepeatMessages[i].FRepeatCount - RepeatCount;
  end;
  { Print any remaining messages }
  for i := 0 to NextMatch - 1 do
    if FRepeatMessages[i].FRepeatCount > 0 then
      PrintOneMsg(i);
  FRepeatMessages.Clear;
  { Add the new message and print it }
  AddMsgToQueue(Msg, HashValue, ATimestamp);
  OutputToFile(Msg, ATimestamp);
  {$IFDEF USE_CODESITE}
  // JustForCodeSite('Queue at end');
  {$ENDIF}
end;
{ -------------------------------------------------------------------------------------------------------------------- }
{$IFDEF UNIT_TEST}
procedure TThreadLog.ClearLog;
var
  LogFile: TextFile;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( Self, 'ClearLog' );{$ENDIF}
  ThreadLogLock.Enter;
  try
    FileMode := fmOpenReadWrite + fmExclusive;
    AssignFile(LogFile, FFileName);
    try
      if System.SysUtils.FileExists(FFileName) then
      begin
        {$I-}Rewrite(LogFile);{$i+}
        if IOResult = 0 then
          CloseFile(LogFile);
      end;
    except on E: Exception do
    end;
  finally
    ThreadLogLock.Leave;
  end;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
{$ENDIF}
class function TThreadLog.Create(AFileName, AFilePath: string): TThreadLog;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'TThreadLog.Create: ' + AFilePath + '&' + AFileName );{$ENDIF}
  if _ThreadLogInstance = nil then
  begin
    try
      _ThreadLogInstance := inherited Create;
      _ThreadLogInstance.FBaseLogPath := IncludeTrailingPathDelimiter(AFilePath);
      _ThreadLogInstance.FFileName :=  _ThreadLogInstance.BaseLogPath + ExtractFileName(AFileName);
      _ThreadLogInstance.FTrackDuplicateLines := True;
      _ThreadLogInstance.FRepeatMessages := TObjectList<TRepeatMessages>.Create();  // MCD
      _ThreadLogInstance.FRepeatMessages.OwnsObjects := True;
      _ThreadLogInstance.FRepeatMessages.Capacity := MAX_REPEAT_MSGS;
      _ThreadLogInstance.ThreadLogLock := TCriticalSection.Create;
      _ThreadLogInstance.FileAccessLock := TCriticalSection.Create;
    except
      _ThreadLogInstance := nil;
    end;
  end;
  Result := _ThreadLogInstance;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
destructor TThreadLog.Destroy;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( Self, 'Destroy' );{$ENDIF}
  While FRepeatMessages.Count > 0 do
    FRepeatMessages.Delete(0);
  FreeAndNil(FRepeatMessages);
  _ThreadLogInstance := nil;
  FreeAndNil(FileAccessLock);
  FreeAndNil(ThreadLogLock);
  FreeLogSignalEvent.SetEvent;
  inherited;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
{
If the message is found in the queue, then check any messages before it in the queue. If the RepeatCount for
any preceeding messages is 0, then delete them from the queue. If the RepeatCount < this repeat count, then
output them to the file.
}
function TThreadLog.MessageFoundInQueue(Msg: string; var HashValue: integer): TMatchResult;
var
  i:         integer;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( Self, 'MessageFoundInQueue: ' + Msg );{$ENDIF}
  Result := mrNotFound;
  HashValue := BobJenkinsHash(Msg[1], ByteLength(Msg), 0);
  {$ENDIF}
  if FRepeatMessages.Count > 0 then
  begin
    for i := 0 to FRepeatMessages.Count - 1 do
    begin

      {$IFDEF USE_CODESITE}
      CodeSite.Send(Format('%s[%d]', [FRepeatMessages[i].FMsg, FRepeatMessages[i].FRepeatCount]));
      {$ENDIF}

      { If it is found, set Result to mrFound if it is also the next one. If it is foun but not the next one,
        set Result to mrFoundOutOfOrder otherwise set to mrNotFound. If mrFound, increment the RepeatCount }
      if (HashValue = FRepeatMessages[i].HashValue) and SameStr(Msg, FRepeatMessages[i].FMsg) then
      begin
        FMatchedMsgNo := i;
        if i = NextMsgToMatch then
        begin
          Result := mrFound;
          Inc(FRepeatMessages[i].FRepeatCount);
          FRepeatMessages[i].FWhenAdded := Now;
        end
        else
          Result := mrFoundOutOfOrder;
        Break;
      end;
    end;
  end;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
function TThreadLog.NextMsgToMatch: integer;
var
  i: integer;
  PrevCount: integer;
begin
  Result := -1;
  { Start at the beginning of the list and see if RepeatCount of the Next Item is one less than the RepeatCount
    of the current item. If it is, the next item is the one to match, otherwise the first item is the one to match }
  if FRepeatMessages.Count > 0 then
  begin
    if FRepeatMessages.Count = 1 then
      Result := 0
    else
    begin
      PrevCount := FRepeatMessages[0].FRepeatCount;
      for i := 0 to FRepeatMessages.Count - 2 do
      begin
        if FRepeatMessages[i + 1].FRepeatCount = PrevCount - 1 then
        begin
          Result := i + 1;
          Break;
        end;
      end;
      { if all items have equal counts, the first item is the one to match }
      if Result = -1 then
        Result := 0;
    end;
  end;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TThreadLog.OutputToFile(Msg: string; DateTime: TDateTime);
var
  LogFile: TextFile;
  TmpFmtDate: string;
  Counter: integer;
  Done: boolean;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod(Self, 'OutputToFile:' + FFileName);{$ENDIF}
  FileAccessLock.Enter;
  try
    if DateTime = 0 then
      TmpFmtDate := FormatDateTime('mm/dd/yy hh:nn:ss.zzz', Now)
    else
      TmpFmtDate := FormatDateTime('mm/dd/yy hh:nn:ss.zzz', DateTime);
    AssignFile(LogFile, FFileName);
    FileMode := fmOpenWrite or fmShareDenyWrite;
    Counter := 0;
    Done := False;
    while not Done and (Counter < 10) do
    begin
      try
        if System.SysUtils.FileExists(FFileName) then
          {$I-}Append(LogFile){$I+}
        else
          {$I-}Rewrite(LogFile);{$I+}
        if IOResult = 0 then
          Done := True;
      except
        Inc(Counter);
        Sleep(50);
      end;
    end;
    Writeln(LogFile, TmpFmtDate + ' ' + Msg);
  except
  end;
  try
    Flush(LogFile);
    CloseFile(LogFile);
  finally
    FileAccessLock.Leave;
  end;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TThreadLog.PrintAndCleanRepeatedMsgs;
var
  MsgsRepeated: integer;
  i, MsgNo: integer;
  RepeatCount: integer;
  NextMatch: integer;
  FirstMsgNo: integer;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( Self, 'PrintAndCleanRepeatedMsgs' );{$ENDIF}
  {$IFDEF USE_CODESITE}
  // JustForCodeSite('Queue at start');
  {$ENDIF}
  { if the last message in the queue has a repeat count > 0, then print the repeated messages and delete them }
  if FRepeatMessages[FRepeatMessages.Count - 1].FRepeatCount > 0 then
  begin
    { work from the back to the front and count how many messages have at leaset the same repeat count }
    MsgNo := FRepeatMessages.Count - 1;
    FirstMsgNo := MsgNo;
    MsgsRepeated := 1;
    RepeatCount := FRepeatMessages[FRepeatMessages.Count - 1].FRepeatCount;
    Dec(MsgNo);
    { Determine the common RepeatCount and how many messages are repeated }
    while MsgNo > -1 do
    begin
      if FRepeatMessages[MsgNo].FRepeatCount >= RepeatCount then
      begin
        FirstMsgNo := MsgNo;
        Inc(MsgsRepeated);
        Dec(MsgNo);
      end
      else
        Break;
    end;
    { Print the line about x messages repeated y times }
    PrintRepeatedMsgLine(RepeatCount, MsgsRepeated);
    { Reduce the RepeatedCount for each message by the common RepeatCount and Delete those with 0 Count}
    while MsgsRepeated > 0 do
    begin
      Dec(FRepeatMessages[FirstMsgNo].FRepeatCount, RepeatCount);
      if FRepeatMessages[FirstMsgNo].FRepeatCount = 0 then
        FRepeatMessages.Delete(FirstMsgNo)
      else
      { if the RepeatCount is still > 0, print the message, reduce the RepeatCOunt and leave it queued }
      begin
        PrintOneMsg(FirstMsgNo);
        Dec(FRepeatMessages[FirstMsgNo].FRepeatCount);
        Inc(FirstMsgNo);
      end;
      Dec(MsgsRepeated);
    end;
    {$IFDEF USE_CODESITE}
    // JustForCodeSite('Queue at end');
    {$ENDIF}
    Exit;
  end;

  NextMatch := NextMsgToMatch;
  {$IFDEF USE_CODESITE}CodeSite.Send('NextMsgToMatch:' + IntToStr(NextMatch));{$ENDIF}
  { if the NrxtMsgToMatch has a repeat count of 0, then all preceeding messages with a repeat count of 1 need to be
    printed and removed }
  if FRepeatMessages[NextMatch].FRepeatCount = 0 then
  begin
    for i := 0 to NextMatch -1 do
      PrintOneMsg(i);
    { Now move the messages to the back of the queue since they were just printed}
    MsgNo := 0;
    while MsgNo < NextMatch do
    begin
      FRepeatMessages.Add(TRepeatMessages.Create(FRepeatMessages[0].FMsg, FRepeatMessages[0].FHashValue));
      FRepeatMessages.Delete(0);
      Inc(MsgNo);
    end;
    {$IFDEF USE_CODESITE}
    // JustForCodeSite('Queue at end');
    {$ENDIF}
    Exit;
  end;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TThreadLog.PrintOneMsg(MsgNo: integer);
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( Self, 'PrintOneMsg' );{$ENDIF}
  OutputToFile(FRepeatMessages[MsgNo].FMsg, FRepeatMessages[MsgNo].FWhenAdded);
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TThreadLog.PrintQueue(PrintLogClosed: boolean);
var
  Msg: string;
begin
  PrintRepeatedMsgs;
  if PrintLogClosed then
  begin
    Msg := 'Log file closed';
    AddMsgToQueue(Msg, 0, Now);
    OutputToFile(Msg, Now);
  end;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TThreadLog.PrintRepeatedMsgLine(MsgCount, NumberOfLines: integer);
var
  AboveLine: string;
  Times: string;
begin
  if NumberOfLines <= 1 then
    AboveLine := '                  (Above line is repeated '
  else
    AboveLine := '                  (Above ' + IntToStr(NumberOfLines) + ' lines are repeated ';
  if MsgCount = 1 then
    Times := '1 more time.)'
  else
    Times := IntToStr(MsgCount) + ' more times.)';
  OutputToFile(AboveLine + Times);
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TThreadLog.PrintRepeatedMsgs;
var
  QueueMaxedOut: boolean;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( Self, 'PrintRepeatedMsgs' );{$ENDIF}
  {$IFDEF USE_CODESITE}
  // JustForCodesite('Queue at start');
  {$ENDIF}
  { Could be nothing in the queue to check }
  if FRepeatMessages.Count = 0 then
  begin
    Exit;
  end;
  QueueMaxedOut := FRepeatMessages.Count >= MAX_REPEAT_MSGS;
  { Need to find a place to squeeze in the new message }
  { Start at the beginning of the queue and see if the first messages need printing }
  { If it doesn't need printing, delete it and that is enough }
  if FRepeatMessages[0].FRepeatCount = 0 then
  begin
    if QueueMaxedOut then
    begin
      {$IFDEF USE_CODESITE}CodeSite.Send('Deleting: ' + FRepeatMessages[0].Msg);{$ENDIF}
      FRepeatMessages.Delete(0);
    end;
    Exit;
  end;
  { If we got this far, that means we are out of room in the queue and all the messages left in the queue are repeated
    the same number of times or one less than the first message}
  PrintAndCleanRepeatedMsgs;

  if FRepeatMessages.Count > 0 then
  begin
    QueueMaxedOut := FRepeatMessages.Count >= MAX_REPEAT_MSGS;
    { Need to find a place to squeeze in the new message }
    { Start at the beginning of the queue and see if the first messages need printing }
    { If it doesn't need printing, delete it and that is enough }
    if FRepeatMessages[0].FRepeatCount = 0 then
    begin
      if QueueMaxedOut then
        FRepeatMessages.Delete(0);
    end;
  end;
  {$IFDEF USE_CODESITE}
  // JustForCodesite('Queue at end');
  {$ENDIF}
end;
{ -------------------------------------------------------------------------------------------------------------------- }
{$IFDEF UNIT_TEST}
function TThreadLog.Read: TStrings;
begin
  try
    Result := TStringList.Create;
    if FileExists(FFileName) then
      Result.LoadFromFile(FFileName);
  finally
    ReadSignalEvent.SetEvent;
  end;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TThreadLog.ReadAndReport;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( Self, 'ReadAndReport' );{$ENDIF}
  ThreadLogLock.Enter;
  try
    FileContents.Clear;
    FileAccessLock.Enter;
    if FileExists(FFileName) then
      FileContents.LoadFromFile(FFileName);
  finally
    ReadSignalEvent.SetEvent;
    FileAccessLock.Leave;
    ThreadLogLock.Leave;
  end;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
{$ENDIF}
function TThreadLog.RenameIfDateChanged: Boolean;
var
  InFile:              TextFile;
  InStr:               string;
  Done:                boolean;
  FileDate:            TDateTime;
  MTXFormatSettings:   TFormatSettings;
  TmpInt:              integer;
  CanUseName:          boolean;
  NewFileName:         string;
  BaseFileName:        string;
  BaseFileExt:         string;
  ThreadLockMine:      boolean;

procedure DeleteReceiptFileForVT;
var
  SR: TSearchRec;
  FileSpec: TFileName;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'DeleteReceiptFileForVT' );{$ENDIF}
  FileSpec := FBaseLogPath + 'Transactions*.dat';
  while (FindFirst(FileSpec, 0, SR) = 0) do
    TFile.Delete(ExtractFilePath(FFileName) + SR.Name);
  System.SysUtils.FindClose(SR);
end;

begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod(Self, 'RenameIfDateChanged');{$ENDIF}
  Result := True;   // Doesn't matter - change to procedure
  if FileExists(FFileName) then
  begin
    ThreadLogLock.Enter;
    ThreadLockMine := True;
    try
      AssignFile(InFile, FFileName);
      FileMode := fmOpenRead;
      {$I-}Reset(InFile);{$I+}
      if IOResult = 0 then
      begin
        Done := False;
        MTXFormatSettings := TFormatSettings.Create;
        MTXFormatSettings.ShortDateFormat := 'mm/dd/yy';
        ReadLn(InFile, InStr);
        while not Eof(InFile) and not Done do
        begin
          try
            FileDate := StrToDate(Copy(InStr, 1, 8), MTXFormatSettings);
            Done := True;
          except

          end;
        end;
        CloseFile(InFile);
        if not Done then     // never found a date
          FileDate := Date;
      end
      else
        FileDate := 0;

      if FileDate <> Date then
      begin
        TmpInt := 0;
        CanUseName := False;
        BaseFileName := ExtractFileName(FFileName);
        BaseFileExt := ExtractFileExt(FFileName);
        if Pos(BaseFileExt, BaseFileName) > 0 then
          Delete(BaseFileName, Pos(BaseFileExt, BaseFileName), Length(BaseFileExt));
        while not CanUseName do
        begin
          NewFileName := BaseFileName + '-' + FormatDateTime('yyyymmdd', FileDate);
          if TmpInt > 0 then
            NewFileName := NewFileName + '_' + IntToStr(TmpInt);
          NewFileName := NewFileName + '.txt';
          if not FileExists(NewFileName) then
          begin
            CanUseName := True;
            Break;
          end;
          Inc(TmpInt);
        end;
        ThreadLogLock.Leave;
        ThreadLockMine := False;
        ChangeFile(NewFileName, fcRenameKeepingCurrentFile);
      end;

      ThreadLogLock.Enter;
      ThreadLockMine := True;
      DeleteReceiptFileForVT
    finally
      if ThreadLockMine then
        ThreadLogLock.Leave;
    end;
  end;
  FileChangeSignalEvent.SetEvent;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TThreadLog.SetBaseLogPath(const Value: string);
begin
  FBaseLogPath := Value;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TThreadLog.SetFileName(const Value: string);
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( Self, 'SetFileName' );{$ENDIF}
  ThreadLogLock.Enter;
  FFileName := Value;
  ThreadLogLock.Leave;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TThreadLog.SetTrackDuplicateLines(const Value: Boolean);
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( Self, 'SetTrackDuplicateLines' );{$ENDIF}
  ThreadLogLock.Enter;
  FTrackDuplicateLines := Value;
  ThreadLogLock.Leave;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
{ TLoggerThread }
{ -------------------------------------------------------------------------------------------------------------------- }
constructor TLoggerThread.Create(CreateSuspended: boolean; AFileName,  AFilePath: string);
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'TLoggerThread.Create: ' + AFilePath + '&' + AFileName );{$ENDIF}
  try
    inherited Create(CreateSuspended);
    FreeOnTerminate := False;
    FQueueLock := TCriticalSection.Create;
    FLogPath := AFilePath;
    FFileName := AFileName;
    FKilled := False;
    if CreateSuspended then
      FLogThreadStarted := False
    else
      FLogThreadStarted := True;
    FThreadLog := TThreadLog.Create(FFileName, FLogPath);
    FQueueAdded := TEvent.Create(nil, True, False, 'msg.added');
    FLogInternalQueue := TSimpleThreadedQueue<TLogInternalMsg>.Create;
    {$IFDEF USE_CODESITE}CodeSite.Send('Thread created successfully'){$ENDIF}
  except
    {$IFDEF USE_CODESITE}CodeSite.Send('Exception in creating thread'){$ENDIF}
  end;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
destructor TLoggerThread.Destroy;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'TLoggerThread.Destroy' );{$ENDIF}
  FThreadLog.Free;
  if FreeLogSignalEvent.WaitFor(25*WAIT_TIME) = wrSignaled then
    FreeLogSignalEvent.ResetEvent;
  FLogInternalQueue.Free;
  FQueueLock.Free;
  FQueueAdded.Free;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TLoggerThread.Execute;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'TLoggerThread.Execute' );{$ENDIF}
  {$IFDEF USE_CODESITE}CodeSiteManager.SetCurrentThreadName('LoggingThread');{$ENDIF}
  {$IFDEF DEBUG}
  NameThreadForDebugging('Logging Thread');
  {$ENDIF}
  FLogThreadStarted := True;
  while not Terminated do
  begin
    FQueueLock.Enter;
    if QueueCount > 0 then
    begin
      {$IFDEF USE_CODESITE}CodeSite.Send('QueueCount > 0'){$ENDIF};
      while (QueueCount > 0) and Not Terminated do
        ProcessQueueMsg(GetNextQueueMsg);
      FQueueLock.Leave;
    end
    else
    begin
      FQueueAdded.ResetEvent;
      FQueueLock.Leave;
      {$IFDEF USE_CODESITE}CodeSite.Send('WaitingOnQueue'){$ENDIF};
      if Not Terminated then
        FQueueAdded.WaitFor(WAIT_TIME);
    end;
  end;
  {$IFDEF USE_CODESITE}CodeSite.Send('Thread terminated');{$ENDIF}
end;
{ -------------------------------------------------------------------------------------------------------------------- }
function TLoggerThread.GetNextQueueMsg: TLogInternalMsg;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'TLoggerThread.GetNextQueueMsg' );{$ENDIF}
  try
    FLogInternalQueue.Dequeue(Result, WAIT_TIME);
  except
    Result := nil;
  end;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TLoggerThread.Kill;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'TLoggerThread.Kill' );{$ENDIF}
  FKilled := True;
  // Terminate;
  // FQueueAdded.SetEvent;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TLoggerThread.ProcessQueueMsg(LogInternalMsg: TLogInternalMsg);
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'TLoggerThread.ProcessQueueMsg' );{$ENDIF}
  if LogInternalMsg = nil then
    Exit;
  try
    case LogInternalMsg.LogMsgType of
      lmtAdd:                       FThreadLog.Add(LogInternalMsg.LogMsg, LogInternalMsg.LogMsgDate);
      lmtRenameCurrentFile:         FThreadLog.ChangeFile(LogInternalMsg.LogMsg, fcRenameCurrentFile);
      lmtStartNewFile:              FThreadLog.ChangeFile(LogInternalMsg.LogMsg, fcStartNewFile);
      lmtRenameKeepingCurrentFile:  FThreadLog.ChangeFile(LogInternalMsg.LogMsg, fcRenameKeepingCurrentFile);
      {$IFDEF UNIT_TEST}
      lmtClear:                     FThreadLog.ClearLog;
      lmtRead:                      FThreadLog.ReadAndReport;
      {$ENDIF}
      lmtDupsOn:                    FThreadLog.FTrackDuplicateLines := True;
      lmtDupsOff:                   FThreadLog.FTrackDuplicateLines := False;
      lmtRenameIfDateChanged:       FThreadLog.RenameIfDateChanged;
    end;
  finally
  end;
  LogInternalMsg.Free;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
function TLoggerThread.QueueCount: integer;
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'TLoggerThread.QueueCount:' + IntToStr(FLogInternalQueue.QueueSize));{$ENDIF}
  FQueueLock.Enter;
  try
    Result := FLogInternalQueue.QueueSize;
  finally
    FQueueLock.Leave;
  end;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TLoggerThread.QueueMsg(LogMsgType: TLogMsgType; LogMsg: string);
begin
  {$IFDEF USE_CODESITE}CodeSite.TraceMethod( 'TLoggerThread.QueueMsg' );{$ENDIF}
  FQueueLock.Enter;
  try
    FLogInternalQueue.Enqueue(TLogInternalMsg.Create(LogMsgType, LogMsg));
    FQueueAdded.SetEvent;
  finally
    FQueueLock.Leave;
  end;
end;
{ -------------------------------------------------------------------------------------------------------------------- }
procedure TLoggerThread.SetLogPath(const Value: string);
begin
  FLogPath := Value;
end;
{ -------------------------------------------------------------------------------------------------------------------- }

initialization
  ReadSignalEvent := TEvent.Create(nil, True, False, 'read.completed');
  FileChangeSignalEvent := TEvent.Create(nil, True, False, 'filechange.completed');
  FreeLogSignalEvent := TEvent.Create(nil, True, False, 'free.log');
  FileContents := TStringList.Create;
  {$IFDEF USE_CODESITE}CodeSite.Send('Logging initialization completed');{$ENDIF}

finalization
  FreeAndNil(FileContents);
  FreeAndNil(ReadSignalEvent);
  FreeAndNil(FileChangeSignalEvent);
  FreeAndNil(FreeLogSignalEvent);

end.


