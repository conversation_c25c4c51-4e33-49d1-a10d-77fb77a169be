// (c) MTXEPS, Inc. 1988-2008
unit RecFile;
//===== File Header ==========================================================
//	File Name: RecFile.pas
//
//	Description:
//	This file contains the class definition and implementation for the TRecFile
//	class. The intent of the TRecFile class is to represent a transaction log
//	and provide various file manipulations (i.e. add, delete, find, modify,
//	read, etc.)
//
//	Required Modules:
//	MTXFileName- TMTXFileName definition, used as a base class for TRecFile.
//
//============================================================================
//
//	This document contains MTXEPS, Inc. proprietary data and is not to
//	be used, copied, reproduced, stored in a retrieval system, transmitted,
//	distributed or divulged to unauthorized persons in whole or in part,
//	without proper authorization from MTXEPS, Inc. This information is
//	the property of MTXEPS, Inc. with all rights reserved.
//
//===== Revision History =====================================================
//    TSL-07  08-11-08    Look at deleted records, that way the training transactions can be voided, etc. this change rolls
//                        back the change on 04-10-08: Dev-7769 Make sure we don't read deleted records with reqCode = ''
//    TSL-06  08-04-08    Take out all the Sleep(1) - other applications running will cause us to slow down too much
//    DAEv823 05-01-07    Add function GetTotalTRXAmt
//    TSL-05  12-12-06    Add TORReq to setVoidVars in TCPIPCOMFindRec
//    TSL-04  09-01-06    Add function FindRecordButNotVoid for ACI host
//    TSL-03  05-26-05    A little clean up
//    TSL-02  07-08-04    Change erase to delete file, got error couldn't rename
//    TRI-A   13Jan2004   Add exception handlers
//    TSL-01  15Jan2003   In ClearDeletedRecords change rename to renameFile
//    GAG     14Aug2001   Modify MoveRecordToEOF() method to change variable
//                        name from TmpRecord to TmpMoveRec.
//    GAG     26Jul2001   Add MoveRecordToEOF() method.
//    GAG-D   14Feb2001   Use CreateTmpFileName() method to set the temp file
//                        name that will be used.
//    GAG     14Feb2001   Add CreateTmpFileName() method.
//    GAG-C   18Oct2000   Change return value for ClearDeletedRecords(). Also,
//                        use try .. catch block for deleting/renaming the
//                        files in this function.
//    GAG     18Oct2000   Add TranslateErrorCode() function.
//    GAG-B   24Aug2000   Modify Uses clause for use with TrxLogViewer.
//    GAG-A   21Aug2000   Fix memory leak associated with TStringList object
//                        in TRecFile.GetRecordCount().
//    GAG     17May2000   Code Created.
//============================================================================

interface

uses
  FinalizationLog,
  TypInfo,
  XMLDoc, XMLIntf, // DEV-8000
  hostname,
  {$IFDEF MSWINDOWS}
  Windows,
  {$ENDIF}
  TrxLog,
  classes,
  Sysutils,
  MTX_Lib,
  MTX_Utils,
  MTXEncryptionUtils,
  MTX_Constants,
  MdMsg;

const
  PERSISTENT_FILE = true;
  offFileCount = 2;
  offFileArray : Array[1..offFileCount] of string = ('ofline01.*', 'tor.*');

type
  mdFile      = file of MdmsgRec;
{
  Class Name: TRecFile
  Base Class: TFileName

  Description:
    TRecFile automates handling of transaction record files (logs).

  Notes:
    Base classes that do not have inheritance access specifiers (i.e.
    public, protected, or private) will receive the default inheritance
    access of private.
}
  TRecFile = class(TTrxLog)
  private
    IsUALOn: boolean; // DEV-49608
  public
    {$IFDEF TEST}
    PersistentFile: boolean;  // stops Teardown from deleting file, if true
    //function DeleteTestFile: boolean; override;
    {$ENDIF}

    constructor  Create(QFileName: string); override;
    destructor   Destroy; override;

    function  AppendRecord(QRecord : MDMsgRec) : Boolean; override;
    function ResetFile: boolean; override;
    function  ClearDeletedRecords : integer; override;
    function  ClearVisaFields : boolean; override;
    function  ChangeOfflinePreAuthCompToPurch : boolean; override;
    procedure DeleteTORsAtEOD(aSuffix: string); override;
    procedure DeleteExistingOfflineAndTORFiles; override;
    function  SaveOutstandingPreauths(QPreAuthFileName: string) : boolean; override;
//    function  MovePreAuthsToActlog(QPreAuthFileName: string) : boolean; override;
//    function  CopyOfflineFilesToActlog : boolean; override;
//    function  SetTORTypeFromTORFiles: boolean; override; // DEV-24478
//    function  MakeOfflinesAndTORsFromActlog(var QList: TStringList; MakeTORs: boolean = true) : boolean; override;
//    function  RptInfoExists(RptInfoType: TRptInfoType): boolean; override;
//    function  IsReportBlock(var QRecord: mdMsgRec): boolean; override; // DEV-28982
    function  IsValidTransaction(QRecord : MDMsgRec): boolean; override;  
    function  FindDeclAdvBySeqNo(QSeqNo: string6): integer; override;
    function  FindRecord(var QRecord : MDMsgRec; QSeqNo : string6; QSeqType : TSeqNumType) : boolean; override;
    function  FindRecordNotDeleted(var QRecord : MDMsgRec; QSeqNo : string6; QSeqType : TSeqNumType) : boolean; override;
    function  FindRecordButNotVoid(var QRecord : MDMsgRec; QSeqNo : string6; QSeqType : TSeqNumType) : boolean; override;
    function  FindPreAuth(var QRecord: MDMsgRec; QSeqNo: string6; QAcctNoLast4: string) : boolean; override;
    function  WriteSettlementRecord(QRecord : MDMsgRec; var QFound:boolean) : boolean; override;
    function  FindRecordByAcctNoAndReqCode(var QRecord: MdMsgRec; QAcctNoLast4: string; QReqCode: string): boolean; override;
    function  FindRecordByAcctNoAndTrxAmtN(var QRecord: MDMsgRec; QAcctNoLast4: string; QTrxAmtN: int64) : boolean; override;
    function  FindRecordByAcctNo(var QRecord : MDMsgRec; QAcctNoLast4: string) : boolean; override;
    function  FindRecordNo(QSeqNo : string6; QSeqType : TSeqNumType) : integer; override;
    function  FindRecordNoNew(QSeqNo: integer; QSeqType: TSeqNumType): integer; override;
    function  FindRecordNotVoidByOldSeqNo(var QRecord : MDMsgRec; QOldSeqNo : string6) : boolean; override;
    function  FindRecordNotVoidByOldSeqNoAndReqCode(var QRecord : MDMsgRec; QOldSeqNo : string6; QReqCode: string) : boolean; override;
    function  FindRecordNotVoidNotInFlightByOldSeqNo(var QRecord : MDMsgRec; QOldSeqNo : string6) : boolean; override;
    function  FindRecordVoidByOldSeqNo(var QRecord : MDMsgRec; QOldSeqNo : string6) : boolean; override;
    function  FindRecordToMakeTOR(var QRecord: MDMsgRec; OldSeqNo, SeqNo: string6): boolean; override;  //JTG 33934 33801 requires a new method to scan the Actlog

    function  TruncateCardData: boolean; override;                              
    function  BlankOfflines: boolean; override;  // DEV-29582
//    function  GetVoucherHeldCountAndAmount(var aTotalHeldCount: integer; var aTotalHeldAmount: integer; var aTotalWaitingCount: integer; var aTotalWaitingAmount: integer): boolean; override;// DEV-8000
//    function  GetVoucherHeldTrxs(aStartingNum: integer): string; override; // DEV-8000
//    function  VoucherUpdate(aXML: string): string; override; // DEV-8000


    {$IFNDEF HOSTSIM}
    {$IFNDEF MTXEPSDLL}
    {$IFNDEF MTXPOSDLL}
    {$IFNDEF RPT}
//    function  TCPIPComFindRecord(var QRecord: MDMsgRec; QSeqNo: string255; QVoidType: char; QMsgTypeID: string4) : boolean; override;
    {$ENDIF RPT}
    {$ENDIF MTXPOSDLL}
    {$ENDIF MTXEPSDLL}
    {$ENDIF HOSTSIM}
    function  GetFileSize: integer; override;
    function  GetRecordCount(QShowDeleted: Boolean = False) : integer; override;
    function  GetRecordCountApproved(aHost: string3 = ''; aIncludeLocalApproved: boolean=false) : integer; override; // DEV-11499: add aIncludeLocalApproved
    function  GetRecordCountDeclined(aHost: string3 = '') : integer; override;
    function  GetRecordSequenceNumbers(QSeqType : TSeqNumType; var QSeqNums : TStringList; QShowDeleted : Boolean = False) : integer; override;
    function  MarkRecordDeleted(QSeqNo : string6; QSeqType : TSeqNumType) : Boolean; override;
    function  MoveRecordToEOF(QSeqNo : string6; QSeqType : TSeqNumType) : Boolean; override;
    function  ReadByRecordNum(RecordNum: integer; var QRecord : MDMsgRec): integer; override;
    function  WriteByRecordNum(RecordNum: integer; QRecord : MDMsgRec): Boolean; override;
    function  WriteByRecordNumNoEncrypt(RecordNum: integer; QRecord : MDMsgRec): boolean; override;
    function  RemoveRecord(QSeqNo : string6; QSeqType : TSeqNumType) : Boolean; override;
    function  UpdateRecord(QRecord : MDMsgRec; QSeqType : TSeqNumType) : Boolean; override;
    function  WriteRecord(QRecord : MDMsgRec; newKEK3: string=''; encryptIt: boolean=true) : Boolean; override;
    function RewriteFile: boolean; override;
    function  InitLaneMsgFile: boolean; override;
    function  GetExt:string; override;
    function  GetFileName: string;
    function  GetFullName: string; override;
    procedure SetFullName(aName: string); override;
    function GetTotalTrxAmt(aHost: string3 = '') : integer; override;
    function GetStoreMonitoringTotalsForEngine(aHost: string3; var approvedCount, approvedAmount, declinedCount, offlineCount, offlineAmount, torCount, torAmount, SignatureCount: integer) : boolean; override;
    function OpenFile: boolean;
    procedure GetKeyFromTrxFile(var QRecord : MDMsgRec); override;
    function PadBlankString(aString: ShortString; dataSize: integer; key: string): string;
    procedure EncryptFields(var QRecord: mdMsgRec; newKEK3: string = '');  override; // DEV-8030: moved from private
    function DecryptFields(var QRecord: MdMsgRec; newKEK3: string = ''): boolean; override; // DEV-8030
    procedure RecFileLog(aMsg: string);
    procedure GetCountAndAmount(var Count,Amount: integer); override;

    {$IFDEF TEST}
    procedure SetupTest(dir: string);
    function TearDownTest: boolean;
    function IsSigRecord(var HBuf: B_Record): boolean;
    function CountSignaturesInERCFile(aHost: string): integer;
    {$ENDIF TEST}

    function CloseEftFile(var RecFile: mdFile): boolean; // DEV-18500 <
    function ResetTmpFile: boolean;
    function UpdateRecordKEK3(newKEK3: string=''; OldKEK3: string=''
        {$IFDEF TEST}; FailStep: integer=0; StartStep: integer=1; EndStep: integer=100 {$ENDIF}): integer; // DEV-18500 >

  private
    FRecFile : mdFile;   // File handle for the transaction file
    TmpFile  : mdFile;   // File handle for temporary clean up file

    { from MTXFileName.pas }
    FExt:      string; // string containing file extension
    FFullName: string; // string containing full filename
    FName:     string; // string containing file name
    procedure  SetFileName(QFileName: string);
    function  CreateTmpFileName(QFileName : string) : string;
    function  CreateTmpFile(TmpFileName: string) : boolean;

    procedure ResetFileEx(var RecFile: mdFile; var IOResultEx: integer; var ErrCount: integer; FuncName: string); // DEV-15409
    procedure WriteEx(var RecFile: mdFile; MDMsg: MDMsgRec; var IOResultEx: integer; var ErrCount: integer; FuncName: string); // DEV-15409
    procedure RewriteFileEx(var RecFile: mdFile; var IOResultEx: integer; var ErrCount: integer; FuncName: string); // DEV-15409
    function GetFileSizeEx(var RecFile: mdFile; var IOResultEx: integer; var ErrCount: integer; FuncName: string): integer; // DEV-15409
    procedure ShutDownEngineWithLog(Msg: string);
    procedure CloseEftFileEx(var RecFile: mdFile; var IOResultEx: integer; var ErrCount: integer; FuncName: string); // DEV-18500

    {$IFNDEF TEST}
    function IsSigRecord(var HBuf: B_Record): boolean;
    function CountSignaturesInERCFile(aHost: string): integer;
    {$ENDIF TEST}
  end;

{$IFDEF RS_SECURITY_DEBUG}         //Jason must have another MsgSecurity in MTXEncryption, so we'll use
procedure MsgSecurity(S: string);  //this one when we need to debug KEK issues in RS. In order to do so
{$ENDIF}                           //both RS_SECURITY_DEBUG and SECURITY_DEBUG need to be defined

const
  RECFILE_FAIL_ENCRYPTION = -2;
  RECFILE_FAIL = -1;
  RECFILE_SUCCESS = 1;

implementation

uses
  GeneralUtilities,
{$IFNDEF HOSTSIM}
{$IFNDEF MTXEPSDLL}
{$IFNDEF MTXPOSDLL}
{$IFNDEF RPT}
  {$IFDEF UPDATE}
  UUpdateMain,
  {$ENDIF}
  MTX_XMLClasses, 
//  LaneMsg,
{$ENDIF RPT}
{$ENDIF MTXPOSDLL}
{$ENDIF MTXEPSDLL}
{$ENDIF HOSTSIM}

  {$IFDEF RSSRV}
  RSLog,
  {$ENDIF}
  uKEK3,
//  VoucherClearXMLClass,
  strUtils;    // moved this down here so as to always have at least one so as to allow us to put a 'uses' clause outside the IFDEFs

Const
  TMP_FILE_INDICATOR = 'tmp';
  fFound    = 1;
  fMismatch = 2;
  fNotFound = 3;
  fDuplicate = 4;

procedure TRecFile.RecFileLog(aMsg: string);
begin
  {$IFDEF RSSRV}
  RSLog.Log(aMsg);
  {$ELSE}
  sm(aMsg);
  {$ENDIF RSSRV}
end;

{$IFDEF SECURITY_DEBUG}
procedure MsgSecurity(S: string);
begin
  {$IFDEF RSSRV}
  RSLog.Log('SECURITY DEBUG: '+S);
  {$ELSE}
  sm('SECURITY DEBUG: '+S);
  {$ENDIF RSSRV}
end;
{$ENDIF}

function TRecFile.OpenFile: boolean;
begin
  if FileExists(FFullName)
    then result := ResetFile                // open existing file for read/write access
    else result := RewriteFile;             // create a file & open for read/write access
end;

constructor TRecFile.Create(QFileName : string);
/// <summary name=TRecFile.Create>
/// initializes all member variables & the class instance
/// </summary>
/// <type> TRecFile Constructor </type>
/// <input>
/// filename string, no specified length
/// </input>
begin
  IsUALOn := true;
  SetFileName(QFileName);
  {$IFDEF TEST}
   SetUpTest(QFileName);
  {$ENDIF TEST}
  AssignFile(FRecFile, FFullName); // Initialize the file handle, FRecFile.
  OpenFile;
end;

{
  destructor Destroy() TRecFile class

    Description: This method deallocates memory and terminates the class instance
}
destructor TRecFile.Destroy;
begin
  CloseFile(FRecFile);  // Make sure that the record file, FRecFile is closed.
  {$IFDEF TEST}
   TearDownTest;
  {$ENDIF TEST}
  inherited Destroy;    // Call the base class's destructor.
end;

function  TRecFile.AppendRecord(QRecord: MDMsgRec): Boolean;
begin
  result := WriteRecord(QRecord);
end;

{
  procedure SetFileName()

    Description:
      Set the file name. This procedure will accept a fully qualified path name
      for a file (both UNC and drive letter specified.) The file name is split
      into its various elements and also stored in FFullName for FullName
      property read access.

    Parameter(s): QFullName - string containing the file name (full or in part)
}
procedure TRecFile.SetFileName(QFileName: string);
begin
  FFullName := QFileName;
  FName := ExtractFileName(QFileName);
  FExt := ExtractFileExt(QFileName); // Save the extension of the file.
  if Pos('.', FExt) = 1 then // Remove the period from the extension
    FExt := Copy(FExt, 2, Length(FExt));
//  if CompareText(Copy(ExtractFileName(QFileName), 1, Length(AcctLog_Pfx)), AcctLog_Pfx) = 0 then
//    FKeyType := ekActlog
//  else if CompareText(Copy(ExtractFileName(QFileName), 1, Length(LaneMsgPrefix)), LaneMsgPrefix) = 0 then
//    FKeyType := ekLaneMsg
//  else if CompareText(Copy(ExtractFileName(QFileName), 1, Length(OffDSN_Pfx)), OffDSN_Pfx) = 0 then  //todo: openeps has different file name & format
//    FKeyType := ekOffline
//  else if CompareText(Copy(ExtractFileName(QFileName), 1, Length(TOR_Pfx)), TOR_Pfx) = 0 then        //todo: openeps has different format
//    FKeyType := ekTor
//  else
//    RecFileLog('*** ERROR: TRecFile.SetFileName unknown file type >' + QFileName + '<');
end;

function TRecFile.GetExt : string;
/// <summary name=TMTXFileName.GetExt>
///  Expose the filename extension
/// </summary>
/// <type>
///  function
/// </type>
/// <output>
///   string containing the extension of the file
/// </output>
begin
  Result := FExt;
end;

function TRecFile.GetFileName: string;
begin
  Result := FName;
end;

procedure TRecFile.ResetFileEx(var RecFile: mdFile; var IOResultEx: integer; var ErrCount: integer; FuncName: string); // DEV-15409
begin
  IOResultEx := 0;
  try
    {I-} Reset(RecFile); {$I+}
  except
    on e: Exception do
    begin
      IOResultEx := -1;
      Inc(ErrCount);
      RecFileLog(Format('TRecFile.%s Failed to reset "%s" (%d) - %s', [FuncName, FFullName, ErrCount, e.message]));
    end;
  end;
  if IOResultEx = 0 then
    IOResultEx := IOResult;
  if (IOResultEx = 0) and (ErrCount > 0) then
    RecFileLog(Format('TRecFile.%s Reset "%s" successfully after %d tries', [FuncName, FFullName, ErrCount]));
end;

procedure TRecFile.WriteEx(var RecFile: mdFile; MDMsg: MDMsgRec; var IOResultEx: integer; var ErrCount: integer; FuncName: string); // DEV-15409
begin
  IOResultEx := 0;
  try
    {I-} Write(RecFile, MDMsg); {$I+}
  except
    on e: Exception do
    begin
      IOResultEx := -1;
      Inc(ErrCount);
      RecFileLog(Format('TRecFile.%s Failed to write "%s" (%d) - %s', [FuncName, FFullName, ErrCount, e.message]));
    end;
  end;
  if IOResultEx = 0 then
    IOResultEx := IOResult;
  if (IOResultEx = 0) and (ErrCount > 0) then
    RecFileLog(Format('TRecFile.%s Write "%s" successfully after %d tries', [FuncName, FFullName, ErrCount]));
end;

procedure TRecFile.RewriteFileEx(var RecFile: mdFile; var IOResultEx: integer; var ErrCount: integer; FuncName: string); // DEV-15409
var IsFileExists: boolean;
begin
  IsFileExists := FileExists(FFullName);
  IOResultEx := 0;
  try
    {I-} Rewrite(RecFile); {$I+}
  except
    on e: Exception do
    begin
      IOResultEx := -1;
      Inc(ErrCount);
      RecFileLog(Format('TRecFile.%s Failed to rewrite "%s" (%d) - %s', [FuncName, FFullName, ErrCount, e.message]));
    end;
  end;
  if IOResultEx = 0 then
  begin
    IOResultEx := IOResult;
    if NOT IsFileExists and FileExists(FFullName) and IsUALOn then
      SMUserActivity(Format('TRecFile.RewriteFileEx created %s', [FFullName]));
    if ErrCount > 0 then
      RecFileLog(Format('TRecFile.%s Rewrite "%s" successfully after %d tries', [FuncName, FFullName, ErrCount]));
  end;
end;

function TRecFile.GetFileSizeEx(var RecFile: mdFile; var IOResultEx: integer; var ErrCount: integer; FuncName: string): integer; // DEV-15409
begin
  IOResultEx := 0;
  result := -1;
  try
    result := FileSize(RecFile);
  except
    on e: EInOutError do
    begin
      IOResultEx := -1;
      Inc(ErrCount);
      RecFileLog(Format('TRecFile.%s Failed to get file size "%s" (%d) - %s', [FuncName, FFullName, ErrCount, e.message]));
    end;
  end;
  if (IOResultEx = 0) and (ErrCount > 0) then
    RecFileLog(Format('TRecFile.%s Get file size "%s" successfully after %d tries', [FuncName, FFullName, ErrCount]));
end;

procedure TRecFile.ShutDownEngineWithLog(Msg: string); // DEV-15409
var MsgStr: string;
begin
  MsgStr := Msg + ' WinEPS SHUTTING DOWN!';
  {$IFDEF MSWINDOWS}
  WriteToEventLog(MsgStr, EVENTLOG_WARNING_TYPE);
  {$ENDIF}
  RecFileLog(MsgStr);
  {$IFNDEF TEST} // DEV-18500
    {$IFDEF MSWINDOWS}
    EasyCreateProcess('net stop WinEPS');
    {$ENDIF MSWINDOWS}
    raise Exception.Create(MsgStr);
  {$ENDIF}  
end;  

{
  function ClearDeletedRecord()

    Description: Remove all records marked as deleted (MDMsgRec.ReqCode = '')

    Notes:
      This function should be called to clean up the file during wait or down
      times (i.e. after a processing run or at the close of a business day).
      Calling this function at other times could result in extended periods of
      high disk usage and, thereby, slow down transaction processing.

    Return Value(s):
      0 - The deleted records were cleared from the file
          otherwise, the deleted records were not cleared from the file and
          the return value is:
     -1 - The deleted records were not cleared from the file
      1 - The records were deleted but the temporary file has not been renamed yet
      2 - File not found
      3 - Invalid file name
      4 - Too many open files
      5 - Access denied
     32 - Sharing violation
    100 - EOF
    101 - Disk full
    106 - Invalid input
}
function TRecFile.ClearDeletedRecords: integer;
var TmpFileName: string;
    TmpRec:      MDMsgRec;
    WaitTime: Longint; // DEV-15409
    _IOResult: integer; // DEV-15409
    _ErrCount: integer;
begin
  result := 0;                           // assume this will work
  //if (GetFileSize <> 0) then             // if no records in file, get out
  if GetFileSize > 0 then // DOEP-58911 - if no records or error result (-1), get out
  begin
    if (GetRecordCount(false) = 0) then  // don't included deleted records
      RewriteFile                        // if no records just rewrite file
    else
    try
      CloseFile(FRecFile);               // close current offline file
      TmpFileName := CreateTmpFileName(FFullName);  // get a tmp file name
      if fileExists(TmpFileName) then               // delete any existing tmp file
        if deleteFile(TmpFileName) then
          SMUserActivity(Format('TRecFile.ClearDeletedRecords deleted %s', [FFullName]));
      if not RenameFile(FFullName, TmpFileName) then // rename current file to tmp
      begin                                          // if rename won't go, get out
        RecFileLog('****WARNING: could not rename ' + FFullName + ' to ' + TmpFileName + ' abort function ClearDeletedRecords');
        ResetFile;                         // re-open the old offline file
        result := 1;                       // say it was unsuccessful
      end
      else
      begin                                // rename current to tmp file OK
        IsUALOn := false; // DEV-49608: prevent excessive UAL
        OpenFile;                          // makes a new empty ofline01.xxx file
        IsUALOn := true;
        AssignFile(TmpFile, TmpFileName);  // actually the old offline file

        WaitTime := 0; // DEV-15409 <
        _ErrCount := 0;
        repeat
          ResetFileEx(TmpFile, _IOResult, _ErrCount, 'ClearDeletedRecords');
          if _IOResult <> 0 then
          begin
            Delay(RETRY_FILE_LOCK_MS);
            Inc(WaitTime, RETRY_FILE_LOCK_MS);
            ResetFileEx(TmpFile, _IOResult, _ErrCount, 'ClearDeletedRecords');
          end;
        until (_IOResult = 0) or (WaitTime > FILE_LOCK_WAIT_MS);
        if _IOResult <> 0 then
        begin
          ShutDownEngineWithLog('TRecFile.ClearDeletedRecords ResetFileEx timed out'); // DEV-15409
        end;

        while not Eof(TmpFile) do          // read through old or tmp file
        begin
          Read(TmpFile, TmpRec);           // read thru old or tmp file
          if (TmpRec.ReqCode <> '') then   // get non deleted records
          begin
            WaitTime := 0; // DEV-15409 <
            _ErrCount := 0; 
            repeat
              WriteEx(FRecFile, TmpRec, _IOResult, _ErrCount, 'ClearDeletedRecords');
              if _IOResult <> 0 then
              begin
                Delay(RETRY_FILE_LOCK_MS);
                Inc(WaitTime, RETRY_FILE_LOCK_MS);
                WriteEx(FRecFile, TmpRec, _IOResult, _ErrCount, 'ClearDeletedRecords');
              end;
            until (_IOResult = 0) or (WaitTime > FILE_LOCK_WAIT_MS);
            if _IOResult <> 0 then
            begin
              ShutDownEngineWithLog('TRecFile.ClearDeletedRecords WriteEx timed out'); // DEV-15409
            end;
          end;
        end;
        CloseFile(TmpFile);                // close old file
        if FileExists(TmpFileName) and deleteFile(TmpFileName) then           // get rid of old file
          ; //SMUserActivity(Format('TRecFile.ClearDeletedRecords deleted %s', [TmpFileName])); // prevent spam
        resetFile;                         // reset new file with no deleted records
        //RecFileLog('****NOTICE ClearDeletedRecords successful'); /// DEV-8188
        result := 0;                       // say we are good
      end;
    except
      on E: Exception do { TRI-A }
      begin
        RecFileLog('Try..Except: TRecFile.ClearDeletedRecords ' + E.Message + ' stop engine');
        {$IFNDEF LINUX}
        EasyCreateProcess('net stop WinEPS');
        {$ENDIF}
      end;
    end;
  end;
end;

{
  function CreateTmpFileName()

    Description:
      Create a temporary filename from the name in QFileName by adding
      TMP_FILE_INDICATOR to the beginning of the filename as follows:

    c:\myfile.txt   >> becomes >>   c:\tmpmyfile.txt

    Parameter(s)   : QFileName - string containing the full filename
    Return Value(s): string containing the temporary filename
}
function TRecFile.CreateTmpFileName(QFileName: string) : string;
begin
  result := ExtractFilePath(QFileName) + TMP_FILE_INDICATOR +
            ExtractFileName(QFileName);
end;

{
  function CreateTmpFile

    Description:
      Create a temporary file for reorganizing normal file.

    Return Value(s): true if it worked else false
}
function TRecFile.CreateTmpFile(TmpFileName: string) : boolean;
begin
  result := false;
  try
    AssignFile(TmpFile, TmpFileName);
    Rewrite(TmpFile);
    SMUserActivity(Format('TRecFile.CreateTmpFile created %s', [TmpFileName]));
    result := true;
  except
    on e: Exception do
      RecFileLog('Try..Except: RecFile.CreateTmpFile() - ' + e.message);
  end;
end;

function TRecFile.ClearVisaFields: boolean;
var
  tmpRecord: MDMsgRec;
  iRec: integer;
begin
  Result := false;
  try
    FillChar(tmpRecord, SizeOf(MDMsgRec),0);
    ResetFile;
    iRec := GetFileSize - 1;
    while (iRec >= 0) do
    begin
      if (ReadByRecordNum(iRec, tmpRecord) = RECFILE_SUCCESS) and (tmpRecord.ReqCode <> '') then
      begin
        ClearVISARegFieldsIfNeeded(tmpRecord);
        WriteByRecordNum(iRec, tmpRecord);
      end;
      Dec(iRec);
    end;
    result := (iRec = -1);
  except
    on e:Exception do
      RecFileLog('Try..Except: RecFile.ClearVisaFields() - ' + e.Message);
  end;
end;

function  TRecFile.ChangeOfflinePreAuthCompToPurch: boolean;
var
  tmpRec: MdMsgRec;
  readResult, iRec: integer;
  maxRecords: integer;
  actFile: TTrxLog;
begin
  result := false;
  try
    actFile := CreateTrxLogObject(acctFileName);
    try
      iRec := 0;
      maxRecords := GetFileSize;
      while (iRec < maxRecords) do
      begin
        readResult := ReadByRecordNum(iRec, tmpRec);           // read the offline record from the file
        if (tmpRec.ReqCodeN = DbPreAuthCompN) then
        begin
          if readResult = RECFILE_SUCCESS then
          begin
            RecFileLog('****NOTICE:  SeqNum ' + tmpRec.SeqNo + ': Change offline debit preAuthComp to purchase.');
            tmpRec.ReqCode := DbPurch;
            tmpRec.ReqCodeN := DbPurchN;
            WriteByRecordNum(iRec, tmpRec);
            actFile.WriteByRecordNum(tmpRec.AcctFileRecNoN - 1, tmpRec)   // offline and actlog buffers are the same here
          end
          else if readResult = RECFILE_FAIL_ENCRYPTION then
          begin
            SM('TRecFile.ChangeOfflinePreAuthCompToPurch - rec read failed');
          end;
        end;
        Inc(iRec);
      end;
    finally
      actFile.Free;
    end;
    result := (iRec = maxRecords);
  except
    on e:Exception do
      RecFileLog('Try..Except: RecFile.ChangeOfflinePreAuthCompToPurch() - ' + e.Message);
  end;
end;

procedure TRecFile.DeleteTORsAtEOD(aSuffix: string);
var i, maxRecords: integer;
    torRec: MdMsgRec;
    tmpRec: MdMsgRec;
    torFile: TTrxLog;
    torFileName: string;
begin
  torFileName := 'TOR.' + aSuffix;
  torFile := CreateTrxLogObject(torFileName);
  try
    try
      i := 0;
      maxRecords := torFile.GetFileSize;
      while (i < maxRecords) do
      begin
        torFile.ReadByRecordNum(i, torRec);
        if ReadByRecordNum(torRec.AcctFileRecNoN-1, tmpRec) = RECFILE_SUCCESS then   // get actlog record
        begin
          tmpRec.TORType := '';
          WriteByRecordNum(torRec.AcctFileRecNoN-1, tmpRec);
        end;
        Inc(i);
      end;
    except
      on e:Exception do
        SM('Try..Except: RecFile.DeleteTORsAtEOD - ' + e.Message);
    end;
  finally
    torFile.RewriteFile;           // clear out TOR records
    torFile.Free;
  end;
  if DeleteFile(torFileName) then                     // get rid of file
    SMUserActivity(Format('TRecFile.DeleteTORsAtEOD deleted %s', [torFileName]));   
end;

procedure TRecFile.DeleteExistingOfflineAndTORFiles;
var i: integer;
    sRec: TSearchRec;
begin
  try
    for i := 1 to offFileCount do { JMR-H : delete existing ofline01.hhh & tor.hhh }
      if (FindFirst(WinEPSDir + offFileArray[i], faAnyFile, sRec) = 0) then
      try
        if DeleteFile(WinEPSDir + sRec.Name) then
        begin
          SMUserActivity(Format('TRecFile.DeleteExistingOfflineAndTORFiles deleted %s', [WinEPSDir + sRec.Name]));
          {$IFDEF MSWINDOWS}
          //_RSLog(sRec.Name + ' file deletion successful');
          {$ENDIF}
        end;
        while (FindNext(sRec) = 0) do
          if DeleteFile(WinEPSDir + sRec.Name) then
          begin
            SMUserActivity(Format('TRecFile.DeleteExistingOfflineAndTORFiles (2) deleted %s', [WinEPSDir + sRec.Name]));
            {$IFDEF MSWINDOWS}
            //_RSLog(sRec.Name + ' file deletion successful');
            {$ENDIF}
          end;
      finally
        FindClose(sRec);
      end;
  except
    on e : Exception do
      RecFileLog('****Exception: account.MakeOfflineFilesFromActLog.DeleteExistingOfflineAndTORFiles.  ' + e.Message);
  end;
end;

function  TRecFile.SaveOutstandingPreauths(QPreAuthFileName: string) : boolean;
var
  tmpRec: MdMsgRec;
  iRec,
  maxRecords: integer;
  preFile : TTrxLog;
begin
  result := false;
  try
    {$IFDEF TEST}
    preFile := CreateTrxLogObject(QPreAuthFileName,PERSISTENT_FILE);
    {$ELSE}
    preFile := CreateTrxLogObject(QPreAuthFileName);
    {$ENDIF}
    try
      iRec := 0;
      maxRecords := GetFileSize;
      while (iRec < maxRecords) do
      begin
        ReadByRecordNum(iRec, tmpRec);
        { save preauth if no completion yet and it is less than 7 days old }
        if (tmpRec.ReqCodeN in PreAuth_Set) and (tmpRec.ReqCode <> '') and (tmpRec.TermRspCode[1] = 'A')  and
           (tmpRec.CompletionRecNoN = 0) and
           (tmpRec.ReqCodeN <> eWicPreAuthN) then // DEV-49632: don't keep uncompleted eWIC PreAuth in EOD
        begin
          if (aJulianDate(fnYYMMDD) - aJulianDate(tmpRec.trxDate) < 7) then  { keep it }
            preFile.WriteRecord(tmpRec);
          ClearVisaRegFields(tmpRec);
          WriteByRecordNum(iRec, tmpRec);
        end;
        Inc(iRec);
      end;
    finally
      preFile.Free;
    end;
    result := (iRec = maxRecords);
  except
    on e:Exception do
      RecFileLog('Try..Except: RecFile.SaveOutstandingPreauths() - ' + e.Message);
  end;
end;

//function  TRecFile.MovePreAuthsToActlog(QPreAuthFileName: string) : boolean;
//var
//  tmpRec: MdMsgRec;
//  ReadResult,
//  iRec,
//  maxRecords: integer;
//  preFile : TTrxLog;
//begin
//  result := false;
//  try
//    {$IFDEF TEST}
//    preFile := CreateTrxLogObject(QPreAuthFileName,PERSISTENT_FILE);
//    {$ELSE}
//    preFile := CreateTrxLogObject(QPreAuthFileName);
//    {$ENDIF}
//    try
//      iRec := 0;
//      maxRecords := preFile.GetFileSize;
//      while (iRec < maxRecords) do
//      begin
//        readResult := preFile.ReadByRecordNum(iRec, tmpRec);
//        if readResult = RECFILE_SUCCESS then
//        begin
//          tmpRec.AcctFileRecNoN := GetFileSize + 1;
//          WriteRecord(tmpRec);
//        end
//        else if readResult = RECFILE_FAIL_ENCRYPTION then
//        begin
//          WriteRecord(tmpRec, '', false);
//        end;
//        inc(iRec);
//      end;
//    finally
//      preFile.Free;
//    end;
//    result := (iRec = maxRecords);
//  except
//    on e:Exception do
//      RecFileLog('Try..Except: RecFile.MovePreAuthsToActlog() - ' + e.Message);
//  end;
//end;

//function  TRecFile.CopyOfflineFilesToActlog : boolean;
//var
//  sRec : TSearchRec;
//  tmpOffRec: MdMsgRec;
//  recReadResult,
//  iRec,
//  maxRecords: integer;
//  offFile : TTrxLog;
//  aFile: string;
//begin
//  result := true;
//  try
//    aFile := WinEPSDir + OffDSN_Pfx + '01' +'.*';
//    if (FindFirst(aFile, faAnyFile, sRec) = 0) then
//      try
//        repeat
//          if MTX_Lib.IsKnownHost(Copy(sRec.Name, Pos('.', sRec.Name)+1, Length(sRec.Name))) then
//          begin
//            {$IFDEF TEST}
//            offFile := CreateTrxLogObject(WinEPSDir + sRec.Name,PERSISTENT_FILE);
//            {$ELSE}
//            offFile := CreateTrxLogObject(WinEPSDir + sRec.Name);
//            {$ENDIF}
//            try
//              iRec  := 0;
//              maxRecords := offFile.GetFileSize;
//              While (iRec < maxRecords) Do
//              Begin
//                recReadResult := offFile.ReadByRecordNum(iRec, tmpOffRec);
//                if (tmpOffRec.ReqType = OfflineFwd) then   { don't Copy deleted records }
//                begin
//                  if recReadResult = RECFILE_SUCCESS then
//                  begin
//                    //if (tmpOffRec.ReqCodeN <> settleTotReqN) then // DEV-40605, DEV-40841
//                    begin
//                      tmpOffRec.AcctFileRecNoN := GetFileSize + 1;
//                      tmpOffRec.BankOff := '*';                     { Show accounted for }
//                      offFile.writeByRecordNum(iRec, tmpOffRec);  { update buffer in offline file }
//                    end;
//                    {$IFDEF UPDATE}
//                    if (FindRecordNo(tmpOffRec.MTX_SeqNo, tsMTX) = -1) then // if not found
//                    begin
//                      WriteRecord(tmpOffRec); { write to actlog }
//                      SMsg(SPC + 'CopyOfflineFilesToActlog: Offline transaction copied to actlog. MTX_SeqNo=' + tmpOffRec.MTX_SeqNo);
//                    end
//                    else
//                      SMsg(SPC + 'CopyOfflineFilesToActlog: Offline transaction exists already. MTX_SeqNo=' + tmpOffRec.MTX_SeqNo);
//                    {$ELSE}
//                    WriteRecord(tmpOffRec); { write to actlog }
//                    {$ENDIF}
//                  end
//                  else if recReadResult = RECFILE_FAIL_ENCRYPTION then
//                  begin
//                    if not WriteRecord(tmpOffRec, '', false) then
//                      SM('CopyOfflineFilesToActlog: Block copy without encryption');
//                  end;
//                end;
//                Inc(iRec);
//              End;
//            finally
//              offFile.Free;
//            end;
//            if result then
//              result := (iRec = maxRecords);
//          end
//          else
//          if Copy(sRec.Name, Pos('.', sRec.Name)+1, Length(sRec.Name)) <> 'REC' then   // DAE 11/02/07 Don't print if this is REC host
//          begin
//            RecFileLog('WARNING: TRecFile.CopyOfflineFilesToActlog found unrecognized host file ' + sRec.Name);
//            result := false;
//          end;
//        until (FindNext(sRec) <> 0);
//      finally
//        FindClose(sRec);
//      end
//    else
//      RecFileLog('****NOTICE: No offline files (' + aFile + ') to copy to actlog');
//  except
//    on e:Exception do
//    begin
//      RecFileLog('Try..Except: RecFile.CopyOfflineFilesToActlog() - ' + e.Message);
//      result := false;
//    end;
//  end;
//end;

//function  TRecFile.SetTORTypeFromTORFiles: boolean; // DEV-24478
//var
//  sRec : TSearchRec;
//  tmpTORRec, tmpRec: MdMsgRec;
//  recReadResult,
//  iRec,
//  maxRecords: integer;
//  TORFile : TTrxLog;
//begin
//  result := false;
//  try
//    if FindFirst(WinEPSDir + TOR_PFX + '.*', faAnyFile, sRec) = 0 then
//    begin
//      try
//        repeat
//          if MTX_Lib.IsKnownHost(Copy(sRec.Name, Pos('.', sRec.Name)+1, Length(sRec.Name))) then
//          begin
//            //
//            TORFile := CreateTrxLogObject(WinEPSDir + sRec.Name);
//            try
//              iRec  := 0;
//              maxRecords := TORFile.GetFileSize;
//              while (iRec < maxRecords) Do
//              begin
//                recReadResult := TORFile.ReadByRecordNum(iRec, tmpTORRec);
//                if recReadResult = RECFILE_SUCCESS then
//                begin
//                  FillChar(tmpRec, SizeOf(MDMsgRec), 0);
//                  if FindRecord(tmpRec, tmpTORRec.SeqNo, tsCurrent) then
//                  begin
//                    if tmpRec.TORType <> tmpTORRec.ReqType then
//                    begin
//                      tmpRec.TORType := tmpTORRec.ReqType;
//                      if UpdateRecord(tmpRec, tsCurrent) then
//                        RecFileLog(Format('SetTORTypeFromTORFiles: Set TORType=%s for seqNo %s', [tmpRec.TORType, tmpTORRec.SeqNo]))
//                      else
//                        RecFileLog(Format('SetTORTypeFromTORFiles: Failed to set TORType=%s for seqNo %s - %s', [tmpRec.TORType, tmpTORRec.SeqNo, WinEPSDir + sRec.Name]));
//                    end;
//                  end
//                  else
//                    RecFileLog(Format('SetTORTypeFromTORFiles: Failed to find transaction (seqNo=%s) - %s', [tmpTORRec.SeqNo, WinEPSDir + sRec.Name]));
//                end;
//                Inc(iRec);
//              end;
//            finally
//              TORFile.Free;
//            end;
//            if result then
//              result := (iRec = maxRecords);
//          end;
//        until (FindNext(sRec) <> 0);
//      finally
//        FindClose(sRec);
//      end;
//    end
//    else
//      RecFileLog('****NOTICE: SetTORTypeFromTORFiles - No TOR file exists');
//  except
//    on e: Exception do
//    begin
//      RecFileLog('Try..Except: RecFile.SetTORTypeFromTORFiles - ' + e.Message);
//      result := false;
//    end;
//  end;
//end;

{ only used in utility MakeOfflineFiles.exe }
//function  TRecFile.MakeOfflinesAndTORsFromActlog(var QList: TStringList; MakeTORs: boolean = true) : boolean;
//var
//  tmpRecord: MdMsgRec;
//  iRec,
//  maxRecords: integer;
//  offName : string;
//  offFile : TTrxLog;
//  ReadResult: integer;
//begin
//  result := true;
//  try
//    QList.Sorted := true;
//    QList.Duplicates := dupIgnore;
//    ResetFile;
//    iRec := 0;
//    maxRecords := GetFileSize;
//
//    while (iRec < maxRecords) do
//    begin
//      ReadResult := ReadByRecordNum(iRec, tmpRecord);
//      if ReadResult <> RECFILE_FAIL then
//      begin
//        if ( (tmpRecord.ReqCodeN = SettleTotReqN) and
//             (tmpRecord.trxFinalDisp[1] <> DispFwdApp) // do not copy approved settlement
//           ) or  // Publix settle
//           ( (tmpRecord.ReqCodeN <> SettleTotReqN) and
//             (tmpRecord.trxFinalDisp[1] in [DispOffApp,DispFwdHeld]) and // offline candidates
//             (tmpRecord.ReqType[1] in [normalTrx,OfflineFwd,OfflineReq]) and
//             not(tmpRecord.ReqCodeN in preAuth_Set)
//           ) then                  { no offlines for these }
//        begin
//          tmpRecord.RecNoN  := 0;                                { set offline stuff }
//          tmpRecord.RecNo   := '00';
//          tmpRecord.ReqType := OffLineFwd;
//          tmpRecord.SentToModemYN := 'N';
//          tmpRecord.VoidRecNoN := 0;          { otherwise pub_util and others have errors in Update_orig }
//          {$IFDEF FUEL}
//          offName := WinEPSDir + 'ofline01.eps';  // file name only this for fuel
//          {$ELSE FUEL}
//          offName := WinEPSDir + 'ofline01.' + tmpRecord.HostSuffixCode; { specific to host }
//          {$ENDIF FUEL}
//          QList.Add(offName);
//          {$IFDEF TEST}
//          offFile := CreateTrxLogObject(offName,PERSISTENT_FILE);
//          {$ELSE}
//          offFile := CreateTrxLogObject(offName);
//          {$ENDIF}
//          try
//            if not offFile.WriteRecord(tmpRecord, '', ReadResult=RECFILE_SUCCESS) then
//            begin
//              {$IFNDEF LINUX}
////              _RSLog('****ERROR: Writing to file  ' + offName +
////                '  Pending offline transactions may be lost.  (Note: if ' +
////                'possible, retrieve the file from the primary WinEPS and ' +
////                'contact support for help in retrieving these transactions)');
//              {$ENDIF}
//              result := false;
//             end;
//          finally
//            offFile.Free;
//          end;
//        end;
//        if makeTORs and (tmpRecord.TORType <> '')                              and   { now see if we need a TOR }
//           (tmpRecord.ReqType[1] in [normalTrx,OfflineFwd])                    and
//           (IsPublix(tmpRecord.HostSuffixCode) or (tmpRecord.VoidCode <> 'V')) then  // don't reverse voids unless it is Publix
//        begin
//          tmpRecord.RecNoN  := 0;                                { set offline stuff }
//          tmpRecord.RecNo   := '00';
//          tmpRecord.ReqType := tmpRecord.TORType;
//          tmpRecord.SentToModemYN := 'N';
//          {$IFDEF FUEL}
//          offName := WinEPSDir + 'TOR.EPS';    // file name only this for fuel
//          {$ELSE FUEL}
//          if (tmpRecord.TORSuffixCode = '')
//            then offName := WinEPSDir + 'TOR.' + tmpRecord.HostSuffixCode // file is specific to host.  Note that Stater
//            else offName := WinEPSDir + 'TOR.' + tmpRecord.TORSuffixCode; // Bros could have a TOR in LML offline in LM2
////          RecFileLog('***** JMR DEBUG:2 offName = ' + offName);
//          {$ENDIF FUEL}
//          QList.Add(offName);
//          {$IFDEF TEST}
//          offFile := CreateTrxLogObject(offName,PERSISTENT_FILE);
//          {$ELSE}
//          offFile := CreateTrxLogObject(offName);
//          {$ENDIF}
//          try
//            if not offFile.WriteRecord(tmpRecord, '', ReadResult=RECFILE_SUCCESS) then
//            begin
//              {$IFNDEF LINUX}
//              //JTG - this wrote to a 'special' RSLog..  SM is better
//              //_RSLog('ERROR: Writing to file ' + offName + '.  TORs (reversals) may be lost.  ' +
//              //   '(Note: if possible, retrieve the TOR file(s) from the primary WinEPS and contact support to retrieve these transactions)');
//              RecFileLog('ERROR: Writing to file ' + offName + '.  TORs (reversals) may be lost.  ' +
//                 '(Note: if possible, retrieve the TOR file(s) from the primary WinEPS and contact support to retrieve these transactions)');
//              {$ENDIF}
//              result := false;
//            end;
//          finally
//            offFile.Free;
//          end;
//        end;
//      end;
//      Inc(iRec);
//    end;
//    if result then
//      result := (iRec = maxRecords);
//  except
//    on e:Exception do
//    begin
//      RecFileLog('Try..Except: RecFile.MakeOfflinesAndTORsFromActlog() - ' + e.Message);
//      result := false;
//    end;
//  end;
//end;

//function  TRecFile.RptInfoExists(RptInfoType: TRptInfoType): boolean;
//var
//  TmpRecord: MDMsgRec;
//  iRec: integer;
//begin
//  result := false;
//  FillChar(TmpRecord, SizeOf(MDMsgRec),0);
//  try
//    ResetFile;
//    iRec := GetFileSize - 1;
//    while iRec >= 0 do
//    begin
//      if ReadByRecordNum(iRec, TmpRecord) = RECFILE_SUCCESS then
//      case RptInfoType of
//        rtRptInfo: result := (copy(tmpRecord.RptInfo,1,3) = 'RPT');
//        rtRptBlock: result := (copy(tmpRecord.RptVersion,1,3) = 'RPT');
//      end;
//      if result then exit;
//      Dec(iRec);
//    end;
//  except
//    on e:Exception do
//      RecFileLog('Try..Except: RecFile.RptInfoExists - ' + e.Message);
//  end;
//end;

//function TRecFile.IsReportBlock(var QRecord: mdMsgRec): boolean; // DEV-28982
//begin
//  result := SameText(Copy(QRecord.rptVersion,1,3), 'RPT') or // is it a report record?
//            (Pos(Settle_Pfx, QRecord.SettleVersion) > 0)  or // Settle_Pfx  = 'settle'
//            SameText(Copy(QRecord.RptInfo,1,3), 'RPT')    or
//            (QRecord.ReqCodeN = SettleTotReqN); // Publix settle req in offline
//end;

function TRecFile.IsValidTransaction(QRecord : MDMsgRec): boolean; 
begin
  result := false;
  try
    result := NOT IsReportBlock(QRecord) and
        (QRecord.ReqCode <> '') and // deleted record
        (QRecord.ReqCodeN <> 0); // if this is zero, it ain't a trx either
  except
    on e:Exception do
      RecFileLog('Try..Except: RecFile.IsTransactionRecord - ' + e.Message);
  end;
end;

function TRecFile.TruncateCardData: boolean;                                    
var
  TmpRecord: MDMsgRec;
  iRec: integer;

  {$UNDEF DEF_RecFile_TenderExcluded}
  {$IFDEF MTXEPSDLL} {$DEFINE DEF_RecFile_TenderExcluded} {$ENDIF}
  {$IFDEF MTXPOSDLL} {$DEFINE DEF_RecFile_TenderExcluded} {$ENDIF}
  {$IFDEF RPT}       {$DEFINE DEF_RecFile_TenderExcluded} {$ENDIF}
  {$IFDEF HOSTSIM}   {$DEFINE DEF_RecFile_TenderExcluded} {$ENDIF}

  function TenderExcluded: boolean;
  {$IFNDEF DEF_RecFile_TenderExcluded}
  var
      ExcludeTender: TStringList;
      i: integer;
      inTender: string;

      function IsInTenderEBT: boolean;
      begin
        result := (inTender = tnEBT_FS) or (inTender = tnEBT_CA);
      end;
  {$ENDIF}
  begin
    {$IFDEF DEF_RecFile_TenderExcluded}
    result := false;
    {$ELSE DEF_RecFile_TenderExcluded}
    ExcludeTender := TStringList.Create;
    try
      SplitStr(DSProcBuf.ExcludedTenders, ',', ExcludeTender, true); // put tenders into a string list
      inTender := MTX_Lib.SetTenderName(TmpRecord);
      result := (ExcludeTender.IndexOf(inTender) >= 0);
      if not result then        // one last check to see if generic EBT was set
        result := IsInTenderEBT and (ExcludeTender.IndexOf(pmEBT) >= 0);

      // debug code follows:
      for i := 0 to ExcludeTender.Count - 1 do
        RecFileLog('****DEBUG TruncateCardData: Tender to exlude >%s<' + ExcludeTender[i]);
      RecFileLog(format('          Tender in >%s<, so result is %s', [inTender, BoolStr(result)]));
      // end of debug code
    finally
      ExcludeTender.Free;
    end;
    {$ENDIF DEF_RecFile_TenderExcluded}
  end;

begin
  result := false;
  try
    FillChar(TmpRecord, SizeOf(MDMsgRec),0);
    ResetFile;
    iRec := GetFileSize - 1;

    while iRec >= 0 do
    begin
      if ReadByRecordNum(iRec, TmpRecord) = RECFILE_SUCCESS then
        if IsValidTransaction(TmpRecord) and (not TenderExcluded) then
        begin
          if Trim(tmpRecord.AcctNo) <> '' then
          begin
            tmpRecord.AcctNo := MTX_Utils.TruncAcctNo(MTXEncryptionUtils.RetrieveMdMsgPersonalAccountNum(tmpRecord));
            MTXEncryptionUtils.StoreMdMsgPersonalAccountNum(tmpRecord, tmpRecord.AcctNo);
          end;

          if Trim(tmpRecord.ExpDate) <> '' then
            tmpRecord.ExpDate := MTX_Utils.TruncExpDate(Trim(tmpRecord.ExpDate));

          WriteByRecordNum(iRec, tmpRecord);
        end;
      Dec(iRec);
    end;
    result := true;
  except
    on e:Exception do
      RecFileLog('Try..Except: RecFile.TruncateCardData - ' + e.Message);
  end;
end;

function TRecFile.BlankOfflines: boolean;   // DEV-29582: blank out offline: trackdata, CVV, pin, ksn
var
  tmpRecord: MDMsgRec;
  iRec: integer;
begin
  result := false;
  try
    FillChar(tmpRecord, SizeOf(MDMsgRec),0);
    ResetFile;
    iRec := GetFileSize - 1;

    while iRec >= 0 do
    begin
      if ReadByRecordNum(iRec, tmpRecord) = RECFILE_SUCCESS then
        if IsValidTransaction(tmpRecord) and (tmpRecord.TrxFinalDisp = DispOffApp) then
        begin
          StoreMdMsgTrack2(tmpRecord, '');
          tmpRecord.PIN  := StringOfChar(' ', 16);
          tmpRecord.DukptKeySerialNumber := StringOfChar(' ', 20);
          tmpRecord.CVV2 := StringOfChar(' ', 5);
          WriteByRecordNum(iRec, tmpRecord);
        end;
      Dec(iRec);
    end;
    result := true;
  except
    on e:Exception do
      RecFileLog('Try..Except: RecFile.BlankOfflines - ' + e.Message);
  end;
end;

function TRecFile.FindDeclAdvBySeqNo(QSeqNo: string6): integer;
var bFound: Boolean;
  TmpRec: MDMsgRec;
  aTestSeqNo: integer;
begin
  Result := -1;
  aTestSeqNo := strToIntDef(QSeqNo, -1);
  bFound := False;
  ResetFile;    // Reset to start from the beginning of the file.
  while not Eof(FRecFile) and not bFound do
  begin
    Read(FRecFile, TmpRec);   // Read a record at a time
    bFound := ((QSeqNo = TmpRec.SeqNo) or (aTestSeqNo = strToIntDef(TmpRec.SeqNo, 0))) and TmpRec.DeclinedAdvice;
    if bFound then
      result := filepos(FRecFile)-1;
  end;
end;

// Search actlog by sequence number/type starting with the most recent trx.
function TRecFile.FindRecord(var QRecord : MDMsgRec; QSeqNo : string6; QSeqType : TSeqNumType) : boolean;
var
  bFound   : Boolean;
  TmpRecord: MDMsgRec;
  iRec: integer;
begin
  Result := false;
  try
    bFound := False;
    FillChar(TmpRecord, SizeOf(MDMsgRec),0);  // do NOT init buffer in
    if (trim(QSeqNo) <> '') then
    begin
      ResetFile;
      iRec := GetFileSize - 1;
      while (not bFound) and (iRec >= 0) do
      begin
        if ReadByRecordNum(iRec, TmpRecord) = RECFILE_SUCCESS {and (TmpRecord.ReqCode <> '')} then  // remove this so voids of traing trx work
          case QSeqType of
            tsCurrent:   bFound := (trim(QSeqNo) = trim(TmpRecord.SeqNo));
            tsOld:       bFound := (trim(QSeqNo) = trim(TmpRecord.OldSeq));
            tsMTX:       bFound := (trim(QSeqNo) = trim(TmpRecord.MTX_SeqNo));
            else         exit;
          end;
        Dec(iRec);
      end;
      if bFound then
      begin
        QRecord := TmpRecord;
        result := true;
      end;
    end;
  except
    on e:Exception do
      RecFileLog('Try..Except: RecFile.FindRecord() - ' + e.Message);
  end;
end;

function TRecFile.FindRecordNotDeleted(var QRecord : MDMsgRec; QSeqNo : string6; QSeqType : TSeqNumType) : boolean;
var
  bFound   : Boolean;
  TmpRecord: MDMsgRec;
  iRec, iSize: integer;
begin
  Result := false;
  try
    bFound := False;
    FillChar(TmpRecord, SizeOf(MDMsgRec),0);  // do NOT init buffer in
    if (trim(QSeqNo) <> '') then
    begin
      ResetFile;
      iSize := GetFileSize;
      iRec := 0;
      while (not bFound) and (iRec < iSize) do
      begin
        if (ReadByRecordNum(iRec, TmpRecord) = RECFILE_SUCCESS) and (TmpRecord.ReqCode <> '') then  // no deleted records
          case QSeqType of
            tsCurrent:   bFound := (trim(QSeqNo) = trim(TmpRecord.SeqNo));
            tsOld:       bFound := (trim(QSeqNo) = trim(TmpRecord.OldSeq));
            tsMTX:       bFound := (trim(QSeqNo) = trim(TmpRecord.MTX_SeqNo));
            else         exit;
          end;
        inc(iRec);
      end;
      if bFound then
      begin
        QRecord := TmpRecord;
        result := true;
      end;
    end;
  except
    on e:Exception do
      RecFileLog('Try..Except: RecFile.FindRecord() - ' + e.Message);
  end;
end;

// Search actlog by sequence number/type starting with the most recent trx.
function TRecFile.FindRecordButNotVoid(var QRecord : MDMsgRec; QSeqNo : string6; QSeqType : TSeqNumType) : boolean;
var
  bFound   : Boolean;
  TmpRecord: MDMsgRec;
  iRec: integer;
begin
  Result := false;
  try
    bFound := False;
    FillChar(TmpRecord, SizeOf(MDMsgRec),0);
    FillChar(QRecord, SizeOf(MDMsgRec), 0);
    if (trim(QSeqNo) <> '') then
    begin
      ResetFile;
      iRec := GetFileSize - 1;
      while (not bFound) and (iRec >= 0) do
      begin
        if (ReadByRecordNum(iRec, TmpRecord) = RECFILE_SUCCESS) {and (TmpRecord.ReqCode <> '')} and
           (TmpRecord.VoidCode <> 'V') then
          case QSeqType of
            tsCurrent:   bFound := (trim(QSeqNo) = trim(TmpRecord.SeqNo));
            tsOld:       bFound := (trim(QSeqNo) = trim(TmpRecord.OldSeq));
            tsMTX:       bFound := (trim(QSeqNo) = trim(TmpRecord.MTX_SeqNo));
            else         exit;
          end;
        Dec(iRec);
      end;
      if bFound then
      begin
        QRecord := TmpRecord;
        result := true;
      end;
    end;
  except
    on e:Exception do
      RecFileLog('Try..Except: RecFile.FindRecordButNotVoid() - ' + e.Message);
  end;
end;

function TRecFile.FindRecordToMakeTOR(var QRecord: MDMsgRec; OldSeqNo, SeqNo: string6): boolean; //JTG 33934 33801 requires a new method to scan the Actlog
var
  TmpRecord: MDMsgRec;
  iRec: integer;
begin
  result := false;
  try
    FillChar(TmpRecord, SizeOf(MDMsgRec),0);
    OldSeqNo := trim(OldSeqNo);        //JTG: call trim just once instead of on every record like in FindRecordButNotVoid...
    SeqNo := trim(SeqNo);
    if (length(OldSeqNo) > 0) or (length(SeqNo) > 0) then
      begin
      ResetFile;
      iRec := GetFileSize - 1;
      while (not result) and (iRec >= 0) do
        begin
        if (ReadByRecordNum(iRec, TmpRecord) = RECFILE_SUCCESS) and (TmpRecord.VoidCode <> 'V') then
          if TmpRecord.ReqCodeN = eWicPreAuthCompN        //JTG: eWicPreAuthCompN TORs operate differently, as per Tom
            then result := SeqNo = trim(TmpRecord.SeqNo)
            else result := OldSeqNo = trim(TmpRecord.OldSeq);
        dec(iRec);
        end;
      if result then
        begin
        FillChar(QRecord, SizeOf(MDMsgRec), 0);
        QRecord := TmpRecord;
        end;
      end;
  except on e:Exception do
    RecFileLog('Try..Except: RecFile.FindRecordToMakeTOR - ' + e.Message);
  end;
end;

function  TRecFile.FindPreAuth(var QRecord: MDMsgRec; QSeqNo: string6; QAcctNoLast4: string) : boolean;
var
  bFound   : Boolean;
  TmpRecord: MDMsgRec;
  iRec: integer;
begin
  Result := false;
  try
    bFound := False;
    FillChar(TmpRecord, SizeOf(MDMsgRec),0);
    FillChar(QRecord, SizeOf(MDMsgRec), 0);
    if (trim(QSeqNo) <> '') and (QAcctNoLast4 <> '') then
    begin
      ResetFile;
      iRec := GetFileSize - 1;
      while (not bFound) and (iRec >= 0) do
      begin
        if (ReadByRecordNum(iRec, TmpRecord) = RECFILE_SUCCESS) {and (TmpRecord.ReqCode <> '')} then
          bFound := (trim(QSeqNo) = trim(TmpRecord.OldSeq)) and
                    (QAcctNoLast4 = TmpRecord.AcctNoLast4)  and
                    (TmpRecord.ReqCodeN in PreAuth_Set);
        Dec(iRec);
      end;
      if bFound then
      begin
        QRecord := TmpRecord;
        result := true;
      end;
    end;
  except
    on e:Exception do
      RecFileLog('Try..Except: RecFile.FindPreAuth() - ' + e.Message);
  end;
end;

// Write settlement record to actlog.  Update if already present, otherwise insert.
// Function returns true if record successfully written, otherwise false.
// QFound returns true if already present, otherwise false.
function  TRecFile.WriteSettlementRecord(QRecord : MDMsgRec; var QFound:boolean) : boolean;
var
  TmpRecord: MDMsgRec;
  NumRecords,
  iRec: integer;
begin
  Result := false;
  try
    QFound := False;
    FillChar(TmpRecord, SizeOf(MDMsgRec),0);
    if (trim(QRecord.HostName) <> '') then
    begin
      ResetFile;
      numRecords := GetFileSize;
      iRec := 0;
      while (not QFound) and (iRec < numRecords) do
      begin
        if ReadByRecordNum(iRec, TmpRecord) = RECFILE_SUCCESS then
          QFound := ((POS(Settle_Pfx,TmpRecord.SettleVersion) > 0) and
                     (SameText(trim(QRecord.HostName), trim(TmpRecord.HostName))));
        if not QFound then
          Inc(iRec);
      end;
      if QFound then
        result := WriteByRecordNum(iRec, QRecord)
      else
        result := WriteRecord(QRecord);
    end;
  except
    on e:Exception do
      RecFileLog('Try..Except: RecFile.WriteSettlementRecord() - ' + e.Message);
  end;
end;

function  TRecFile.FindRecordByAcctNoAndReqCode(var QRecord: MdMsgRec; QAcctNoLast4: string; QReqCode: string): boolean;
var
  bFound   : Boolean;
  TmpRecord: MDMsgRec;
  iRec: integer;
begin
  Result := false;
  try
    bFound := False;
    FillChar(TmpRecord, SizeOf(MDMsgRec),0);
    FillChar(QRecord, SizeOf(MDMsgRec), 0);
    if (QAcctNoLast4 <> '') and (Trim(QReqCode) <> '') then
    begin
      ResetFile;
      iRec := GetFileSize - 1;
      while (not bFound) and (iRec >= 0) do
      begin
        if ReadByRecordNum(iRec, TmpRecord) = RECFILE_SUCCESS {and (TmpRecord.Reqcode <> '')} then
          bFound := (QAcctNoLast4 = tmpRecord.AcctNoLast4) and
                    (QReqCode = TmpRecord.ReqCode)         and
                    (TmpRecord.VoidCode <> 'V');
        Dec(iRec);
      end;
      if bFound then
      begin
        QRecord := TmpRecord;
        result := true;
      end;
    end;
  except
    on e:Exception do
      RecFileLog('Try..Except: RecFile.FindRecordByAcctNoAndReqCode() - ' + e.Message);
  end;
end;

// Search actlog by account number & trxAmtN starting with the most recent trx.
function  TRecFile.FindRecordByAcctNoAndTrxAmtN(var QRecord: MDMsgRec; QAcctNoLast4: string; QTrxAmtN: int64) : boolean;
var
  bFound   : Boolean;
  TmpRecord: MDMsgRec;
  iRec: integer;
begin
  Result := false;
  try
    bFound := False;
    FillChar(TmpRecord, SizeOf(MDMsgRec),0);
    FillChar(QRecord, SizeOf(MDMsgRec), 0);
    if (QAcctNoLast4 <> '') and (QTrxAmtN > 0) then
    begin
      ResetFile;
      iRec := GetFileSize - 1;
      while (not bFound) and (iRec >= 0) do
      begin
        if ReadByRecordNum(iRec, TmpRecord) = RECFILE_SUCCESS {and (TmpRecord.Reqcode <> '')} then
          bFound := (QAcctNoLast4 = tmprecord.AcctNoLast4) and
                    (QTrxAmtN = TmpRecord.TrxAmtN)         and
                    (TmpRecord.VoidCode <> 'V');

        Dec(iRec);
      end;
      if bFound then
      begin
        QRecord := TmpRecord;
        result := true;
      end;
    end;
  except
    on e:Exception do
      RecFileLog('Try..Except: RecFile.FindRecordByAcctNoAndTrxAmtN() - ' + e.Message);
  end;
end;

// Search actlog by account number starting with the most recent trx.
function TRecFile.FindRecordByAcctNo(var QRecord : MDMsgRec; QAcctNoLast4: string) : boolean;
var
  bFound   : Boolean;
  TmpRecord: MDMsgRec;
  iRec: integer;
begin
  Result := false;
  try
    bFound := False;
    FillChar(TmpRecord, SizeOf(MDMsgRec),0);
    FillChar(QRecord, SizeOf(MDMsgRec), 0);
    if (QAcctNoLast4 <> '') then
    begin
      ResetFile;
      iRec := GetFileSize - 1;
      while (not bFound) and (iRec >= 0) do
      begin
        if ReadByRecordNum(iRec, TmpRecord) = RECFILE_SUCCESS {and (TmpRecord.Reqcode <> '')} then
          bFound := (QAcctNoLast4 = TmpRecord.AcctNoLast4) and (TmpRecord.VoidCode <> 'V');
        Dec(iRec);
      end;
      if bFound then
      begin
        QRecord := TmpRecord;
        result := true;
      end;
    end;
  except
    on e:Exception do
      RecFileLog('Try..Except: RecFile.FindRecordByAcctNo() - ' + e.Message);
  end;
end;

{
  function FindRecordNo()

  Description:
    Find the record offset in FRecFile for the record indicated by QSeqNo.
    QIsOldSeqNo allows this function to search for a record by its current
    Sequence Number or by its original sequence number.

  Notes:
    It is important to remember that the offset returned is a record offset,
    not a byte offset.

  Parameter(s):
    QSeqNo   -  string indicating the sequence number
    QSeqType -  TSeqNumType enum indicating the sequence type to be used
                for the search (i.e. tsCurrent, tsOld, or tsMTX)

  Return Value(s):
    integer indicating the record number of the requested record
}
function TRecFile.FindRecordNo(QSeqNo: string6; QSeqType: TSeqNumType) : integer;
var
  bFound   : Boolean;
  TmpRecord: MDMsgRec;
  recNum   : integer;
  fileSize : integer;
  aTestSeqNo: integer;
begin
  Result := -1;
  aTestSeqNo := strToIntDef(QSeqNo, -1);
  bFound := False;
  FillChar(TmpRecord, SizeOf(MDMsgRec),0);
  ResetFile;    // Reset to start from the beginning of the file.
  fileSize := GetFileSize;
  recNum := 0;
  while not Eof(FRecFile) and not bFound do
  begin
    Read(FRecFile, TmpRecord);   // Read a record at a time
    with TmpRecord do
      case QSeqType of
        tsCurrent:   bFound := (QSeqNo = SeqNo) or (aTestSeqNo = strToIntDef(SeqNo, 0));
        tsOld:       bFound := (QSeqNo = OldSeq) or (aTestSeqNo = strToIntDef(OldSeq, 0));
        tsMTX:       bFound := (QSeqNo = MTX_SeqNo) or (aTestSeqNo = strToIntDef(MTX_SeqNo, 0));
      end;
    if (fileSize <= recNum) then
    begin
      RecFileLog(Format('WARNING FindRecordNo - Record number (%d) exceeded file size (%d): SeqType=%d, SeqNo=%d, MTX_SeqNo=%d',
          [recNum, fileSize, Ord(QSeqType), tmpRecord.SeqNo, tmpRecord.OldSeq, tmpRecord.MTX_SeqNo]));
      Exit;
    end;
    if bFound
      then Result := recNum     // Save the record number or
      else inc(recNum);         // go to next record
  end;
end;

//jtg redesigned
function TRecFile.FindRecordNoNew(QSeqNo: integer; QSeqType: TSeqNumType): integer;
const
  ERR = -1;
var
  aRecord: MDMsgRec;
  RecordSeq: integer;
begin
  result := ERR;
  ResetFile;    // Reset to start from the beginning of the file.
  while not eof(FRecFile) do
  begin
    read(FRecFile,aRecord);   // Read a record at a time
    //if (aRecord.ReqCode <> '') then
      case QSeqType of
        tsCurrent: RecordSeq := StrToIntDef(aRecord.SeqNo,ERR);  // this is now format-independent
        tsOld:     RecordSeq := StrToIntDef(aRecord.OldSeq,ERR);
        tsMTX:     RecordSeq := StrToIntDef(aRecord.MTX_SeqNo,ERR);
        else       RecordSeq := ERR;
      end;
    if QSeqNo = RecordSeq then
    begin
      result := filepos(FRecFile)-1;  //why increment our own counter?
      exit;
    end;
  end;
end;

function  TRecFile.FindRecordNotVoidByOldSeqNo(var QRecord : MDMsgRec; QOldSeqNo : string6) : boolean;
var
  aRecord: MDMsgRec;
begin
  result := false;
  try
    ResetFile;
    while not eof(FRecFile) and not result do
    begin
      read(FRecFile, aRecord);
      if {(aRecord.Reqcode <> '') and} (aRecord.VoidCode <> 'V') and (aRecord.OldSeq = QOldSeqNo) then
      begin
        result := true;
        QRecord := aRecord;
      end;
    end;
  except
    on e:Exception do
      RecFileLog('Try..Except: RecFile.FindRecordNotVoidByOldSeqNo() - ' + e.Message);
  end;
end;

function TRecFile.FindRecordNotVoidByOldSeqNoAndReqCode(var QRecord : MDMsgRec; QOldSeqNo : string6; QReqCode: string): boolean;
var
  aRecord: MDMsgRec;
begin
  result := false;
  try
    ResetFile;
    while not eof(FRecFile) and not result do
    begin
      read(FRecFile, aRecord);
      if {(aRecord.Reqcode <> '') and} (aRecord.VoidCode <> 'V') and (aRecord.OldSeq = QOldSeqNo)  and
         (aRecord.ReqCode = QReqCode) then
      begin
        result := true;
        QRecord := aRecord;
      end;
    end;
  except
    on e:Exception do
      RecFileLog('Try..Except: RecFile.FindRecordNotVoidByOldSeqNoAndReqCode() - ' + e.Message);
  end;
end;

function  TRecFile.FindRecordNotVoidNotInFlightByOldSeqNo(var QRecord : MDMsgRec; QOldSeqNo : string6) : boolean;
begin  { for flat file system this method same as NotVoidByOldSeqNo }
  result := FindRecordNotVoidByOldSeqNo(QRecord, QOldSeqNo);
end;

function  TRecFile.FindRecordVoidByOldSeqNo(var QRecord : MDMsgRec; QOldSeqNo : string6) : boolean;
var
  aRecord: MDMsgRec;
begin
  result := false;
  ResetFile;
  while not eof(FRecFile) and not result do
  begin
    read(FRecFile, aRecord);
    if {(aRecord.Reqcode <> '') and} (aRecord.VoidCode = 'V') and (aRecord.OldSeq = QOldSeqNo) then
    begin
      result := true;
      QRecord := aRecord;
    end;
  end;
end;

{$IFNDEF HOSTSIM}
{$IFNDEF MTXEPSDLL}
{$IFNDEF MTXPOSDLL}
{$IFNDEF RPT}
{$ENDIF RPT}
{$ENDIF MTXPOSDLL}
{$ENDIF MTXEPSDLL}
{$ENDIF HOSTSIM}

{
  function GetFileSize()

  Description    :  Get the size of the file, FRecFile.
  Return Value(s):  integer indicating the size of the file (in records).
}
function TRecFile.GetFileSize : integer;
var
  WaitTime: Longint; // DEV-15409
  _IOResult: integer;
  _ErrCount: integer;
begin
  WaitTime := 0;
  _ErrCount := 0;
  repeat
    result := GetFileSizeEx(FRecFile, _IOResult, _ErrCount, 'GetFileSize');
    if _IOResult <> 0 then
    begin
      Delay(RETRY_FILE_LOCK_MS);
      Inc(WaitTime, RETRY_FILE_LOCK_MS);
      ResetFile;
      result := GetFileSizeEx(FRecFile, _IOResult, _ErrCount, 'GetFileSize');
    end;
  until (_IOResult = 0) or (WaitTime > FILE_LOCK_WAIT_MS);
  if _IOResult <> 0 then
    ShutDownEngineWithLog('TRecFile.GetFileSize timed out'); // DEV-15409
end;

{
  function GetRecordCount()

  Description:  Get the number of records in the record file, FRecFile.

  Notes: gets the record count by calling the GetRecordSequenceNumbers method.
  Return Value(s): integer as the number of records (transactions) found in the file
}

function TRecFile.GetRecordCount(QShowDeleted: Boolean) : integer;
var
  tmpSeqNums : TStringList;
begin
  tmpSeqNums := TStringList.Create;
  try
    result := GetRecordSequenceNumbers(tsMTX, TmpSeqNums, QShowDeleted);
  finally
    tmpSeqNums.Free;
  end;
end;

procedure TRecFile.GetCountAndAmount(var Count,Amount: integer);
var
  R: MDMsgRec;
begin
  Count := 0;
  Amount := 0;
  try
    ResetFile;    // reset to start search from beginning
    while not eof(FRecFile) do
      begin
      read(FRecFile,R);
      inc(Count);
      inc(Amount,R.TrxAmtN);
      RecFileLog(format('Count[%d] ThisAmt[%d] TotalAmt[%d]',[Count,R.TrxAmtN,Amount]));
      end;
  except on e: exception do
    RecFileLog('ERROR in RecFile.GetCountAndAmount - ' + e.Message);
  end;
end;

function TRecFile.GetRecordCountApproved(aHost: string3 = ''; aIncludeLocalApproved: boolean=false) : integer; // DEV-11499: add aIncludeLocalApproved
var
  tmpRecord : MDMsgRec;
begin
  result := 0;
  try
    FillChar(TmpRecord, SizeOf(MDMsgRec),0);
    ResetFile;    // reset to start search from beginning
    while not eof(FRecFile) do
    begin
      Read(FRecFile, TmpRecord);
      if (TmpRecord.ReqCode <> '') then
        if (aHost = '') or (Trim(Copy(TmpRecord.HostSuffixCode, 1, 3)) = aHost) then
        begin
          TmpRecord.TrxFinalDisp := rpad(TmpRecord.TrxFinalDisp, 1); { len must be > 0 for next if stmt }
          if (TmpRecord.TrxFinalDisp[1] in [DispOnlineApp, DispFwdApp]) then
            Inc(result);
          if aIncludeLocalApproved and SameText(Copy(TmpRecord.AuthCode, 1, 2), 'LA') then // DEV-11499
            Inc(result);
        end;
    end;
  except
    on e:Exception do
      RecFileLog('Try..Except: RecFile.GetRecordCountApproved - ' + e.Message);
  end;
end;

function TRecFile.GetRecordCountDeclined(aHost: string3 = '') : integer;
var
  tmpRecord : MDMsgRec;
begin
  result := 0;
  try
    FillChar(TmpRecord, SizeOf(MDMsgRec),0);
    ResetFile;    // reset to start search from beginning
    while not eof(FRecFile) do
    begin
      Read(FRecFile, TmpRecord);
      if (TmpRecord.ReqCode <> '') then
        if (aHost = '') or (Trim(Copy(TmpRecord.HostSuffixCode, 1, 3)) = aHost) then
        begin
          TmpRecord.TrxFinalDisp := rpad(TmpRecord.TrxFinalDisp, 1); { len must be > 0 for next if stmt }
          if not (TmpRecord.TrxFinalDisp[1] in ApprovedDispSet) then
            Inc(result);
        end;
    end;
  except
    on e:Exception do
      RecFileLog('Try..Except: RecFile.GetRecordCountDeclined - ' + e.Message);
  end;
end;

//function  TRecFile.GetVoucherHeldCountAndAmount(var aTotalHeldCount: integer; var aTotalHeldAmount: integer; var aTotalWaitingCount: integer; var aTotalWaitingAmount: integer): boolean; // DEV-8000
//var
//  tmpOffRec: MdMsgRec;
//begin
//  result := false;
//  try
////    if MTX_Lib.IsKnownHost(aHost) then
//    begin
//      aTotalHeldCount := 0;       // held until auth code is entered manually
//      aTotalHeldAmount := 0;
//      aTotalWaitingCount := 0;    // waiting to be sent to host
//      aTotalWaitingAmount := 0;
//      FillChar(tmpOffRec, SizeOf(MDMsgRec), 0);
//      ResetFile;    // reset to start search from beginning
//      while not eof(FRecFile) do
//      begin
//        Read(FRecFile, tmpOffRec);
////        RecFileLog('===DEBUG-JMR: XXXXX RecFile.GetVoucherHeldCountAndAmount TrxFinalDisp = >' + tmpOffRec.TrxFinalDisp[1] + '<  Amount = >' + tmpOffRec.TrxAmt + '< SeqNo = >' + tmpOffRec.SeqNo + '< ReqType = >' + tmpOffRec.ReqType + '<');
//        if {(tmpOffRec.ReqType[1] in [OfflineReq, OfflineFwd]) and} (tmpOffRec.VoidCode <> 'V') and tmpOffRec.VoucherClearProcess and (tmpOffRec.TrxFinalDisp[1] in [DispOffApp,DispFwdHeld]) then
//        begin
//          if (trim(tmpOffRec.AuthCode) = trim(tmpOffRec.AuthCodeIfLocal)) then
//          begin
//            Inc(aTotalHeldCount);
//            aTotalHeldAmount := aTotalHeldAmount + tmpOffRec.TrxAmtN;
//          end
//          else if (trim(tmpOffRec.AuthCode) <> trim(tmpOffRec.AuthCodeIfLocal)) then
//          begin
//            Inc(aTotalWaitingCount);
//            aTotalwaitingAmount := aTotalwaitingAmount + tmpOffRec.TrxAmtN;
//          end;
//        end;
//      end;
//    end;
////    else
////      RecFileLog('WARNING: TRecFile.GetVoucherHeldCount unrecognized host file: ' + aHost);
//    result := (aTotalHeldAmount > 0) and (aTotalHeldAmount > 0);
//  except
//    on e:Exception do
//      RecFileLog('Try..Except: RecFile.GetVoucherHeldCountAndAmount() - ' + e.Message);
//  end;
//end;

// given a starting sequence number, return an xml string with the next X voucher held trxs
//function TRecFile.GetVoucherHeldTrxs(aStartingNum: integer): string; // DEV-8000
//var
//  tmpRec: MdMsgRec;
//  aTotalHeldCount, aTotalHeldAmount, aTotalWaitingCount, aTotalWaitingAmount,
//  foundCount: integer;
//  VoucherClear: IXMLVoucherClearType;
//  VoucherTran: IXMLTransactionType;
//  s: AnsiString;
//begin
//  result := '';
//  try
////    if MTX_Lib.IsKnownHost(aHost) then
//    begin
//      VoucherClear := NewVoucherClear;
////      found := false;
//      foundCount := 0;
//      ResetFile;    // reset to start search from beginning
//      while (not eof(FRecFile))  and (foundCount < _MaxVoucherClearTrxs) do
//      Begin
//        FillChar(tmpRec, SizeOf(tmpRec), 0);
//        Read(FRecFile, tmpRec);
//        if tmpRec.ReqCode = '' then
//          continue;
////        SM('===DEBUG-JMR: XXXXX RecFile.GetVoucherHeldTrxs TrxFinalDisp = >' + tmpOffRec.TrxFinalDisp[1] + '<  Amount = >' + tmpOffRec.TrxAmt + '< SeqNo = >' + tmpOffRec.SeqNo + '< ReqType = >' + tmpOffRec.ReqType + '<');
////        if not found then
////          found := (tmpRec.AcctFileRecNoN >= aStartingNum);
//          //found := (StrToIntDef(tmpRec.SeqNo, 0) >= aStartingSeqNo);
//        if (tmpRec.AcctFileRecNoN >= aStartingNum) and (tmpRec.VoidCode <> 'V') {and (tmpRec.ReqType[1] in [OfflineReq, OfflineFwd])}
//          and tmpRec.VoucherClearProcess and (tmpRec.TrxFinalDisp[1] in [DispOffApp,DispFwdHeld])
//          and (trim(tmpRec.AuthCode) = trim(tmpRec.AuthCodeIfLocal)) then
//        begin
//          DecryptFields(tmpRec);
////          SM('===DEBUG-JMR: RecFile.GetVoucherHeldTrxs TrxFinalDisp = >' + tmpOffRec.TrxFinalDisp[1] + '<  Amount = >' + tmpOffRec.TrxAmt + '< SeqNo = >' + tmpOffRec.SeqNo + '<');
//          VoucherTran := VoucherClear.Transactions.Add;
////          VoucherTran.Host := tmpRec.HostSuffixCode;
//          VoucherTran.Num := tmpRec.AcctFileRecNoN;
//          VoucherTran.SeqNo := tmpRec.SeqNo;
//          VoucherTran.DateTime := tmpRec.TrxDate + ' ' + tmpRec.TrxTime;                         // todo format date time
//          VoucherTran.Lane := tmpRec.LaneNoN;
//          VoucherTran.Cashier := StrToIntDef(tmpRec.Cashier, 0);
//          VoucherTran.ManagerId := StrToIntDef(tmpRec.ManagerID, 0);
//          VoucherTran.Amount := tmpRec.TrxAmtN;
////          VoucherTran.LocalAuthCode := tmpRec.AuthCodeIfLocal;
//          if Trim(tmpRec.LastAuthCode) = '' then
//            VoucherTran.AuthCode := tmpRec.AuthCodeIfLocal
//          else
//            VoucherTran.AuthCode := tmpRec.LastAuthCode;
//          VoucherTran.VoucherNumber := tmpRec.EBT_Voucher_Num;
//          s := RetrieveMdMsgTrack2(tmpRec);
//          s := EncryptString(s);
//          VoucherTran.CardNumber := s;
////          SM('===DEBUG-JMR: RecFile.GetVoucherHeldTrxs Track2Data = >' + tmpRec.Track2Data + '<');
//          VoucherTran.CardType := tmpRec.CardProcID;                                                //todo
//          VoucherTran.DeclineCode := tmpRec.TermRspCode;
//          if Copy(tmpRec.TermRspCode, 1, 1) <> 'A' then
//            VoucherTran.DeclineInfo := tmpRec.CashPadDisp;
//          Inc(foundCount);
//        end;
//      End;
//
////offFile := CreateTrxLogObject(WinEPSDir + OffDSN_Pfx + '01.ACI'); //test
////try
//
//      {offFile.}GetVoucherHeldCountAndAmount(aTotalHeldCount, aTotalHeldAmount, aTotalWaitingCount, aTotalWaitingAmount);
//
////finally
////  offFile.Free;
////end;
//
//      VoucherClear.Transactions.TotalTrxCount := aTotalHeldCount;
//      VoucherClear.Transactions.TotalTrxAmount := aTotalHeldAmount;
//      VoucherClear.Transactions.TotalTrxWaitingCount := aTotalWaitingCount;
//      VoucherClear.Transactions.TotalTrxWaitingAmount := aTotalWaitingAmount;
//      result := VoucherClear.xml;
//    end
////    else
////    begin
////      SM('WARNING: TRecFile.GetVoucherHeldTrxs unrecognized host file: ' + aHost);
////      result := 'ERROR in Recfile.GetVoucherHeldTrxs: unrecognized host file: ' + aHost;
////    end;
//  except
//    on e:Exception do
//      SM('Try..Except: RecFile.GetVoucherHeldTrxs() - ' + e.Message);
//  end;
//end;

// Given a host code and an xml string containing necessary trx data, update the trx in the actlog01.eft & ofline01.xxx file.
// There should be only one trx in the string. Others trxs will be ignored.
//function TRecFile.VoucherUpdate(aXML: string): string; // DEV-8000
//var
//  tmpOffRec: MdMsgRec;
//  offFile : TTrxLog;
//  VoucherClear: IXMLVoucherClearType;
//  //VoucherTran: IXMLTransactionType;
//  XmlDoc: IXMLDocument;
//  offlineFilename: string;
//begin
////  RecFileLog('===DEBUG-JMR: TRecFile.VoucherUpdate aXML=>>>>>'+aXML+'<<<<<');
//  result := 'VoucherUpdate FAIL';
//  try
//    XMLDoc := LoadXMLData(aXML);
//    VoucherClear := GetVoucherClear(XMLDoc);
//    if VoucherClear.Transactions.Count > 0 then
//    begin
//      FillChar(tmpOffRec, SizeOf(MDMsgRec), 0);
//      FindRecord(tmpOffRec, VoucherClear.Transactions[0].SeqNo, tsCurrent);
//      if (tmpOffRec.ReqCode <> '') and (tmpOffRec.ReqType[1] in [OfflineReq, OfflineFwd]) and tmpOffRec.VoucherClearProcess
//        and (trim(tmpOffRec.AuthCode) = trim(tmpOffRec.AuthCodeIfLocal)) then
//      begin
//        if length(VoucherClear.Transactions[0].AuthCode) > 0 then
//          begin
//          tmpOffRec.AuthCode := RPad(VoucherClear.Transactions[0].AuthCode, 8);
//          tmpOffRec.LastAuthCode := tmpOffRec.AuthCode;
//          end;
//        if length(VoucherClear.Transactions[0].VoucherNumber) > 0 then
//          tmpOffRec.EBT_Voucher_Num := RPad(VoucherClear.Transactions[0].VoucherNumber, 15);
//        try
//          if VoucherClear.Transactions[0].Amount > 0 then
//          begin
//            if (VoucherClear.Transactions[0].Amount <= tmpOffRec.TrxAmtN) then  // don't set a greater amount!
//            begin
//              tmpOffRec.TrxAmtN := VoucherClear.Transactions[0].Amount;
//              tmpOffRec.TrxAmt := IntToStr(VoucherClear.Transactions[0].Amount);
//            end;
//          end;
//        except
//          ;// non-existent xml integer tags blow up
//        end;
//        if SameText(VoucherClear.Transactions[0].DeclineCode, 'REMOVE') then
//          tmpOffRec.TrxFinalDisp := DispVoucherRemoved;
//        if UpdateRecord(tmpOffRec, tsCurrent) then // update actlog
//        begin
//          tmpOffRec.ReqType := offlineFwd; // ofline file needs this to be offlineFwd, actlog needs it to offlineReq ???
//          offlineFilename := WinEPSDir + OffDSN_Pfx + '01.' + tmpOffRec.HostSuffixCode;
////          RecFileLog('===DEBUG-JMR: TRecFile.VoucherUpdate offlineFilename = >'+offlineFilename+'<');
//          offFile := CreateTrxLogObject(offlineFilename);
//          try
//            if offFile.UpdateRecord(tmpOffRec, tsCurrent) then  // update ofline01.xxx
//              result := 'OK update voucher!'
//            else
//              result := 'VoucherUpdate FAILED updating ' + offlineFilename;
//          finally
//            offFile.Free;
//          end;
//        end
//        else
//          result := 'VoucherUpdate FAILED updating actlog01.eft';
//      end
//      else
//        result := 'VoucherUpdate FAILED. Trx not voucher held.';
//    end
//    else
//      RecFileLog('VoucherUpdate: no transactions in incoming XML string');
//  except
//    on e:Exception do
//      RecFileLog('Try..Except: RecFile.VoucherUpdate() - ' + e.Message);
//  end;
//end;

function TRecFile.GetRecordSequenceNumbers(QSeqType : TSeqNumType;
                                           var QSeqNums : TStringList;
                                           QShowDeleted : Boolean) : integer;
var
  tmpRecord : MDMsgRec;
//  ioerr     : integer;
begin
  QSeqNums.Clear;
  FillChar(TmpRecord, SizeOf(MDMsgRec),0);
  ResetFile;    // reset to start search from beginning
  while not eof(FRecFile) do  // Search for the record.
  begin
    Read(FRecFile, TmpRecord);
    if (TmpRecord.ReqCode <> '') or QShowDeleted then
      case QSeqType of
        tsCurrent: QSeqNums.Add(TmpRecord.SeqNo);
        tsOld:     QSeqNums.Add(TmpRecord.OldSeq);
        tsMTX:     QSeqNums.Add(TmpRecord.MTX_SeqNo);
      end;
  end;
  result := QSeqNums.Count;
end;

{
  functin MarkRecordDeleted()

    Description:
      Mark the specified record, indicated by QSeqNo, to be deleted from the
      file. This will be done by setting TmpRecord.ReqCode := ''.

    Notes:
      This function DOES NOT delete the record. It only marks it for deletion.
      The actual deletion of the record is accomplished by invoking the
      ClearDeletedRecords() function.

   Parameter(s):
     QSeqNo   - string indicating the sequence number
     QSeqType - TSeqNumType enum indicating the sequence type to be used
                for the search (i.e. tsCurrent, tsOld, or tsMTX)

   Return Value(s):
     True  - The record was marked for deletion
     False - The record was not marked for deletion
}
function TRecFile.MarkRecordDeleted(QSeqNo : string6;
                                    QSeqType : TSeqNumType) : Boolean;
var
  TmpRecord : MDMsgRec;
begin
  Result := False;
  FillChar(TmpRecord, SizeOf(MDMsgRec), 0);

  if (FindRecord(TmpRecord, QSeqNo, QSeqType) = True) then
  begin
    TmpRecord.ReqCode := '';
    ClearVISARegFields(tmpRecord);
    Result := UpdateRecord(TmpRecord, tsMTX);
  end;
end;

{
  function MoveRecordToEOF()

    Description:
      Move the specified record, indicated by QSeqNo, to the end of the file.
      This is done by marking the record to be deleted from the file, via a
      call to MarkRecordDeleted(), and then writing the record to the end of
      the file.

    Notes:
      This function DOES NOT delete the original record. It only marks it for
      deletion. The actual deletion of the record is accomplished by invoking
      the ClearDeletedRecords() function.

    Parameter(s):
      QSeqNo   - string indicating the sequence number
      QSeqType - TSeqNumType enum indicating the sequence type to be used
                 for the search (i.e. tsCurrent, tsOld, or tsMTX)

    Return Value(s):
    True  - The record was moved to the end of the file
    False - The record was not moved to the end of the file
}
function TRecFile.MoveRecordToEOF(QSeqNo : string6;
                                  QSeqType : TSeqNumType) : Boolean;
var TmpMoveRec : MDMsgRec;
begin
  Result := False;
  FillChar(TmpMoveRec, SizeOf(MDMsgRec), 0);

  if FindRecord(TmpMoveRec, QSeqNo, QSeqType) then
  begin
    if MarkRecordDeleted(QSeqNo, QSeqType) then   // First, mark the record for deletion.
    begin
      if not WriteRecord(TmpMoveRec)          // Then, write the record back to the end of the file.
        then UpdateRecord(TmpMoveRec, tsMTX)  // If the write failed, update the record so that it will not be deleted.
        else result := true;
    end;
  end;
end;

{
  function ReadByRecordNum()

  description:  Read directly by the record number
  parameters :  RecordNum points to rec, QRecord is the receiving buffer
  Return val :  True if successful
}
function TRecFile.ReadByRecordNum(RecordNum: integer;
                                  var QRecord : MDMsgRec): integer;
var
  S: string;
begin
  //result := RECFILE_FAIL; // -1
  try
    FillChar(QRecord, SizeOf(MDMsgRec),0);
    Seek(FRecFile, RecordNum);
    Read(FRecFile, QRecord);
    if decryptFields(QRecord)
      then result := RECFILE_SUCCESS // 1
      else result := RECFILE_FAIL_ENCRYPTION // -2
    // should we fillchar QRecord?
  except
    on e:Exception do
    begin
      result := RECFILE_FAIL; // -1
      S := 'Try..Except: RecFile.ReadByRecordNum('+inttostr(RecordNum)+'), ' + FFullName + ' - ' + e.Message;
      if pos('end of file', e.message) > 0
        then S := S + ' Records in file = ' + intToStr(GetFileSize);
      RecFileLog(S);
    end;
  end;
end;    { ReadByRecordNum }

{
  function WriteByRecordNum()

  description:  Write directly by the record number
  parameters :  RecordNum points to rec, QRecord is the buffer to write
  Return val :  True if successful
}
function TRecFile.WriteByRecordNum(RecordNum: integer;
                                   QRecord : MDMsgRec): Boolean;
begin
  result := false;
  try
    encryptFields(QRecord);
    Seek(FRecFile, RecordNum);
    write(FRecFile, QRecord);
    resetFile;
    result := true;
  except
    on e:Exception do
      RecFileLog('Try..Except: RecFile.WriteByRecordNum() - ' + e.Message);
  end;
end;   { WriteByRecordNum }

function  TRecFile.WriteByRecordNumNoEncrypt(RecordNum: integer; QRecord : MDMsgRec): boolean;
begin
  result := false;
  try
    resetFile;
    //RecFileLog(format('RecFile.WriteByRecordNumNoEncrypt RecordNum = %d; Current Records in file = %d',[RecordNum,Filesize(FRecFile)]));
    seek(FRecFile, RecordNum);
    write(FRecFile, QRecord);
    //resetFile;
    result := true;
  except on e:Exception do
    {$IFDEF RSSRV}
    RecFileLog(format('RecFile.WriteByRecordNumNoEncrypt EXCEPTION: %s (RecordNum %d; SeqNo %s; NumRecords in file now = %d)',
      [e.Message,RecordNum,QRecord.SeqNo,FileSize(FRecFile)]));
    {$ELSE}
    RecFileLog('Try..Except: RecFile.WriteByRecordNumNoEncrypt - ' + e.Message);
    {$ENDIF}
  end;
end;   { WriteByRecordNum }

(* Replace by FindRecord
  function ReadRecord()

  Description:  Read the specified record, indicated by QSeqNo
  Parameter(s):
    QSeqNo   - string indicating the sequence number
    QSeqType - TSeqNumType enum indicating the sequence type to be used
               for the search (i.e. tsCurrent, tsOld, or tsMTX)
    QRecord  - MDMsgRec that will contain the record that is read from the file.

  Return Value(s): True is successful

function TRecFile.ReadRecord(QSeqNo : string6;
                             QSeqType : TSeqNumType;
                             var QRecord : MDMsgRec) : Boolean;
var
  TmpOffset  : Longint;
begin
  Result := False;
  FillChar(QRecord, SizeOf(MDMsgRec), 0);

  if (QSeqNo <> '') then
  begin
    TmpOffset := FindRecordNo(QSeqNo, QSeqType); // Find the record to read.

    if (TmpOffset > -1) then // if the record was found, read it into QRecord.
    begin
      try
        Seek(FRecFile, TmpOffset);
        Read(FRecFile, QRecord);
        decryptFields(QRecord);
        Result := True;
      except
        on e:Exception do
          RecFileLog('Try..Except: RecFile.ReadRecord() - ' + e.Message);
      end;
    end;
  end;
end;    { readRecord }
*)

{
  function RemoveRecord()

  Description:
    Remove the specified record, indicated by QSeqNo, from the file.

  Notes:
    This method will delete the record from the file, which causes a rewrite
    of the entire file. This is not a concern while the file is small, but
    should be considered as the files get larger.

  Typically, the record should be marked as deleted (via a call to
  MarkRecordDeleted()) and then cleaned up later (via a call to
  ClearDeletedRecords()) so as not to hamper performance by  disk access
  (of particular concern as the file grows large due to many records in
  the file -- each record occupies ~1K of disk space).

  Parameter(s):
    QSeqNo   - string indicating the sequence number
    QSeqType - TSeqNumType enum indicating the sequence type to be used
               for the search (i.e. tsCurrent, tsOld, or tsMTX)

  Return Value(s):
    True  - The record was successfully deleted from the file
    False - The record was not deleted from the file
}
function TRecFile.RemoveRecord(QSeqNo : string6; QSeqType : TSeqNumType) : Boolean;
var
  TmpFileName : string;
  TmpSeqNo    : string;
  TmpRec      : MDMsgRec;
  WaitTime: Longint; // DEV-15409
  _IOResult: integer; // DEV-15409
  _ErrCount: integer; 
begin
  Result := False;

  try
    if (FindRecordNo(QSeqNo, QSeqType) > -1) then
    begin
      FillChar(TmpRec, SizeOf(MDMsgRec), 0);

      // Create a temp file for copying that differs from the original file only
      // by a .tmp extension. Open this file for use via the rewrite procedure.
      TmpFileName := CreateTmpFileName(FFullName);
      if CreateTmpFile(TmpFileName) then
      begin
        ResetFile; // Reset FRecFile so that all records are processed.

        // Copy from FRecFile to Tmp File skipping the recs specified by QSeqNo.
        while not Eof(FRecFile) do
        begin
          Read(FRecFile, TmpRec);
          case QSeqType of
            tsCurrent: TmpSeqNo := TmpRec.SeqNo;
            tsOld:     TmpSeqNo := TmpRec.OldSeq;
            tsMTX:     TmpSeqNo := TmpRec.MTX_SeqNo;
          end;
          if (TmpSeqNo <> QSeqNo) then
          begin
            WaitTime := 0; // DEV-15409 <
            _ErrCount := 0; 
            repeat
              WriteEx(TmpFile, TmpRec, _IOResult, _ErrCount, 'RemoveRecord');
              if _IOResult <> 0 then
              begin
                Delay(RETRY_FILE_LOCK_MS);
                Inc(WaitTime, RETRY_FILE_LOCK_MS);
                WriteEx(TmpFile, TmpRec, _IOResult, _ErrCount, 'RemoveRecord');
              end;
            until (_IOResult = 0) or (WaitTime > FILE_LOCK_WAIT_MS);
            if _IOResult <> 0 then
            begin
              ShutDownEngineWithLog('TRecFile.RemoveRecord WriteEx timed out'); // DEV-15409
            end;
          end;
        end;

        // Release the file handle, TmpFile, and close the temporary file.
        CloseFile(TmpFile);

        // Now that the file has been copied, we need to close the original
        // file, deleted it, and rename the temporary file to the original
        // filename.
        if FileExists(TmpFileName) then
        begin
          // Release the file handle, FRecFile, and close the original file.
          CloseFile(FRecFile);

          // Delete the original file.
          if DeleteFile(FFullName) = True then
          begin
            SMUserActivity(Format('TRecFile.RemoveRecord deleted %s', [FFullName]));
            // Rename temporary file to the original name & set return value.
            // NOTE: If rename OK, success is indicated for this entire function
            Result := RenameFile(TmpFileName, FFullName);

            // Reset the file handle, FRecFile, to point to "new" original file.
            if Result then
              AssignFile(FRecFile, FFullName);
          end;
        end;
      end;
    end;
  except { TRI-A }
    on E: Exception do
    begin
      RecFileLog('Try..Except:  Exception: (' + E.Message + ') in TRecFile.RemoveRecord. Trying to stop the service...');
      {$IFNDEF LINUX}
      EasyCreateProcess('net stop WinEPS');
      {$ENDIF}
    end;
  end;
end;

{
  procedure ResetFile

  Description:
    Wrapper for the Reset() procedure call. This ensures that Reset() is
    called for the file, FRecFile, and that the record is specified to be the
    value indicated by SizeOf(TMDMsgRec).
}
function TRecFile.ResetFile: boolean;
var
  WaitTime: Longint; // DEV-15409
  _IOResult: integer;
  _ErrCount: integer;
  (*
  procedure ResetFileEx; // DEV-15409
  begin
    _IOResult := 0;
    try
      {I-} Reset(FRecFile); {$I+}
    except
      on e: Exception do
      begin
        _IOResult := -1;
        RecFileLog(Format('TRecFile.ResetFile Failed to reset %s - %s', [FFullName, e.message]));
      end;
    end;
    if _IOResult = 0 then
      _IOResult := IOResult;
  end; *)
begin

  FileMode := 66;
  WaitTime := 0;
  _ErrCount := 0;
  repeat
    ResetFileEx(FRecFile, _IOResult, _ErrCount, 'ResetFile');
    if _IOResult <> 0 then
    begin
      Delay(RETRY_FILE_LOCK_MS);
      Inc(WaitTime, RETRY_FILE_LOCK_MS);
      ResetFileEx(FRecFile, _IOResult, _ErrCount, 'ResetFile');
    end;
  until (_IOResult = 0) or (WaitTime > FILE_LOCK_WAIT_MS);
  result := _IOResult = 0;
  if _IOResult <> 0 then
    ShutDownEngineWithLog('TRecFile.ResetFile timed out'); // DEV-15409
end;

{
  procedure RewriteFile

  Description:
    Wrapper for the Rewrite() procedure call. This ensures that Rewrite() is
    called for the file, QFile, and that the record size is specified to be
    the value indicated by SizeOf(TMDMsgRec).

  Notes: This procedure opens the file indicated by QFile.
}
function TRecFile.RewriteFile: boolean;
var
  WaitTime: Longint; // DEV-15409
  _IOResult: integer;
  _ErrCount: integer;
begin
  FileMode := 66;
  WaitTime := 0;
  _ErrCount := 0; 
  repeat
    RewriteFileEx(FRecFile, _IOResult, _ErrCount, 'RewriteFile');
    if _IOResult <> 0 then
    begin
      Delay(RETRY_FILE_LOCK_MS);
      Inc(WaitTime, RETRY_FILE_LOCK_MS);
      RewriteFileEx(FRecFile, _IOResult, _ErrCount, 'RewriteFile');
    end;
  until (_IOResult = 0) or (WaitTime > FILE_LOCK_WAIT_MS);
  result := _IOResult = 0;
  if _IOResult <> 0 then
    ShutDownEngineWithLog('TRecFile.RewriteFile timed out'); // DEV-15409
end;

function  TRecFile.InitLaneMsgFile: boolean;
var
  i: integer;
  tmpRec: MdMsgRec;
begin
  result := false;
  try
    FileMode := 2;
    try
      RewriteFile;           // create a new file
      InitMdMsg(tmpRec);
      RecFileLog('****NOTICE: Create LaneMsg File, record size = ' + intToStr(sizeOf(tmpRec)));  //JTG get rid of global DSLaneMsgBuf
      i := 0;
      while (i <= MaxTerms) do
      Begin
        tmpRec.RecNoN := i;
        tmpRec.RecNo  := TwoDigits(i);
        WriteRecord(tmpRec);
        Inc(i);
      End;
    finally
      FileMode := 66;
    end;
    result := (i = MaxTerms + 1);   { last inc(i), above, adds one to MaxTerms }
  except
    on e: exception do
      RecFileLog('Try..Except: RecFile.InitLaneMsgFile - ' + e.Message);
  end;
end;

{
  function UpdateRecord() TRecFile class Pubilc

  Description:
    Update (modify) an existing record in the file.

  Notes:
    This function verifies the existence of the record in the file prior to
    attempting to update it. If the record is not found, QRecord is appended
    to the file (via WriteRecord()).

  Parameter(s):
    QRecord  - MDMsgRec containing the record data to update
    QSetType - which seq no to use to search for record

  Return Value(s):
    True  - The update was successful
    False - The update failed
}
function TRecFile.UpdateRecord(QRecord : MDMsgRec; QSeqType : TSeqNumType) : Boolean;
var
  TmpRecNo : integer;
  WaitTime: Longint; // DEV-15409
  _IOResult: integer; // DEV-15409
  _ErrCount: integer;   
begin
  result := false;
  try
    TmpRecNo := -1;
    case QSeqType of
      tsMTX    : TmpRecNo := FindRecordNo(QRecord.MTX_SeqNo, QSeqType);
      tsOld    : TmpRecNo := FindRecordNo(QRecord.Oldseq, QSeqType);
      tsCurrent: TmpRecNo := FindRecordNo(QRecord.seqNo, QSeqType);
    end;
    if (TmpRecNo <> -1) then
    begin
      ResetFile;
      encryptFields(QRecord);
      Seek(FRecFile, TmpRecNo);

      WaitTime := 0; // DEV-15409 <
      _ErrCount := 0; 
      repeat
        WriteEx(FRecFile, QRecord, _IOResult, _ErrCount, 'UpdateRecord'); // overwrite existing record
        if _IOResult <> 0 then
        begin
          Delay(RETRY_FILE_LOCK_MS);
          Inc(WaitTime, RETRY_FILE_LOCK_MS);
          WriteEx(FRecFile, QRecord, _IOResult, _ErrCount, 'UpdateRecord');
        end;
      until (_IOResult = 0) or (WaitTime > FILE_LOCK_WAIT_MS);
      if _IOResult <> 0 then
      begin
        ShutDownEngineWithLog('TRecFile.UpdateRecord WriteEx timed out'); // DEV-15409
      end;
      Result := (_IOResult = 0);
    end
    else
      Result := WriteRecord(QRecord);   // write new record

  except { TRI-A }
    on E: Exception do
    begin
      RecFileLog('****Try..Except: TRecFile.UpdateRecord: ' + E.Message + '  Try to stop the service...');
      {$IFNDEF LINUX}
      EasyCreateProcess('net stop WinEPS');
      {$ENDIF}
    end;
  end;
end;

{
  function WriteRecord()

  Description:
    Write the specified record, QRecord, to the file indicated by FRecFile.

  Notes:
    This function appends the record to the end of the file, without
    verifying that it does not already exist.

  Parameter(s):
    QRecord - MDMsgRec contiaining the record data to write to the file

  Return Value(s):
    True  - The write was successful
    False - The write failed
}
function TRecFile.WriteRecord(QRecord : MDMsgRec; newKEK3: string=''; encryptIt: boolean=true) : Boolean;
var
  WaitTime: Longint; // DEV-15409
  _IOResult: integer; // DEV-15409
  _ErrCount: integer;
begin
  Result := False;
  try
    if encryptIt then 
      encryptFields(QRecord, newKEK3);
    Seek(FRecFile, FileSize(FRecFile));      // Seek to the end to prepare for append.

    WaitTime := 0; // DEV-15409 <
    _ErrCount := 0;
    repeat
      WriteEx(FRecFile, QRecord, _IOResult, _ErrCount, 'WriteRecord'); // Append the record to end of file
      if _IOResult <> 0 then
      begin
        Delay(RETRY_FILE_LOCK_MS);
        Inc(WaitTime, RETRY_FILE_LOCK_MS);
        Seek(FRecFile, FileSize(FRecFile)); // Seek to the end to prepare for append.
        WriteEx(FRecFile, QRecord, _IOResult, _ErrCount, 'WriteRecord');
      end;
    until (_IOResult = 0) or (WaitTime > FILE_LOCK_WAIT_MS);
    if _IOResult <> 0 then
      ShutDownEngineWithLog('TRecFile.WriteRecord WriteEx timed out'); // DEV-15409
    ResetFile;              // Reset FRecFile so that all records are processed.
    result := (_IOResult = 0);
  except { TRI-A }
    on E: Exception do
    begin
      RecFileLog('Try..Except:  (' + E.Message + ') in TRecFile.WriteRecord. Trying to stop the service...');
      {$IFNDEF LINUX}
      EasyCreateProcess('net stop WinEPS');
      {$ENDIF}
    end;
  end;
end;

function  TRecFile.GetFullName: string;
begin
  result := FFullName;
end;

procedure TRecFile.SetFullName(aName: string);
begin
  FFullName := aName;
end;

{$IFDEF TEST}
// limitation: dir must be only one level deep
procedure TRecFile.SetupTest(dir: string);
const
  TEST_DIR = 'C:\UnitTests\';
var
  sDir: string;
begin
  // persistency is false by default; app has to set this T if we want to keep file
  PersistentFile := false;
  sDir := ExtractFilePath(dir);
  if length(sDir) = 0  // we sent in something pathless, so set to default TEST dir 
    then sDir := TEST_DIR;
  if not DirectoryExists(sDir) then
    if not CreateDir(sDir) then
      raise Exception.Create('Cannot create ' + ExtractFilePath(dir));
end;

function TRecFile.TearDownTest: boolean;
begin
  if PersistentFile
    then result := true
//    else result := sysutils.DeleteFile(FFullName);
end;

// add our own method to explicitly delete the file if needed
{
function TRecFile.DeleteTestFile: boolean;
begin
  result := sysutils.DeleteFile(FFullName);
end;
}
{$ENDIF TEST}

function TRecFile.GetTotalTrxAmt(aHost: string3 = ''): integer;
var TrxAmtOut : Integer;
    TmpRecord : MDMsgRec;
begin
  result := 0;
  try
    TrxAmtOut:= 0;
    FillChar(TmpRecord, sizeof(MDMsgRec), 0);
    ResetFile;
    while not eof(FRecFile) do
    begin
      Read(FRecFile, TmpRecord);
      if (TmpRecord.ReqCode <> '') then
        if {(aHost = '') or} (Trim(Copy(TmpRecord.HostSuffixCode, 1, 3)) = aHost) then
        begin
          TmpRecord.TrxFinalDisp := rpad(TmpRecord.TrxFinalDisp, 1); { len must be > 0 for next if stmt }
          if ({(aHost <> '') and} (TmpRecord.TrxFinalDisp[1] in [DispOnlineApp, DispFwdApp]))   // actlog
              or
             ({(aHost = '') and} (TmpRecord.TrxFinalDisp[1] in [DispOnlineApp, DispFwdApp, DispOffApp])) then  // offline or tor
          begin
            if TmpRecord.VoidCode = 'V' then { void amount is negative }
              TrxAmtOut:= TrxAmtOut - TmpRecord.TrxAmtN
            else
              TrxAmtOut:= TrxAmtOut + TmpRecord.TrxAmtN;
          end;
        end;
    end;
    Result:= TrxAmtOut;
  except
    on e: exception do
      RecFileLog('****Exception: RecFile.GetTotalTrxAmt - ' + e.Message);
  end;
end;

function TRecFile.IsSigRecord(var HBuf: B_Record): boolean;
const
  MSG_TYPE_LEN = 4;
var
  IsoMsgType: string[MSG_TYPE_LEN];
begin
  SetLength(IsoMsgType,MSG_TYPE_LEN);                   //7934.. setlength first
  move(HBuf.B_Data[1],IsoMsgType[1],MSG_TYPE_LEN);
  result := IsoMsgType = Req0320;
end;

//JTG Dev-5878; just count the signatures in the ERCFile.xxx file
function TRecFile.CountSignaturesInERCFile(aHost: string): integer;
type
  ERCRec = record
    IsoMsg: B_Record;
    ERCMdmsgBuf: MdMsgRec;
  end;
var
  ERCFile: file of ERCRec;
  Filename: string;
  ERCBuf: ERCRec;
begin
  result := 0;
  FileName := DefaultDir + RECEIPT_FILE_NAME + '.'+ aHost;
  if FileExists(Filename) then
    try
      AssignFile(ERCFile,FileName);
      reset(ERCFile);
      try
        while not eof(ERCFile) do
        begin
          read(ERCFile,ERCBuf);
          if IsSigRecord(ERCBuf.IsoMsg) then
            inc(result);
        end;
      finally
        CloseFile(ERCFile);
      end;
    except on e: exception do
      RecFileLog('****ERROR Processing ERC file '+FileName);
    end;
end;

function TRecFile.GetStoreMonitoringTotalsForEngine(aHost: string3; var approvedCount, approvedAmount, declinedCount, offlineCount, offlineAmount, torCount, torAmount, SignatureCount: integer): boolean;
var
  TmpRecord: MDMsgRec;
  Sign: Int64;
begin
  result := false;
  try
    approvedCount := 0;
    approvedAmount := 0;
    declinedCount := 0;
    offlineCount := 0;
    offlineAmount := 0;
    torCount := 0;
    torAmount := 0;
    SignatureCount := CountSignaturesInERCFile(aHost);  //JTG-5876
    Sign := 1;            // overridden if  VoidCode = 'V'...
    FillChar(TmpRecord, sizeof(MDMsgRec), 0);
    ResetFile;
    while not eof(FRecFile) do
    begin
      Read(FRecFile, TmpRecord);
      if TmpRecord.VoidCode = 'V' then { void amount is negative }
        Sign := -1;
      if (TmpRecord.ReqCode <> '') then
        if (Trim(Copy(TmpRecord.HostSuffixCode, 1, 3)) = aHost) then
        begin
          if TmpRecord.TORType <> '' then { TOR : an online or offline trx with the TOR flag set. }
          begin
            Inc(torCount);
            torAmount:= torAmount + (Sign*TmpRecord.TrxAmtN);
          end;
          TmpRecord.TrxFinalDisp := rpad(TmpRecord.TrxFinalDisp, 1); { len must be > 0 for next if stmt }
          if (TmpRecord.TrxFinalDisp[1] in DeclinedDispSet) then { declined }
          begin
            Inc(declinedCount);
          end
          else
          begin
            if (TmpRecord.TrxFinalDisp[1] in [DispOnlineApp, DispFwdApp]) then { online approved }
            begin
              Inc(approvedCount);
              approvedAmount:= approvedAmount + (Sign*TmpRecord.TrxAmtN);
            end
            else
            begin
              if (TmpRecord.TrxFinalDisp[1] in [DispOffApp, DispFwdHeld]) then { offline approved }
              begin
                Inc(offlineCount);
                offlineAmount:= offlineAmount + (Sign*TmpRecord.TrxAmtN);
              end;
            end;
          end;
        end;
    end;
    result := true;
  except
    on e: exception do
      RecFileLog('****Exception: RecFile.GetStoreMonitoringTotalsForEngine - ' + e.Message);
  end;
end;

procedure TRecFile.GetKeyFromTrxFile(var QRecord : MDMsgRec);
var
  found   : Boolean;
  TmpRecord: MDMsgRec;
begin
  try
    found := False;
    FillChar(TmpRecord, SizeOf(TmpRecord),0);
    begin
      ResetFile;
      while (not found) and (not Eof(FRecFile)) do
      begin
        Read(FRecFile, TmpRecord);
//        if (TmpRec.ReqCode = '') then
        begin
          if IsValidTransaction(TmpRecord) then // a record that has encrypted fields
            if (trim(TmpRecord.Key) <> '') then
              TmpRecord.Key := MTXEncryptionUtils.DecryptFileKeyWithVersion(TmpRecord.Key, TmpRecord.KeyVersion);
          if TmpRecord.Key <> '' then
            found := MTXEncryptionUtils.KeyVerificationStringOK(TmpRecord.KeyVerificationString, TmpRecord.Key)
          else
            RecFileLog('TRecFile.GetSecFromTrxFile: Security might be out-of-date.');
        end;
      end;

      if found then
        QRecord := TmpRecord;
    end;
  except
    on e:Exception do
      RecFileLog(Format('EXCEPTION: RecFile.GetKeyFromTrxFile [%s].  %s', [FName, e.message]));
  end;
end;

function TRecFile.PadBlankString(aString: ShortString; dataSize: integer; key: string): string;
var
  i: integer;
  s: string;
begin
  try
    if (trim(aString) = '') then
      result := stringOfChar(' ',high(aString)) // with 'high' there is no need for 'fieldSize'
    else
      begin
      s := copy(aString, 1, dataSize);
      //RecFileLog(Format('!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! PadBlankString before s=%s', [s]));
      result := MTXEncryptionUtils.EncryptFileData(s, key);
      //RecFileLog(Format('!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! PadBlankString after s=%s', [result]));
      if (s <> '') and (result = '') then { encryption failed }
        begin
        for i := 1 to 3 do { try a few times to see if it will work }
          begin
          RecFileLog('****WARNING: RecFile.PadBlankString.MTXEncryptionUtils.EncryptFileData returned a blank string!  Attempting to encrypt again. (' + IntToStr(i) + ')');
          result := MTXEncryptionUtils.EncryptFileData(FName, s);
          if result <> '' then { it worked }
            break;
          end;
        if result = '' then { multiple encryption attempts failed }
          RecFileLog('****ERROR: RecFile.PadBlankString FAILED TO ENCRYPT STRING!!!');
        end;
      end;
  except
    on e : Exception do
      RecFileLog('****Try..Except: RecFile.PadBlankString : ' + e.Message);
  end;
end;

procedure TRecFile.EncryptFields(var QRecord: mdMsgRec; newKEK3: string = '');
{$IFDEF SECURITY_DEBUG}
var
  regKEK3: AnsiString;
{$ENDIF}
  //key: string;
  //tmpStr: string;
begin
//tmpStr := MTXEncryption.EncryptFileData('fn', 'ABCD');
//RecFileLog('tmp Encrypt ********************************************* tmpStr='+tmpStr);
//tmpStr := MTXEncryption.DecryptFileData('fn', tmpStr{, '48B6156A5895EDF2553A14785B3AC6854E7FD026AEB37806CD055A5D42BEE8D7'});
//RecFileLog('tmp DECRYPT********************************************* tmpStr='+tmpStr);
  try
    if IsValidTransaction(QRecord) then
    begin
      if Trim(QRecord.Key) = '' then
      begin
        QRecord.Key := RandomHexString(AES_KEY_LENGTH * 2);
      end;
      //RecFileLog(Format('---------------------EncryptFields key=%s', [key]));
      //RecFileLog('DEBUG-PCI+++++++++++++++++++++++++++++++++++++ EncryptFields QRecord.acctNo='+QRecord.acctNo);
      QRecord.Track2Data := RetrieveMdMsgTrack2(QRecord);
      QRecord.Track2Data := PadBlankString(QRecord.Track2Data, 40, QRecord.Key); { the first size is the max }
      QRecord.AcctNo := MTXEncryptionUtils.RetrieveMdMsgPersonalAccountNum(QRecord);
      QRecord.AcctNo := PadBlankString(QRecord.AcctNo, SIZEOFACCTN0, QRecord.Key);         { data for the field, the }
      //RecFileLog('DEBUG-PCI+++++++++++++++++++++++++++++++++++++ EncryptFields encrypted QRecord.acctNo='+QRecord.acctNo);
//tmpStr := MTXEncryption.DecryptFileData(FName, QRecord.acctNo, Key);
//RecFileLog('DECRYPT********************************************* resuult = tmpStr='+tmpStr);
//RecFileLog('DEBUG JMR: expDate = >'+expDate+'<');
      QRecord.expDate    := PadBlankString(QRecord.expDate, 4, QRecord.Key);        { second size is the max size }
      QRecord.customer_name := PadBlankString(QRecord.customer_name, 40, QRecord.Key);
      QRecord.CVV2       := PadBlankString(QRecord.CVV2, 9, QRecord.Key);              { after encryption }
      QRecord.ZipCode    := PadBlankString(QRecord.ZipCode, 10, QRecord.Key);
      QRecord.PIN        := PadBlankString(QRecord.PIN, 16, QRecord.Key);
      QRecord.DukptKeySerialNumber := PadBlankString(QRecord.DukptKeySerialNumber, 20, QRecord.Key);

{$IFDEF SECURITY_DEBUG}
msgSecurity('EncryptFields QRecord.SeqNo=' + QRecord.SeqNo);
GetKEK3(regKEK3);
msgSecurity('EncryptFields Registry KEK3=' + regKEK3);
msgSecurity(Format('EncryptFields QRecord.Key=%s', [QRecord.Key]));
{$ENDIF}

      QRecord.KeyVersion := KEY_VERSION;
      QRecord.KeyVerificationString := GetEncryptedKeyVerificationString(QRecord.Key);
      QRecord.Key := MTXEncryptionUtils.EncryptFileKey(QRecord.Key, newKek3);
    end;
  except
    on e : Exception do
      RecFileLog('****Try..Except: RecFile.EncryptFields : ' + e.Message);
  end;
end;    { EncryptFields }

function TRecFile.DecryptFields(var QRecord: mdMsgRec; newKEK3: string = ''): boolean;
var
  encryptionError: boolean;
  FileKey: string;
  {$IFDEF SECURITY_DEBUG}
  regKEK3: AnsiString;
  {$ENDIF}
const
  _encryptionErrorMsg = 'ERR';
begin
  result := false;
  try
    encryptionError := false;
    if IsValidTransaction(QRecord) then
    begin
      //RecFileLog(Format('UpdateRecordKEK3_CopyRecord SeqNo=%s, MTX_SeqNo=%s, ReqCode=%s, TrxDate=%s, TrxTime=%s, TrxAmt=%s, KeyVersion=%s, Length(Key)=%d',
      //    [QRecord.SeqNo, QRecord.MTX_SeqNo, QRecord.ReqCode, QRecord.TrxDate, QRecord.TrxTime, QRecord.TrxAmt, QRecord.KeyVersion, Length(QRecord.Key)]));
      if (trim(QRecord.Key) = '') then
      begin
        RecFileLog('ERROR in TRecFile.DecryptFields. Key not found.');
        exit;
      end;

      FileKey := MTXEncryptionUtils.DecryptFileKeyWithVersion(QRecord.Key, QRecord.KeyVersion, newKEK3);
      if FileKey = '' then // fail
      begin
        RecFileLog('TRecFile.DecryptFields: Security might be out-of-date.');
        Exit;
      end
      else
        QRecord.Key := FileKey;

//      if GetKeyFromArray(FName) = '' then  // add to key array if not already there
//        UpdateKeyArray(FName, QRecord.Key);
      //RecFileLog(Format('---------------------DecryptFields fname: %s, key=%s; AcctNo[%s]', [FName, QRecord.Key,QRecord.AcctNo]));
{$IFDEF SECURITY_DEBUG}
msgSecurity('DecryptFields QRecord.SeqNo=' + QRecord.SeqNo);
GetKEK3(regKEK3);
msgSecurity(Format('DecryptFields Registry KEK3 >%s<', [regKEK3]));
msgSecurity(Format('DecryptFields newKEK3       >%s<', [newKEK3]));
msgSecurity(Format('DecryptFields QRecord.Key   >%s<', [QRecord.Key]));
//msgSecurity('DecryptFields QRecord.KeyVerificationString=' + QRecord.KeyVerificationString);
//msgSecurity('DecryptFields MTXEncryption.KeyVerificationStringOK(QRecord.KeyVerificationString, QRecord.Key)=' + YN(MTXEncryption.KeyVerificationStringOK(QRecord.KeyVerificationString, QRecord.Key)))
{$ENDIF}
      encryptionError := not MTXEncryptionUtils.KeyVerificationStringOK(QRecord.KeyVerificationString, QRecord.Key);
      if encryptionError then
        RecFileLog('ERROR in TRecFile.DecryptFields. KeyVerificationStringOK failed.');

      if (trim(QRecord.track2Data) <> '') then
      begin
        if encryptionError then
          QRecord.track2Data := _encryptionErrorMsg
        else
          QRecord.track2Data := MTXEncryptionUtils.DecryptFileData(QRecord.track2Data, QRecord.Key);
        MTXEncryptionUtils.StoreMdMsgTrack2(QRecord, QRecord.track2Data);
      end;
      if (trim(QRecord.acctNo) <> '') then
      begin
        if encryptionError then
          QRecord.acctNo := _encryptionErrorMsg
        else
        QRecord.acctNo := trim(MTXEncryptionUtils.DecryptFileData(QRecord.acctNo, QRecord.Key));
        MTXEncryptionUtils.StoreMdMsgPersonalAccountNum(QRecord, QRecord.acctNo);
      end;
      //RecFileLog('DEBUG-PCI+++++++++++++++++++++++++++++++++++++ DecryptFields QRecord.acctNo='+QRecord.acctNo);
      if (trim(QRecord.expDate) <> '') then
      begin
        if encryptionError then
          QRecord.expDate := _encryptionErrorMsg
        else
          QRecord.expDate := MTXEncryptionUtils.DecryptFileData(QRecord.expDate, QRecord.Key);
      end;
      if (trim(QRecord.Customer_Name) <> '') then
      begin
        if encryptionError then
          QRecord.Customer_Name := _encryptionErrorMsg
        else
          QRecord.Customer_Name := MTXEncryptionUtils.DecryptFileData(QRecord.Customer_Name, QRecord.Key);
      end;
      if (trim(QRecord.CVV2) <> '') then
      begin
        if encryptionError then
          QRecord.CVV2 := _encryptionErrorMsg
        else
          QRecord.CVV2 := MTXEncryptionUtils.DecryptFileData(QRecord.CVV2, QRecord.Key);
      end;
      if (trim(QRecord.ZipCode) <> '') then
      begin
        if encryptionError then
          QRecord.ZipCode := _encryptionErrorMsg
        else
          QRecord.ZipCode := MTXEncryptionUtils.DecryptFileData(QRecord.zipCode, QRecord.Key);
      end;
      if (trim(QRecord.PIN) <> '') then
      begin
        if encryptionError then
          QRecord.PIN := _encryptionErrorMsg
        else
          QRecord.PIN := MTXEncryptionUtils.DecryptFileData(QRecord.PIN, QRecord.Key);
      end;
      if (trim(QRecord.DukptKeySerialNumber) <> '') then
      begin
        if encryptionError then
          QRecord.DukptKeySerialNumber := _encryptionErrorMsg
        else
          QRecord.DukptKeySerialNumber := MTXEncryptionUtils.DecryptFileData(QRecord.DukptKeySerialNumber, QRecord.Key);
      end;
    end;
    result := not encryptionError;
  except
    on e : Exception do
      RecFileLog('****Try..Except: RecFile.DecryptFields : ' + e.Message);
  end;
end;    { DecryptFields }

//Yohan

function TRecFile.ResetTmpFile: boolean; // DEV-18500
var
  WaitTime: Longint;
  _IOResult: integer;
  _ErrCount: integer;
begin
  FileMode := 66;
  WaitTime := 0;
  _ErrCount := 0;
  repeat
    ResetFileEx(TmpFile, _IOResult, _ErrCount, 'ResetTmpFile');
    if _IOResult <> 0 then
    begin
      Delay(RETRY_FILE_LOCK_MS);
      Inc(WaitTime, RETRY_FILE_LOCK_MS);
      ResetFileEx(FRecFile, _IOResult, _ErrCount, 'ResetTmpFile');
    end;
  until (_IOResult = 0) or (WaitTime > FILE_LOCK_WAIT_MS);
  result := _IOResult = 0;
  if _IOResult <> 0 then
  begin
    ShutDownEngineWithLog('TRecFile.ResetTmpFile timed out'); // DEV-15409
  end;
end;

procedure TRecFile.CloseEftFileEx(var RecFile: mdFile; var IOResultEx: integer; var ErrCount: integer; FuncName: string); // DEV-18500
begin
  IOResultEx := 0;
  try
    {I-} CloseFile(RecFile); {$I+}
  except
    on e: Exception do
    begin
      IOResultEx := -1;
      Inc(ErrCount);
      RecFileLog(Format('TRecFile.%s Failed to close "%s" (%d) - %s', [FuncName, FFullName, ErrCount, e.message]));
    end;
  end;
  if IOResultEx = 0 then
    IOResultEx := IOResult;
  if (IOResultEx = 0) and (ErrCount > 0) then
    RecFileLog(Format('TRecFile.%s Close file "%s" successfully after %d tries', [FuncName, FFullName, ErrCount]));
end;

function TRecFile.CloseEftFile(var RecFile: mdFile): boolean; // DEV-18500
var
  WaitTime: Longint;
  _IOResult: integer;
  _ErrCount: integer;
begin
  FileMode := 66;
  WaitTime := 0;
  _ErrCount := 0;
  repeat
    CloseEftFileEx(RecFile, _IOResult, _ErrCount, 'CloseEftFile');
    if _IOResult <> 0 then
    begin
      Delay(RETRY_FILE_LOCK_MS);
      Inc(WaitTime, RETRY_FILE_LOCK_MS);
      CloseEftFileEx(RecFile, _IOResult, _ErrCount, 'CloseEftFile');
    end;
  until (_IOResult = 0) or (WaitTime > FILE_LOCK_WAIT_MS);
  result := _IOResult = 0;
  {
  if _IOResult <> 0 then
    ShutDownEngineWithLog('TRecFile.CloseEftFile timed out'); }
end;

function TRecFile.UpdateRecordKEK3(newKEK3: string=''; OldKEK3: string=''
    {$IFDEF TEST}; FailStep: integer=0; StartStep: integer=1; EndStep: integer=100 {$ENDIF}): integer; // DEV-18500
var
  TmpFileName   : string;
  TmpRec        : MDMsgRec;
  cp            : integer;
  RecFileHash   : string; // for validation
  RecFileSize   : integer; // for validation
  InRollBack    : boolean;

  procedure _Log(Step: integer; Msg: string);
  begin
    RecFileLog(Format('****WARNING: TRecFile.UpdateRecordSec: [STEP %d] %s', [Step, Msg]));
  end;

  function UpdateRecordKEK3_CheckFileSize: integer;
  begin
    //result := GetFileSize;
    result := GetRecordCount;
    {$IFDEF TEST} if NOT InRollBack and (FailStep = cp) then result := -1 else begin {$ENDIF}
    if result = 0 then
      _Log(cp, 'No records in ' + FFullName);
    {$IFDEF TEST} end; {$ENDIF}      
    if result = -1 then
      _Log(cp, 'faile to get file size - ' + FFullName);
  end;

  function UpdateRecordKEK3_GetFileHash: string;
  var
    MemoryStream : TMemoryStream;
    tmpStr: string;
    var rec1: MDMsgRec;
  begin
    result := '';
    {$IFDEF TEST} if NOT InRollBack and (FailStep = cp) then result := '' else begin {$ENDIF}
    MemoryStream := TMemoryStream.Create();
    try
      Reset(FRecFile);
      while NOT EOF(FRecFile) do
      begin
        Read(FRecFile, rec1);
        MemoryStream.Write(rec1, SizeOf(rec1));
      end;
      SetString(tmpStr, PChar(MemoryStream.Memory),MemoryStream.Size);
      result := GetCryptoHash(tmpStr);
    finally
      MemoryStream.Free;
    end;
    {$IFDEF TEST} if NOT InRollBack and (cp = 2) then {$ENDIF}
    RecFileLog(Format('TRecFile.UpdateRecordSec: %s File Hash >%s<', [FFullName, result]));
    {$IFDEF TEST} end; {$ENDIF}
    if result = '' then
      _Log(cp, 'failed to get hash from ' + FFullName);
  end;

  function UpdateRecordKEK3_CloseEftFile(var RecFile: mdFile): boolean;
  begin
    {$IFDEF TEST} if FailStep <> cp then {$ENDIF}
    result := CloseEftFile(RecFile);
    if NOT result then
      _Log(cp, 'failed to close file');
  end;
  
  (* // XE: Remove WinEPS - not in use
  function UpdateRecordKEK3_ClearTmpFileRecord: boolean;
  var
    tmpRecord: MDMsgRec;
    iRec: integer;
    _IOResult, _ErrCount: integer;
  begin
    result := true; // if file doesn't exists, return true
    {$IFDEF TEST} if FailStep = cp then result := false else {$ENDIF}
    if FileExists(TmpFileName) then
    begin
      FillChar(tmpRecord, SizeOf(MDMsgRec),0);
      ResetTmpFile;
      iRec := GetFileSizeEx(TmpFile, _IOResult, _ErrCount, 'UpdateRecordSec_ClearTmpFileRecord'); 
      while (iRec >= 0) do
      begin
        if ReadByRecordNum(iRec, tmpRecord) = RECFILE_SUCCESS then
        begin
          FillChar(tmpRecord, SizeOf(MDMsgRec),0);
          WriteByRecordNum(iRec, tmpRecord);
        end;
        Dec(iRec);
      end;
      result := (iRec = -1);
    end;
    if NOT result then
      _Log(cp, 'failed to clear temporary file records - ' + TmpFileName);
  end;
  *)

  function UpdateRecordKEK3_DeleteTmpFileIfExists: boolean;
  begin
    result := true; // if file doesn't exists, return true
    {$IFDEF TEST} if FailStep = cp then result := false else {$ENDIF}
    if FileExists(TmpFileName) then
      //result := DeleteFile(TmpFileName);
      result := MTX_Utils.SecureDeleteFile(TmpFileName);
    if result
      then SMUserActivity(Format('UpdateRecordSec_DeleteTmpFileIfExists deleted %s', [TmpFileName]))
      else _Log(cp, 'failed to delete temporary file ' + TmpFileName);
  end;

  function UpdateRecordKEK3_RenameOriginToTmpFile: boolean;
  begin
    {$IFDEF TEST} if FailStep = cp then result := false else {$ENDIF}
    result := RenameFile(FFullName, TmpFileName);
    if not result then // if rename won't go, get out
    begin
      _Log(cp, 'could not rename ' + FFullName + ' to ' + TmpFileName);
      //ResetFile; // re-open the old offline file
    end;
  end;

  function UpdateRecordKEK3_MakeEmptyEftFile: boolean;
  begin
    {$IFDEF TEST} if FailStep = cp then result := false else {$ENDIF}
    result := OpenFile;
    if NOT result then
      _Log(cp, 'failed to create new file - ' + FFullName);
  end;

  function UpdateRecordKEK3_OpenTmpFile: boolean;
  begin
    AssignFile(TmpFile, TmpFileName);  // actually the old eft file // DEV-18500 TODO: handle exception
    {$IFDEF TEST} if FailStep = cp then result := false else {$ENDIF}
    result := ResetTmpFile;
    if NOT result then
      _Log(cp, 'failed to open temporary file - ' + TmpFileName);
  end;

  function UpdateRecordKEK3_CopyRecord: boolean;
  var
    TotalCnt, RptBlockCnt, ValidCnt: integer;
    IsValidTrx, IsReportData: boolean;
  begin
{$IFDEF SECURITY_DEBUG}
msgSecurity('UpdateRecordKEK3.UpdateRecordKEK3_CopyRecord -------------------------<');
{$ENDIF}
    result := false;
    TotalCnt := 0;
    RptBlockCnt := 0;
    ValidCnt := 0;
    while NOT EOF(TmpFile) do          // read through old or tmp file
    begin
      Read(TmpFile, tmpRec);           // read thru old or tmp file // DEV-18500 TODO: handle exception
      TotalCnt := TotalCnt + 1;

      IsReportData := IsReportBlock(TmpRec); // DEV-28982
      IsValidTrx := IsValidTransaction(TmpRec); 
      if IsValidTrx then // DEV-28982: decrypt transaction only or decrypt error in DEK
        if NOT decryptFields(tmpRec, OldKEK3) then
        begin
          _Log(cp, 'failed to decrypt fields');
          Exit;
        end;

      {$IFDEF TEST} if FailStep = cp then result := false else {$ENDIF}
      result := WriteRecord(tmpRec, newKek3, IsValidTrx);
      if IsReportData then
        Inc(RptBlockCnt);
      if IsValidTrx then
        Inc(ValidCnt);

      if NOT result then
      begin
        _Log(cp, 'failed to copy record');
        Exit;
      end;
    end;
    result := true; // DEV-28982: if no transaction exists
    RecFileLog(Format('%d transaction(s) copied from %s (ReportBlock=%d, TotalRecord=%d)', [ValidCnt, FFullName, RptBlockCnt, TotalCnt]));
{$IFDEF SECURITY_DEBUG}
msgSecurity('UpdateRecordKEK3.UpdateRecordKEK3_CopyRecord ------------------------->');
{$ENDIF}    
  end;

  function UpdateRecordKEK3_ValidateFileSize: boolean;
  var tmpSize: integer;
  begin
    {$IFDEF TEST} if FailStep = cp then result := false else {$ENDIF}
    //result := GetFileSize = FileSize(TmpFile);
    tmpSize := GetRecordCount;
    result := tmpSize = RecFileSize;
    if NOT result then
      _Log(cp, Format('validate result: file size doesn''t match. (new=%d, old=%d)', [tmpSize, RecFileSize]));
  end;

  function UpdateRecordKEK3_CompareFileData: boolean; // DEV-18500
  var
    rec1, rec2: MDMsgRec;
  begin
    result := false;
    try
      Reset(FRecFile);
      Reset(TmpFile);
      while NOT EOF(FRecFile) do          // read through old or tmp file
      begin
        Read(FRecFile, rec1);           // read thru old or tmp file // DEV-18500 TODO: handle exception
        if NOT IsValidTransaction(rec1) then
          Continue;
        Read(TmpFile, rec2);
        //while (rec2.ReqCode = '') do
        while NOT IsValidTransaction(rec2) do
          Read(TmpFile, rec2); // keep read original until found valid trx

        if rec1.SeqNo <> rec2.SeqNo then
        begin
          _Log(cp, 'failed to find same transaction with SeqNo=' + rec1.SeqNo);
          Exit;        
        end;
                  
        if NOT decryptFields(rec1, newKEK3) then // new file
        begin
          _Log(cp, 'failed to decrypt fields in new file');
          Exit;
        end;

        if NOT decryptFields(rec2, oldKEK3) then // old file
        begin
          _Log(cp, 'failed to decrypt fields in temp file');
          Exit;
        end;

        result := Trim(rec1.AcctNo) = Trim(rec2.AcctNo);
        if result then result := Trim(rec1.Track2Data) = Trim(rec2.Track2Data);
        if result then result := Trim(rec1.expDate) = Trim(rec2.expDate);
        if result then result := Trim(rec1.customer_name) = Trim(rec2.customer_name);
        if result then result := Trim(rec1.CVV2) = Trim(rec2.CVV2);
        if result then result := Trim(rec1.ZipCode) = Trim(rec2.ZipCode);
        if result then result := Trim(rec1.PIN) = Trim(rec2.PIN);
        if result then result := Trim(rec1.DukptKeySerialNumber) = Trim(rec2.DukptKeySerialNumber);
        if NOT result then
        begin
          RecFileLog(Format('****WARNING: TRecFile.CompareFileData - mismatch data seqNo=%s', [rec1.SeqNo]));
{$IFDEF SECURITY_DEBUG}
          msgSecurity(Format('AcctNo: rec1=%s, rec2=%s', [Trim(rec1.AcctNo), Trim(rec2.AcctNo)]));
          msgSecurity(Format('Track2Data: rec1=%s, rec2=%s', [Trim(rec1.Track2Data), Trim(rec2.Track2Data)]));
          msgSecurity(Format('expDate: rec1=%s, rec2=%s', [Trim(rec1.expDate), Trim(rec2.expDate)]));
          msgSecurity(Format('CustomerName: rec1=%s, rec2=%s', [Trim(rec1.customer_name), Trim(rec2.customer_name)]));
          msgSecurity(Format('CVV2: rec1=%s, rec2=%s', [Trim(rec1.CVV2), Trim(rec2.CVV2)]));
          msgSecurity(Format('ZipCode: rec1=%s, rec2=%s', [Trim(rec1.ZipCode), Trim(rec2.ZipCode)]));
          msgSecurity(Format('PIN: rec1=%s, rec2=%s', [Trim(rec1.PIN), Trim(rec2.PIN)]));
{$ENDIF}
          Exit;
        end;
      end;
      result := true; // DEV-28982: if no transaction exists
    except
      on E: Exception do
        RecFileLog('Try..Except: TRecFile.CompareFileData ' + E.Message);
    end;
  end;

  function UpdateRecordKEK3_ValidateRecord: boolean;
  begin
{$IFDEF SECURITY_DEBUG}
msgSecurity('UpdateRecordKEK3.UpdateRecordKEK3_ValidateRecord -------------------------<');
{$ENDIF}
    {$IFDEF TEST} if FailStep = cp then result := false else begin {$ENDIF}
    result := UpdateRecordKEK3_CompareFileData;
    {$IFDEF TEST} end; {$ENDIF}
    if NOT result then
      _Log(cp, 'validate result: data doesn''t match.');
{$IFDEF SECURITY_DEBUG}
msgSecurity('UpdateRecordKEK3.UpdateRecordKEK3_ValidateRecord ------------------------->');
{$ENDIF}
  end;

  function UpdateRecordKEK3_RollBack: boolean;
  var
    tmpFileSize: integer;
    tmpFileHash: string;
  begin
    RecFileLog('****WARNING: TRecFile.RollBack BEGIN cp=' + IntToStr(cp));
    InRollBack := true;
    try
      case cp of // failed at
        1: ; // check file size
        2: ; // get hash
        3: ; // close eft file
        4, // delete tmp file if exists
        5: // rename eft to tmp
          begin
            if NOT ResetFile then RecFileLog('****WARNING: TRecFile.RollBack - failed to ResetFile');
          end;
        6: // make new empty eft file
          begin
            if NOT RenameFile(TmpFileName, FFullName) then RecFileLog('****WARNING: TRecFile.RollBack - failed to RenameFile');
            if NOT ResetFile then RecFileLog('****WARNING: TRecFile.RollBack - failed to ResetFile');
          end;
        7, // open tmp file
        13: // delete tmp file
          begin
            if NOT CloseEftFile(FRecFile) then RecFileLog('****WARNING: TRecFile.RollBack - failed to CloseEftFile(FRecFile)');
            if DeleteFile(FFullName)
              then SMUserActivity(Format('UpdateRecordSec_RollBack step %d deleted %s', [cp, FFullName]))
              else RecFileLog('****WARNING: TRecFile.RollBack - failed to DeleteFile(FFullName)');
            if NOT RenameFile(TmpFileName, FFullName) then RecFileLog('****WARNING: TRecFile.RollBack - failed to RenameFile(TmpFileName, FFullName)');
            if NOT ResetFile then RecFileLog('****WARNING: TRecFile.RollBack - failed to ResetFile');
          end;
        8, // copy record
        9, // validate result: check file size
        10, // validate result: check file data
        //11, // secure delete temp file record
        12: // close tmp file
          begin
            if NOT CloseEftFile(TmpFile) then RecFileLog('****WARNING: TRecFile.RollBack - failed to CloseEftFile(TmpFile)');
            if NOT CloseEftFile(FRecFile) then RecFileLog('****WARNING: TRecFile.RollBack - failed to CloseEftFile(FRecFile)');
            if DeleteFile(FFullName)
              then SMUserActivity(Format('UpdateRecordSec_RollBack step %d deleted %s', [cp, FFullName]))
              else RecFileLog('****WARNING: TRecFile.RollBack - failed to DeleteFile(FFullName)');
            if NOT RenameFile(TmpFileName, FFullName) then RecFileLog('****WARNING: TRecFile.RollBack - failed to RenameFile(TmpFileName, FFullName)');
            if NOT ResetFile then RecFileLog('****WARNING: TRecFile.RollBack - failed to ResetFile');
          end;
      end;
      tmpFileSize := GetRecordCount;
      if RecFileSize <> tmpFileSize then
        RecFileLog(Format('****WARNING: TRecFile.RollBack - FileSize doesn''t match: before >%d< after >%d<', [RecFileSize, tmpFileSize]));
      tmpFileHash := UpdateRecordKEK3_GetFileHash;
      if RecFileHash <> tmpFileHash then
        RecFileLog(Format('****WARNING: TRecFile.RollBack - File hash doesn''t match: before >%s< after >%s<', [RecFileHash, tmpFileHash]));
      result := iif(cp > 1, (RecFileSize = tmpFileSize), true);
      result := iif(cp > 2, result and (RecFileHash = tmpFileHash), result);
      RecFileLog('****WARNING: TRecFile.RollBack END result=' + YN(result));
    finally
      InRollBack := false;
    end;
  end;
begin
  // Assumption: FRecFile is opened.
  result := UPDATE_KEK3_RESULT_FAIL;
  //if NewKEK3 = '' then
  //  OldKEK3 := '';
  try
    try
      cp := 1; // check file size
      {$IFDEF TEST} if (StartStep <= cp) and (cp <= EndStep) then begin {$ENDIF}
      RecFileSize := UpdateRecordKEK3_CheckFileSize;
      if RecFileSize = 0 then
        result := UPDATE_KEK3_RESULT_SUCCESS;
      if RecFileSize <= 0 then // -1: fail to get filesize
        Exit;
      {$IFDEF TEST} end; {$ENDIF}

      cp := 2;
      {$IFDEF TEST} if (StartStep <= cp) and (cp <= EndStep) then begin {$ENDIF}
      RecFileHash := UpdateRecordKEK3_GetFileHash;
      if RecFileHash = '' then // fail
        Exit;
      {$IFDEF TEST} end; {$ENDIF}

      cp := 3; // close current file
      {$IFDEF TEST} if (StartStep <= cp) and (cp <= EndStep) then begin {$ENDIF}
      if NOT UpdateRecordKEK3_CloseEftFile(FRecFile) then
        Exit;
      {$IFDEF TEST} end; {$ENDIF}

      TmpFileName := CreateTmpFileName(FFullName); // get a tmp file name
      cp := 4; // delete tmp file if exists
      {$IFDEF TEST} if (StartStep <= cp) and (cp <= EndStep) then begin {$ENDIF}
      if NOT UpdateRecordKEK3_DeleteTmpFileIfExists then
        Exit;
      {$IFDEF TEST} end; {$ENDIF}

      cp := 5; // rename current file to tmp
      {$IFDEF TEST} if (StartStep <= cp) and (cp <= EndStep) then begin {$ENDIF}
      if NOT UpdateRecordKEK3_RenameOriginToTmpFile then
        Exit;
      {$IFDEF TEST} end; {$ENDIF}

      // rename current to tmp file OK
      cp := 6; // makes a new empty eft file
      {$IFDEF TEST} if (StartStep <= cp) and (cp <= EndStep) then begin {$ENDIF}
      if NOT UpdateRecordKEK3_MakeEmptyEftFile then
        Exit;
      {$IFDEF TEST} end; {$ENDIF}

      cp := 7; // open tmp file
      {$IFDEF TEST} if (StartStep <= cp) and (cp <= EndStep) then begin {$ENDIF}
      if NOT UpdateRecordKEK3_OpenTmpFile then
        Exit;
      {$IFDEF TEST} end; {$ENDIF}

      cp := 8; // copy record
      {$IFDEF TEST} if (StartStep <= cp) and (cp <= EndStep) then begin {$ENDIF}
      if NOT UpdateRecordKEK3_CopyRecord then
        Exit;
      {$IFDEF TEST} end; {$ENDIF}

      cp := 9; // validate result: check file size
      {$IFDEF TEST} if (StartStep <= cp) and (cp <= EndStep) then begin {$ENDIF}
      if NOT UpdateRecordKEK3_ValidateFileSize then
        Exit;
      {$IFDEF TEST} end; {$ENDIF}

      cp := 10; // validate result: compare file data
      {$IFDEF TEST} if (StartStep <= cp) and (cp <= EndStep) then begin {$ENDIF}
      if NOT UpdateRecordKEK3_ValidateRecord then
        Exit;
      {$IFDEF TEST} end; {$ENDIF}

      cp := 11; // secure delete temp file record // file will be deleted securely
      (*
      {$IFDEF TEST} if (StartStep <= cp) and (cp <= EndStep) then begin {$ENDIF}
      if NOT UpdateRecordKEK3_ClearTmpFileRecord then
        Exit;
      {$IFDEF TEST} end; {$ENDIF}*)

      cp := 12; // close tmp file // NON-CRITICAL but will affect to next file, so rollback
      {$IFDEF TEST} if (StartStep <= cp) and (cp <= EndStep) then begin {$ENDIF}
      if NOT UpdateRecordKEK3_CloseEftFile(TmpFile) then // close tmp file
        Exit;
      {$IFDEF TEST} end; {$ENDIF}

      cp := 13; // delete tmp file // NON-CRITICAL but will rollback
      {$IFDEF TEST} if (StartStep <= cp) and (cp <= EndStep) then begin {$ENDIF}
      if NOT UpdateRecordKEK3_DeleteTmpFileIfExists then
        Exit;
      {$IFDEF TEST} end; {$ENDIF}

      //CloseEftFile(FRecFile); // close new file
      ResetFile; // reset new file
      result := UPDATE_KEK3_RESULT_SUCCESS;
    except
      on E: Exception do
        RecFileLog('Try..Except: TRecFile.UpdateRecordSec ' + E.Message);
    end;
  finally
    if result = UPDATE_KEK3_RESULT_FAIL then // has error
    begin
      RecFileLog(Format('TRecFile.UpdateRecordSec: Failed to update security - Step=%d, IOResult=%d', [cp, IOResult]));
      result := cp + 100; // if rollback fails, result will be 1XX
      if NOT UpdateRecordKEK3_Rollback then
        RecFileLog('ERROR!!!!!!!! TRecFile.UpdateRecordSec: Failed to rollback. STOP UPDATING SECURITY PROCESS!!!')
      else
        result := result + 100; // if rollback success, result will be 2XX
    end;
  end;
end;

initialization
  ExtendedLog('RecFile Initialization');
finalization
  ExtendedLog('RecFile Finalization');

end.
