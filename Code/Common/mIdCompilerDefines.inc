{$DEFINE Indy90}
// Delphi 4
{$IFDEF VER120}
  {$DEFINE VCL4ORABOVE}
  {$DEFINE VCL4O}
  {$DEFINE DELPHI}
  {$DEFINE DELPHI4}
  {$DEFINE OVERLOADING}
  {$DEFINE OPTIONALPARAMS}
  {$DEFINE MSWINDOWS}
{$ENDIF}

// C++ Builder 4
{$IFDEF VER125}
  {$DEFINE VCL4ORABOVE}
  {$DEFINE VCL4O}
  {$DEFINE CBUILDER}
  {$DEFINE CBUILDER4}
  {$DEFINE OVERLOADING}
  {$DEFINE OPTIONALPARAMS}
  {$DEFINE MSWINDOWS}
{$ENDIF}

// Delphi 5 & CBuilder 5
{$IFDEF VER130}
  {$DEFINE VCL4ORABOVE}
  {$DEFINE VCL5ORABOVE}
  {$DEFINE VCL5O}
  {$IFDEF BCB}
    {$DEFINE CBUILDER}
    {$DEFINE CBUILDER5}
  {$ELSE}
    {$DEFINE DELPHI}
    {$DEFINE DELPHI5}
  {$ENDIF}
  {$DEFINE OVERLOADING}
  {$DEFINE OPTIONALPARAMS}
  {$DEFINE SAMETEXT}
  {$DEFINE MSWINDOWS}
{$ENDIF}

//Delphi 6
{$IFDEF VER140}
  {$DEFINE VCL4ORABOVE}
  {$DEFINE VCL5ORABOVE}
  {$IFDEF BCB}
    {$DEFINE CBUILDER}
    {$DEFINE CBUILDER6}
  {$ELSE}
    {$DEFINE DELPHI}
    {$DEFINE DELPHI6}
  {$ENDIF}
  {$DEFINE OVERLOADING}
  {$DEFINE OPTIONALPARAMS}
  {$DEFINE SAMETEXT}
  {$DEFINE VCL6ORABOVE}
  {$DEFINE VCL6O}
  {$IFDEF CONDITIONALEXPRESSIONS}
    {$IF RTLVersion >= 14.5}
      {$DEFINE USEZLIBUNIT}
    {$IFEND}
  {$ENDIF}
{$ENDIF}

//Delphi 7
{$IFDEF VER150}
  {$DEFINE VCL4ORABOVE}
  {$DEFINE VCL5ORABOVE}
  {$IFDEF BCB}
    {$DEFINE CBUILDER}
    {$DEFINE CBUILDER7}
  {$ELSE}
    {$DEFINE DELPHI}
    {$DEFINE DELPHI7}
  {$ENDIF}
  {$DEFINE OVERLOADING}
  {$DEFINE OPTIONALPARAMS}
  {$DEFINE SAMETEXT}
  {$DEFINE VCL6ORABOVE}
  {$DEFINE VCL7ORABOVE}
  {$DEFINE VCL7O}
  {$DEFINE USEZLIBUNIT}
{$ENDIF}

//Delphi 8
{$IFDEF CONDITIONALEXPRESSIONS}
{$IF CompilerVersion >= 16.0 }  
  {$DEFINE VCL4ORABOVE}
  {$DEFINE VCL5ORABOVE}
  {$IFDEF BCB}
    {$DEFINE CBUILDER}
    {$DEFINE CBUILDER8}
  {$ELSE}
    {$DEFINE DELPHI}
    {$DEFINE DELPHI8}
  {$ENDIF}
  {$DEFINE OVERLOADING}
  {$DEFINE OPTIONALPARAMS}
  {$DEFINE SAMETEXT}
  {$DEFINE VCL6ORABOVE}
  {$DEFINE VCL7ORABOVE}
  {$DEFINE VCL8ORABOVE}
  {$DEFINE VCL8O}
  {$DEFINE USEZLIBUNIT}
{$IFEND}
{$ENDIF}

{$IFDEF LINUX}
  {$IFDEF CONDITIONALEXPRESSIONS}
    {$IFDEF CompilerVersion}
    //Important:  Don't use CompilerVersion here as
    //$IF's are evaluated before $IFDEF's
    //and Kylix 1 does not have CompilerVersion defined at all.
       {$IF RTLVersion = 14.1}
         {$DEFINE KYLIX2}
         {$DEFINE USEZLIBUNIT}
         {$DEFINE KYLIX1ORABOVE}
         {$DEFINE KYLIX2ORABOVE}
       {$IFEND}
       {$IF RTLVersion = 14.5}
         {$DEFINE KYLIX3}
         {$DEFINE USEZLIBUNIT}
         {$DEFINE KYLIX1ORABOVE}
         {$DEFINE KYLIX2ORABOVE}
       {$IFEND}
    {$ELSE}
       //CompilerVersion is not defined under Kylix 1
       {$DEFINE KYLIX1}
       {$DEFINE KYLIX1ORABOVE}
    {$ENDIF}

  {$ENDIF}
  {$DEFINE VCL4ORABOVE}
  {$DEFINE VCL5ORABOVE}
  {$DEFINE OVERLOADING}
  {$DEFINE OPTIONALPARAMS}
  {$DEFINE SAMETEXT}
  {$DEFINE VCL6ORABOVE}
  {$DEFINE VCL6O}
{$ENDIF}
