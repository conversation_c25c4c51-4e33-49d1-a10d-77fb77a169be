// (c) MTXEPS, Inc. 1988-2008
unit UCommonMDI;
{
v817.0 12-08-05 YHJ-56 Support 800x600 resolution
}

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ExtCtrls, StdCtrls, IniFiles;

type
  TFCommonMDI = class(TForm)
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure FormActivate(Sender: TObject);
    procedure FormKeyDown(Sender: TObject; var Key: Word;
      Shift: TShiftState);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
  private
    procedure GetFormInfo;
    procedure SetFormInfo;
    { block moving form }
    procedure WMNChitTest(var Msg: TWMNCHitTest); message WM_NCHITTEST;
    { block moving / resize form }
    //procedure WMSysCommand(var Msg: TWMSysCommand); message WM_SYSCOMMAND;
  public
    procedure FitToMDIForm;
  end;

var
  FCommonMDI: TFCommonMDI;

implementation

uses
  {$IFDEF GUIJR}
  MenuSE,
  {$ELSE}
  MenuU,
  {$ENDIF}
  USysInfo, MTX_Lib;

{$R *.dfm}
{$IFDEF GUIJR}
var
  SysInfo : TSysInfo;
{$ENDIF}  
  
procedure TFCommonMDI.FormCreate(Sender: TObject);
begin
  {$IFDEF GUIJR}
  if NOT Assigned(SysInfo) then
    SysInfo := TSysInfo.Create;
  SysInfo.OneWindow := True;
  FMenuSE.AddMDIChildList(Self);
  {$ELSE}
  MenuF.AddMDIChildList(Self);
  {$ENDIF}
  if Name <> 'FMsgViewer' then
  begin
    BorderStyle := iif(SysInfo.OneWindow, bsSingle, bsSizeable);
    if SysInfo.OneWindow then
      BorderIcons := [biSystemMenu]
    else
      BorderIcons := [biSystemMenu, biMinimize];
    AutoScroll := True;                                                         { YHJ-56 }
  end;
end;

procedure TFCommonMDI.FormShow(Sender: TObject);
begin
  {//$IFNDEF GUIJR}
//  pnTitle.Align := alTop;
//  bvTitle.Align := alTop;
  GetFormInfo;
  {//$ENDIF}
end;

procedure TFCommonMDI.FormActivate(Sender: TObject);
begin
  {$IFDEF GUIJR}
  FMenuSE.SetActiveMDIChild(Self);
  {$ELSE}
  MenuF.SetActiveMDIChild(Self);
  {$ENDIF}
end;

procedure TFCommonMDI.FormKeyDown(Sender: TObject; var Key: Word;
  Shift: TShiftState);
begin
  {$IFNDEF GUIJR}
  if Shift = [] then
    case Key of
      VK_ESCAPE: if Name <> 'FMsgViewer' then Close;
    end;
  {$ENDIF}
end;

procedure TFCommonMDI.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  if Name = 'FMsgViewer' then
  begin
    Action := caNone;
    Exit;
  end;
  {$IFNDEF GUIJR}
  SetFormInfo;
  MenuF.DeleteMDIChildList(Self);
  {$ELSE}
  FMenuSE.DeleteMDIChildList(Self);
  {$ENDIF}
  Action := caFree;
end;

//------------------------------------------------------------------------------

{ block moving form }

procedure TFCommonMDI.WMNChitTest(var Msg: TWMNCHitTest);
begin
  inherited;
  if Msg.Result = HTCAPTION then
    Msg.Result := iif(Name = 'FMsgViewer', 0, iif(SysInfo.OneWindow, 0, HTCAPTION));
end;

{ block moving / resize form }
{
procedure TFCommonMDI.WMSysCommand(var Msg: TWMSysCommand);
begin
  if ((Msg.CmdType and $FFF0) = SC_MOVE) or ((Msg.CmdType and $FFF0) = SC_SIZE) then
  begin
    Msg.Result := 0;
    Exit;
  end;
  inherited;
end;
}
procedure TFCommonMDI.GetFormInfo;
  function GetFirstInt(S: String): Integer;
  begin
    Result := 0;
    if S = '' then Exit;
    S := Trim(S) + ' ';
    Result := StrToInt(Copy(S, 1, Pos(' ', S) - 1));
  end;
var
  IniF: TIniFile;
  S: String;
begin
  try
    if SysInfo.OneWindow or (Name = 'FMsgViewer') then
      FitToMDIForm
    else
    begin
      IniF := TIniFile.Create(SysInfo.ConfigFile);
      with IniF do
      begin
        S := ReadString('Forms', Name, '');
        if S <> '' then
        begin
          Left := GetFirstInt(S);
          if Left < 0 then Left := 0; 
          S := Copy(S, Pos(' ', S) + 1, Length(S) - Pos(' ', S));
          Top := GetFirstInt(S);
          if Top < 0 then Top := 0;
          
          S := Copy(S, Pos(' ', S) + 1, Length(S) - Pos(' ', S));
          Height := GetFirstInt(S);
          S := Copy(S, Pos(' ', S) + 1, Length(S) - Pos(' ', S));
          Width := GetFirstInt(S);
        end;
      end;
      IniF.Free;
    end;
  except
  ;
  end;
end;

procedure TFCommonMDI.SetFormInfo;
var
  IniF: TIniFile;
begin
  try
    if not SysInfo.OneWindow and (Name <> 'FMsgViewer') then
    begin
      IniF := TIniFile.Create(SysInfo.ConfigFile);
      IniF.WriteString('Forms', Name, IntToStr(Left) + ' ' + IntToStr(Top) + ' '
          + IntToStr(Height) + ' ' +IntToStr(Width));
      IniF.Free;
    end;
  except
  ;
  end;
end;

procedure TFCommonMDI.FitToMDIForm;
begin
  if SysInfo.OneWindow or (Name = 'FMsgViewer') then
  begin
    Left := 0;
    Top := 0;
    {$IFDEF GUIJR}
    Height := FMenuSE.ClientHeight - FMenuSE.CoolBar.Height - FMenuSE.CompanyManagementModePanel.Height -6 -2;
    Width := FMenuSE.ClientWidth - FMenuSE.pnSidebar.Width - FMenuSE.Splitter.Width -6;
    {$ELSE}
    Height := MenuF.ClientHeight - MenuF.CoolBar.Height - 2 - 4;
    Width := MenuF.ClientWidth - MenuF.pnSidebar.Width {- MenuF.Splitter.Width} - 2 - 4;
    {$ENDIF}
    //ShowMessage('Height : ' + IntToStr(Height) + ' / Width : ' + IntToStr(Width));
  end;
end;

end.
