[FileVersion]
Version=6.0
[Compiler]
A=1
B=0
C=1
D=1
E=0
F=0
G=1
H=1
I=1
J=1
K=0
L=1
M=0
N=1
O=1
P=1
Q=0
R=0
S=0
T=0
U=0
V=1
W=0
X=1
Y=1
Z=1
ShowHints=1
ShowWarnings=1
UnitAliases=WinTypes=Windows;WinProcs=Windows;DbiTypes=BDE;DbiProcs=BDE;DbiErrs=BDE
[Linker]
MapFile=3
OutputObjs=0
ConsoleApp=1
DebugInfo=0
RemoteSymbols=0
MinStackSize=16384
MaxStackSize=1048576
ImageBase=1073741824
ExeDescription=
[Directories]
OutputDir=r:\
UnitOutputDir=O:\
PackageDLLOutputDir=
PackageDCPOutputDir=
SearchPath=..\common;Z:\Delphi 6\Component Source\DCP-IBM;Z:\Delphi 6\Component Source\ICS 050605\Delphi\Vc32;Z:\Delphi 6\Component Source\XmlParser (D4-7);Z:\Delphi 6\Component Source\LockBox 2.07 (D3-7)\source;Z:\Delphi 6\Component Source\Abbrevia 3.04\source;Z:\Delphi 6\Component Source\ZipForge 2.65\Source;Z:\Delphi 6\Component Source\Indy 9.0.18;Z:\Delphi 6\Delphi 6 Source\Indy;Z:\Delphi 6\Component Source\madAll;..\WebServer;..\RS;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Sources\dcu;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\Sockets\Client;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\Sockets\Server;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\IndySSL\Server;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\IndySSL\Client;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\ICS\Client;Z:\Delphi 6\Component Source\HVSyncObjs;Z:\Delphi 6\Component Source\IPWorks SSL V9 Delphi Edition\pas;..\Common\MTXEncryption.sbb6_;__Z:\Delphi 6\Delphi 7 Source\Xml;__Z:\Delphi 6\Delphi 7 Source\Internet;__Z:\Delphi 6\Delphi 7 Source\Soap;_Z:\Delphi 6\Component Source\madCollection\madShell\Sources;_Z:\Delphi 6\Component Source\madCollection\madSecurity\Sources;_Z:\Delphi 6\Component Source\madCollection\madExcept\Delphi 6;_Z:\Delphi 6\Component Source\madCollection\madDisAsm\Delphi 6;_Z:\Delphi 6\Component Source\madCollection\madBasic\Sources;_Z:\Delphi 6\Component Source\madCollection\madKernel\Sources
Packages=Vcl40;Vclx40;Vcldb40;Qrpt40;NMFast40;ibevnt40;Vclmid40;Inet40;Inetdb40;TeeUI40;teedb40;tee40;VclSmp40;Dss40
Conditionals=MTXEPSDLL;MSWINDOWS;IPWORKS;__LOGGING;__USE_INDY;__INDYSSL
DebugSourceDirs=
UsePackages=0
[Parameters]
RunParams=
HostApplication=C:\Program Files\MicroTrax\OpenEPS\VT2.exe
Launcher=
UseLauncher=0
DebugCWD=
[Language]
ActiveLang=
ProjectLang=
RootDir=
[Version Info]
IncludeVerInfo=1
AutoIncBuild=1
MajorVer=828
MinorVer=3
Release=0
Build=241
Debug=0
PreRelease=0
Special=0
Private=0
DLL=0
Locale=1033
CodePage=1200
[Version Info Keys]
CompanyName=Retalix, Inc.
FileDescription=OpenEPS EPS DLL
FileVersion=828.3.0.241
InternalName=MTX_EPS
LegalCopyright=Copyright 2014 Retalix, Inc.
LegalTrademarks=OpenEPS
OriginalFilename=MTX_EPS
ProductName=OpenEPS
ProductVersion=1. 0. 0. 0
Built By=
[Excluded Packages]
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvAppFrmD6D.bpl=JVCL Application and Form Components
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvBandsD6D.bpl=JVCL Band Objects
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvBDED6D.bpl=JVCL BDE Components
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvCmpD6D.bpl=JVCL Non-Visual Components
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvCoreD6D.bpl=JVCL Core Components
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvCryptD6D.bpl=JVCL Encryption and Compression
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvCtrlsD6D.bpl=JVCL Visual Controls
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvCustomD6D.bpl=JVCL Custom Controls
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvDBD6D.bpl=JVCL Database Components
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvDlgsD6D.bpl=JVCL Dialog Components
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvDockingD6D.bpl=JVCL Docking Components
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvDotNetCtrlsD6D.bpl=JVCL DotNet Controls
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvEDID6D.bpl=JVCL EDI Components
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvGlobusD6D.bpl=JVCL Globus Components
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvHMID6D.bpl=JVCL HMI Controls
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvInterpreterD6D.bpl=JVCL Interpreter Components
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvJansD6D.bpl=JVCL Jans Components
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvManagedThreadsD6D.bpl=JVCL Managed Threads
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvMMD6D.bpl=JVCL Multimedia and Image Components
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvNetD6D.bpl=JVCL Network Components
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvPageCompsD6D.bpl=JVCL Page Style Components
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvPluginD6D.bpl=JVCL Plugin Components
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvPrintPreviewD6D.bpl=JVCL Print Preview Components
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvRuntimeDesignD6D.bpl=JVCL Runtime Design Components Runtime Package
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvStdCtrlsD6D.bpl=JVCL Standard Controls
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvSystemD6D.bpl=JVCL System Components
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvTimeFrameworkD6D.bpl=JVCL Time Framework
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvUIBD6D.bpl=JVCL Unified Interbase Components
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvValidatorsD6D.bpl=JVCL Validators and Error Provider Components
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvWizardD6D.bpl=JVCL Wizard
C:\Program Files (x86)\Borland\Delphi6\Projects\Bpl\JvXPCtrlsD6D.bpl=JVCL XP Controls
[HistoryLists\hlConditionals]
Count=27
Item0=MTXEPSDLL;MSWINDOWS;IPWORKS;__LOGGING;__USE_INDY;__INDYSSL
Item1=MTXEPSDLL;MSWINDOWS;__LOGGING;__USE_INDY;__INDYSSL
Item2=MTXEPSDLL;MSWINDOWS;__LOGGING;__USE_INDY;__INDYSSL;madExcept;LeakChecking
Item3=tcp;publix;service;HOSTDLL;PUBLIX_10054_FIX
Item4=tcp;publix;service;HOSTDLL;PUBLIX_10054_FIX;PUBLIX_10054_FIX
Item5=tcp;publix;service;HOSTDLL;HOSTDLL
Item6=TCP;flchost
Item7=TCP
Item8=MTXEPSDLL;MSWINDOWS;_LOGGING
Item9=MTXEPSDLL;MSWINDOWS;_LOGGING;P2P
Item10=MTXEPSDLL;MSWINDOWS;_LOGGING;SECURITY_DEBUG;SECURITY_DEBUG
Item11=MTXEPSDLL;MSWINDOWS;LOGGING;SECURITY_DEBUG;SOCKET_DEBUG;SOCKET_DEBUG
Item12=MTXEPSDLL;MSWINDOWS;LOGGING;SECURITY_DEBUG;SECURITY_DEBUG
Item13=MTXEPSDLL;MSWINDOWS;_LOGGING;_LOGGING;SECURITY_DEBUG1;SECURITY_DEBUG1
Item14=MTXEPSDLL;MSWINDOWS;_LOGGING;_LOGGING;SECURITY_DEBUG;SECURITY_DEBUG1
Item15=MTXEPSDLL;MSWINDOWS;_LOGGING;_LOGGING;SECURITY_DEBUG;SECURITY_DEBUG
Item16=MTXEPSDLL;MSWINDOWS;_LOGGING;_LOGGING
Item17=MTXEPSDLL;MSWINDOWS;LOGGING
Item18=MTXEPSDLL;MSWINDOWS
Item19=MTXEPSDLL;MSWINDOWS;SPECIALLOGGING
Item20=MTXEPSDLL;MSWINDOWS;madExcept
Item21=MTXEPSDLL;MSWINDOWS;TOMDEBUG
Item22=MTXEPSDLL;MSWINDOWS;SECURE_BLACKBOX_DEBUG
Item23=MTXEPSDLL
Item24=MSG_ENCRYPTION;NOQT;MTXEPSDLL
Item25=MSG_ENCRYPTION;NOQT;MTXEPSDLL;PCI_LOG_NONCOMPLIANCE
Item26=MSG_ENCRYPTION;NOQT;MTXEPSDLL;PCI_LOG_NONCOMPLIANCE;PCI_LOG_NONCOMPLIANCE
[HistoryLists\hlUnitAliases]
Count=2
Item0=WinTypes=Windows;WinProcs=Windows;DbiTypes=BDE;DbiProcs=BDE;DbiErrs=BDE
Item1=WinTypes=Windows;WinProcs=Windows;DbiTypes=BDE;DbiProcs=BDE;DbiErrs=BDE;
[HistoryLists\hlSearchPath]
Count=29
Item0=..\common;Z:\Delphi 6\Component Source\DCP-IBM;Z:\Delphi 6\Component Source\ICS 050605\Delphi\Vc32;Z:\Delphi 6\Component Source\XmlParser (D4-7);Z:\Delphi 6\Component Source\LockBox 2.07 (D3-7)\source;Z:\Delphi 6\Component Source\Abbrevia 3.04\source;Z:\Delphi 6\Component Source\ZipForge 2.65\Source;Z:\Delphi 6\Component Source\Indy 9.0.18;Z:\Delphi 6\Delphi 6 Source\Indy;Z:\Delphi 6\Component Source\madAll;..\WebServer;..\RS;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Sources\dcu;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\Sockets\Client;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\Sockets\Server;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\IndySSL\Server;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\IndySSL\Client;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\ICS\Client;Z:\Delphi 6\Component Source\HVSyncObjs;Z:\Delphi 6\Component Source\IPWorks SSL V9 Delphi Edition\pas;..\Common\MTXEncryption.sbb6_;__Z:\Delphi 6\Delphi 7 Source\Xml;__Z:\Delphi 6\Delphi 7 Source\Internet;__Z:\Delphi 6\Delphi 7 Source\Soap;_Z:\Delphi 6\Component Source\madCollection\madShell\Sources;_Z:\Delphi 6\Component Source\madCollection\madSecurity\Sources;_Z:\Delphi 6\Component Source\madCollection\madExcept\Delphi 6;_Z:\Delphi 6\Component Source\madCollection\madDisAsm\Delphi 6;_Z:\Delphi 6\Component Source\madCollection\madBasic\Sources;_Z:\Delphi 6\Component Source\madCollection\madKernel\Sources
Item1=..\common;Z:\Delphi 6\Component Source\DCP-IBM;Z:\Delphi 6\Component Source\ICS 050605\Delphi\Vc32;Z:\Delphi 6\Component Source\XmlParser (D4-7);Z:\Delphi 6\Component Source\LockBox 2.07 (D3-7)\source;Z:\Delphi 6\Component Source\Abbrevia 3.04\source;Z:\Delphi 6\Component Source\ZipForge 2.65\Source;Z:\Delphi 6\Component Source\Indy 9.0.18;Z:\Delphi 6\Delphi 6 Source\Indy;Z:\Delphi 6\Component Source\madAll;..\WebServer;..\RS;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Sources\dcu;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\Sockets\Client;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\Sockets\Server;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\IndySSL\Server;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\IndySSL\Client;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\ICS\Client;Z:\Delphi 6\Component Source\HVSyncObjs;..\Common\MTXEncryption.sbb6_;__Z:\Delphi 6\Delphi 7 Source\Xml;__Z:\Delphi 6\Delphi 7 Source\Internet;__Z:\Delphi 6\Delphi 7 Source\Soap;_Z:\Delphi 6\Component Source\madCollection\madShell\Sources;_Z:\Delphi 6\Component Source\madCollection\madSecurity\Sources;_Z:\Delphi 6\Component Source\madCollection\madExcept\Delphi 6;_Z:\Delphi 6\Component Source\madCollection\madDisAsm\Delphi 6;_Z:\Delphi 6\Component Source\madCollection\madBasic\Sources;_Z:\Delphi 6\Component Source\madCollection\madKernel\Sources;Z:\Delphi 6\Component Source\IPWorks SSL V9 Delphi Edition\pas
Item2=..\common;Z:\Delphi 6\Component Source\DCP-IBM;Z:\Delphi 6\Component Source\ICS 050605\Delphi\Vc32;Z:\Delphi 6\Component Source\XmlParser (D4-7);Z:\Delphi 6\Component Source\LockBox 2.07 (D3-7)\source;Z:\Delphi 6\Component Source\Abbrevia 3.04\source;Z:\Delphi 6\Component Source\ZipForge 2.65\Source;Z:\Delphi 6\Component Source\Indy 9.0.18;Z:\Delphi 6\Delphi 6 Source\Indy;Z:\Delphi 6\Component Source\madAll;..\WebServer;..\RS;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Sources\dcu;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\Sockets\Client;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\Sockets\Server;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\IndySSL\Server;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\IndySSL\Client;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\ICS\Client;Z:\Delphi 6\Component Source\HVSyncObjs;..\Common\MTXEncryption.sbb6_;__Z:\Delphi 6\Delphi 7 Source\Xml;__Z:\Delphi 6\Delphi 7 Source\Internet;__Z:\Delphi 6\Delphi 7 Source\Soap;_Z:\Delphi 6\Component Source\madCollection\madShell\Sources;_Z:\Delphi 6\Component Source\madCollection\madSecurity\Sources;_Z:\Delphi 6\Component Source\madCollection\madExcept\Delphi 6;_Z:\Delphi 6\Component Source\madCollection\madDisAsm\Delphi 6;_Z:\Delphi 6\Component Source\madCollection\madBasic\Sources;_Z:\Delphi 6\Component Source\madCollection\madKernel\Sources
Item3=..\common;$(DELPHI)\Lib;Z:\Delphi 6\Component Source\LockBox 2.07 (D3-7)\source;Z:\Delphi 6\Component Source\XmlParser (D4-7);Z:\Delphi 6\Component Source\ZipForge 2.65\Source;Z:\Delphi 6\Component Source\ICS 050605\Delphi\Vc32;Z:\Delphi 6\Component Source\Abbrevia 3.04\source;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Sources\dcu;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\IndySSL\Client;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\IndySSL\Server;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\Sockets\Client;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\Sockets\Server;Z:\Delphi 6\Component Source\Indy 9.0.18;Z:\Delphi 6\Delphi 6 Source\Indy;Z:\Delphi 6\Component Source\DCP-IBM;Z:\Delphi 6\Component Source\madAll
Item4=..\common;$(DELPHI)\Lib;Z:\Delphi 6\Component Source\LockBox 2.07 (D3-7)\source;Z:\Delphi 6\Component Source\XmlParser (D4-7);Z:\Delphi 6\Component Source\ZipForge 2.65\Source;Z:\Delphi 6\Component Source\ICS 050605\Delphi\Vc32;Z:\Delphi 6\Component Source\Abbrevia 3.04\source;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Sources\dcu;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\IndySSL\Client;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\IndySSL\Server;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\Sockets\Client;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\Sockets\Server;Z:\Delphi 6\Component Source\Indy 9.0.18;Z:\Delphi 6\Delphi 6 Source\Indy;Z:\Delphi 6\Component Source\DCP-IBM
Item5=..\common;$(DELPHI)\Lib;C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\XmlParser (D4-7);C:\Program Files\ComponentAce\ZipForge\Source;C:\dev\compo\ICS\Delphi\Vc32;C:\dev\compo\Abbrevia 3.04\source;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Sources\dcu;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\IndySSL\Client;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\IndySSL\Server;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\Sockets\Client;Z:\Delphi 6\Component Source\SecureBlackbox 9.0.203\Classes\Sockets\Server;C:\Program Files\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy;C:\Dev\Compo\DCP-IBM
Item6=Z:\Delphi 6\Component Patch\Publix 10054 (DEV-28893);..\common;$(DELPHI)\Lib;C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\XmlParser (D4-7);C:\Program Files\ComponentAce\ZipForge\Source;C:\dev\compo\ICS\Delphi\Vc32;C:\dev\compo\Abbrevia 3.04\source;C:\Program Files\EldoS\SecureBlackbox9\Sources\DCU;C:\Program Files\EldoS\SecureBlackbox9\Classes\IndySSL\Client;C:\Program Files\EldoS\SecureBlackbox9\Classes\IndySSL\Server;C:\Program Files\EldoS\SecureBlackbox9\Classes\Sockets\Client;C:\Program Files\EldoS\SecureBlackbox9\Classes\Sockets\Server;C:\Program Files\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy;C:\Dev\Compo\DCP-IBM
Item7=..\common;$(DELPHI)\Lib;C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\XmlParser (D4-7);C:\Program Files\ComponentAce\ZipForge\Source;C:\dev\compo\ICS\Delphi\Vc32;C:\dev\compo\Abbrevia 3.04\source;C:\Program Files\EldoS\SecureBlackbox9\Sources\DCU;C:\Program Files\EldoS\SecureBlackbox9\Classes\IndySSL\Client;C:\Program Files\EldoS\SecureBlackbox9\Classes\IndySSL\Server;C:\Program Files\EldoS\SecureBlackbox9\Classes\Sockets\Client;C:\Program Files\EldoS\SecureBlackbox9\Classes\Sockets\Server;C:\Program Files\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy;C:\Dev\Compo\DCP-IBM
Item8=..\common;$(DELPHI)\Lib;C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\XmlParser (D4-7);C:\dev\compo\ICS\Delphi\Vc32;C:\Program Files\ComponentAce\ZipForge\Source;C:\dev\compo\Abbrevia 3.04\source;C:\Program Files\EldoS\SecureBlackbox9\Sources\DCU;C:\Program Files\EldoS\SecureBlackbox9\Classes\IndySSL\Client;C:\Program Files\EldoS\SecureBlackbox9\Classes\IndySSL\Server;C:\Program Files\EldoS\SecureBlackbox9\Classes\Sockets\Client;C:\Program Files\EldoS\SecureBlackbox9\Classes\Sockets\Server;C:\Program Files\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy;C:\Dev\Compo\DCP-IBM;$(ProgramFiles)\EldoS\SecureBlackbox9\Sources\DCU;$(ProgramFiles)\EldoS\SecureBlackbox9\Classes\IndySSL\Client;$(ProgramFiles)\EldoS\SecureBlackbox9\Classes\IndySSL\Server;$(ProgramFiles)\EldoS\SecureBlackbox9\Classes\Sockets\Client;$(ProgramFiles)\EldoS\SecureBlackbox9\Classes\Sockets\Server;$(ProgramFiles)\Borland\Delphi6\Source\Indy9
Item9=..\common;$(DELPHI)\Lib;C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\XmlParser (D4-7);C:\dev\compo\ICS\Delphi\Vc32;C:\Program Files\ComponentAce\ZipForge\Source;C:\dev\compo\Abbrevia 3.04\source;C:\Program Files\EldoS\SecureBlackbox9\Sources\DCU;C:\Program Files\EldoS\SecureBlackbox9\Classes\IndySSL\Client;C:\Program Files\EldoS\SecureBlackbox9\Classes\IndySSL\Server;C:\Program Files\EldoS\SecureBlackbox9\Classes\Sockets\Client;C:\Program Files\EldoS\SecureBlackbox9\Classes\Sockets\Server;C:\Program Files\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy;C:\Dev\Compo\DCP-IBM
Item10=..\common;C:\Dev\Compo\DCP-IBM;C:\dev\compo\XmlParser (D4-7);C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\Abbrevia 3.04\source;C:\Program Files\ComponentAce\ZipForge\Source;C:\Program Files\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy;..\WebServer;..\RS;C:\Dev\Compo\ICS\Delphi\VC32;C:\Program Files\EldoS\SecureBlackbox\Sources\DCU;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Server;C:\Program ..\common;$(DELPHI)\Lib;C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\XmlParser (D4-7);C:\dev\compo\ICS\Delphi\Vc32;C:\Program Files\ComponentAce\ZipForge\Source;C:\dev\compo\Abbrevia 3.04\source;C:\Program Files\EldoS\SecureBlackbox\Sources\DCU;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Server;C:\Program Files\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy;C:\Dev\Compo\DCP-IBM
Item11=..\common;C:\Dev\Compo\DCP-IBM;C:\dev\compo\XmlParser (D4-7);C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\Abbrevia 3.04\source;C:\Program Files\ComponentAce\ZipForge\Source;C:\Program Files\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy;..\WebServer;..\RS;C:\Dev\Compo\ICS\Delphi\VC32;C:\Program Files\EldoS\SecureBlackbox\Sources\DCU;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\ICS\Client;$(ProgramFiles)\ComponentAce\ZipForge\Source;$(ProgramFiles)\Borland\Delphi6\Source\Indy9;$(ProgramFiles)\Borland\Delphi6\Source\Indy;$(ProgramFiles)\EldoS\SecureBlackbox\Sources\DCU;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\Sockets\Client;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\Sockets\Server;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\IndySSL\Client;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\IndySSL\Server;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\ICS\Client
Item12=..\common;C:\Dev\Compo\DCP-IBM;C:\dev\compo\XmlParser (D4-7);C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\Abbrevia 3.04\source;C:\Program Files\ComponentAce\ZipForge\Source;C:\Program Files\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy;..\WebServer;..\RS;C:\Dev\Compo\ICS\Delphi\VC32;C:\Program Files\EldoS\SecureBlackbox\Sources\;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\ICS\Client;$(ProgramFiles)\ComponentAce\ZipForge\Source;$(ProgramFiles)\Borland\Delphi6\Source\Indy9;$(ProgramFiles)\Borland\Delphi6\Source\Indy;$(ProgramFiles)\EldoS\SecureBlackbox\Sources\DCU;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\Sockets\Client;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\Sockets\Server;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\IndySSL\Client;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\IndySSL\Server;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\ICS\Client
Item13=..\common;C:\Dev\Compo\DCP-IBM;C:\dev\compo\XmlParser (D4-7);C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\Abbrevia 3.04\source;C:\Program Files\ComponentAce\ZipForge\Source;$(ProgramFiles)\ComponentAce\ZipForge\Source;C:\Program Files\Borland\Delphi6\Source\Indy9;$(ProgramFiles)\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy;$(ProgramFiles)\Borland\Delphi6\Source\Indy;C:\Dev\Compo\ICS\Delphi\VC32;C:\Program Files\EldoS\SecureBlackbox\Sources\DCU;$(ProgramFiles)\EldoS\SecureBlackbox\Sources\DCU;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Client;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\Sockets\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Server;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\Sockets\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Client;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\IndySSL\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Server;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\IndySSL\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\ICS\Client;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\ICS\Client;..\WebServer;..\RS
Item14=..\common;C:\dev\compo\XmlParser (D4-7);C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\Abbrevia 3.04\source;C:\Program Files\ComponentAce\ZipForge\Source;$(ProgramFiles)\ComponentAce\ZipForge\Source;C:\Program Files\Borland\Delphi6\Source\Indy9;$(ProgramFiles)\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy;$(ProgramFiles)\Borland\Delphi6\Source\Indy;C:\Dev\Compo\ICS\Delphi\VC32;C:\Program Files\EldoS\SecureBlackbox\Sources\DCU;$(ProgramFiles)\EldoS\SecureBlackbox\Sources\DCU;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Client;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\Sockets\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Server;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\Sockets\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Client;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\IndySSL\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Server;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\IndySSL\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\ICS\Client;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\ICS\Client;e:\Dev\Compo\DCP-IBM;C:\Dev\Compo\DCP-IBM;..\WebServer;..\RS
Item15=..\common;C:\dev\compo\XmlParser (D4-7);C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\Abbrevia 3.04\source;C:\Program Files\ComponentAce\ZipForge\Source;$(ProgramFiles)\ComponentAce\ZipForge\Source;C:\Program Files\Borland\Delphi6\Source\Indy9;$(ProgramFiles)\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy;$(ProgramFiles)\Borland\Delphi6\Source\Indy;C:\Dev\Compo\ICS\Delphi\VC32;C:\Program Files\EldoS\SecureBlackbox\Sources\DCU;$(ProgramFiles)\EldoS\SecureBlackbox\Sources\DCU;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Client;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\Sockets\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Server;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\Sockets\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Client;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\IndySSL\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Server;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\IndySSL\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\ICS\Client;$(ProgramFiles)\EldoS\SecureBlackbox\Classes\ICS\Client;C:\Dev\Compo\DCP-IBM;e:\Dev\Compo\DCP-IBM;..\WebServer;..\RS
Item16=..\common;C:\dev\compo\XmlParser (D4-7);C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\Abbrevia 3.04\source;C:\Program Files\ComponentAce\ZipForge\Source;$(ProgramFiles)\ComponentAce\ZipForge\Source;C:\Program Files\Borland\Delphi6\Source\Indy9;$(ProgramFiles)\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy;$(ProgramFiles)\Borland\Delphi6\Source\Indy;C:\Dev\Compo\ICS\Delphi\VC32;C:\Program Files\EldoS\SecureBlackbox\Sources\DCU;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\ICS\Client;C:\Dev\Compo\DCP-IBM;..\WebServer;..\RS
Item17=..\common;C:\dev\compo\XmlParser (D4-7);C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\Abbrevia 3.04\source;C:\Program Files\ComponentAce\ZipForge\Source;C:\Program Files\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy;C:\Dev\Compo\ICS\Delphi\VC32;C:\Program Files\EldoS\SecureBlackbox\Sources\DCU;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\ICS\Client;C:\Dev\Compo\DCP-IBM;..\WebServer;..\RS
Item18=..\common;C:\dev\compo\XmlParser (D4-7);C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\Abbrevia 3.04\source;C:\Program Files\ComponentAce\ZipForge\Source;C:\Program Files\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy;C:\Dev\Compo\ICS\Delphi\VC32;C:\Program Files\EldoS\SecureBlackbox\Sources\DCU;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\ICS\Client;C:\Dev\Compo\DCP-IBM;..\WebServer;..\RSs;..\RS
Item19=..\common;C:\dev\compo\XmlParser (D4-7);C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\Abbrevia 3.04\source;C:\Program Files\ComponentAce\ZipForge\Source;C:\Program Files\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy;C:\Dev\Compo\ICS\Delphi\VC32;C:\Program Files\EldoS\SecureBlackbox\Sources\DCU;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\ICS\Client;C:\Dev\Compo\DCP-IBM;..\WebServer;..\RSs
Item20=..\common;C:\dev\compo\XmlParser (D4-7);C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\Abbrevia 3.04\source;C:\Program Files\ComponentAce\ZipForge\Source;C:\Program Files\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy;C:\Dev\Compo\ICS\Delphi\VC32;C:\Program Files\EldoS\SecureBlackbox\Sources\DCU;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\ICS\Client;C:\Dev\Compo\DCP-IBM;..\WebServer
Item21=..\common;C:\dev\compo\XmlParser (D4-7);C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\Abbrevia 3.04\source;C:\Program Files\ComponentAce\ZipForge\Source;C:\Program Files\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy;C:\Dev\Compo\ICS\Delphi\VC32;C:\Program Files\EldoS\SecureBlackbox\Sources\DCU;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\ICS\Client;C:\Dev\Compo\DCP-IBM
Item22=..\common;C:\dev\compo\XmlParser (D4-7);C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\Abbrevia 3.04\source;C:\Program Files\ComponentAce\ZipForge\Source;C:\Program Files\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy;C:\Dev\Compo\ICS\Delphi\VC32;C:\Program Files\EldoS\SecureBlackbox\Sources\DCU;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\ICS\Client
Item23=..\common;C:\dev\compo\XmlParser (D4-7);C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\Abbrevia 3.04\source;C:\Program Files\ComponentAce\ZipForge\Source;C:\Program Files\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy;C:\Dev\Compo\ICS\Delphi\VC32;C:\Program Files\EldoS\SecureBlackbox\Sources;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\ICS\Client
Item24=..\common;C:\dev\compo\XmlParser (D4-7);C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\Abbrevia 3.04\source;C:\Program Files\ComponentAce\ZipForge\Source;C:\Program Files\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy;C:\Dev\Compo\ICS\Delphi\VC32;C:\Program Files\EldoS\SecureBlackbox\Sources;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Server;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Client;C:\Program Files\EldoS\SecureBlackbox\Classes\IndySSL\Server
Item25=..\common;C:\dev\compo\XmlParser (D4-7);C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\Abbrevia 3.04\source;C:\Program Files\ComponentAce\ZipForge\Source;C:\Program Files\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy;C:\Dev\Compo\ICS\Delphi\VC32;C:\Program Files\EldoS\SecureBlackbox\Sources;C:\Program Files\EldoS\SecureBlackbox\Classes\Sockets\Client
Item26=..\common;C:\dev\compo\XmlParser (D4-7);C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\Abbrevia 3.04\source;C:\Program Files\ComponentAce\ZipForge\Source;C:\Program Files\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy;C:\Dev\Compo\ICS\Delphi\VC32;C:\Program Files\EldoS\SecureBlackbox\Sources
Item27=..\common;C:\dev\compo\XmlParser (D4-7);C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\Abbrevia 3.04\source;C:\Program Files\ComponentAce\ZipForge\Source;C:\Program Files\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy;C:\Dev\Compo\ICS\Delphi\VC32
Item28=..\common;C:\dev\compo\XmlParser (D4-7);C:\dev\compo\LockBox 2.07 (D3-7)\source;C:\dev\compo\Abbrevia 3.04\source;C:\Program Files\ComponentAce\ZipForge\Source;C:\Program Files\Borland\Delphi6\Source\Indy9;C:\Program Files\Borland\Delphi6\Source\Indy
[HistoryLists\hlUnitOutputDirectory]
Count=1
Item0=O:\
[HistoryLists\hlOutputDirectorry]
Count=5
Item0=R:\
Item1=C:\Program Files\Microtrax\EPS
Item2=r:
Item3=C:\Program Files\Microtrax\OpenEPS
Item4=r;\
