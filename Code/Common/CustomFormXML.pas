
{*********************************************************************}
{                                                                     }
{                       Delphi XML Data Binding                       }
{                                                                     }
{         Generated on: 7/14/2014 5:27:28 PM                          }
{       Generated from: C:\dev\projects\828.3\Common\CustomForm.xml   }
{   Settings stored in: C:\dev\projects\828.3\Common\CustomForm.xdb   }
{                                                                     }
{*********************************************************************}
unit CustomFormXML;

interface

uses xmldom, XMLDoc, XMLIntf;

type

{ Forward Decls }

  IXMLFormType = interface;
  IXMLButtonsType = interface;
  IXMLButtonType = interface;
  IXMLLabelsType = interface;
  IXMLLabelType = interface;
  IXMLEditBoxesType = interface;
  IXMLEditBoxType = interface;
  IXMLListPromptsType = interface;
  IXMLListPromptType = interface;
  IXMLSignatureBoxType = interface;
  IXMLCardReaderType = interface;
  IXMLEncryptedDataRequestType = interface;
  IXMLListBoxesType = interface;
  IXMLListBoxType = interface;
  IXMLLineType = interface;
  IXMLPINEntryRequestType = interface;
  IXMLTimeoutType = interface;
  IXMLDisplayLineType = interface;
  IXMLServerNumType = interface;
  IXMLTipAmountType = interface;

{ IXMLFormType }

  IXMLFormType = interface(IXMLNode)
    ['{7245FA5F-065C-4E6E-8742-3A75A0AF9BF5}']
    { Property Accessors }
    function Get_Name: WideString;
    function Get_DisplayOnly: WideString;                               // CPCLIENTS-11878
    function Get_Buttons: IXMLButtonsType;
    function Get_Labels: IXMLLabelsType;
    function Get_EditBoxes: IXMLEditBoxesType;
    function Get_SignatureBox: IXMLSignatureBoxType;
    function Get_CardReader: IXMLCardReaderType;
    function Get_EncryptedDataRequest: IXMLEncryptedDataRequestType;
    function Get_ListBoxes: IXMLListBoxesType;
    function Get_ListPrompts: IXMLListPromptsType;
    function Get_Timeout: IXMLTimeoutType;
    function Get_DisplayLine: IXMLDisplayLineType;
    function Get_PINEntryRequest: IXMLPINEntryRequestType;
    function Get_ServerNum: IXMLServerNumType;
    function Get_TipAmount: IXMLTipAmountType;
    procedure Set_Name(Value: WideString);
    function HasNode(Value: WideString): boolean;
    { Methods & Properties }
    property Name: WideString read Get_Name write Set_Name;
    property DisplayOnly: WideString read Get_DisplayOnly;
    property Buttons: IXMLButtonsType read Get_Buttons;
    property Labels: IXMLLabelsType read Get_Labels;
    property EditBoxes: IXMLEditBoxesType read Get_EditBoxes;
    property SignatureBox: IXMLSignatureBoxType read Get_SignatureBox;
    property CardReader: IXMLCardReaderType read Get_CardReader;
    property EncryptedDataRequest: IXMLEncryptedDataRequestType read Get_EncryptedDataRequest;
    property ListBoxes: IXMLListBoxesType read Get_ListBoxes;
    property PINEntryRequest: IXMLPINEntryRequestType read Get_PINEntryRequest;
    property ListPrompts: IXMLListPromptsType read Get_ListPrompts;
    property Timeout: IXMLTimeoutType read Get_Timeout;
    property DisplayLine: IXMLDisplayLineType read Get_DisplayLine;
    property ServerNum: IXMLServerNumType read Get_ServerNum;
    property TipAmount: IXMLTipAmountType read Get_TipAmount;
  end;

{ IXMLButtonsType }

  IXMLButtonsType = interface(IXMLNodeCollection)
    ['{0685EDDC-B81C-407C-8665-ADB96D605AD7}']
    { Property Accessors }
    function Get_Button(Index: Integer): IXMLButtonType;
    { Methods & Properties }
    function Add: IXMLButtonType;
    function Insert(const Index: Integer): IXMLButtonType;
    property Button[Index: Integer]: IXMLButtonType read Get_Button; default;
  end;

{ IXMLButtonType }

  IXMLButtonType = interface(IXMLNode)
    ['{6935CB8C-932B-44E5-8A95-1723AEC3E127}']
    { Property Accessors }
    function Get_Number: WideString;
    function Get_Caption: WideString;
    function Get_ID: WideString;
    procedure Set_Number(Value: WideString);
    procedure Set_Caption(Value: WideString);
    procedure Set_ID(Value: WideString);
    { Methods & Properties }
    property Number: WideString read Get_Number write Set_Number;
    property Caption: WideString read Get_Caption write Set_Caption;
    property ID: WideString read Get_ID write Set_ID;
  end;

{ IXMLLabelsType }

  IXMLLabelsType = interface(IXMLNodeCollection)
    ['{B15E4D73-C828-42A5-A79F-9B3A4B69AF5A}']
    { Property Accessors }
    function Get_Label_(Index: Integer): IXMLLabelType;
    { Methods & Properties }
    function Add: IXMLLabelType;
    function Insert(const Index: Integer): IXMLLabelType;
    property Label_[Index: Integer]: IXMLLabelType read Get_Label_; default;
  end;

{ IXMLLabelType }

  IXMLLabelType = interface(IXMLNode)
    ['{5E83A068-5E85-4EB0-93BB-DFBE67D440E3}']
    { Property Accessors }
    function Get_Number: WideString;
    function Get_Caption: WideString;
    function Get_ID: WideString;
    procedure Set_Number(Value: WideString);
    procedure Set_Caption(Value: WideString);
    procedure Set_ID(Value: WideString);
    { Methods & Properties }
    property Number: WideString read Get_Number write Set_Number;
    property Caption: WideString read Get_Caption write Set_Caption;
    property ID: WideString read Get_ID write Set_ID;
  end;

{ IXMLEditBoxesType }

  IXMLEditBoxesType = interface(IXMLNodeCollection)
    ['{6F9BE789-58B9-48F7-B1EB-48AA548F2B86}']
    { Property Accessors }
    function Get_EditBox(Index: Integer): IXMLEditBoxType;
    { Methods & Properties }
    function Add: IXMLEditBoxType;
    function Insert(const Index: Integer): IXMLEditBoxType;
    property EditBox[Index: Integer]: IXMLEditBoxType read Get_EditBox; default;
  end;

{ IXMLEditBoxType }

  IXMLEditBoxType = interface(IXMLNode)
    ['{18E5F3EA-9BE3-402A-874B-87837EC1E42B}']
    { Property Accessors }
    function Get_Number: WideString;
    function Get_ValType: WideString;
    function Get_DisplayString: WideString;
    function Get_FormatString: WideString;
    function Get_Min: WideString;
    function Get_Max: WideString;
    function Get_ID: WideString;
    function Get_Value: WideString;
    procedure Set_Number(Value: WideString);
    procedure Set_ValType(Value: WideString);
    procedure Set_DisplayString(Value: WideString);
    procedure Set_FormatString(Value: WideString);
    procedure Set_Min(Value: WideString);
    procedure Set_Max(Value: WideString);
    procedure Set_ID(Value: WideString);
    procedure Set_Value(Value: WideString);
    { Methods & Properties }
    property Number: WideString read Get_Number write Set_Number;
    property ValType: WideString read Get_ValType write Set_ValType;
    property DisplayString: WideString read Get_DisplayString write Set_DisplayString;
    property FormatString: WideString read Get_FormatString write Set_FormatString;
    property Min: WideString read Get_Min write Set_Min;
    property Max: WideString read Get_Max write Set_Max;
    property ID: WideString read Get_ID write Set_ID;
    property Value: WideString read Get_Value write Set_Value;
  end;

{ IXMLSignatureBoxType }

  IXMLSignatureBoxType = interface(IXMLNode)
    ['{6426EFD5-55F5-4035-AF5B-F924CDA1D1B2}']
    { Property Accessors }
    function Get_CaptureSignature: WideString;
    function Get_Coordinates: WideString;
    function Get_SignatureData: WideString;
    function Get_ID: WideString;
    procedure Set_CaptureSignature(Value: WideString);
    procedure Set_Coordinates(Value: WideString);
    procedure Set_SignatureData(Value: WideString);
    procedure Set_ID(Value: WideString);
    { Methods & Properties }
    property CaptureSignature: WideString read Get_CaptureSignature write Set_CaptureSignature;
    property Coordinates: WideString read Get_Coordinates write Set_Coordinates;
    property SignatureData: WideString read Get_SignatureData write Set_SignatureData;
    property ID: WideString read Get_ID write Set_ID;
  end;

{ IXMLCardReaderType }

  IXMLCardReaderType = interface(IXMLNode)
    ['{187AA435-A280-47D0-ADA4-8DAF70175A4E}']
    { Property Accessors }
    function Get_EnableCardReader: WideString;
    function Get_EnableChipReader: WideString;
    function Get_EnableRFID: WideString;
    function Get_PersonalAccountNumberMasked: WideString;
    function Get_TokenData: WideString;
    function Get_PanFirst6: WideString;
    function Get_PanLast4: WideString;
    function Get_ExpirationDate: WideString;
    function Get_CardHolderName: WideString;
    function Get_Track1Data: WideString;
    function Get_Track2Data: WideString;
    function Get_WinEPSCardType: WideString;
    function Get_TenderType: WideString;
    function Get_DriverLicense: WideString;
    procedure Set_EnableCardReader(Value: WideString);
    procedure Set_EnableChipReader(Value: WideString);
    procedure Set_EnableRFID(Value: WideString);
    procedure Set_PersonalAccountNumberMasked(Value: WideString);
    procedure Set_TokenData(Value: WideString);
    procedure Set_PanFirst6(Value: WideString);
    procedure Set_PanLast4(Value: WideString);
    procedure Set_ExpirationDate(Value: WideString);
    procedure Set_CardHolderName(Value: WideString);
    procedure Set_Track1Data(Value: WideString);
    procedure Set_Track2Data(Value: WideString);
    procedure Set_WinEPSCardType(Value: WideString);
    procedure Set_TenderType(Value: WideString);
    procedure Set_DriverLicense(Value: WideString);
    { Methods & Properties }
    property EnableCardReader: WideString read Get_EnableCardReader write Set_EnableCardReader;
    property EnableChipReader: WideString read Get_EnableChipReader write Set_EnableChipReader;
    property EnableRFID: WideString read Get_EnableRFID write Set_EnableRFID;
    property PersonalAccountNumberMasked: WideString read Get_PersonalAccountNumberMasked write Set_PersonalAccountNumberMasked;
    property TokenData: WideString read Get_TokenData write Set_TokenData;
    property PanFirst6: WideString read Get_PanFirst6 write Set_PanFirst6;
    property PanLast4: WideString read Get_PanLast4 write Set_PanLast4;
    property ExpirationDate: WideString read Get_ExpirationDate write Set_ExpirationDate;
    property CardHolderName: WideString read Get_CardHolderName write Set_CardHolderName;
    property Track1Data: WideString read Get_Track1Data write Set_Track1Data;
    property Track2Data: WideString read Get_Track2Data write Set_Track2Data;
    property WinEPSCardType: WideString read Get_WinEPSCardType write Set_WinEPSCardType;
    property TenderType: WideString read Get_TenderType write Set_TenderType;
    property DriverLicense: WideString read Get_DriverLicense write Set_DriverLicense;
  end;



{ IXMLEncryptedDataRequestType }

  IXMLEncryptedDataRequestType = interface(IXMLNode)
    ['{CE5CD71D-14CC-4377-A885-309F1B4F591E}']
    { Property Accessors }
    function Get_EntryText: WideString;
    function Get_ReEntryText: WideString;
    function Get_DisplayString: WideString;
    function Get_FormatString: WideString;
    function Get_Min: WideString;
    function Get_Max: WideString;
    function Get_DataType: WideString;
    function Get_DataOptions: WideString;
    function Get_TokenData: WideString;
    procedure Set_EntryText(Value: WideString);
    procedure Set_ReEntryText(Value: WideString);
    procedure Set_DisplayString(Value: WideString);
    procedure Set_FormatString(Value: WideString);
    procedure Set_Min(Value: WideString);
    procedure Set_Max(Value: WideString);
    procedure Set_DataType(Value: WideString);
    procedure Set_DataOptions(Value: WideString);
    procedure Set_TokenData(Value: WideString);
    { Methods & Properties }
    property EntryText: WideString read Get_EntryText write Set_EntryText;
    property ReEntryText: WideString read Get_ReEntryText write Set_ReEntryText;
    property DisplayString: WideString read Get_DisplayString write Set_DisplayString;
    property FormatString: WideString read Get_FormatString write Set_FormatString;
    property Min: WideString read Get_Min write Set_Min;
    property Max: WideString read Get_Max write Set_Max;
    property DataType: WideString read Get_DataType write Set_DataType;
    property DataOptions: WideString read Get_DataOptions write Set_DataOptions;
    property TokenData: WideString read Get_TokenData write Set_TokenData;
  end;

  { IXMLListBoxesType }

  IXMLListBoxesType = interface(IXMLNodeCollection)
    ['{E75A5BCC-2C24-490A-B7CC-81D672A2606B}']
    { Property Accessors }
    function Get_ListBox(Index: Integer): IXMLListBoxType;
    { Methods & Properties }
    function Add: IXMLListBoxType;
    function Insert(const Index: Integer): IXMLListBoxType;
    property ListBox[Index: Integer]: IXMLListBoxType read Get_ListBox; default;
  end;

  { IXMLListBoxType }

  IXMLListBoxType = interface(IXMLNodeCollection)
    ['{ED405924-2505-4D7B-A1CA-6CA9984FE20B}']
    { Property Accessors }
    function Get_Number: WideString;
    function Get_ID: WideString;
    function Get_Line(Index: Integer): IXMLLineType;
    procedure Set_Number(Value: WideString);
    procedure Set_ID(Value: WideString);
    { Methods & Properties }
    function Add: IXMLLineType;
    function Insert(const Index: Integer): IXMLLineType;
    property Number: WideString read Get_Number write Set_Number;
    property ID: WideString read Get_ID write Set_ID;
    property Line[Index: Integer]: IXMLLineType read Get_Line; default;
  end;

  { IXMLListPromptsType }

  IXMLListPromptsType = interface(IXMLNodeCollection)
    ['{A75A3BCC-2C24-450A-B7CC-86D672A2606B}']
    { Property Accessors }
    function Get_ListPrompt(Index: Integer): IXMLListPromptType;
    { Methods & Properties }
    function Add: IXMLListPromptType;
    function Insert(const Index: Integer): IXMLListPromptType;
    property ListPrompt[Index: Integer]: IXMLListPromptType read Get_ListPrompt; default;
  end;

  { IXMLListBoxType }

  IXMLListPromptType = interface(IXMLNodeCollection)
    ['{DD245924-2505-4D7B-B1CA-6CA9984FE20B}']
    { Property Accessors }
    function Get_Caption: WideString;
    procedure Set_Caption(Value: WideString);
    function Get_PromptForAmount: WideString;
    procedure Set_PromptForAmount(Value: WideString);
    function Get_DefaultAmount: WideString;
    procedure Set_DefaultAmount(Value: WideString);
    function Get_TipAmountFormat: WideString;
    procedure Set_TipAmountFormat(Value: WideString);
    { Methods & Properties }
    property Caption: WideString read Get_Caption write Set_Caption;
    property PromptForAmount: WideString read Get_PromptForAmount write Set_PromptForAmount;
    property DefaultAmount: WideString read Get_DefaultAmount write Set_DefaultAmount;
    property AmountFormat: WideString read Get_TipAmountFormat write Set_TipAmountFormat;
  end;

{ IXMLLineType }

  IXMLLineType = interface(IXMLNode)
    ['{90EB85AB-4DAA-4D2A-8709-E8B88499AB74}']
    { Property Accessors }
    function Get_Number: WideString;
    function Get_Text: WideString;
    procedure Set_Number(Value: WideString);
    procedure Set_Text(Value: WideString);
    { Methods & Properties }
    property Number: WideString read Get_Number write Set_Number;
    property Text: WideString read Get_Text write Set_Text;
  end;

{ IXMLPINEntryRequestType }

  IXMLPINEntryRequestType = interface(IXMLNode)
    ['{90EB85AB-4DAA-4D2A-8709-E8B88499AB75}']
    { Property Accessors }
    function Get_PINEntry: WideString;
    function Get_Text: WideString;
    function Get_RandomNumber: WideString;
    function Get_PINBlock: WideString;
    function Get_MinPIN: WideString;
    function Get_MaxPIN: WideString;
    procedure Set_PINEntry(Value: WideString);
    procedure Set_Text(Value: WideString);
    procedure Set_RandomNumber(Value: WideString);
    procedure Set_PINBlock(Value: WideString);
    procedure Set_MinPIN(Value: WideString);
    procedure Set_MaxPIN(Value: WideString);
    { Methods & Properties }
    property PINEntry: WideString read Get_PINEntry write Set_PINEntry;
    property Text: WideString read Get_Text write Set_Text;
    property RandomNumber: WideString read Get_RandomNumber write Set_RandomNumber;
    property PINBlock: WideString read Get_PINBlock write Set_PINBlock;
    property MinPIN: WideString read Get_MinPIN write Set_MinPIN;
    property MaxPIN: WideString read Get_MaxPIN write Set_MaxPIN;
  end;

  { IXMLTimeoutType }

  IXMLTimeoutType = interface(IXMLNode)
    ['{90AB85AB-4DBB-4D2A-8709-E5B88499AB75}']
    { Property Accessors }
    function Get_Timeout: WideString;
    procedure Set_Timeout(Value: WideString);
    { Methods & Properties }
    property Value: WideString read Get_Timeout write Set_Timeout;
  end;

  { IXMLDisplayLineType }

  IXMLDisplayLineType = interface(IXMLNode)
    ['{80AC85AA-4DAB-4D2A-8709-E5B88499AB75}']
    { Property Accessors }
    function Get_DisplayLine: WideString;
    procedure Set_DisplayLine(Value: WideString);
    { Methods & Properties }
    property Value: WideString read Get_DisplayLine write Set_DisplayLine;
  end;

  { IXMLServerNumType }

  IXMLServerNumType = interface(IXMLNode)
    ['{D49F85EB-1D3E-47EE-9702-EEB6B32E3698}']
    { Property Accessors }
    function Get_ServerNum: WideString;
    procedure Set_ServerNum(Value: WideString);
    { Methods & Properties }
    property Value: WideString read Get_ServerNum write Set_ServerNum;
  end;

  { IXMLTipAmountType }

  IXMLTipAmountType = interface(IXMLNode)
    ['{926015AC-50AB-4A78-B673-307D51B55E9D}']
    { Property Accessors }
    function Get_TipAmount: WideString;
    procedure Set_TipAmount(Value: WideString);
    { Methods & Properties }
    property Value: WideString read Get_TipAmount write Set_TipAmount;
  end;



{ Forward Decls }

  TXMLFormType = class;
  TXMLButtonsType = class;
  TXMLButtonType = class;
  TXMLLabelsType = class;
  TXMLLabelType = class;
  TXMLEditBoxesType = class;
  TXMLEditBoxType = class;
  TXMLSignatureBoxType = class;
  TXMLCardReaderType = class;
  TXMLEncryptedDataRequestType = class;
  TXMLListBoxesType = class;
  TXMLListBoxType = class;
  TXMLLineType = class;
  TXMLPINEntryRequestType = class;
  TXMLTimeoutType = class;
  TXMLDisplayLineType = class;
  TXMLServerNumType = class;
  TXMLTipAmountType = class;

{ TXMLFormType }

  TXMLFormType = class(TXMLNode, IXMLFormType)
  protected
    { IXMLFormType }
    function Get_Name: WideString;
    function Get_DisplayOnly: WideString;                        // CPCLIENTS-11878
    function Get_Buttons: IXMLButtonsType;
    function Get_ListPrompts: IXMLListPromptsType;
    function Get_Labels: IXMLLabelsType;
    function Get_EditBoxes: IXMLEditBoxesType;
    function Get_SignatureBox: IXMLSignatureBoxType;
    function Get_CardReader: IXMLCardReaderType;
    function Get_EncryptedDataRequest: IXMLEncryptedDataRequestType;
    function Get_ListBoxes: IXMLListBoxesType;
    function Get_PINEntryRequest: IXMLPINEntryRequestType;
    function Get_Timeout: IXMLTimeoutType;
    function Get_DisplayLine: IXMLDisplayLineType;
    function Get_ServerNum: IXMLServerNumType;
    function Get_TipAmount: IXMLTipAmountType;
    procedure Set_Name(Value: WideString);
    function HasNode(Value: WideString): boolean;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLButtonsType }

  TXMLButtonsType = class(TXMLNodeCollection, IXMLButtonsType)
  protected
    { IXMLButtonsType }
    function Get_Button(Index: Integer): IXMLButtonType;
    function Add: IXMLButtonType;
    function Insert(const Index: Integer): IXMLButtonType;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLButtonType }

  TXMLButtonType = class(TXMLNode, IXMLButtonType)
  protected
    { IXMLButtonType }
    function Get_Number: WideString;
    function Get_Caption: WideString;
    function Get_ID: WideString;
    procedure Set_Number(Value: WideString);
    procedure Set_Caption(Value: WideString);
    procedure Set_ID(Value: WideString);
  end;

{ TXMLLabelsType }

  TXMLLabelsType = class(TXMLNodeCollection, IXMLLabelsType)
  protected
    { IXMLLabelsType }
    function Get_Label_(Index: Integer): IXMLLabelType;
    function Add: IXMLLabelType;
    function Insert(const Index: Integer): IXMLLabelType;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLLabelType }

  TXMLLabelType = class(TXMLNode, IXMLLabelType)
  protected
    { IXMLLabelType }
    function Get_Number: WideString;
    function Get_Caption: WideString;
    function Get_ID: WideString;
    procedure Set_Number(Value: WideString);
    procedure Set_Caption(Value: WideString);
    procedure Set_ID(Value: WideString);
  end;

{ TXMLEditBoxesType }

  TXMLEditBoxesType = class(TXMLNodeCollection, IXMLEditBoxesType)
  protected
    { IXMLEditBoxesType }
    function Get_EditBox(Index: Integer): IXMLEditBoxType;
    function Add: IXMLEditBoxType;
    function Insert(const Index: Integer): IXMLEditBoxType;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLEditBoxType }

  TXMLEditBoxType = class(TXMLNode, IXMLEditBoxType)
  protected
    { IXMLEditBoxType }
    function Get_Number: WideString;
    function Get_ValType: WideString;
    function Get_DisplayString: WideString;
    function Get_FormatString: WideString;
    function Get_Min: WideString;
    function Get_Max: WideString;
    function Get_ID: WideString;
    function Get_Value: WideString;
    procedure Set_Number(Value: WideString);
    procedure Set_ValType(Value: WideString);
    procedure Set_DisplayString(Value: WideString);
    procedure Set_FormatString(Value: WideString);
    procedure Set_Min(Value: WideString);
    procedure Set_Max(Value: WideString);
    procedure Set_ID(Value: WideString);
    procedure Set_Value(Value: WideString);
  end;

{ TXMLSignatureBoxType }

  TXMLSignatureBoxType = class(TXMLNode, IXMLSignatureBoxType)
  protected
    { IXMLSignatureBoxType }
    function Get_CaptureSignature: WideString;
    function Get_Coordinates: WideString;
    function Get_SignatureData: WideString;
    function Get_ID: WideString;
    procedure Set_CaptureSignature(Value: WideString);
    procedure Set_Coordinates(Value: WideString);
    procedure Set_SignatureData(Value: WideString);
    procedure Set_ID(Value: WideString);
  end;

{ TXMLCardReaderType }

  TXMLCardReaderType = class(TXMLNode, IXMLCardReaderType)
  protected
    { IXMLCardReaderType }
    function Get_EnableCardReader: WideString;
    function Get_EnableChipReader: WideString;
    function Get_EnableRFID: WideString;
    function Get_PersonalAccountNumberMasked: WideString;
    function Get_TokenData: WideString;
    function Get_PanFirst6: WideString;
    function Get_PanLast4: WideString;
    function Get_ExpirationDate: WideString;
    function Get_CardHolderName: WideString;
    function Get_Track1Data: WideString;
    function Get_Track2Data: WideString;
    function Get_WinEPSCardType: WideString;
    function Get_TenderType: WideString;
    function Get_DriverLicense: WideString;
    procedure Set_EnableCardReader(Value: WideString);
    procedure Set_EnableChipReader(Value: WideString);
    procedure Set_EnableRFID(Value: WideString);
    procedure Set_PersonalAccountNumberMasked(Value: WideString);
    procedure Set_TokenData(Value: WideString);
    procedure Set_PanFirst6(Value: WideString);
    procedure Set_PanLast4(Value: WideString);
    procedure Set_ExpirationDate(Value: WideString);
    procedure Set_CardHolderName(Value: WideString);
    procedure Set_Track1Data(Value: WideString);
    procedure Set_Track2Data(Value: WideString);
    procedure Set_WinEPSCardType(Value: WideString);
    procedure Set_TenderType(Value: WideString);
    procedure Set_DriverLicense(Value: WideString);
  end;

{ TXMLEncryptedDataRequestType }

  TXMLEncryptedDataRequestType = class(TXMLNode, IXMLEncryptedDataRequestType)
  protected
    { IXMLEncryptedDataRequestType }
    function Get_EntryText: WideString;
    function Get_ReEntryText: WideString;
    function Get_DisplayString: WideString;
    function Get_FormatString: WideString;
    function Get_Min: WideString;
    function Get_Max: WideString;
    function Get_DataType: WideString;
    function Get_DataOptions: WideString;
    function Get_TokenData: WideString;
    procedure Set_EntryText(Value: WideString);
    procedure Set_ReEntryText(Value: WideString);
    procedure Set_DisplayString(Value: WideString);
    procedure Set_FormatString(Value: WideString);
    procedure Set_Min(Value: WideString);
    procedure Set_Max(Value: WideString);
    procedure Set_DataType(Value: WideString);
    procedure Set_DataOptions(Value: WideString);
    procedure Set_TokenData(Value: WideString);
  end;

  { TXMLListPromptsType }

  TXMLListPromptsType = class(TXMLNodeCollection, IXMLListPromptsType)
  protected
    { IXMLListPromptsType }
    function Get_ListPrompt(Index: Integer): IXMLListPromptType;
    function Add: IXMLListPromptType;
    function Insert(const Index: Integer): IXMLListPromptType;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLListPromptType }

  TXMLListPromptType = class(TXMLNodeCollection, IXMLListPromptType)
  protected
    function Get_Caption: WideString;
    procedure Set_Caption(Value: WideString);
    function Get_PromptForAmount: WideString;
    procedure Set_PromptForAmount(Value: WideString);
    function Get_DefaultAmount: WideString;
    procedure Set_DefaultAmount(Value: WideString);
    function Get_TipAmountFormat: WideString;
    procedure Set_TipAmountFormat(Value: WideString);
  public
    procedure AfterConstruction; override;
  end;

{ TXMLListBoxesType }

  TXMLListBoxesType = class(TXMLNodeCollection, IXMLListBoxesType)
  protected
    { IXMLListBoxesType }
    function Get_ListBox(Index: Integer): IXMLListBoxType;
    function Add: IXMLListBoxType;
    function Insert(const Index: Integer): IXMLListBoxType;
  public
    procedure AfterConstruction; override;
  end;

{ TXMLListBoxType }

  TXMLListBoxType = class(TXMLNodeCollection, IXMLListBoxType)
  protected
    { IXMLEditBoxType }
    function Get_Line(Index: Integer): IXMLLineType;
    function Add: IXMLLineType;
    function Insert(const Index: Integer): IXMLLineType;
    function Get_Number: WideString;
    function Get_ID: WideString;
    procedure Set_Number(Value: WideString);
    procedure Set_ID(Value: WideString);
  public
    procedure AfterConstruction; override;
  end;

{ TXMLLineType }

  TXMLLineType = class(TXMLNode, IXMLLineType)
  protected
    { IXMLLineType }
    function Get_Number: WideString;
    function Get_Text: WideString;
    procedure Set_Number(Value: WideString);
    procedure Set_Text(Value: WideString);
  end;

{ TXMLPINEntryRequestType }

  TXMLPINEntryRequestType = class(TXMLNode, IXMLPINEntryRequestType)
  protected
    { IXMLPINEntryRequestType }
    function Get_PINEntry: WideString;
    function Get_Text: WideString;
    function Get_RandomNumber: WideString;
    function Get_PINBlock: WideString;
    function Get_MinPIN: WideString;
    function Get_MaxPIN: WideString;
    procedure Set_PINEntry(Value: WideString);
    procedure Set_Text(Value: WideString);
    procedure Set_RandomNumber(Value: WideString);
    procedure Set_PINBlock(Value: WideString);
    procedure Set_MinPIN(Value: WideString);
    procedure Set_MaxPIN(Value: WideString);
  end;

  TXMLTimeoutType = class(TXMLNode, IXMLTimeoutType)
  protected
    { IXMLTimeoutType }
    function Get_Timeout: WideString;
    procedure Set_Timeout(Value: WideString);
  end;

  TXMLDisplayLineType = class(TXMLNode, IXMLDisplayLineType)
  protected
    { IXMLDisplayLineType }
    function Get_DisplayLine: WideString;
    procedure Set_DisplayLine(Value: WideString);
  end;

  TXMLServerNumType = class(TXMLNode, IXMLServerNumType)
  protected
    { IXMLServerNumType }
    function Get_ServerNum: WideString;
    procedure Set_ServerNum(Value: WideString);
  end;

  TXMLTipAmountType = class(TXMLNode, IXMLTipAmountType)
  protected
    { IXMLTipAmountType }
    function Get_TipAmount: WideString;
    procedure Set_TipAmount(Value: WideString);
  end;


{ Global Functions }

function GetForm(Doc: IXMLDocument): IXMLFormType;
function LoadForm(const FileName: WideString): IXMLFormType;
function LoadFormFromXML(const XmlData: WideString): IXMLFormType;
function NewForm: IXMLFormType;

implementation

uses MTX_Lib;

{ Global Functions }

function GetForm(Doc: IXMLDocument): IXMLFormType;
begin
  Result := Doc.GetDocBinding('Form', TXMLFormType) as IXMLFormType;
end;

function LoadForm(const FileName: WideString): IXMLFormType;
begin
  Result := LoadXMLDocument(FileName).GetDocBinding('Form', TXMLFormType) as IXMLFormType;
end;

function LoadFormFromXML(const XmlData: WideString): IXMLFormType;
begin
  Result := LoadXMLData(XmlData).GetDocBinding('Form', TXMLFormType) as IXMLFormType;
end;

function NewForm: IXMLFormType;
begin
  Result := NewXMLDocument.GetDocBinding('Form', TXMLFormType) as IXMLFormType;
end;

{ TXMLFormType }

procedure TXMLFormType.AfterConstruction;
begin
  RegisterChildNode('Buttons', TXMLButtonsType);
  RegisterChildNode('Labels', TXMLLabelsType);
  RegisterChildNode('EditBoxes', TXMLEditBoxesType);
  RegisterChildNode('SignatureBox', TXMLSignatureBoxType);
  RegisterChildNode('CardReader', TXMLCardReaderType);
  RegisterChildNode('EncryptedDataRequest', TXMLEncryptedDataRequestType);
  RegisterChildNode('ListBoxes', TXMLListBoxesType);
  RegisterChildNode('ListPrompts', TXMLListPromptsType);
  RegisterChildNode('PINEntryRequest', TXMLPinEntryRequestType);
  RegisterChildNode('Timeout', TXMLTimeoutType);
  RegisterChildNode('DisplayLine', TXMLDisplayLineType);
  RegisterChildNode('ServerNum', TXMLServerNumType);
  RegisterChildNode('TipAmount', TXMLTipAmountType);
  inherited;
end;

function TXMLFormType.Get_Name: WideString;
begin
  Result := AttributeNodes['Name'].Text;
end;

procedure TXMLFormType.Set_Name(Value: WideString);
begin
  SetAttribute('Name', Value);
end;

function TXMLFormType.Get_DisplayOnly: WideString;                        // CPCLIENTS-11878
begin
  Result := AttributeNodes['DisplayOnly'].Text;
end;

function TXMLFormType.HasNode(Value: WideString): boolean;
begin
  result := Assigned(ChildNodes.FindNode(Value));
end;

function TXMLFormType.Get_Buttons: IXMLButtonsType;
begin
  Result := ChildNodes['Buttons'] as IXMLButtonsType;
end;

function TXMLFormType.Get_ListPrompts: IXMLListPromptsType;
begin
  Result := ChildNodes['ListPrompts'] as IXMLListPromptsType;
end;

function TXMLFormType.Get_Labels: IXMLLabelsType;
begin
  Result := ChildNodes['Labels'] as IXMLLabelsType;
end;

function TXMLFormType.Get_EditBoxes: IXMLEditBoxesType;
begin
  Result := ChildNodes['EditBoxes'] as IXMLEditBoxesType;
end;

function TXMLFormType.Get_SignatureBox: IXMLSignatureBoxType;
begin
  Result := ChildNodes['SignatureBox'] as IXMLSignatureBoxType;
end;

function TXMLFormType.Get_CardReader: IXMLCardReaderType;
begin
  Result := ChildNodes['CardReader'] as IXMLCardReaderType;
end;

function TXMLFormType.Get_EncryptedDataRequest: IXMLEncryptedDataRequestType;
begin
  Result := ChildNodes['EncryptedDataRequest'] as IXMLEncryptedDataRequestType;
end;

function TXMLFormType.Get_ListBoxes: IXMLListBoxesType;
begin
  Result := ChildNodes['ListBoxes'] as IXMLListBoxesType;
end;

function TXMLFormType.Get_PinEntryRequest: IXMLPinEntryRequestType;
begin
  Result := ChildNodes['PINEntryRequest'] as IXMLPinEntryRequestType;
end;

function TXMLFormType.Get_Timeout: IXMLTimeoutType;
begin
  Result := ChildNodes['Timeout'] as IXMLTimeoutType;
end;

function TXMLFormType.Get_DisplayLine: IXMLDisplayLineType;
begin
  Result := ChildNodes['DisplayLine'] as IXMLDisplayLineType;
end;

function TXMLFormType.Get_ServerNum: IXMLServerNumType;
begin
  Result := ChildNodes['ServerNum'] as IXMLServerNumType;
end;

function TXMLFormType.Get_TipAmount: IXMLTipAmountType;
begin
  Result := ChildNodes['TipAmount'] as IXMLTipAmountType;
end;


{ TXMLButtonsType }

procedure TXMLButtonsType.AfterConstruction;
begin
  RegisterChildNode('Button', TXMLButtonType);
  ItemTag := 'Button';
  ItemInterface := IXMLButtonType;
  inherited;
end;

function TXMLButtonsType.Get_Button(Index: Integer): IXMLButtonType;
begin
  Result := List[Index] as IXMLButtonType;
end;

function TXMLButtonsType.Add: IXMLButtonType;
begin
  Result := AddItem(-1) as IXMLButtonType;
end;

function TXMLButtonsType.Insert(const Index: Integer): IXMLButtonType;
begin
  Result := AddItem(Index) as IXMLButtonType;
end;


{ TXMLButtonType }

function TXMLButtonType.Get_Number: WideString;
begin
  Result := AttributeNodes['Number'].Text;
end;

procedure TXMLButtonType.Set_Number(Value: WideString);
begin
  SetAttribute('Number', Value);
end;

function TXMLButtonType.Get_Caption: WideString;
begin
  Result := AttributeNodes['Caption'].Text;
end;

procedure TXMLButtonType.Set_Caption(Value: WideString);
begin
  SetAttribute('Caption', Value);
end;

function TXMLButtonType.Get_ID: WideString;
begin
  Result := AttributeNodes['ID'].Text;
end;

procedure TXMLButtonType.Set_ID(Value: WideString);
begin
  SetAttribute('ID', Value);
end;

{ TXMLLabelsType }

procedure TXMLLabelsType.AfterConstruction;
begin
  RegisterChildNode('Label', TXMLLabelType);
  ItemTag := 'Label';
  ItemInterface := IXMLLabelType;
  inherited;
end;

function TXMLLabelsType.Get_Label_(Index: Integer): IXMLLabelType;
begin
  Result := List[Index] as IXMLLabelType;
end;

function TXMLLabelsType.Add: IXMLLabelType;
begin
  Result := AddItem(-1) as IXMLLabelType;
end;

function TXMLLabelsType.Insert(const Index: Integer): IXMLLabelType;
begin
  Result := AddItem(Index) as IXMLLabelType;
end;


{ TXMLLabelType }

function TXMLLabelType.Get_Number: WideString;
begin
  Result := AttributeNodes['Number'].Text;
end;

procedure TXMLLabelType.Set_Number(Value: WideString);
begin
  SetAttribute('Number', Value);
end;

function TXMLLabelType.Get_Caption: WideString;
begin
  Result := AttributeNodes['Caption'].Text;
end;

procedure TXMLLabelType.Set_Caption(Value: WideString);
begin
  SetAttribute('Caption', Value);
end;

function TXMLLabelType.Get_ID: WideString;
begin
  Result := AttributeNodes['ID'].Text;
end;

procedure TXMLLabelType.Set_ID(Value: WideString);
begin
  SetAttribute('ID', Value);
end;

{ TXMLEditBoxesType }

procedure TXMLEditBoxesType.AfterConstruction;
begin
  RegisterChildNode('EditBox', TXMLEditBoxType);
  ItemTag := 'EditBox';
  ItemInterface := IXMLEditBoxType;
  inherited;
end;

function TXMLEditBoxesType.Get_EditBox(Index: Integer): IXMLEditBoxType;
begin
  Result := List[Index] as IXMLEditBoxType;
end;

function TXMLEditBoxesType.Add: IXMLEditBoxType;
begin
  Result := AddItem(-1) as IXMLEditBoxType;
end;

function TXMLEditBoxesType.Insert(const Index: Integer): IXMLEditBoxType;
begin
  Result := AddItem(Index) as IXMLEditBoxType;
end;


{ TXMLEditBoxType }

function TXMLEditBoxType.Get_Number: WideString;
begin
  Result := AttributeNodes['Number'].Text;
end;

procedure TXMLEditBoxType.Set_Number(Value: WideString);
begin
  SetAttribute('Number', Value);
end;

function TXMLEditBoxType.Get_ValType: WideString;
begin
  Result := AttributeNodes['ValType'].Text;
end;

procedure TXMLEditBoxType.Set_ValType(Value: WideString);
begin
  SetAttribute('ValType', Value);
end;

function TXMLEditBoxType.Get_DisplayString: WideString;
begin
  Result := AttributeNodes['DisplayString'].Text;
end;

procedure TXMLEditBoxType.Set_DisplayString(Value: WideString);
begin
  SetAttribute('DisplayString', Value);
end;

function TXMLEditBoxType.Get_FormatString: WideString;
begin
  Result := AttributeNodes['FormatString'].Text;
end;

procedure TXMLEditBoxType.Set_FormatString(Value: WideString);
begin
  SetAttribute('FormatString', Value);
end;

function TXMLEditBoxType.Get_Min: WideString;
begin
  Result := AttributeNodes['Min'].Text;
end;

procedure TXMLEditBoxType.Set_Min(Value: WideString);
begin
  SetAttribute('Min', Value);
end;

function TXMLEditBoxType.Get_Max: WideString;
begin
  Result := AttributeNodes['Max'].Text;
end;

procedure TXMLEditBoxType.Set_Max(Value: WideString);
begin
  SetAttribute('Max', Value);
end;

function TXMLEditBoxType.Get_ID: WideString;
begin
  Result := AttributeNodes['ID'].Text;
end;

procedure TXMLEditBoxType.Set_ID(Value: WideString);
begin
  SetAttribute('ID', Value);
end;

function TXMLEditBoxType.Get_Value: WideString;
begin
  Result := AttributeNodes['Value'].Text;
end;

procedure TXMLEditBoxType.Set_Value(Value: WideString);
begin
  SetAttribute('Value', Value);
end;

{ TXMLSignatureBoxType }

function TXMLSignatureBoxType.Get_CaptureSignature: WideString;
begin
  Result := AttributeNodes['CaptureSignature'].Text;
end;

procedure TXMLSignatureBoxType.Set_CaptureSignature(Value: WideString);
begin
  SetAttribute('CaptureSignature', Value);
end;

function TXMLSignatureBoxType.Get_Coordinates: WideString;
begin
  Result := AttributeNodes['Coordinates'].Text;
end;

procedure TXMLSignatureBoxType.Set_Coordinates(Value: WideString);
begin
  SetAttribute('Coordinates', Value);
end;

function TXMLSignatureBoxType.Get_SignatureData: WideString;
begin
  Result := AttributeNodes['SignatureData'].Text;
end;

procedure TXMLSignatureBoxType.Set_SignatureData(Value: WideString);
begin
  SetAttribute('SignatureData', Value);
end;

function TXMLSignatureBoxType.Get_ID: WideString;
begin
  Result := AttributeNodes['ID'].Text;
end;

procedure TXMLSignatureBoxType.Set_ID(Value: WideString);
begin
  SetAttribute('ID', Value);
end;

{ TXMLCardReaderType }

function TXMLCardReaderType.Get_EnableCardReader: WideString;
begin
  Result := AttributeNodes['EnableCardReader'].Text;
end;

procedure TXMLCardReaderType.Set_EnableCardReader(Value: WideString);
begin
  SetAttribute('EnableCardReader', Value);
end;

function TXMLCardReaderType.Get_EnableChipReader: WideString;
begin
  Result := AttributeNodes['EnableChipReader'].Text;
end;

procedure TXMLCardReaderType.Set_EnableChipReader(Value: WideString);
begin
  SetAttribute('EnableChipReader', Value);
end;

function TXMLCardReaderType.Get_EnableRFID: WideString;
begin
  Result := AttributeNodes['EnableRFID'].Text;
end;

procedure TXMLCardReaderType.Set_EnableRFID(Value: WideString);
begin
  SetAttribute('EnableRFID', Value);
end;

function TXMLCardReaderType.Get_PersonalAccountNumberMasked: WideString;
begin
  Result := AttributeNodes['PersonalAccountNumberMasked'].Text;
end;

procedure TXMLCardReaderType.Set_PersonalAccountNumberMasked(Value: WideString);
begin
  SetAttribute('PersonalAccountNumberMasked', Value);
end;

function TXMLCardReaderType.Get_TokenData: WideString;
begin
  Result := AttributeNodes['TokenData'].Text;
end;

procedure TXMLCardReaderType.Set_TokenData(Value: WideString);
begin
  SetAttribute('TokenData', Value);
end;

function TXMLCardReaderType.Get_PanFirst6: WideString;
begin
  Result := AttributeNodes['PanFirst6'].Text;
end;

procedure TXMLCardReaderType.Set_PanFirst6(Value: WideString);
begin
  SetAttribute('PanFirst6', Value);
end;

function TXMLCardReaderType.Get_PanLast4: WideString;
begin
  Result := AttributeNodes['PanLast4'].Text;
end;

procedure TXMLCardReaderType.Set_PanLast4(Value: WideString);
begin
  SetAttribute('PanLast4', Value);
end;

function TXMLCardReaderType.Get_ExpirationDate: WideString;
begin
  Result := AttributeNodes['ExpirationDate'].Text;
end;

procedure TXMLCardReaderType.Set_ExpirationDate(Value: WideString);
begin
  SetAttribute('ExpirationDate', Value);
end;

function TXMLCardReaderType.Get_CardHolderName: WideString;
begin
  Result := AttributeNodes['CardHolderName'].Text;
end;

procedure TXMLCardReaderType.Set_CardHolderName(Value: WideString);
begin
  SetAttribute('CardHolderName', Value);
end;

function TXMLCardReaderType.Get_Track1Data: WideString;
begin
  Result := AttributeNodes['Track1Data'].Text;
end;

procedure TXMLCardReaderType.Set_Track1Data(Value: WideString);
begin
  SetAttribute('Track1Data', Value);
end;

function TXMLCardReaderType.Get_Track2Data: WideString;
begin
  Result := AttributeNodes['Track2Data'].Text;
end;

procedure TXMLCardReaderType.Set_Track2Data(Value: WideString);
begin
  SetAttribute('Track2Data', Value);
end;

function TXMLCardReaderType.Get_WinEPSCardType: WideString;
begin
  Result := AttributeNodes['WinEPSCardType'].Text;
end;

procedure TXMLCardReaderType.Set_WinEPSCardType(Value: WideString);
begin
  SetAttribute('WinEPSCardType', Value);
end;

function TXMLCardReaderType.Get_TenderType: WideString;
begin
  Result := AttributeNodes['TenderType'].Text;
end;

procedure TXMLCardReaderType.Set_TenderType(Value: WideString);
begin
  SetAttribute('TenderType', Value);
end;

function TXMLCardReaderType.Get_DriverLicense: WideString;
begin
  Result := AttributeNodes['DriverLicense'].Text;
end;

procedure TXMLCardReaderType.Set_DriverLicense(Value: WideString);
begin
  SetAttribute('DriverLicense', Value);
end;

{ TXMLEncryptedDataRequestType }

function TXMLEncryptedDataRequestType.Get_EntryText: WideString;
begin
  Result := AttributeNodes['EntryText'].NodeValue;
end;

procedure TXMLEncryptedDataRequestType.Set_EntryText(Value: WideString);
begin
  SetAttribute('EntryText', Value);
end;

function TXMLEncryptedDataRequestType.Get_ReEntryText: WideString;
begin
  Result := AttributeNodes['ReEntryText'].NodeValue;
end;

procedure TXMLEncryptedDataRequestType.Set_ReEntryText(Value: WideString);
begin
  SetAttribute('ReEntryText', Value);
end;

function TXMLEncryptedDataRequestType.Get_DisplayString: WideString;
begin
  Result := AttributeNodes['DisplayString'].NodeValue;
end;

procedure TXMLEncryptedDataRequestType.Set_DisplayString(Value: WideString);
begin
  SetAttribute('DisplayString', Value);
end;

function TXMLEncryptedDataRequestType.Get_FormatString: WideString;
begin
  Result := AttributeNodes['FormatString'].NodeValue;
end;

procedure TXMLEncryptedDataRequestType.Set_FormatString(Value: WideString);
begin
  SetAttribute('FormatString', Value);
end;

function TXMLEncryptedDataRequestType.Get_Min: WideString;
begin
  Result := AttributeNodes['Min'].NodeValue;
end;

procedure TXMLEncryptedDataRequestType.Set_Min(Value: WideString);
begin
  SetAttribute('Min', Value);
end;

function TXMLEncryptedDataRequestType.Get_Max: WideString;
begin
  Result := AttributeNodes['Max'].NodeValue;
end;

procedure TXMLEncryptedDataRequestType.Set_Max(Value: WideString);
begin
  SetAttribute('Max', Value);
end;

function TXMLEncryptedDataRequestType.Get_DataType: WideString;
begin
  Result := AttributeNodes['DataType'].NodeValue;
end;

procedure TXMLEncryptedDataRequestType.Set_DataType(Value: WideString);
begin
  SetAttribute('DataType', Value);
end;

function TXMLEncryptedDataRequestType.Get_DataOptions: WideString;
begin
  Result := AttributeNodes['DataOptions'].NodeValue;
end;

procedure TXMLEncryptedDataRequestType.Set_DataOptions(Value: WideString);
begin
  SetAttribute('DataOptions', Value);
end;

function TXMLEncryptedDataRequestType.Get_TokenData: WideString;
begin
  Result := AttributeNodes['TokenData'].NodeValue;
end;

procedure TXMLEncryptedDataRequestType.Set_TokenData(Value: WideString);
begin
  SetAttribute('TokenData', Value);
end;

{ TXMLListPromptsType }

procedure TXMLListPromptsType.AfterConstruction;
begin
  RegisterChildNode('ListPrompt', TXMLListPromptType);
  ItemTag := 'ListPrompt';
  ItemInterface := IXMLListPromptType;
  inherited;
end;

function TXMLListPromptsType.Get_ListPrompt(Index: Integer): IXMLListPromptType;
begin
  Result := List[Index] as IXMLListPromptType;
end;

function TXMLListPromptsType.Add: IXMLListPromptType;
begin
  Result := AddItem(-1) as IXMLListPromptType;
end;

function TXMLListPromptsType.Insert(const Index: Integer): IXMLListPromptType;
begin
  Result := AddItem(Index) as IXMLListPromptType;
end;

{ TXMLListPromptType }

function TXMLListPromptType.Get_Caption: WideString;
begin
  Result := AttributeNodes['Caption'].Text;
end;

procedure TXMLListPromptType.Set_Caption(Value: WideString);
begin
  SetAttribute('Caption', Value);
end;

function TXMLListPromptType.Get_PromptForAmount: WideString;
begin
  Result := AttributeNodes['PromptForAmount'].Text;
end;

procedure TXMLListPromptType.Set_PromptForAmount(Value: WideString);
begin
  SetAttribute('PromptForAmount', Value);
end;

function TXMLListPromptType.Get_DefaultAmount: WideString;
begin
  Result := AttributeNodes['DefaultAmount'].Text;
end;

procedure TXMLListPromptType.Set_DefaultAmount(Value: WideString);
begin
  SetAttribute('DefaultAmount', Value);
end;

function TXMLListPromptType.Get_TipAmountFormat: WideString;
begin
  Result := AttributeNodes['AmountFormat'].Text;
end;

procedure TXMLListPromptType.Set_TipAmountFormat(Value: WideString);
begin
  SetAttribute('AmountFormat', Value);
end;

procedure TXMLListPromptType.AfterConstruction;
begin
  inherited;
end;

{ TXMLListBoxesType }

procedure TXMLListBoxesType.AfterConstruction;
begin
  RegisterChildNode('ListBox', TXMLListBoxType);
  ItemTag := 'ListBox';
  ItemInterface := IXMLListBoxType;
  inherited;
end;

function TXMLListBoxesType.Get_ListBox(Index: Integer): IXMLListBoxType;
begin
  Result := List[Index] as IXMLListBoxType;
end;

function TXMLListBoxesType.Add: IXMLListBoxType;
begin
  Result := AddItem(-1) as IXMLListBoxType;
end;

function TXMLListBoxesType.Insert(const Index: Integer): IXMLListBoxType;
begin
  Result := AddItem(Index) as IXMLListBoxType;
end;

{ TXMLListBoxType }

procedure TXMLListBoxType.AfterConstruction;
begin
  RegisterChildNode('Line', TXMLLineType);
  ItemTag := 'Line';
  ItemInterface := IXMLLineType;
  inherited;
end;

function TXMLListBoxType.Get_Line(Index: Integer): IXMLLineType;
begin
  Result := List[Index] as IXMLLineType;
end;

function TXMLListBoxType.Add: IXMLLineType;
begin
  Result := AddItem(-1) as IXMLLineType;
end;

function TXMLListBoxType.Insert(const Index: Integer): IXMLLineType;
begin
  Result := AddItem(Index) as IXMLLineType;
end;

function TXMLListBoxType.Get_Number: WideString;
begin
  Result := AttributeNodes['Number'].Text;
end;

procedure TXMLListBoxType.Set_Number(Value: WideString);
begin
  SetAttribute('Number', Value);
end;

function TXMLListBoxType.Get_ID: WideString;
begin
  Result := AttributeNodes['ID'].Text;
end;

procedure TXMLListBoxType.Set_ID(Value: WideString);
begin
  SetAttribute('ID', Value);
end;

{ TXMLLineType }

function TXMLLineType.Get_Number: WideString;
begin
  Result := AttributeNodes['Number'].Text;
end;

procedure TXMLLineType.Set_Number(Value: WideString);
begin
  SetAttribute('Number', Value);
end;

function TXMLLineType.Get_Text: WideString;
begin
  Result := AttributeNodes['Text'].Text;
end;

procedure TXMLLineType.Set_Text(Value: WideString);
begin
  SetAttribute('Text', Value);
end;

{ TXMLPINEntryRequestType }

function TXMLPINEntryRequestType.Get_PINEntry: WideString;
begin
  Result := AttributeNodes['PINEntry'].Text;
end;

procedure TXMLPINEntryRequestType.Set_PINEntry(Value: WideString);
begin
  SetAttribute('PINEntry', Value);
end;

function TXMLPINEntryRequestType.Get_Text: WideString;
begin
  Result := AttributeNodes['Text'].Text;
end;

procedure TXMLPINEntryRequestType.Set_Text(Value: WideString);
begin
  SetAttribute('Text', Value);
end;

function TXMLPINEntryRequestType.Get_RandomNumber: WideString;
begin
  Result := AttributeNodes['RandomNumber'].Text;
end;

procedure TXMLPINEntryRequestType.Set_RandomNumber(Value: WideString);
begin
  SetAttribute('RandomNumber', Value);
end;

function TXMLPINEntryRequestType.Get_PINBlock: WideString;
begin
  Result := AttributeNodes['PINBlock'].Text;
end;

procedure TXMLPINEntryRequestType.Set_PINBlock(Value: WideString);
begin
  SetAttribute('PINBlock', Value);
end;

function TXMLPINEntryRequestType.Get_MinPIN: WideString;
begin
  Result := AttributeNodes['MinPIN'].Text;
end;

procedure TXMLPINEntryRequestType.Set_MinPIN(Value: WideString);
begin
  SetAttribute('MinPIN', Value);
end;

function TXMLPINEntryRequestType.Get_MaxPIN: WideString;
begin
  Result := AttributeNodes['MaxPIN'].Text;
end;

procedure TXMLPINEntryRequestType.Set_MaxPIN(Value: WideString);
begin
  SetAttribute('MaxPIN', Value);
end;

function TXMLTimeoutType.Get_Timeout: WideString;
begin
  Result := AttributeNodes['Value'].Text;
end;

procedure TXMLTimeoutType.Set_Timeout(Value: WideString);
begin
  SetAttribute('Value', Value);
end;

function TXMLDisplayLineType.Get_DisplayLine: WideString;
begin
  Result := AttributeNodes['Value'].Text;
end;

procedure TXMLDisplayLineType.Set_DisplayLine(Value: WideString);
begin
  SetAttribute('Value', Value);
end;

function TXMLServerNumType.Get_ServerNum: WideString;
begin
  Result := AttributeNodes['Value'].Text;
end;

procedure TXMLServerNumType.Set_ServerNum(Value: WideString);
begin
  SetAttribute('Value', Value);
end;

function TXMLTipAmountType.Get_TipAmount: WideString;
begin
  Result := AttributeNodes['Value'].Text;
end;

procedure TXMLTipAmountType.Set_TipAmount(Value: WideString);
begin
  SetAttribute('Value', Value);
end;

end.


