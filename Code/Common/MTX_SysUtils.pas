// (c) MTXEPS, Inc. 1988-2008
unit MTX_SysUtils;

interface

uses
  MTX_Types;

function MTX_BoolToStr(const ABoolValue: TMTXBoolean; const ATrueValue, AFalseValue: TMTXString): TMTXString;
function MTX_PadIntToStr(const AIntValue, APadLen: TMTXInteger; const APadChr: TMTXChar = '0'): TMTXString;

implementation

uses
  FinalizationLog,
  StrUtils,
  SysUtils;

function MTX_BoolToStr(const ABoolValue: TMTXBoolean; const ATrueValue, AFalseValue: TMTXString): TMTXString;
begin
  Result:= IfThen(ABoolValue, ATrueValue, AFalseValue);
end;

function MTX_PadIntToStr(const AIntValue, APadLen: TMTXInteger; const APadChr: TMTXChar): TMTXString;
begin
  Result:= IntToStr(AIntValue);
  while Length(Result) < APadLen do Result:= APadChr + Result;
end;

initialization
  ExtendedLog('MTX_SysUtils Initialization');
finalization
  ExtendedLog('MTX_SysUtils Finalization');

end.
