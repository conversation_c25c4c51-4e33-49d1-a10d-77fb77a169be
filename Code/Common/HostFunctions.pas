unit HostFunctions;

interface

uses
  Classes,
  SysUtils,
  StoreConfigurationsXML;

var
  hostNumForTran: integer;      //interfaced for now - an MdMsgRec func in fct uses and maybe modifies it

function pblHostDefined: boolean;
procedure InitHostFile;
function GetHostBuf(tenderNum: integer; const IsPersonalCheck: boolean = true): boolean; // will update global HostBuf
function GetXMLHostByTender(tenderNum: integer): TXMLHostType; // TFS-112676
function IsHostBufSet: boolean;
function HostDefined(aHost: string) : boolean;
function ERCHostDefined(aHost: string): boolean;
function anyERCHostDefined : boolean;
function IsTDBHost(tenderNum: integer): boolean;
function IsBYLHostGroup(HostSuffix: string): boolean; // CPCLIENTS-5058

implementation

uses
  MTX_Constants,
  MTX_Lib,
  MTX_XMLClasses,
  UXMLCommon,
  MdMsg;   // for iDebit constant

function pblHostDefined: boolean;
var i: integer;
begin
  result := false;
  if Assigned(HostArray) then
  begin
    i := 0;
    while (i < HostArray.Count) and not result do
    begin
      result := IsPublix(HostArray[i].Suffix);
      inc(i);
    end;
  end;
end;

procedure InitHostFile;
var
  i,j: Integer;
begin
  try
    for i := 1 to 10 do          { JMR-C : initialize array }  //TODO JTG - should be CONST??  HI/LO
      DLLTruncAcct[i] := 'N';
    DLLHostPO := 'N';
    if Assigned(HostArray) then
    begin
      for i := 0 to HostArray.Count-1 do
      begin
        if Assigned(HostBuf) then
        begin
          for j := 1 to 10 do                   //TODO JTG should be CONST...
            if (GetArrayText(HostBuf.HostTruncateAcctNo,j,1) = 'Y') then  { TSL-K }
              DLLTruncAcct[j] := 'Y';
        end
        else
          SM('****ERROR in FCT.pas (InitHostFile) - HostBuf not assigned');
        if (HostArray[i].AddressLines = 'Y') then         { TSL-K }    //TODO SAMETEXT
          DLLHostPO := 'Y';
      end
    end
    else
      SM('****ERROR in FCT.pas (InitHostFile) - HostArray not assigned');
  except on e: exception do
    SM('Try..Except: FCT.InitHostFile reading ' + StoreConfigurationsXML_ + ' : ' + e.message);
  end;
end;

function GetHostBuf(tenderNum: integer; const IsPersonalCheck: boolean = true): boolean;
var i,j: integer;
begin
  result := false;
  if assigned(HostArray) then
  begin
    hostNumForTran := 0;
    i := 0;
    while (i < HostArray.Count) and (hostNumForTran = 0) do
    begin
      try
        if GetArrayText(HostArray[i].TransactionAllowed,tenderNum,1) = 'Y' then
        begin
          if SameText(HostArray[i].Suffix, 'FLC') and (not IsPersonalCheck) then  // FLC for personal checks, FL2 for non personal checks
          begin
            j := 0;
            while (j < HostArray.Count) and (hostNumForTran = 0) do
              if SameText(HostArray[j].Suffix, 'FL2')
                then hostNumForTran := j + 1
                else inc(j);
          end;
          if (hostNumForTran = 0) then            // make this an if, in case FL2 is not defined
            hostNumForTran := i + 1;
        end;
      except on e: exception do
        SM(format('Try..Except: fct.pas.SetHostBuf HostArray[%d] or TenderNum %d not exist - %s',[i,tenderNum,e.message]));
      end;
      inc(i);    // do this outside the try..except so loop will terminate if we enter except block
    end;

    if hostNumForTran = 0 then
    begin
      SM('****ERROR: No host defined for >' + WinEPSTenderDescrip[tenderNum] + '< fct.pas.SetHostBuf');
        {$IFNDEF RPT}                                                           
        {$IFNDEF MTXEPSDLL}
        {$IFNDEF TEST}
        {$IFNDEF OPENIP}
      MsgToMenu('****ERROR: No host defined for >' + WinEPSTenderDescrip[tenderNum] + '<');
        {$ENDIF}
        {$ENDIF}
        {$ENDIF MTXEPSDLL}
        {$ENDIF RPT}
    end
    else
    begin
      try     // JTG add'l try block
        HostBuf := HostArray[hostNumForTran - 1];
        result := Assigned(HostBuf);
        if Assigned(HostBuf) then        
          SM(Format('GetHostBuf TenderNum >%d: %s< HostBuf.Suffix >%s<', [tenderNum, WinEPSTenderDescrip[tenderNum], HostBuf.Suffix]));
      except on e: exception do
        SM('Try..Except: fct.pas.SetHostBuf (Accessing, setting HostBuf) ' + e.message);
      end;
    end;
  end;
end;

function GetXMLHostByTender(tenderNum: integer): TXMLHostType;
var
  i: Integer;
  found : Boolean;
begin
  result := nil;
  if Assigned(HostArray) then
  begin
    i := 0;
    while (i < HostArray.Count) and (not found) do
    begin
      found := GetArrayText(HostArray[i].TransactionAllowed, tenderNum, 1) = 'Y';
      if found then
        result  := HostArray[i]
      else
        inc(i);
    end;
  end;
end;

function IsHostBufSet: boolean;
begin
  result := (hostNumForTran <> 0);
end;

function HostDefined(aHost: string) : boolean;
var i: integer;
begin
  result := false;
  i := 0;
  while (i <= HostArray.Count-1) and (result = false) do
    if SameText(Trim(HostArray[i].Suffix), Trim(aHost)) then
      result := true
    else
      inc(i);
end;   { HostDefined }

function setDSHostBuffer(aHost: string): TXMLHostType;
var i   : integer;
begin
  result := nil;                                                                
  for i := 0 to HostArray.Count-1 do
    if (HostArray[i].Suffix = aHost) then
    begin
      result := HostArray[i];
      Break;
    end;
end;   { setDSHostBuffer }

function ERCHostDefined(aHost: string): boolean;
var aHostBuf: TXMLHostType;
begin
  if HostDefined(aHost) then
  begin
    aHostBuf := setDSHostBuffer(aHost);
    if (aHost = 'ERC') then
    begin
      if (aHostBuf.ProcessingMode <> 'N') then                                  
        result := true          //JTG TODO
      else
        result := false;
    end
    else
    if (aHostBuf.HostERCUse = 'Y') then                                         
      result := true
    else
      result := false;
  end
  else
    result := false;
end;   { ERCHostDefined }

function anyERCHostDefined : boolean;
begin
  result := ERCHostDefined('ERC') or HostDefined('REC') or ERCHostDefined('ACI');
end;   { ERCHostDefined }

function IsTDBHost(tenderNum: integer): boolean;
var
  host : TXMLHostType;
begin
  result := false;
  host := GetXMLHostByTender(tenderNum); // TFS-112676
  if Assigned(host) then
   result := host.Suffix.Equals('TDB');
end;

function IsBYLHostGroup(HostSuffix: string): boolean; // CPCLIENTS-5058
begin
  result := IsHostOneOf(HostSuffix, 'BYL/ATL/JET');
end;

end.
