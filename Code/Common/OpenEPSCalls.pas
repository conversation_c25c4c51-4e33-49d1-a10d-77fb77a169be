// (c) MTXEPS, Inc. 1988-2008
unit OpenEPSCalls;
(*
ver823 04-024-07 TSL-01 Create because I need some OpenEPS fn in WinEPS.
*)

interface

{$IFDEF TEST}
{$DEFINE MTXEPSDLL}
{$ENDIF TEST}

uses
  FinalizationLog,
  {$IFNDEF LINUX}
  windows,
  {$ENDIF}
  SysUtils,
  StrUtils,
  StringUtils,
  Classes,
  {$IFDEF MTXEPSDLL}
  DllTypes,
  epsTrace,
  {$ENDIF MTXEPSDLL}
  {$IFDEF FUEL}
  DllTypes,
  {$ENDIF FUEL}
  MTX_Constants,
  MTX_Utils,
  GeneralUtilities,
  MTX_Lib,
  MTX_XMLClasses,
  MTXEncryptionUtils,
{$IFNDEF WOLF}
  LBCipher,
  LbString,
{$ENDIF}
  MTX_Types,
  SCATConstants;

{$IFDEF MTXEPSDLL}
  {$DEFINE MTXEPSDLL_OR_FUEL}
{$ENDIF MTXEPSDLL}

{$IFDEF FUEL}
  {$DEFINE MTXEPSDLL_OR_FUEL}
{$ENDIF FUEL}


function ActivationRecordOK(aData: string; var bItems: TStringList; var aLogMsg: string): boolean;
function BlockDecrypt(SessionKey,sEncryptedHex: AnsiString): AnsiString;
function BlockEncrypt(SessionKey,sPlain: AnsiString): AnsiString;
{$IFDEF MTXEPSDLL_OR_FUEL}
function IsNameInMXData(tmpData: string; var aName: string): boolean;
function IsTrack2InMXData(tmpData: string; var aExpDate: string): boolean;
{$ENDIF MTXEPSDLL_OR_FUEL}

{$IFDEF MTXEPSDLL}
function IsPANOk(aid: byte; testPAN: string): boolean;
function IsPANOkP2P(aid: byte; testPAN: string; testLen: integer): boolean;
//function IsExpDateOkP2P(aExpirationDate: string): boolean; // XE: Remove WinEPS
function GetAccountNumFromTrack2Simple(var aTrack2: string): string;
function GetAccountNumFromTrack2(const aTrack2: string; aEncryptString: string = ''): string;
{$ENDIF MTXEPSDLL}

function GetMaskedValueByXML(Value: string; ReturnBlank: Boolean=false): string; // MTXEPSDLL, but for Menu compile

implementation

const
  DECRYPT = false;
  ENCRYPT = true;
  KEYLEN = 16;

{$IFDEF MTXEPSDLL_OR_FUEL}
var MXPan: string;
{$ENDIF}

function ActivationRecordOK(aData: string; var bItems: TStringList; var aLogMsg: string): boolean;
var aTender: integer;
  aTranType: integer;
begin
  result := false;
  // DOEP-56987,57871 - TARGET: Need to remove field separator from Ah field
  aData := ReplaceString(aData, ';', ''); // make sure we get rid of any ';'
  bItems.Text := ReplaceString(aData, ',', CRLF);
  aLogMsg := '****ERROR: Activation Record: ';
  if (bItems.Count <> 7) then { validate that each record has 7 items }
    aLogMsg := aLogMsg + 'item count less than 7'
  else
  if (length(bItems[0]) <> 9) then   { batch num is fixed to 9 digits }
    aLogMsg := aLogMsg + 'batch number not 9 digits >' + bItems[0] + '<'
  else
  begin
    aTender := strToIntDef(bItems[1], 0);
    aTranType := strToIntDef(bItems[2], 0);
    if (aTender < ttDebit) or (aTender > ttMaxTenderValue) then
      aLogMsg := aLogMsg + 'invalid tender ' + bItems[1]
    else
    if (aTranType < trtPurchase) or (aTranType > trtLAST) then
      aLogMsg := aLogMsg + 'invalid transaction type >' + bItems[2] + '<'
    else
    if (bItems[3] <> '') and NOT MTX_Lib.IsNumeric(bItems[3]) then // amount must be numeric or blank
      aLogMsg := aLogMsg + 'invalid amount >' + bItems[3] + '<'
    else
    if (length(bItems[4]) > 25) then    { PAN <= 25 digits }
      aLogMsg := aLogMsg + 'PAN length greater than 25 >' + bItems[4] + '<'
    else
    if (length(bItems[5]) > 14) then { PLU <= 14 digits }
      aLogMsg := aLogMsg + 'PLU length greater than 14 >' + bItems[5] + '<'
    else
    if (length(bItems[6]) > 22) then { PostTranNumber <= 22 }
      aLogMsg := aLogMsg + 'Post Tran Number length greater than 22 >' + bItems[6] + '<'
    else
      result := true;
  end;
end;   { ActivationRecordOK }

function BlockDecrypt(SessionKey,sEncryptedHex: AnsiString): AnsiString;
var
{$IFDEF WOLF}
  Key: TMtxKey128;  // array [0..15] of byte
{$ELSE}
  Key: TKey128;  // array [0..15] of byte
{$ENDIF}
  i: integer;
  S: AnsiString;
begin
  try
    for i := 0 to KEYLEN-1 do
      Key[i] := ord(SessionKey[i+1]);
    S := HexToB64(sEncryptedHex);
{$IFDEF WOLF}
    result := MTXEncryptionUtils.AES128DecryptCBC(Key, S);
{$ELSE}
    result := RDLEncryptStringEx(S,Key,KEYLEN,DECRYPT);  //this is not CBC!
{$ENDIF}
    if result = '' then
      result := format('Error attempting to decrypt [%s]',[sEncryptedHex]);
  except on e: exception do
    result := format('Error [%s] in attempting to decrypt [%s]',[E.Message,sEncryptedHex]);
  end;
end;

function BlockEncrypt(SessionKey,sPlain: AnsiString): AnsiString;
var
{$IFDEF WOLF}
  Key: TMtxKey128;
{$ELSE}
  Key: TKey128;
{$ENDIF}
  i: integer;
begin
  try   // encrypt sPlain (HOSTFINISHED) using SessionKey
    for i := 0 to KEYLEN-1 do
      Key[i] := ord(SessionKey[i+1]);
{$IFDEF WOLF}
    result := MTXEncryptionUtils.AES128Encrypt(Key, sPlain);
{$ELSE}
    result := RDLEncryptStringEx(sPlain,Key,KEYLEN,ENCRYPT);  //this is not CBC!
{$ENDIF}
    if result = ''
      then result := format('Error attempting to encrypt [%s]',[sPlain])
      else result := B64ToHex(result)
  except on e: exception do
    result := format('Error [%s] in attempting to encrypt [%s]',[E.Message,sPlain]);
  end;
end;

{$IFDEF MTXEPSDLL_OR_FUEL}
function IsNameInMXData(tmpData: string; var aName: string): boolean; // is name in track1 data in the MX encrypted string?
var tmpTrackPart: string;
    i,h1,h2: integer;
begin
  result := true;        // if there is no name then we get track1^^track1 for this
  aName := '';
  tmpTrackPart := '';
  i := 1;
  h2 := 0;
  // first find out if we have name by finding the first ^
  while (Pos('^', tmpTrackPart) = 0) and (i <= length(tmpData)) do
  begin
    tmpTrackPart := Copy(BlockDecrypt(DllTypes.SessionKey,Copy(tmpData, i, 32)), 1, SESSION_KEY_LENGTH);
    inc(i, 32);   // point to next block
  end;
  h1 := Pos('^', tmpTrackPart);                      // did we find the ^
  if (h1 > 0) then                                   // start storing the name
  begin
    h2 := Pos('^', copy(tmpTrackPart, succ(h1), length(tmpTrackPart)));
    if (h2 > 0)         // we have the complete name, just set it and get out
      then aName := copy(tmpTrackPart, h1, h2+1)
      else aName := copy(tmpTrackPart, h1, length(tmpTrackPart));
  end
  else
    result := false;                     // no ^ found, so there must be no name

  if result and (h2 = 0) then            // OK, we have first part of name, get the next ^ to end it
  begin
    while (Pos('^', tmpTrackPart) = 0) and (i <= length(tmpData)) do
    begin
      tmpTrackPart := Copy(BlockDecrypt(DllTypes.SessionKey,Copy(tmpData, i, 32)), 1, SESSION_KEY_LENGTH);
      inc(i, 32);   // point to next block
      if (Pos('^', tmpTrackPart) = 0) then     // if there is no ^, then we have to add this part to the name
        aName := aName + tmpTrackPart;
    end;
    h2 := Pos('^', tmpTrackPart);                           // did we find the last ^??
    if (h2 > 0) then                                        // we did find it, add rest of the name
      aName := aName + copy(tmpTrackPart, 1, h2)
    else
    begin
      aName := '';
      result := false;
    end;
  end;
end;

procedure GetPan(var aPan: string; aData: string; aPos: integer);
var i: integer;
begin
  i := pos('=',aData);          // do not go past the =
  if (i = 0) then
  begin
    i := pos(FS,aData);         // do not go past the FS
    {$IFDEF MTXEPSDLL}
    //if (i > 0) then
    //   showTrace(3,'****DEBUG: FOUND <FS>');
    {$ENDIF MEXEPSDLL}
  end
  else
  begin
  {$IFDEF MTXEPSDLL}
   //  showTrace(3,'****DEBUG: FOUND = sign');
  {$ENDIF MEXEPSDLL}
  end;
  if (i = 0) then
  begin
    i := length(aData) + 1;      // if neither there use length
    {$IFDEF MTXEPSDLL}
     //showTrace(3,'****DEBUG: Did not Find = or  <FS>');
    {$ENDIF MEXEPSDLL}
  end;

  aPan := aPan + copy(aData, aPos, pred(i));     // use pred to not copy the = or FS
  {$IFDEF MTXEPSDLL}
     //showTrace(3,'****DEBUG: Add to PAN >' + copy(aData, aPos, pred(i)) + '< PAN is now >' + aPan + '<');
  {$ENDIF MEXEPSDLL}
end;

function IsTrack2InMXData(tmpData: string; var aExpDate: string): boolean;  // is track2 data in the MX terminal encrypted string?
var tmpTrackPart: string;
    i,j: integer;
    isAmexCard : Boolean;
begin                    // the entire tmpData is encrypted.  Decrypted format is track1<FS>track2<FS>unused<FS>RFID
  result := true;        // if there is no track2 then we get track1<FS><FS>unused<FS>RFID: so look for this
  aExpDate := '';
  tmpTrackPart := '';
  MXPan := '';
  i := 1;
  isAmexCard := False;
  // first find out if we have track2 by finding the FS between track1 and track2, decrypt in 16 byte chuncks
  while (Pos(FS, tmpTrackPart) = 0) and (i <= length(tmpData)) do
  begin
    tmpTrackPart := Copy(BlockDecrypt(DllTypes.SessionKey,Copy(tmpData, i, 32)), 1, SESSION_KEY_LENGTH);
    inc(i, 32);   // point to next block
  end;
  j := Pos(FS, tmpTrackPart);                      // did we find the FS??
  if (j > 0) and (j = length(tmpTrackPart)) then   // is it the last char of decrypted part?
  begin                                            // if yes, then decrypt the next 16 bytes
    tmpTrackPart := Copy(BlockDecrypt(DllTypes.SessionKey,Copy(tmpData, i, 32)), 1, SESSION_KEY_LENGTH);
    if Copy(tmpTrackPart, Length(tmpTrackPart), 1) = '=' then // DOEP-56627
     isAmexCard := True;
    inc(i, 32);   // point to next block
    if (tmpTrackPart <> '') and (tmpTrackPart[1] = FS) then  // if the first char of the next block is FS, then no track2
      result := false;
  end
  else
  if (j > 0) then
  begin
    if (tmpTrackPart[succ(j)] = FS) then  // FS found check next char, if no track2 then j+1 will also be an FS
      result := false;
  end
  else
    result := false;                     // no FS found, so there must be no track2
  if result then     // OK, we have track2, let's get the expdate
  begin
    tmpTrackPart := rightStr(tmpTrackPart,length(tmpTrackPart) - j);  // get rid of FS and what is before it
    GetPan(MXPan,tmpTrackPart, 1);
    while (Pos('=', tmpTrackPart) = 0) and (Pos(FS, tmpTrackPart) = 0) and (i <= length(tmpData)) do
    begin
      if isAmexCard then  // DOEP-56627
        tmpTrackPart := tmpTrackPart + Copy(BlockDecrypt(DllTypes.SessionKey,Copy(tmpData, i, 32)), 1, SESSION_KEY_LENGTH)
      else
        tmpTrackPart := Copy(BlockDecrypt(DllTypes.SessionKey,Copy(tmpData, i, 32)), 1, SESSION_KEY_LENGTH);
      inc(i, 32);   // point to next block
      GetPan(MXPan,tmpTrackPart, 1);
    end;
    if isAmexCard then //DOEP-56627
      tmpTrackPart := '=' + tmpTrackPart;
    j := Pos('=', tmpTrackPart);                           // did we find the expDate??
    if (j > 0) and (j > (length(tmpTrackPart) - 4)) then   // we did find it, but we need more data to get it all
    begin
      aExpDate := copy(tmpTrackPart, succ(j), 4);          // get the first part of the date
      tmpTrackPart := Copy(BlockDecrypt(DllTypes.SessionKey,Copy(tmpData, i, 32)), 1, SESSION_KEY_LENGTH);
      //inc(i, 32);   // point to next block // Hint] OpenEPSCalls.pas(346): Value assigned to 'i' never used
      aExpDate := aExpDate + Copy(tmpTrackPart, 1, 4 - length(aExpDate));  // get the rest of the date
    end
    else
    if (j > 0) then                               // we have enough data just get expdate
      aExpDate := copy(tmpTrackPart, succ(j), 4)
    else
      aExpDate := '';                             // didn't find any expDate
  end;
end;

{$ENDIF MTXEPSDLL_OR_FUEL}

{$IFDEF MTXEPSDLL}
function GetAccountNumFromTrack2Simple(var aTrack2: string): string;
var aPos: integer;
begin
  result := trim(aTrack2);
  aPos := pos('=', result);
  if (aPos > 0) then
    result := LeftStr(result, pred(aPos));
end;

function GetAccountNumFromTrack2(const aTrack2: string; aEncryptString: string = ''): string;
var
  TrackList: TStringList;
  i: integer;

  function ParsePAN(aStr: string): string;
  var
    idx: integer;
  begin
    result := '';
    idx := Pos('^', aStr);
    if (idx > 0) then
      result := LeftStr(aStr, pred(idx));
    if (result = '') then // failed to get PAN using "^"
    begin
      idx := Pos('=', aStr);
      if (idx > 0) then
        result := LeftStr(aStr, pred(idx));
    end;
    result := trim(result);
    if NOT IsNumeric(result) then
      result := '';
  end;
begin
  result := trim(aTrack2);
  if (result = '') and (termType in MX800TermSet) then //69739 removed usingServerEPSHost
  begin
    trackList := TStringList.Create;
    //trackList.Delimiter := FS;
    try
      //trackList.Text := TrackDataPart[TrackDataPart1Index] + TrackDataPart[TrackDataPart2Index];
      SplitStr(TrackDataPart[TrackDataPart1Index] + TrackDataPart[TrackDataPart2Index], FS, trackList);
      i := 0;
      while (i <= trackList.Count -1) do
      begin
        result := ParsePan(trackList[i]);
        if result <> '' then
          Break;
        i := i + 1;
      end;
      if result = '' then
      begin
        if IsTrack2InMXData(aEncryptString, result)
          then result := MXPan
          else result := '';
      end;
    finally
      for i := 0 to trackList.Count - 1 do
        trackList[i] := stringOfChar(' ', Length(trackList[i])); // PCI
      trackList.Clear;
      FreeAndNil(trackList);
    end;
  end
  else
    result := GetAccountNumFromTrack2Simple(result);
end;

function IsPANOk(aid: byte; testPAN: string): boolean;
var i: integer;
    lenOK: boolean;
begin
  result := IsPANValid(testPAN, i, lenOK);

  if not result then
    if not lenOK
      then showTrace(aid,format('PAN is incorrect length = %d',[length(testPAN)]))
      else showTrace(aid,format('PAN char >%s< at position %d is invalid.',[testPAN[i],i]));
end;

function IsPANOkP2P(aid: byte; testPAN: string; testLen: integer): boolean;
begin
  result := IsPANOk(aid, testPAN);
  if result then
  begin
    result := IsPANLengthValid(testLen);
    if not result then
      showTrace(aid,format('PAN is incorrect length = %d',[testLen]));
  end
end;

{ // XE: Remove WinEPS
function IsExpDateOkP2P(aExpirationDate: string): boolean;  // expects expDate as YYMM format
var mm: integer;
begin
  result := true;
  if (aExpirationDate <> '') then       // only check if we have an exp date, if there isn't one, just get out
  begin
    result := (length(aExpirationDate) = 4) and (StrToIntDef(aExpirationDate, -1) > 0);  // length is 4 and is a valid number
    if result then
    begin
      mm := StrToIntDef(copy(aExpirationDate,3,2), 0);
      result := (mm >= 1) and (mm <= 12);             // month is valid from 1 to 12
    end;
  end;
end;
}

{$ENDIF MTXEPSDLL}

function GetMaskedValueByXML(Value: string; ReturnBlank: Boolean=false): string;
begin
  result := Value;
  //if Assigned(XMLStoreConfigurations) and SameText(XMLStoreConfigurations.ProcessingOptions.DoNotRetainCustomerName, 'Y') then
  if SameText(DSProcBuf.DoNotRetainCustomerName, 'Y') then
  begin
    if ReturnBlank
      then result := '' // do not send CustName to WinEPS or ServerEPS
      else result := TruncAnyString(Value);
  end;
end;

initialization
  ExtendedLog('OpenEPSCalls Initialization');
finalization
  ExtendedLog('OpenEPSCalls Finalization');

end.
