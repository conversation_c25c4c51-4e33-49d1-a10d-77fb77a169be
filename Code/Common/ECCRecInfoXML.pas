// (c) MTXEPS, Inc. 1988-2008
unit ECCRecInfoXML;

interface

uses
  Classes, SysUtils, IdGlobal;

type

{ Forward Decls }

  TXMLRECINFOType = class;
  TXMLHostType = class;

{ TXMLRECINFOType }

  TXMLRECINFOType = class(TCollection)
  private
    FVersion: string;
    FLastModified: string;
    function GetVersion: string;
    function GetLastModified: string;
    function GetHost(Index: Integer): TXMLHostType;
    procedure SetVersion(Value: string);
    procedure SetLastModified(Value: string);
  public
    property Version: string read GetVersion write SetVersion;
    property LastModified: string read GetLastModified write SetLastModified;
    property Host[Index: Integer]: TXMLHostType read GetHost; default;

    constructor Create(ItemClass: TCollectionItemClass);
    destructor Destroy; override;
    function Add: TXMLHostType;
    function LoadFromFile(aFileName: string): boolean;
    function SaveToFile(aFileName: string): boolean;
  end;

{ TXMLHostType }

  TXMLHostType = class(TCollectionItem)
  private
    FName: string;
    FPhoneNumber: string;
    FReturnCheckFee: string;

    function GetName: string;
    function GetPhoneNumber: string;
    function GetReturnCheckFee: string;
    procedure SetName(Value: string);
    procedure SetPhoneNumber(Value: string);
    procedure SetReturnCheckFee(Value: string);
  public
    AcceptanceAgreement: TStringList;
    constructor Create(Collection: TCollection);
    destructor Destroy; override;
    property Name: string read GetName write SetName;
    property PhoneNumber: string read GetPhoneNumber write SetPhoneNumber;
    property ReturnCheckFee: string read GetReturnCheckFee write SetReturnCheckFee;
  end;

{ Global Functions }

function LoadRECINFO(const FileName: string): TXMLRECINFOType;
function NewRECINFO: TXMLRECINFOType;

implementation

uses
  MTX_Constants,
  MTX_Lib,
  UXMLCommon,
  MTX_XMLClasses;

const
  _RECINFO = 'RECINFO';
        _Version = 'Version';
        _LastModified = 'LastModified';
  _Host = 'Host';
        _Name = 'Name';
        _AcceptanceAgreement = 'AcceptanceAgreement';
                _Line = 'Line';
        _PhoneNumber = 'PhoneNumber';
        _ReturnCheckFee = 'ReturnCheckFee';

{ Global Functions }

function LoadRECINFO(const FileName: string): TXMLRECINFOType;
begin
  result := TXMLRECINFOType.Create(TXMLHostType);
  if Assigned(result) then
    if not result.LoadFromFile(FileName) then
      FreeAndNil(result);
end;

function NewRECINFO: TXMLRECINFOType;
begin
  result := TXMLRECINFOType.Create(TXMLHostType);
end;

{ TXMLRECINFOType }

constructor TXMLRECINFOType.Create(ItemClass: TCollectionItemClass);
begin
  inherited;
  ;
end;

destructor TXMLRECINFOType.Destroy;
begin
  ;
  inherited;
end;

function TXMLRECINFOType.LoadFromFile(aFileName: string): boolean;
var
  I, J, K: Integer;
  XMLConfig: TXMLConfiguration;
  N1, N2, N3: TXMLParserNode;
  aHost: TXMLHostType;
begin
  result := false;
  try
    XMLConfig := TXMLConfiguration.Create;
    try
      XMLConfig.FXMLParser.LoadFromFile(aFileName);
      XMLConfig.FXMLParser.StartScan;
      XMLConfig.ScanElement(nil);
      if NOT SameText(XMLConfig.Root.Name, 'RECINFO') then
      begin
        XMLConfig.Free;
        Exit;
      end;
      {
      Version := XMLConfig.Root.Attr.Values['Version'];
      if Version = '' then Version := DEFAULT_XML_VERSION;
      LastModified := XMLConfig.Root.Attr.Values['LastModified'];
      }
      for I := 0 to XMLConfig.Root.Children.Count - 1 do
      begin
        N1 := XMLConfig.Root.Children[I];
        aHost := TXMLHostType.Create(Self);
        aHost.Name := N1.Name;
        for J := 0 to N1.Children.Count - 1 do
        begin
          N2 := N1.Children[J];
          if SameText(N2.Name, _AcceptanceAgreement) then
          begin
            for K := 0 to N2.Children.Count - 1 do
            begin
              N3 := N2.Children[K];
              aHost.AcceptanceAgreement.Add(N3.Text);
            end;
          end
          else
          if SameText(N2.Name, _PhoneNumber) then
            aHost.PhoneNumber := N2.Text
          else
          if SameText(N2.Name, _ReturnCheckFee) then
            aHost.ReturnCheckFee := N2.Text;
        end;
      end;
      //ValidateVersion;                                                          
    finally
      XMLConfig.Free;
    end;
    result := true;
  except
    ;
  end;
end;

function TXMLRECINFOType.SaveToFile(aFileName: string): boolean;
var
  Root, N1, N2, N3, N4: TXMLParserNode;
  i, j, k: Integer;
begin
  result := false;
  try
    Root := TXMLParserNode.Create(nil);
    try
      Root.Name := _RECINFO;
      Root.Attr.Values[_Version] := Format('%.1f', [GetValidXMLVersion(xfECCRecInfo)]);
      Root.Attr.Values[_LastModified] := FormatDateTime(FORMAT_LASTMODIFIED, Now);
      for i := 0 to Self.Count -1 do
      begin
        N1 := Root.AddChild(Host[i].Name);
        N2 := N1.AddChild(_AcceptanceAgreement);
        for j := 0 to Host[i].AcceptanceAgreement.Count - 1 do
          N2.AddChild('Line' + IntToStr(j+1)).Text := Host[i].AcceptanceAgreement.Strings[j];
        N1.AddChild(_PhoneNumber).Text := Host[i].PhoneNumber;
        N1.AddChild(_ReturnCheckFee).Text := Host[i].ReturnCheckFee;
      end;
      Root.SaveToFile(aFileName);
    finally
      FreeAndNil(Root);
    end;
    result := true;
  except
    on e: exception do
      SM('****TRY..EXCEPT: TFECCRecInfo.SaveXML: ' + aFileName + ' - ' + e.message);
  end;
end;

function TXMLRECINFOType.Add: TXMLHostType;
begin
  result := TXMLHostType.Create(Self);
end;

function TXMLRECINFOType.GetVersion: string;
begin
  Result := FVersion;
end;

procedure TXMLRECINFOType.SetVersion(Value: string);
begin
  if Value = FVersion then Exit;
  FVersion := Value;
end;

function TXMLRECINFOType.GetLastModified: string;
begin
  Result := FLastModified;
end;

procedure TXMLRECINFOType.SetLastModified(Value: string);
begin
  if Value = FLastModified then Exit;
  FLastModified := Value;
end;

function TXMLRECINFOType.GetHost(Index: Integer): TXMLHostType;
begin
  result := inherited Items[Index] as TXMLHostType;
end;

{ TXMLHostType }

constructor TXMLHostType.Create(Collection: TCollection);
begin
  inherited;
  AcceptanceAgreement := TStringList.Create;
end;

destructor TXMLHostType.Destroy;
begin
  FreeAndNil(AcceptanceAgreement);
  inherited;
end;

function TXMLHostType.GetName: string;
begin
  Result := FName;
end;

procedure TXMLHostType.SetName(Value: string);
begin
  if Value = FName then Exit;
  FName := Value;
end;

function TXMLHostType.GetPhoneNumber: string;
begin
  Result := FPhoneNumber;
end;

procedure TXMLHostType.SetPhoneNumber(Value: string);
begin
  if Value = FPhoneNumber then Exit;
  FPhoneNumber := Value;
end;

function TXMLHostType.GetReturnCheckFee: string;
begin
  Result := FReturnCheckFee;
end;

procedure TXMLHostType.SetReturnCheckFee(Value: string);
begin
  if Value = FReturnCheckFee then Exit;
  FReturnCheckFee := Value;
end;

end.
