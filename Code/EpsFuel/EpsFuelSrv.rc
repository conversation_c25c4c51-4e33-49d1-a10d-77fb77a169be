//MAINICON ICON "WinEPS.ico"
///////////////////////////////////////////////////////////////////////
//
// Version
//

#include "EpsFuelSrv_VersionNo.h"
VS_VERSION_INFO VERSIONINFO
 FILEVERSION FILEVER
 PRODUCTVERSION PRODUCTVER
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x4L
 FILETYPE 0x1L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
			VALUE "CompanyName", "Retalix, Inc.\0"
			VALUE "FileDescription", "EpsFuelService\0"
			VALUE "FileVersion", STRFILEVER
			VALUE "InternalName", "MTX_EPS\0"
			VALUE "LegalCopyright", STRLEGALCOPYRIGHT
			VALUE "LegalTrademarks", "OpenEPS\0"
			VALUE "OriginalFilename", "MTX_EPS\0"
			VALUE "ProductName", "OpenEPS\0"
			VALUE "ProductVersion", STRPRODUCTVER
        	VALUE "Built By", STRBUILTBY
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END